#!/usr/bin/env python3

from dataclasses import dataclass
from enum import Enum
from typing import (
    Any,
    DefaultDict,
    Literal,
    Protocol,
    runtime_checkable,
    TypedDict,
    Union,
)
import pydantic

from typing import NotRequired


LogLevels = Literal["debug", "info", "warning", "error"]


@dataclass
class SpotData:
    qualified_name: str
    exchange: str
    base: str
    quote: str

    def __post_init__(self) -> None:
        if self.quote == "ZUSD":
            self.quote = "USD"


class AssetTypes(Enum):
    SPOT = "spot"
    PERPETUAL = "perpetual"
    FUTURE = "future"
    OPTION = "option"


class OptionTypes(Enum):
    CALL = "C"
    PUT = "P"


class BaseInstrumentDetails(TypedDict):
    qualified_name: str
    instrument: str
    baseAsset: str
    quoteAsset: str


class SpotInstrumentDetails(BaseInstrumentDetails):
    pass


class PerpetualInstrumentDetails(BaseInstrumentDetails):
    settlementAsset: str


class FutureInstrumentDetails(BaseInstrumentDetails):
    settlementAsset: str
    expiry: str


class OptionInstrumentDetails(BaseInstrumentDetails):
    settlementAsset: str
    expiry: str
    strike: float
    type: str


InstrumentDetails = Union[
    SpotInstrumentDetails,
    PerpetualInstrumentDetails,
    FutureInstrumentDetails,
    OptionInstrumentDetails,
]

InstrumentsDetailsMap = dict[str, InstrumentDetails]


class GetDateBucketEdgesOptions(TypedDict, total=False):
    right_inclusive: bool
    left_inclusive: bool
    right_offset: int
    left_offset: int


@runtime_checkable
class LogFn(Protocol):
    def __call__(
        self, level: LogLevels, log_msg: str, *args: Any, **kwargs: Any
    ) -> None:
        ...


TimestampPrecision = Literal["s", "ms", "ns", "us"]


class OptionData(OptionInstrumentDetails):
    underlying_index: str
    underlying_price: float
    bid_price: float
    ask_price: float
    mid_price: float
    ffilled: bool
    smoothed: bool


OptionDataMapping = dict[str, OptionData]  # instrument -> OptionData


class SnapUnderlyingData(TypedDict, total=False):
    underlying_index: str
    underlying_price: float  # underlying forward price
    options: OptionDataMapping


class DataQueryResult(TypedDict, total=False):
    timestamp: int
    qualified_name: str
    ffilled: NotRequired[bool]


class DataQueryPxResult(DataQueryResult):
    px: float


class SnapDetailsWithMultiSpot(TypedDict, total=False):
    spot: NotRequired[float]
    bs_spot: NotRequired[bool]
    spot_ffilled: NotRequired[bool]
    spot_timestamp: NotRequired[int]
    spot_quote: NotRequired[float]
    bs_spot_quote: NotRequired[bool]
    spot_quote_ffilled: NotRequired[bool]
    spot_quote_timestamp: NotRequired[int]
    timestamp: int
    data: dict[str, SnapUnderlyingData]


class InstrumentNameDetails(TypedDict):
    underlying_name: str
    base: str
    quote: str | None
    expiry: str | None
    uniform_expiry: str | None
    strike: float | None
    type: str | None
    syn: bool | None
    pcp: bool | None


class SpotUSDQuotedConfig(pydantic.BaseModel):
    use_mids: bool = True
    use_exchange_indices: bool = True
    number_per_ccy: int = 5
    run_type: Literal["scheduled", "live"] = "scheduled"
    use_conversions: bool = True


class ConstantMaturityEnum(Enum):
    DAY = "d"
    MINUTE = "m"


class ExchangeSpotSnap(TypedDict):
    ffilled: NotRequired[bool]
    px: float


# timestamp -> { base -> { quote -> { exchange -> { ffilled, px } } }
SnapshotSpotDetails = DefaultDict[
    int, DefaultDict[str, DefaultDict[str, DefaultDict[str, ExchangeSpotSnap]]]
]


class IndicesConfigItemComponent(pydantic.BaseModel):
    exchange: str
    base: str
    quote: str


class IndicesConfigItem(pydantic.BaseModel):
    index_currency: Literal["USD"] = "USD"
    components: list[IndicesConfigItemComponent] = []


class IndicesConfig(pydantic.BaseModel):
    spot: IndicesConfigItem = IndicesConfigItem()
