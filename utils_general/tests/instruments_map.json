{"binance,spot,ARB,USDT": "ARBUSDT", "binance,spot,BTC,USDT": "BTCUSDT", "binance,spot,ETH,USDT": "ETHUSDT", "binance,spot,LINK,USDT": "LINKUSDT", "binance,spot,OP,USDT": "OPUSDT", "binance,spot,SOL,USDT": "SOLUSDT", "binance,spot,XRP,USDT": "XRPUSDT", "bitstamp,spot,BTC,USD": "BTC-USD", "bitstamp,spot,BTC,USDT": "BTC-USDT", "bitstamp,spot,ETH,USD": "ETH-USD", "bitstamp,spot,ETH,USDT": "ETH-USDT", "bitstamp,spot,LINK,USD": "LINK-USD", "bitstamp,spot,SOL,USD": "SOL-USD", "bitstamp,spot,USDC,USD": "USDC-USD", "bitstamp,spot,USDC,USDT": "USDC-USDT", "bitstamp,spot,USDT,USD": "USDT-USD", "bitstamp,spot,XRP,USD": "XRP-USD", "bitstamp,spot,XRP,USDT": "XRP-USDT", "bitfinex,spot,ARB,USD": "ARBUSD", "bitfinex,spot,ARB,USDT": "ARBUSDT", "bitfinex,spot,BTC,USD": "BTCUSD", "bitfinex,spot,BTC,USDT": "BTCUSDT", "bitfinex,spot,ETH,USD": "ETHUSD", "bitfinex,spot,ETH,USDT": "ETHUSDT", "bitfinex,spot,LINK,USD": "LINK:USD", "bitfinex,spot,LINK,USDT": "LINK:USDT", "bitfinex,spot,SOL,USD": "SOLUSD", "bitfinex,spot,SOL,USDT": "SOLUSDT", "bitfinex,spot,USDC,USD": "USDCUSD", "bitfinex,spot,USDC,USDT": "USDCUSDT", "bitfinex,spot,USDT,USD": "USDTUSD", "bitfinex,spot,XRP,USD": "XRPUSD", "bitfinex,spot,XRP,USDT": "XRPUSDT", "coinbase,spot,ARB,USD": "ARB-USD", "coinbase,spot,BTC,USD": "BTC-USD", "coinbase,spot,BTC,USDT": "BTC-USDT", "coinbase,spot,ETH,USD": "ETH-USD", "coinbase,spot,ETH,USDT": "ETH-USDT", "coinbase,spot,LINK,USD": "LINK-USD", "coinbase,spot,LINK,USDT": "LINK-USDT", "coinbase,spot,OP,USD": "OP-USD", "coinbase,spot,OP,USDT": "OP-USDT", "coinbase,spot,SOL,USD": "SOL-USD", "coinbase,spot,SOL,USDT": "SOL-USDT", "coinbase,spot,USDT,USD": "USDT-USD", "coinbase,spot,XRP,USD": "XRP-USD", "coinbase,spot,XRP,USDT": "XRP-USDT", "bitget,spot,ARB,USDT": "ARBUSDT", "bitget,spot,BTC,USDT": "BTCUSDT", "bitget,spot,ETH,USDT": "ETHUSDT", "bitget,spot,LINK,USDT": "LINKUSDT", "bitget,spot,OP,USDT": "OPUSDT", "bitget,spot,SOL,USDT": "SOLUSDT", "bitget,spot,XRP,USDT": "XRPUSDT", "okx,spot,ARB,USD": "ARB-USD", "okx,spot,ARB,USDT": "ARB-USDT", "okx,spot,BTC,USD": "BTC-USD", "okx,spot,BTC,USDT": "BTC-USDT", "okx,spot,ETH,USD": "ETH-USD", "okx,spot,ETH,USDT": "ETH-USDT", "okx,spot,LINK,USD": "LINK-USD", "okx,spot,LINK,USDT": "LINK-USDT", "okx,spot,OP,USD": "OP-USD", "okx,spot,OP,USDT": "OP-USDT", "okx,spot,SOL,USDT": "SOL-USDT", "okx,spot,XRP,USD": "XRP-USD", "okx,spot,XRP,USDT": "XRP-USDT", "bybit,spot,BTC,USDT": "BTCUSDT", "bybit,spot,ETH,USDT": "ETHUSDT", "bybit,spot,LINK,USDT": "LINKUSDT", "bybit,spot,SOL,USDT": "SOLUSDT", "bybit,spot,XRP,USDT": "XRPUSDT", "kraken,spot,ARB,ZUSD": "ARB-USD", "kraken,spot,BTC,ZUSD": "BTC-USD", "kraken,spot,BTC,USDT": "BTC-USDT", "kraken,spot,ETH,ZUSD": "ETH-USD", "kraken,spot,ETH,USDT": "ETH-USDT", "kraken,spot,LINK,ZUSD": "LINK-USD", "kraken,spot,LINK,USDT": "LINK-USDT", "kraken,spot,OP,ZUSD": "OP-USD", "kraken,spot,SOL,ZUSD": "SOL-USD", "kraken,spot,SOL,USDT": "SOL-USDT", "kraken,spot,USDC,ZUSD": "USDC-USD", "kraken,spot,USDC,USDT": "USDC-USDT", "kraken,spot,USDT,ZUSD": "USDT-USD", "kraken,spot,XRP,ZUSD": "XRP-USD", "kraken,spot,XRP,USDT": "XRP-USDT", "cryptocom,spot,ARB,USD": "ARB_USD", "cryptocom,spot,ARB,USDT": "ARB_USDT", "cryptocom,spot,BTC,USD": "BTC_USD", "cryptocom,spot,BTC,USDT": "BTC_USDT", "cryptocom,spot,ETH,USD": "ETH_USD", "cryptocom,spot,ETH,USDT": "ETH_USDT", "cryptocom,spot,LINK,USD": "LINK_USD", "cryptocom,spot,LINK,USDT": "LINK_USDT", "cryptocom,spot,OP,USD": "OP_USD", "cryptocom,spot,OP,USDT": "OP_USDT", "cryptocom,spot,SOL,USD": "SOL_USD", "cryptocom,spot,SOL,USDT": "SOL_USDT", "cryptocom,spot,USDT,USD": "USDT_USD", "cryptocom,spot,XRP,USD": "XRP_USD", "cryptocom,spot,XRP,USDT": "XRP_USDT", "binance,spot,AAVE,USDT": "AAVEUSDT", "binance,spot,ATOM,USDT": "ATOMUSDT", "binance,spot,DOGE,USDT": "DOGEUSDT", "binance,spot,GRT,USDT": "GRTUSDT", "binance,spot,NEAR,USDT": "NEARUSDT", "binance,spot,SUI,USDT": "SUIUSDT", "binance,spot,TIA,USDT": "TIAUSDT", "bitstamp,spot,AAVE,USD": "AAVE-USD", "bitstamp,spot,DOGE,USD": "DOGE-USD", "bitstamp,spot,GRT,USD": "GRT-USD", "bitstamp,spot,NEAR,USD": "NEAR-USD", "bitstamp,spot,ONDO,USD": "ONDO-USD", "bitstamp,spot,SNX,USD": "SNX-USD", "bitstamp,spot,SUI,USD": "SUI-USD", "bitfinex,spot,AAVE,USD": "AAVE:USD", "bitfinex,spot,AAVE,USDT": "AAVE:USDT", "bitfinex,spot,DOGE,USD": "DOGE:USD", "bitfinex,spot,DOGE,USDT": "DOGE:USDT", "bitfinex,spot,GRT,USD": "GRTUSD", "bitfinex,spot,GRT,USDT": "GRTUSDT", "bitfinex,spot,NEAR,USD": "NEAR:USD", "bitfinex,spot,NEAR,USDT": "NEAR:USDT", "bitfinex,spot,SNX,USD": "SNXUSD", "bitfinex,spot,SNX,USDT": "SNXUSDT", "bitfinex,spot,SUI,USD": "SUIUSD", "bitfinex,spot,SUI,USDT": "SUIUSDT", "bitfinex,spot,TIA,USD": "TIAUSD", "bitfinex,spot,TIA,USDT": "TIAUSDT", "coinbase,spot,AAVE,USD": "AAVE-USD", "coinbase,spot,ATOM,USD": "ATOM-USD", "coinbase,spot,ATOM,USDT": "ATOM-USDT", "coinbase,spot,DOGE,USD": "DOGE-USD", "coinbase,spot,DOGE,USDT": "DOGE-USDT", "coinbase,spot,GRT,USD": "GRT-USD", "coinbase,spot,NEAR,USD": "NEAR-USD", "coinbase,spot,NEAR,USDT": "NEAR-USDT", "coinbase,spot,ONDO,USD": "ONDO-USD", "coinbase,spot,SNX,USD": "SNX-USD", "coinbase,spot,SUI,USD": "SUI-USD", "coinbase,spot,TIA,USD": "TIA-USD", "bitget,spot,AAVE,USDT": "AAVEUSDT", "bitget,spot,ATOM,USDT": "ATOMUSDT", "bitget,spot,DOGE,USDT": "DOGEUSDT", "bitget,spot,GRT,USDT": "GRTUSDT", "bitget,spot,NEAR,USDT": "NEARUSDT", "bitget,spot,ONDO,USDT": "ONDOUSDT", "bitget,spot,SUI,USDT": "SUIUSDT", "bitget,spot,TIA,USDT": "TIAUSDT", "okx,spot,AAVE,USD": "AAVE-USD", "okx,spot,AAVE,USDT": "AAVE-USDT", "okx,spot,ATOM,USD": "ATOM-USD", "okx,spot,ATOM,USDT": "ATOM-USDT", "okx,spot,DOGE,USD": "DOGE-USD", "okx,spot,DOGE,USDT": "DOGE-USDT", "okx,spot,GRT,USD": "GRT-USD", "okx,spot,GRT,USDT": "GRT-USDT", "okx,spot,NEAR,USD": "NEAR-USD", "okx,spot,NEAR,USDT": "NEAR-USDT", "okx,spot,ONDO,USD": "ONDO-USD", "okx,spot,ONDO,USDT": "ONDO-USDT", "okx,spot,SUI,USD": "SUI-USD", "okx,spot,SUI,USDT": "SUI-USDT", "okx,spot,TIA,USD": "TIA-USD", "okx,spot,TIA,USDT": "TIA-USDT", "bybit,spot,AAVE,USDT": "AAVEUSDT", "bybit,spot,DOGE,USDT": "DOGEUSDT", "bybit,spot,NEAR,USDT": "NEARUSDT", "bybit,spot,SUI,USDT": "SUIUSDT", "bybit,spot,TIA,USDT": "TIAUSDT", "kraken,spot,AAVE,ZUSD": "AAVE-USD", "kraken,spot,ATOM,ZUSD": "ATOM-USD", "kraken,spot,ATOM,USDT": "ATOM-USDT", "kraken,spot,GRT,ZUSD": "GRT-USD", "kraken,spot,NEAR,ZUSD": "NEAR-USD", "kraken,spot,ONDO,ZUSD": "ONDO-USD", "kraken,spot,SNX,ZUSD": "SNX-USD", "kraken,spot,SUI,ZUSD": "SUI-USD", "kraken,spot,TIA,ZUSD": "TIA-USD", "cryptocom,spot,AAVE,USD": "AAVE_USD", "cryptocom,spot,AAVE,USDT": "AAVE_USDT", "cryptocom,spot,ATOM,USD": "ATOM_USD", "cryptocom,spot,ATOM,USDT": "ATOM_USDT", "cryptocom,spot,DOGE,USD": "DOGE_USD", "cryptocom,spot,DOGE,USDT": "DOGE_USDT", "cryptocom,spot,GRT,USD": "GRT_USD", "cryptocom,spot,GRT,USDT": "GRT_USDT", "cryptocom,spot,NEAR,USD": "NEAR_USD", "cryptocom,spot,NEAR,USDT": "NEAR_USDT", "cryptocom,spot,ONDO,USD": "ONDO_USD", "cryptocom,spot,ONDO,USDT": "ONDO_USDT", "cryptocom,spot,SNX,USD": "SNX_USD", "cryptocom,spot,SNX,USDT": "SNX_USDT", "cryptocom,spot,SUI,USD": "SUI_USD", "cryptocom,spot,SUI,USDT": "SUI_USDT", "cryptocom,spot,TIA,USD": "TIA_USD", "cryptocom,spot,TIA,USDT": "TIA_USDT", "binance,spot,BNB,USDT": "BNBUSDT", "binance,spot,ENA,USDT": "ENAUSDT", "binance,spot,TAO,USDT": "TAOUSDT", "binance,spot,TRUMP,USDT": "TRUMPUSDT", "binance,spot,UNI,USDT": "UNIUSDT", "binance,spot,WLD,USDT": "WLDUSDT", "bitstamp,spot,UNI,USD": "UNI-USD", "curve-ethereum,spot,USDE,USD": "USDE-USD", "curve-ethereum,spot,WEETH,USD": "WEETH-USD", "kyberswap-ethereum,spot,BITCOIN,ETH": "BITCOIN-ETH", "kyberswap-ethereum,spot,USDE,USDT": "USDE-USDT", "kyberswap-ethereum,spot,WEETH,ETH": "WEETH-ETH", "coinbase,spot,UNI,USD": "UNI-USD", "bitget,spot,BITCOIN,USDT": "BITCOINUSDT", "bitget,spot,BNB,USDT": "BNBUSDT", "bitget,spot,ENA,USDT": "ENAUSDT", "bitget,spot,TAO,USDT": "TAOUSDT", "bitget,spot,UNI,USDT": "UNIUSDT", "bitget,spot,WLD,USDT": "WLDUSDT", "okx,spot,BNB,USD": "BNB-USD", "okx,spot,BNB,USDT": "BNB-USDT", "okx,spot,TRUMP,USD": "TRUMP-USD", "okx,spot,TRUMP,USDT": "TRUMP-USDT", "okx,spot,UNI,USD": "UNI-USD", "okx,spot,UNI,USDT": "UNI-USDT", "okx,spot,WLD,USD": "WLD-USD", "okx,spot,WLD,USDT": "WLD-USDT", "v3uniswap-ethereum,spot,BITCOIN,ETH": "BITCOIN-ETH", "v3uniswap-ethereum,spot,USDE,USDT": "USDE-USDT", "v3uniswap-ethereum,spot,WEETH,ETH": "WEETH-ETH", "bybit,spot,BNB,USDT": "BNBUSDT", "bybit,spot,HPOS10I,USDT": "HPOS10IUSDT", "bybit,spot,TRUMP,USDT": "TRUMPUSDT", "bybit,spot,UNI,USDT": "UNIUSDT", "bybit,spot,WLD,USDT": "WLDUSDT", "kraken,spot,ENA,ZUSD": "ENA-USD", "kraken,spot,TAO,ZUSD": "TAO-USD", "kraken,spot,TRUMP,ZUSD": "TRUMP-USD", "kraken,spot,TRUMP,USDT": "TRUMP-USDT", "kraken,spot,UNI,ZUSD": "UNI-USD", "gateio,spot,BITCOIN,USDT": "BITCOIN_USDT", "gateio,spot,BNB,USDT": "BNB_USDT", "gateio,spot,ENA,USDT": "ENA_USDT", "gateio,spot,HPOS10I,USDT": "HPOS10I_USDT", "gateio,spot,TAO,USDT": "TAO_USDT", "gateio,spot,UNI,ETH": "UNI_ETH", "gateio,spot,UNI,USD": "UNI_USD", "gateio,spot,UNI,USDT": "UNI_USDT", "gateio,spot,WLD,USDT": "WLD_USDT", "cryptocom,spot,ENA,USD": "ENA_USD", "cryptocom,spot,ENA,USDT": "ENA_USDT", "cryptocom,spot,TAO,USD": "TAO_USD", "cryptocom,spot,TRUMP,USD": "TRUMP_USD", "cryptocom,spot,UNI,USD": "UNI_USD", "cryptocom,spot,UNI,USDT": "UNI_USDT", "cryptocom,spot,WLD,USD": "WLD_USD", "cryptocom,spot,WLD,USDT": "WLD_USDT", "zerox-ethereum,spot,BITCOIN,ETH": "BITCOIN-ETH", "zerox-ethereum,spot,USDE,USDT": "USDE-USDT", "zerox-ethereum,spot,WEETH,ETH": "WEETH-ETH", "bitmart,spot,BNB,USDT": "BNB_USDT", "bitmart,spot,UNI,USDT": "UNI_USDT", "binance,spot,AVAX,USDT": "AVAXUSDT", "binance,spot,EIGEN,USDT": "EIGENUSDT", "binance,spot,SEI,USDT": "SEIUSDT", "binance,spot,ZK,USDT": "ZKUSDT", "bitstamp,spot,AVAX,USD": "AVAX-USD", "curve-ethereum,spot,DAI,USD": "DAI-USD", "curve-ethereum,spot,RSETH,USD": "RSETH-USD", "curve-ethereum,spot,RSWETH,USD": "RSWETH-USD", "curve-ethereum,spot,SDAI,USD": "SDAI-USD", "curve-ethereum,spot,SUSDE,USD": "SUSDE-USD", "kyberswap-ethereum,spot,DAI,USDT": "DAI-USDT", "kyberswap-ethereum,spot,RSETH,ETH": "RSETH-ETH", "kyberswap-ethereum,spot,RSWETH,ETH": "RSWETH-ETH", "kyberswap-ethereum,spot,SDAI,USDT": "SDAI-USDT", "kyberswap-ethereum,spot,SUSDE,USDT": "SUSDE-USDT", "coinbase,spot,AVAX,USD": "AVAX-USD", "coinbase,spot,AVAX,USDT": "AVAX-USDT", "coinbase,spot,EIGEN,USD": "EIGEN-USD", "coinbase,spot,SEI,USD": "SEI-USD", "bitget,spot,AVAX,USDT": "AVAXUSDT", "bitget,spot,EIGEN,USDT": "EIGENUSDT", "bitget,spot,SEI,USDT": "SEIUSDT", "bitget,spot,ZK,USDT": "ZKUSDT", "okx,spot,AVAX,USD": "AVAX-USD", "okx,spot,AVAX,USDT": "AVAX-USDT", "okx,spot,EIGEN,USD": "EIGEN-USD", "okx,spot,EIGEN,USDT": "EIGEN-USDT", "okx,spot,ZK,USD": "ZK-USD", "okx,spot,ZK,USDT": "ZK-USDT", "v3uniswap-ethereum,spot,DAI,ETH": "DAI-ETH", "v3uniswap-ethereum,spot,RSETH,ETH": "RSETH-ETH", "v3uniswap-ethereum,spot,RSWETH,ETH": "RSWETH-ETH", "v3uniswap-ethereum,spot,SDAI,ETH": "SDAI-ETH", "v3uniswap-ethereum,spot,SUSDE,ETH": "SUSDE-ETH", "v3uniswap-ethereum,spot,SUSDE,USDT": "SUSDE-USDT", "bybit,spot,AVAX,USDT": "AVAXUSDT", "bybit,spot,EIGEN,USDT": "EIGENUSDT", "bybit,spot,SEI,USDT": "SEIUSDT", "bybit,spot,ZK,USDT": "ZKUSDT", "kraken,spot,AVAX,ZUSD": "AVAX-USD", "kraken,spot,AVAX,USDT": "AVAX-USDT", "kraken,spot,EIGEN,ZUSD": "EIGEN-USD", "kraken,spot,SEI,ZUSD": "SEI-USD", "oneinch-ethereum,spot,SDAI,USD": "SDAI-USD", "gateio,spot,AVAX,USDT": "AVAX_USDT", "gateio,spot,ZK,USDT": "ZK_USDT", "cryptocom,spot,AVAX,USD": "AVAX_USD", "cryptocom,spot,AVAX,USDT": "AVAX_USDT", "cryptocom,spot,EIGEN,USD": "EIGEN_USD", "cryptocom,spot,SEI,USD": "SEI_USD", "cryptocom,spot,SEI,USDT": "SEI_USDT", "cryptocom,spot,ZK,USD": "ZK_USD", "cryptocom,spot,ZK,USDT": "ZK_USDT", "zerox-ethereum,spot,DAI,USDT": "DAI-USDT", "zerox-ethereum,spot,RSETH,ETH": "RSETH-ETH", "zerox-ethereum,spot,RSWETH,ETH": "RSWETH-ETH", "zerox-ethereum,spot,SDAI,USDT": "SDAI-USDT", "bitmart,spot,AVAX,USDT": "AVAX_USDT", "binance,spot,ADA,USDT": "ADAUSDT", "bitstamp,spot,ADA,USD": "ADA-USD", "curve-ethereum,spot,WSTETH,USD": "WSTETH-USD", "kyberswap-ethereum,spot,WSTETH,ETH": "WSTETH-ETH", "coinbase,spot,ADA,USD": "ADA-USD", "coinbase,spot,ADA,USDT": "ADA-USDT", "bitget,spot,ADA,USDT": "ADAUSDT", "okx,spot,ADA,USD": "ADA-USD", "okx,spot,ADA,USDT": "ADA-USDT", "v3uniswap-ethereum,spot,WSTETH,ETH": "WSTETH-ETH", "kraken,spot,ADA,ZUSD": "ADA-USD", "kraken,spot,ADA,USDT": "ADA-USDT", "gateio,spot,ADA,USDT": "ADA_USDT", "cryptocom,spot,ADA,USD": "ADA_USD", "cryptocom,spot,ADA,USDT": "ADA_USDT", "zerox-ethereum,spot,WSTETH,ETH": "WSTETH-ETH", "bitmart,spot,ADA,USDT": "ADA_USDT", "kyberswap-ethereum,spot,EBTC,USDT": "EBTC-USDT", "kyberswap-ethereum,spot,EBTC,WBTC": "EBTC-WBTC", "kyberswap-ethereum,spot,LBTC,USDT": "LBTC-USDT", "kyberswap-ethereum,spot,OLAS,ETH": "OLAS-ETH", "bybit,spot,AI16Z,USDT": "AI16ZUSDT", "bybit,spot,IP,USDT": "IPUSDT", "bybit,spot,WIF,USDT": "WIFUSDT", "kraken,spot,AI16Z,ZUSD": "AI16Z-USD", "kraken,spot,AI16Z,USDC": "AI16Z-USDC", "kraken,spot,AI16Z,USDT": "AI16Z-USDT", "kraken,spot,PEPE,ZUSD": "PEPE-USD", "kraken,spot,POL,ZUSD": "POL-USD", "kraken,spot,VINE,ZUSD": "VINE-USD", "kraken,spot,WIF,ZUSD": "WIF-USD", "jupiter-solana,spot,OLAS,USDC": "OLAS-USDC", "bitmart,spot,TON,USDT": "TON_USDT", "coinbase,spot,IP,USD": "IP-USD", "coinbase,spot,POL,USD": "POL-USD", "coinbase,spot,WIF,USD": "WIF-USD", "bitget,spot,AI16Z,USDT": "AI16ZUSDT", "bitget,spot,IP,USDT": "IPUSDT", "bitget,spot,PEPE,USDC": "PEPEUSDC", "bitget,spot,PEPE,USDT": "PEPEUSDT", "bitget,spot,POL,USDC": "POLUSDC", "bitget,spot,POL,USDT": "POLUSDT", "bitget,spot,TON,USDC": "TONUSDC", "bitget,spot,TON,USDT": "TONUSDT", "bitget,spot,VINE,USDT": "VINEUSDT", "bitget,spot,WIF,USDT": "WIFUSDT", "v3uniswap-ethereum,spot,EBTC,LBTC": "EBTC-LBTC", "v3uniswap-ethereum,spot,LBTC,WBTC": "LBTC-WBTC", "curve-ethereum,spot,EBTC,USD": "EBTC-USD", "curve-ethereum,spot,LBTC,USD": "LBTC-USD", "bitstamp,spot,PEPE,USD": "PEPE-USD", "bitstamp,spot,WIF,USD": "WIF-USD", "bitfinex,spot,PEPE,USD": "PEPE:USD", "bitfinex,spot,PEPE,USDT": "PEPE:USDT", "bitfinex,spot,TON,USD": "TONUSD", "bitfinex,spot,TON,USDT": "TONUSDT", "bitfinex,spot,WIF,USD": "WIFUSD", "bitfinex,spot,WIF,USDT": "WIFUSDT", "cryptocom,spot,AI16Z,USD": "AI16Z_USD", "cryptocom,spot,PEPE,USD": "PEPE_USD", "cryptocom,spot,PEPE,USDT": "PEPE_USDT", "cryptocom,spot,POL,USD": "POL_USD", "cryptocom,spot,POL,USDT": "POL_USDT", "cryptocom,spot,TON,USD": "TON_USD", "cryptocom,spot,TON,USDT": "TON_USDT", "cryptocom,spot,VINE,USD": "VINE_USD", "cryptocom,spot,WIF,USD": "WIF_USD", "cryptocom,spot,WIF,USDT": "WIF_USDT", "binance,spot,PEPE,USDC": "PEPEUSDC", "binance,spot,PEPE,USDT": "PEPEUSDT", "binance,spot,POL,USDC": "POLUSDC", "binance,spot,POL,USDT": "POLUSDT", "binance,spot,TON,USDC": "TONUSDC", "binance,spot,TON,USDT": "TONUSDT", "binance,spot,WIF,USDC": "WIFUSDC", "binance,spot,WIF,USDT": "WIFUSDT", "okx,spot,IP,USDT": "IP-USDT", "okx,spot,PEPE,USD": "PEPE-USD", "okx,spot,PEPE,USDC": "PEPE-USDC", "okx,spot,PEPE,USDT": "PEPE-USDT", "okx,spot,POL,USD": "POL-USD", "okx,spot,POL,USDT": "POL-USDT", "okx,spot,TON,USD": "TON-USD", "okx,spot,TON,USDC": "TON-USDC", "okx,spot,TON,USDT": "TON-USDT", "okx,spot,VINE,USDT": "VINE-USDT", "okx,spot,WIF,USD": "WIF-USD", "okx,spot,WIF,USDC": "WIF-USDC", "okx,spot,WIF,USDT": "WIF-USDT", "gateio,spot,AI16Z,USDT": "AI16Z_USDT", "gateio,spot,IP,USDT": "IP_USDT", "gateio,spot,TON,USDT": "TON_USDT", "gateio,spot,VINE,USDT": "VINE_USDT", "zerox-ethereum,spot,EBTC,WBTC": "EBTC-WBTC", "zerox-ethereum,spot,LBTC,USDT": "LBTC-USDT", "zerox-ethereum,spot,OLAS,ETH": "OLAS-ETH", "kyberswap-ethereum,spot,CBBTC,USDT": "CBBTC-USDT", "kyberswap-ethereum,spot,DEUSD,USDT": "DEUSD-USDT", "kyberswap-ethereum,spot,PYUSD,USDT": "PYUSD-USDT", "kyberswap-ethereum,spot,SOLVBTC-BBN,USDT": "SOLVBTC-BBN-USDT", "kyberswap-ethereum,spot,SOLVBTC-BBN,WBTC": "SOLVBTC-BBN-WBTC", "kyberswap-ethereum,spot,WBTC,USDT": "WBTC-USDT", "kyberswap-bsc,spot,SOLVBTC-BBN,BNB": "SOLVBTC-BBN-BNB", "bybit,spot,AIXBT,USDT": "AIXBTUSDT", "bybit,spot,PENDLE,USDT": "PENDLEUSDT", "bybit,spot,UXLINK,USDT": "UXLINKUSDT", "bybit,spot,VIRTUAL,USDT": "VIRTUALUSDT", "kraken,spot,KAITO,ZUSD": "KAITO-USD", "kraken,spot,PENDLE,ZUSD": "PENDLE-USD", "kraken,spot,VIRTUAL,ZUSD": "VIRTUAL-USD", "kraken,spot,VIRTUAL,USDC": "VIRTUAL-USDC", "kraken,spot,VIRTUAL,USDT": "VIRTUAL-USDT", "bitmart,spot,WBTC,USDT": "WBTC_USDT", "coinbase,spot,KAITO,USD": "KAITO-USD", "bitget,spot,AIXBT,USDT": "AIXBTUSDT", "bitget,spot,KAITO,USDT": "KAITOUSDT", "bitget,spot,PENDLE,USDT": "PENDLEUSDT", "bitget,spot,UXLINK,USDT": "UXLINKUSDT", "bitget,spot,VIRTUAL,USDT": "VIRTUALUSDT", "v3uniswap-ethereum,spot,CBBTC,ETH": "CBBTC-ETH", "v3uniswap-ethereum,spot,DEUSD,USDC": "DEUSD-USDC", "v3uniswap-ethereum,spot,PYUSD,USDC": "PYUSD-USDC", "v3uniswap-ethereum,spot,SOLVBTC-BBN,SOLVBTC": "SOLVBTC-BBN-SOLVBTC", "v3uniswap-ethereum,spot,WBTC,ETH": "WBTC-ETH", "curve-ethereum,spot,CBBTC,USD": "CBBTC-USD", "curve-ethereum,spot,DEUSD,USD": "DEUSD-USD", "curve-ethereum,spot,PYUSD,USD": "PYUSD-USD", "curve-ethereum,spot,SOLVBTC-BBN,USD": "SOLVBTC-BBN-USD", "curve-ethereum,spot,WBTC,USD": "WBTC-USD", "cryptocom,spot,AIXBT,USD": "AIXBT_USD", "cryptocom,spot,PENDLE,USD": "PENDLE_USD", "cryptocom,spot,VIRTUAL,USD": "VIRTUAL_USD", "binance,spot,AIXBT,USDC": "AIXBTUSDC", "binance,spot,AIXBT,USDT": "AIXBTUSDT", "binance,spot,KAITO,USDC": "KAITOUSDC", "binance,spot,KAITO,USDT": "KAITOUSDT", "binance,spot,PENDLE,USDC": "PENDLEUSDC", "binance,spot,PENDLE,USDT": "PENDLEUSDT", "binance,spot,WBTC,USDT": "WBTCUSDT", "okx,spot,AIXBT,USD": "AIXBT-USD", "okx,spot,AIXBT,USDT": "AIXBT-USDT", "okx,spot,KAITO,USD": "KAITO-USD", "okx,spot,KAITO,USDT": "KAITO-USDT", "okx,spot,PENDLE,USD": "PENDLE-USD", "okx,spot,PENDLE,USDT": "PENDLE-USDT", "okx,spot,UXLINK,USD": "UXLINK-USD", "okx,spot,UXLINK,USDT": "UXLINK-USDT", "okx,spot,WBTC,USDT": "WBTC-USDT", "gateio,spot,AIXBT,USDT": "AIXBT_USDT", "gateio,spot,KAITO,USDT": "KAITO_USDT", "gateio,spot,PENDLE,USDT": "PENDLE_USDT", "gateio,spot,UXLINK,USDT": "UXLINK_USDT", "gateio,spot,VIRTUAL,USDT": "VIRTUAL_USDT", "gateio,spot,WBTC,USDT": "WBTC_USDT", "zerox-ethereum,spot,CBBTC,USDT": "CBBTC-USDT", "zerox-ethereum,spot,DEUSD,USDT": "DEUSD-USDT", "zerox-ethereum,spot,PYUSD,USDT": "PYUSD-USDT", "zerox-ethereum,spot,WBTC,USDT": "WBTC-USDT", "kyberswap-ethereum,spot,SOLVBTC,USDT": "SOLVBTC-USDT", "kyberswap-ethereum,spot,SOLVBTC,WBTC": "SOLVBTC-WBTC", "kyberswap-bsc,spot,SOLVBTC,BNB": "SOLVBTC-BNB", "bybit,spot,RED,USDT": "REDUSDT", "kraken,spot,DRV,ZUSD": "DRV-USD", "zerox-base,spot,DRV,ETH": "DRV-ETH", "zerox-base,spot,DRV,USDC": "DRV-USDC", "coinbase,spot,RED,USD": "RED-USD", "bitget,spot,RED,USDT": "REDUSDT", "v3uniswap-ethereum,spot,SOLVBTC,WBTC": "SOLVBTC-WBTC", "curve-ethereum,spot,SOLVBTC,USD": "SOLVBTC-USD", "v2lyra,spot,DRV,USDC": "DRV-USDC", "binance,spot,RED,USDT": "REDUSDT", "gateio,spot,DRV,USDT": "DRV_USDT", "gateio,spot,RED,USDT": "RED_USDT", "zerox-ethereum,spot,SOLVBTC,WBTC": "SOLVBTC-WBTC", "binance,spot,APT,USDT": "APTUSDT", "binance,spot,BCH,USDT": "BCHUSDT", "binance,spot,BERA,USDT": "BERAUSDT", "binance,spot,DOT,USDT": "DOTUSDT", "binance,spot,HBAR,USDT": "HBARUSDT", "binance,spot,JUP,USDT": "JUPUSDT", "binance,spot,LTC,USDT": "LTCUSDT", "binance,spot,NXPC,USDT": "NXPCUSDT", "binance,spot,PENGU,USDT": "PENGUUSDT", "bitstamp,spot,JUP,USD": "JUP-USD", "coinbase,spot,APT,USD": "APT-USD", "coinbase,spot,BCH,USD": "BCH-USD", "coinbase,spot,BERA,USD": "BERA-USD", "coinbase,spot,DOT,USD": "DOT-USD", "coinbase,spot,HBAR,USD": "HBAR-USD", "coinbase,spot,LTC,USD": "LTC-USD", "coinbase,spot,PENGU,USD": "PENGU-USD", "bitget,spot,APT,USDT": "APTUSDT", "bitget,spot,BCH,USDT": "BCHUSDT", "bitget,spot,BERA,USDT": "BERAUSDT", "bitget,spot,DOT,USDT": "DOTUSDT", "bitget,spot,FARTCOIN,USDT": "FARTCOINUSDT", "bitget,spot,HBAR,USDT": "HBARUSDT", "bitget,spot,JUP,USDT": "JUPUSDT", "bitget,spot,LTC,USDT": "LTCUSDT", "bitget,spot,NXPC,USDT": "NXPCUSDT", "bitget,spot,PENGU,USDT": "PENGUUSDT", "okx,spot,APT,USDT": "APT-USDT", "okx,spot,BCH,USDT": "BCH-USDT", "okx,spot,BERA,USD": "BERA-USD", "okx,spot,BERA,USDT": "BERA-USDT", "okx,spot,DOT,USDT": "DOT-USDT", "okx,spot,HBAR,USDT": "HBAR-USDT", "okx,spot,JUP,USD": "JUP-USD", "okx,spot,JUP,USDT": "JUP-USDT", "okx,spot,LTC,USDT": "LTC-USDT", "okx,spot,PENGU,USD": "PENGU-USD", "okx,spot,PENGU,USDT": "PENGU-USDT", "bybit,spot,APT,USDT": "APTUSDT", "bybit,spot,BCH,USDT": "BCHUSDT", "bybit,spot,BERA,USDT": "BERAUSDT", "bybit,spot,DOT,USDT": "DOTUSDT", "bybit,spot,HBAR,USDT": "HBARUSDT", "bybit,spot,JUP,USDT": "JUPUSDT", "bybit,spot,LTC,USDT": "LTCUSDT", "bybit,spot,NXPC,USDT": "NXPCUSDT", "bybit,spot,PENGU,USDT": "PENGUUSDT", "kraken,spot,BERA,ZUSD": "BERA-USD", "kraken,spot,BERA,USDT": "BERA-USDT", "kraken,spot,FARTCOIN,ZUSD": "FARTCOIN-USD", "kraken,spot,FARTCOIN,USDT": "FARTCOIN-USDT", "kraken,spot,JUP,ZUSD": "JUP-USD", "kraken,spot,PENGU,ZUSD": "PENGU-USD", "kraken,spot,PENGU,USDT": "PENGU-USDT", "gateio,spot,BCH,USD": "BCH_USD", "gateio,spot,BCH,USDT": "BCH_USDT", "gateio,spot,BERA,USDT": "BERA_USDT", "gateio,spot,DOT,USDT": "DOT_USDT", "gateio,spot,FARTCOIN,USDT": "FARTCOIN_USDT", "gateio,spot,JUP,USDT": "JUP_USDT", "gateio,spot,LTC,USD": "LTC_USD", "gateio,spot,LTC,USDT": "LTC_USDT", "gateio,spot,NXPC,USDT": "NXPC_USDT", "gateio,spot,PENGU,USDT": "PENGU_USDT", "cryptocom,spot,BERA,USD": "BERA_USD", "cryptocom,spot,FARTCOIN,USD": "FARTCOIN_USD", "cryptocom,spot,JUP,USD": "JUP_USD", "cryptocom,spot,JUP,USDT": "JUP_USDT", "cryptocom,spot,NXPC,USD": "NXPC_USD", "cryptocom,spot,PENGU,USD": "PENGU_USD", "cryptocom,spot,PENGU,USDT": "PENGU_USDT", "bitmart,spot,BCH,USDT": "BCH_USDT", "bitmart,spot,DOT,USDT": "DOT_USDT", "bitmart,spot,LTC,USDT": "LTC_USDT", "binance,spot,BONK,USDT": "BONKUSDT", "binance,spot,FIL,USDT": "FILUSDT", "binance,spot,ICP,USDT": "ICPUSDT", "binance,spot,IMX,USDT": "IMXUSDT", "binance,spot,SHIB,USDT": "SHIBUSDT", "binance,spot,XLM,USDT": "XLMUSDT", "bitstamp,spot,BONK,USD": "BONK-USD", "bitstamp,spot,IMX,USD": "IMX-USD", "bitstamp,spot,POPCAT,USD": "POPCAT-USD", "bitstamp,spot,SHIB,USD": "SHIB-USD", "bitstamp,spot,XLM,USD": "XLM-USD", "coinbase,spot,BONK,USD": "BONK-USD", "coinbase,spot,DEGEN,USD": "DEGEN-USD", "coinbase,spot,FIL,USD": "FIL-USD", "coinbase,spot,ICP,USD": "ICP-USD", "coinbase,spot,IMX,USD": "IMX-USD", "coinbase,spot,IMX,USDT": "IMX-USDT", "coinbase,spot,POPCAT,USD": "POPCAT-USD", "coinbase,spot,SHIB,USD": "SHIB-USD", "coinbase,spot,SHIB,USDT": "SHIB-USDT", "coinbase,spot,XLM,USD": "XLM-USD", "coinbase,spot,XLM,USDT": "XLM-USDT", "bitget,spot,BONK,USDT": "BONKUSDT", "bitget,spot,DEGEN,USDT": "DEGENUSDT", "bitget,spot,FIL,USDT": "FILUSDT", "bitget,spot,ICP,USDT": "ICPUSDT", "bitget,spot,IMX,USDT": "IMXUSDT", "bitget,spot,SHIB,USDT": "SHIBUSDT", "bitget,spot,XLM,USDT": "XLMUSDT", "okx,spot,BONK,USD": "BONK-USD", "okx,spot,BONK,USDT": "BONK-USDT", "okx,spot,DEGEN,USD": "DEGEN-USD", "okx,spot,DEGEN,USDT": "DEGEN-USDT", "okx,spot,FIL,USDT": "FIL-USDT", "okx,spot,ICP,USDT": "ICP-USDT", "okx,spot,IMX,USD": "IMX-USD", "okx,spot,IMX,USDT": "IMX-USDT", "okx,spot,SHIB,USD": "SHIB-USD", "okx,spot,SHIB,USDT": "SHIB-USDT", "okx,spot,XLM,USD": "XLM-USD", "okx,spot,XLM,USDT": "XLM-USDT", "bybit,spot,BONK,USDT": "BONKUSDT", "bybit,spot,FIL,USDT": "FILUSDT", "bybit,spot,ICP,USDT": "ICPUSDT", "bybit,spot,POPCAT,USDT": "POPCATUSDT", "bybit,spot,SHIB,USDT": "SHIBUSDT", "bybit,spot,XLM,USDT": "XLMUSDT", "kraken,spot,BONK,ZUSD": "BONK-USD", "kraken,spot,IMX,ZUSD": "IMX-USD", "kraken,spot,POPCAT,ZUSD": "POPCAT-USD", "kraken,spot,SHIB,ZUSD": "SHIB-USD", "kraken,spot,SHIB,USDT": "SHIB-USDT", "kraken,spot,XLM,ZUSD": "XLM-USD", "gateio,spot,BONK,USDT": "BONK_USDT", "gateio,spot,DEGEN,USDT": "DEGEN_USDT", "gateio,spot,FIL,USDT": "FIL_USDT", "gateio,spot,ICP,USDT": "ICP_USDT", "gateio,spot,POPCAT,USDT": "POPCAT_USDT", "gateio,spot,SHIB,USDT": "SHIB_USDT", "gateio,spot,XLM,USDT": "XLM_USDT", "cryptocom,spot,BONK,USD": "BONK_USD", "cryptocom,spot,BONK,USDT": "BONK_USDT", "cryptocom,spot,DEGEN,USD": "DEGEN_USD", "cryptocom,spot,IMX,USD": "IMX_USD", "cryptocom,spot,IMX,USDT": "IMX_USDT", "cryptocom,spot,POPCAT,USD": "POPCAT_USD", "cryptocom,spot,POPCAT,USDT": "POPCAT_USDT", "cryptocom,spot,SHIB,USD": "SHIB_USD", "cryptocom,spot,SHIB,USDT": "SHIB_USDT", "cryptocom,spot,XLM,USD": "XLM_USD", "cryptocom,spot,XLM,USDT": "XLM_USDT", "bitmart,spot,FIL,USDT": "FIL_USDT", "bitmart,spot,ICP,USDT": "ICP_USDT", "bitmart,spot,SHIB,USDT": "SHIB_USDT", "bitmart,spot,XLM,USDT": "XLM_USDT"}