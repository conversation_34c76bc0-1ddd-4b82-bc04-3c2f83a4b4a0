import functools
import logging
from typing import Optional, cast

import numpy as np
import pandas as pd
import utils_calc
import utils_calc.spline
import utils_calc.utils_calc_svi
import utils_calc.utils_calc_types
import utils_general
from calc_helpers import MONEYNESS_STR
from constants import TOL, load_domestic_rates
from lambda_types import NDArrayFloat64, VolSpace
from utils_calc import (
    BOUNDS_SABR,
    SABR_RHO_BOUND,
    CalibrationQuery,
    Model,
    SabrParams,
)


def calibrate_sabr_with_recalibrations(
    query: CalibrationQuery,
    x0_SABR: NDArrayFloat64,
    bounds_SABR: list[tuple[float, float]],
) -> SabrParams:
    def _recalibration_condition(sabr_parameters: SabrParams) -> bool:
        return bool(
            np.isinf(np.asarray(list(sabr_parameters.values()))).any()
            or np.isnan(np.asarray(list(sabr_parameters.values()))).any()
            or sabr_parameters["sabr_alpha"]
            > 3  # closely tracks ATM, 300% ATM is likely a failed calibration
            or abs(sabr_parameters["sabr_rho"])
            > SABR_RHO_BOUND  # cannot be greater than our bounds
            or sabr_parameters["sabr_volvol"] < 0.01
            or sabr_parameters["sabr_alpha"] < 0.01
        )

    calibration_result = utils_calc.perform_sabr_calibration(
        query=query,
        x0_SABR=x0_SABR,
        bounds_SABR=bounds_SABR,
        method="SLSQP",
        tol=TOL,
        recalibration_condition=functools.partial(
            _recalibration_condition,
        ),
    )

    return calibration_result


def recalibrate_params_post_smoothing(
    row: pd.Series,  # type: ignore
    model: Model,
    exchange: str,
) -> Optional[pd.Series]:  # type: ignore
    """
    This recalibrates SABR or SVI model params. This function is used when recalibrating
    smoothed model parameters.

    :param row: This is a series that will have Union[MONEYNESS_STR, DELTAS_STR] as columns, as well as
        "strikes", "vols", "qualified_name", "timestamp" and a "vol_space" column which denotes whether we are using
        delta strikes or moneyness strikes
    :param model: model to use
    :param exchange: exchange to get r_d for
    """

    results = None
    try:
        qn: str = row["qualified_name"]
        ts = cast(int, row.name)
        forward = row["forward"]
        expiry = row["expiry"]
        isodate = utils_general.to_iso(ts)
        vol_space: VolSpace = row["vol_space"]
        model_parameters: dict[
            str,
            str
            | float
            | int
            | list[utils_calc.utils_calc_types.CubicSplineSegment],
        ]

        try:
            strikes = row["strikes"]
            vols = row["vols"]

            assert len(strikes) == len(vols)

            query: CalibrationQuery = {
                "expiry": expiry,
                "forward": forward,
                "spot": row["spot"],
                "domestic_rate": utils_calc.get_domestic_rate(
                    load_domestic_rates(), expiry * 365
                ),
                "test_type": "strikes",
                "vol_test_type": "vol_lognormal",
                "model": model,
                "LNvols": vols,
                "strikes": strikes,
                "biv": vols,
                "aiv": vols,
            }

            logging.info(
                f"Recalibrating {model} params from smoothed vols using {vol_space} strikes. {qn=:}, {isodate=:}, {ts=:}",
            )

            if model == "SABR":
                x0_SABR = utils_calc.ini_guess_sabr(
                    strikes,
                    vols,
                    forward,
                )
                sabr_model_parameters = calibrate_sabr_with_recalibrations(
                    query=query, x0_SABR=x0_SABR, bounds_SABR=BOUNDS_SABR
                )
                atm_vol = utils_calc.sabr_vol(
                    forward,
                    forward,
                    expiry,
                    sabr_model_parameters["sabr_alpha"],
                    1,
                    sabr_model_parameters["sabr_rho"],
                    sabr_model_parameters["sabr_volvol"],
                )

                model_parameters = {
                    "sabr_alpha": sabr_model_parameters["sabr_alpha"],
                    "sabr_rho": sabr_model_parameters["sabr_rho"],
                    "sabr_volvol": sabr_model_parameters["sabr_volvol"],
                }

            elif model == "SVI":
                calibration_results = (
                    utils_calc.utils_calc_svi.calibrate_svi_with_recalibrations(
                        query
                    )
                )

                svi_model_parameters = calibration_results["svi_parameters"]
                atm_vol = utils_calc.svi_vol(
                    svi_model_parameters["svi_a"],
                    svi_model_parameters["svi_b"],
                    svi_model_parameters["svi_rho"],
                    svi_model_parameters["svi_m"],
                    svi_model_parameters["svi_sigma"],
                    forward,
                    expiry,
                    forward,
                )
                model_parameters = {
                    "svi_a": svi_model_parameters["svi_a"],
                    "svi_b": svi_model_parameters["svi_b"],
                    "svi_rho": svi_model_parameters["svi_rho"],
                    "svi_m": svi_model_parameters["svi_m"],
                    "svi_sigma": svi_model_parameters["svi_sigma"],
                }
            elif model == "spline":
                spline_calibration_results = (
                    utils_calc.perform_spline_calibration(query)
                )
                atm_vol = utils_calc.spline_model_vol(
                    [forward], spline_calibration_results
                )[0]
                model_parameters = {
                    "spline_info": spline_calibration_results["spline_info"],
                }
            else:
                raise NotImplementedError("Unsupported Model type")

            model_parameters["atm_vol"] = float(atm_vol)
            model_parameters["expiry"] = expiry
            model_parameters["forward"] = query["forward"]
            model_parameters["spot"] = query["spot"]
            model_parameters["vol_space"] = vol_space

            model_parameters["strikes"] = row["strikes"]
            model_parameters["vols"] = row["vols"]

            if model == "SVI":
                results_df = pd.DataFrame([model_parameters])
                # expects a dataframe
                results_df = utils_calc.add_svi_jw_params(results_df)
                results = pd.Series(results_df.iloc[0])

            else:
                results = pd.Series(model_parameters)

            results.name = row.name
        except Exception:
            logging.exception(
                f"Failed to recalibrate params {qn=}, {isodate=}, {ts=}"
            )
    except Exception:
        logging.exception(f"Failed to recalibrate params {row=}")

    return results


def fit_spline_with_exception(
    df_vol_matrix_money: pd.DataFrame,
    tenor_completed: list[float | int],
    right_side_tenors: list[float | int],
    tenors_to_extrapolate: list[float | int],
    timestamp: int,
    exchange: str,
    currency: str,
) -> tuple[pd.DataFrame, list[float | int]]:
    try:
        df_vol_matrix_money = utils_calc.spline_fit(
            df_vol_matrix=df_vol_matrix_money,
            tenor_completed=tenor_completed,
            tenor_in_ex_right=right_side_tenors,
            domain_cols=MONEYNESS_STR,
        )
        return df_vol_matrix_money, tenors_to_extrapolate
    except Exception:
        # Nan's in extrapolated vols will cause the spline fit to fail. We remove the extrapolated
        # tenors and prevent the whole extrapolation process to fail. Splines are fitted on each of
        # the moneyness values on the grid and require all values in the grid to not be NAN's. We
        # remove these tenors from volsurface skeleton as well.

        nan_rows = df_vol_matrix_money[df_vol_matrix_money.isna().any(axis=1)]
        nan_dict = nan_rows.to_dict(orient="index")

        logging.exception(
            f"Failed to extrapolate vols using spline fit. Removing right sided extrapolated tenors from snap. {right_side_tenors=} | {utils_general.to_iso(timestamp)} | {timestamp} | {exchange} | {currency} "
            f"| Rows with NaN values: {nan_dict}"
        )
        new_tenors_to_extrapolate = list(
            set(tenors_to_extrapolate) - set(right_side_tenors)
        )
        for tenor in right_side_tenors:
            df_vol_matrix_money = df_vol_matrix_money.drop([tenor])

        return (df_vol_matrix_money, new_tenors_to_extrapolate)
