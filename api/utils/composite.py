import logging
from decimal import Decimal
from typing import Any, Callable

import numpy as np
import pandas as pd
import utils_calc
from constants import (
    EXTRAPOLATION_PENALTY,
    INTERPOLATION_PENALTY,
    STRIKES_RETAINED_RATIO_THRESHOLD,
    UNIQUE_STRIKES_RATIO_THRESHOLD,
)
from typings import (
    CompositeIntermediate,
    ExchangePenalties,
    ExpiryState,
    NoValidExchangesError,
)
from utils_general import log_bsdebug

from utils.common import (
    SSMConfigSettings,
    should_exclude_exchange_expiry,
)


def get_R2_market(df: pd.DataFrame, exchanges: list[str]) -> pd.DataFrame:
    """
    Extrapolated exchanges do not have an R2. This function considers the exchanges that do have R2 values,
    and creates an average market R2 by rescaling their surface weights relative to one another
    """

    r2_df = df[[f"R2_{ex}" for ex in exchanges]]
    weight_df = df[[f"surface_weight_{ex}" for ex in exchanges]]

    # rename the columns so they only have the exchange names
    r2_df.columns = pd.Index([col.split("_")[-1] for col in r2_df.columns])
    weight_df.columns = pd.Index(
        [col.split("_")[-1] for col in weight_df.columns]
    )
    weight_df[r2_df.isna()] = np.nan

    # normalize weights so they sum to 1 (across the columns for each row)
    normalized_weights = weight_df.div(weight_df.sum(axis=1), axis=0)

    # compute the weighted R2 from the new expiry weighte
    df["R2_market"] = (normalized_weights * r2_df).sum(axis=1)

    return df


def calculate_exchange_surface_weights(
    df: pd.DataFrame, exchanges: list[str], static_penalties: ExchangePenalties
) -> pd.DataFrame:
    """
    Calculates the surface weights for each exchange.
    We need the R2, strikescalib, volume_24h, and strikes_prefilter_total and smoothed_strikes_prefilter_total columns for each exchange
    """

    def _rebalance_weight(
        row: "pd.Series[Any]",
        non_penalised_exchanges: list[str],
        penalised_exchanges: list[str],
    ) -> "pd.Series[Any]":

        if non_penalised_exchanges:
            non_penalised_total = sum(
                row[f"surface_weight_{ex}"] for ex in non_penalised_exchanges
            )
            penalised_total = sum(
                row[f"surface_weight_{ex}"] for ex in penalised_exchanges
            )
            for ex in non_penalised_exchanges:
                original_weight = float(row[f"surface_weight_{ex}"])
                row[f"surface_weight_{ex}"] = float(
                    Decimal(
                        utils_calc.safe_div(
                            original_weight, non_penalised_total
                        )
                    )
                    * (Decimal("1") - Decimal(penalised_total))
                )
        else:
            # If all exchanges are penalized or there's only one exchange, normalize to sum to 1
            total_penalised = sum(
                row[f"surface_weight_{ex}"] for ex in penalised_exchanges
            )
            if total_penalised > 0:  # Avoid division by zero
                for ex in penalised_exchanges:
                    row[f"surface_weight_{ex}"] = float(
                        Decimal(row[f"surface_weight_{ex}"])
                        / Decimal(total_penalised)
                    )
            else:
                # In case all weights are zero,
                raise ValueError("All exchange surface weights are zero")

        return row

    total_avg_R2 = df[[f"R2_{ex}" for ex in exchanges]].mean().sum().item()
    total_volume_24h_total = (
        df[[f"volume_24h_{ex}" for ex in exchanges]].sum().sum().item()
    )

    # calculate unique strikes across all expiries across all exchanges
    total_unique_strikes = set()
    for ex in exchanges:
        unique_strikes = {
            strike
            for strikes_calib in df[f"strikescalib_{ex}"]
            if isinstance(strikes_calib, list)
            for strike in strikes_calib
        }
        # make each row hold the full list of unique strikes
        df[f"unique_strikes_{ex}"] = [sorted(unique_strikes)] * len(df)
        total_unique_strikes.update(unique_strikes)

    # sets the total unique strikes for that snapshot
    df["total_unique_strikes"] = [sorted(total_unique_strikes)] * len(df)

    # sets the total volume for that snapshot
    df.loc[:, "volume_24h_total"] = total_volume_24h_total

    for ex in exchanges:
        df.loc[:, f"strikes_postfilter_total_{ex}"] = df[
            f"strikescalib_{ex}"
        ].apply(lambda x: len(x) if isinstance(x, list) else 0)
        # calculate the % of strikes retained for each exchange
        df.loc[:, f"strikes_retained_ratio_{ex}"] = utils_calc.safe_div(
            df[f"strikes_postfilter_total_{ex}"].sum().item(),
            df[f"strikes_prefilter_total_{ex}"].sum().item(),
        )

        # calculate the % of unsmoothed prefiltered strikes
        # we do 1 - ratio so that a high smoothing score means and
        # exchange has fewer stikes being smoothed
        df.loc[:, f"strikes_unsmoothed_ratio_{ex}"] = 1 - utils_calc.safe_div(
            df[f"smoothed_strikes_prefilter_total_{ex}"].sum().item(),
            df[f"strikes_prefilter_total_{ex}"].sum().item(),
        )
        # calculate the ratio of each exchange's unique strikes against the total number of unique strikes available
        df.loc[:, f"unique_strikes_ratio_{ex}"] = utils_calc.safe_div(
            len(df.loc[:, f"unique_strikes_{ex}"].iloc[0]),
            len(total_unique_strikes),
        )

    total_unique_strikes_ratio = (
        df[[f"unique_strikes_ratio_{ex}" for ex in exchanges]]
        .sum(axis=1)
        .iloc[0]
    )
    total_strikes_retained_ratios = (
        df[[f"strikes_retained_ratio_{ex}" for ex in exchanges]]
        .sum(axis=1)
        .iloc[0]
    )
    total_strikes_unsmoothed_ratio = (
        df[[f"strikes_unsmoothed_ratio_{ex}" for ex in exchanges]]
        .sum(axis=1)
        .iloc[0]
    )

    # we have 5 weight components, R2, unique_strikes_total, strikes_retained_ratio, strikes_unsmoothed_ratio and volume_24h.
    # if total volume across exchanges in a snapshot is 0, we must divide by the number of non-zero weight components
    # All non-zero components should contribute a fraction of 1/(len(non_zero_components)) towards the composite weight
    non_zero_count = sum(
        1
        for weight in [
            total_avg_R2,
            total_volume_24h_total,
            total_unique_strikes_ratio,
            total_strikes_retained_ratios,
            total_strikes_unsmoothed_ratio,
        ]
        if weight != 0 and weight != np.nan
    )

    for ex in exchanges:
        df.loc[:, f"strikes_quantity_weight_{ex}"] = utils_calc.safe_div(
            df[f"unique_strikes_ratio_{ex}"], total_unique_strikes_ratio
        )

        df.loc[:, f"strikes_smoothed_weight_{ex}"] = utils_calc.safe_div(
            df[f"strikes_unsmoothed_ratio_{ex}"], total_strikes_unsmoothed_ratio
        )

        df.loc[:, f"strikes_retained_weight_{ex}"] = utils_calc.safe_div(
            df[f"strikes_retained_ratio_{ex}"], total_strikes_retained_ratios
        )
        df.loc[:, f"volumes_weight_{ex}"] = utils_calc.safe_div(
            df[f"volume_24h_{ex}"].sum().item(), total_volume_24h_total
        )
        # we can use .mean() here because nan values are omitted
        avg_R2 = df[f"R2_{ex}"].mean()
        df.loc[:, f"R2_weight_{ex}"] = utils_calc.safe_div(avg_R2, total_avg_R2)

        df.loc[
            :,
            [
                f"strikes_quantity_weight_{ex}",
                f"strikes_smoothed_weight_{ex}",
                f"strikes_retained_weight_{ex}",
                f"volumes_weight_{ex}",
                f"R2_weight_{ex}",
            ],
        ] = df[
            [
                f"strikes_quantity_weight_{ex}",
                f"strikes_smoothed_weight_{ex}",
                f"strikes_retained_weight_{ex}",
                f"volumes_weight_{ex}",
                f"R2_weight_{ex}",
            ]
        ].fillna(
            0
        )

        base_exchange_weights = utils_calc.safe_div(
            df[f"strikes_quantity_weight_{ex}"]
            + df[f"strikes_smoothed_weight_{ex}"]
            + df[f"strikes_retained_weight_{ex}"]
            + df[f"R2_weight_{ex}"]
            + df[f"volumes_weight_{ex}"],
            non_zero_count,
        )

        if ex in static_penalties:
            base_exchange_weights = base_exchange_weights * (
                1 - static_penalties[ex]
            )

        df.loc[:, f"surface_weight_{ex}"] = base_exchange_weights

    # Rebalance penalised exchanges
    if static_penalties:
        penalised_exchanges = [ex for ex in exchanges if ex in static_penalties]
        non_penalised_exchanges = [
            ex for ex in exchanges if ex not in static_penalties
        ]

        df = df.apply(
            lambda row: _rebalance_weight(
                row, non_penalised_exchanges, penalised_exchanges
            ),
            axis=1,
        )

    if (
        all(df[f"surface_weight_{ex}"].sum() == 0 for ex in exchanges)
        and exchanges
    ):
        raise ValueError("All exchange surface weights are zero")

    return df


def calculate_expiry_scores(
    df: pd.DataFrame,
    exchanges: list[str],
    currency: str,
    exclusion_config: SSMConfigSettings,
) -> pd.DataFrame:
    """
    Calculates expiry-specific weights for each exchange by applying penalties for interpolated/extrapolated expiries,
    redistributing any penalized weight among non-penalized exchanges, and then rebalancing for excluded exchanges.

    For each row in the DataFrame:
      1. Initialize expiry weights from the corresponding surface weights.
      2. For each exchange, if the expiry is flagged as interpolated or extrapolated,
         subtract the penalty and accumulate the total penalty.
      3. Redistribute the total penalty among exchanges that were not penalized (using their original weights for proportionality).
      4. Verify that the weights sum to 1 (within tolerance).
      5. Call compute_expiry_scores_with_excluded_exchanges to adjust for any configured exclusions,
         and update the expiry weight columns with the rebalanced values.

    :param: df (pd.DataFrame): DataFrame containing surface weights and flag columns.
    :param: exchanges (list[str]): List of exchange identifiers.
    :param: currency: the currency to look up the exclusion config for
    :param: exclusion_config (SSMConfigSettings): a config loaded from ssm containing penalties that need to be applies
    to specific exchanges and currencies.

    Returns:
        pd.DataFrame: The original DataFrame with updated `expiry_score_{exchange}` columns.
    """

    # Initialize expiry weights from the surface weights.
    for ex in exchanges:
        # expiries that were not interpolated/extrapolated (and therefore excluded as a result of the extrapolation
        # filtering logic) would have their expiry_state_ as 'excluded'. We set their expiry weights to 0 to exclude
        # them entirely. Otherwise we initialize with the surface weight for any other expiry_state_
        df[f"expiry_score_{ex}"] = df[f"surface_weight_{ex}"].where(
            df[f"expiry_state_{ex}"] != ExpiryState.EXCLUDED.value, 0
        )

    penalty_map = {
        ExpiryState.EXTRAPOLATED.value: EXTRAPOLATION_PENALTY,
        ExpiryState.INTERPOLATED.value: INTERPOLATION_PENALTY,
    }

    for idx in df.index:
        total_penalty = 0.0
        active_exchanges = []
        penalties: dict[str, float] = {}
        original_non_pen: dict[str, float] = {}

        underlying_index = df.at[idx, "underlying_index"]
        iso_exp = underlying_index.split("_")[-1]  # e.g. "2025-12-26T08:00:00Z"

        for ex in exchanges:
            penalties[ex] = 0.0
            expiry_state = df.at[idx, f"expiry_state_{ex}"]
            weight = df.at[idx, f"expiry_score_{ex}"]

            if expiry_state == ExpiryState.EXCLUDED.value:
                continue

            active_exchanges.append(ex)

            if expiry_state == ExpiryState.LISTED.value:
                # For non-penalized exchanges, record their original weight for later proportional distribution
                original_non_pen[ex] = weight
                continue

            if expiry_state in penalty_map:
                penalty = penalty_map[expiry_state] * weight
                df.at[idx, f"expiry_score_{ex}"] = weight - penalty
                total_penalty += penalty
                penalties[ex] = penalty
            else:
                raise Exception(
                    f"Invalid expiry state encountered {expiry_state=}"
                )

        log_bsdebug(
            "Penalty summary for underlying_index=%s: penalties=%s",
            underlying_index,
            penalties,
        )
        # Redistribute the deducted penalty if only a subset of exchanges was penalized
        # using their original weights as the basis.
        num_non_penalized = len(original_non_pen.keys())
        if num_non_penalized and num_non_penalized != len(exchanges):
            total_non_pen_original = sum(original_non_pen.values())
            # Avoid division by zero, although this should not happen if at least one exchange has a positive weight.
            if total_non_pen_original > 0:
                for ex in original_non_pen.keys():
                    # re-distrubute based on the original proportion of the exchanges weight relative
                    redistribution = (
                        original_non_pen[ex] / total_non_pen_original
                    ) * total_penalty
                    df.at[idx, f"expiry_score_{ex}"] += redistribution

        # Rebalance weights respecting exchanges that have been excluded
        total_active_weight = sum(
            df.at[idx, f"expiry_score_{ex}"] for ex in active_exchanges
        )
        for ex in active_exchanges:
            df.at[idx, f"expiry_score_{ex}"] /= total_active_weight

        total = sum(df.at[idx, f"expiry_score_{ex}"] for ex in exchanges)
        if not np.isclose(total, 1.0, atol=1e-5):
            raise ValueError(
                f"Expiry weights do not sum to 1 at index {idx}: total={total}"
            )

        # Rebalance the weights based on config excluded expiries - expiries that are included in the config
        # would not have been discounted above because we would have skipped the interpolation/extrapolation
        # of this expiry for an exchange altogether.
        computed_weights = compute_expiry_scores_with_excluded_exchanges(
            currency=currency,
            expiry_row=df.loc[idx],
            exchanges=exchanges,
            iso_expiry=iso_exp,
            exclusion_config=exclusion_config,
        )

        # Update the DataFrame with the computed weights.
        for ex in exchanges:
            df.at[idx, f"expiry_score_{ex}"] = computed_weights[ex]

    return df


def compute_expiry_scores_with_excluded_exchanges(
    expiry_row: "pd.Series[Any]",
    exchanges: list[str],
    currency: str,
    iso_expiry: str,
    exclusion_config: SSMConfigSettings,
) -> dict[str, float]:
    """
    Computes per-exchange surface weights, factoring in any excluded exchanges.

    The process is as follows:
      1. Identify exchanges that should be excluded based on the provided configuration.
      2. Assign a weight of 0.0 to each excluded exchange.
      3. Sum the original weights of the non-excluded exchanges.
      4. If the sum of non-excluded weights is positive, rescale each non-excluded exchange's weight
         so that the total weights across exchanges sum to 1. If not, raise a NoValidExchangesError.
      5. If no exchanges are excluded, simply use the original surface weights for each exchange.

    Note: this function does not modify the "expiry_score_{ex}" keys.
    Returns a dict of {exchange: weight}.
    """

    underlying_index = expiry_row["underlying_index"]
    exchange_weights: dict[str, float] = {}
    config_excluded_exchanges = []

    for exchange in exchanges:
        if should_exclude_exchange_expiry(
            exchange=exchange,
            currency=currency,
            expiry_years=expiry_row["expiry"],
            iso_expiry=iso_expiry,
            exclusion_config=exclusion_config,
        ):
            config_excluded_exchanges.append(exchange)

    if config_excluded_exchanges:
        # Exchanges NOT excluded
        remaining_exchanges = [
            ex for ex in exchanges if ex not in config_excluded_exchanges
        ]

        # Set excluded exchange weights to zero
        for ex in config_excluded_exchanges:
            exchange_weights[ex] = 0.0

        total_remaining_weight = sum(
            expiry_row[f"expiry_score_{ex}"] for ex in remaining_exchanges
        )

        if total_remaining_weight > 0:
            # Rescale the remaining exchanges so total == 1
            for ex in remaining_exchanges:
                old_weight = expiry_row[f"expiry_score_{ex}"]
                exchange_weights[ex] = old_weight / total_remaining_weight

            assert np.isclose(
                sum(exchange_weights.values()), 1.0, atol=1e-5
            ), f"Rebalanced weights do not sum to 1: {exchange_weights=}"
        else:
            # Edge case: if all remaining exchanges had a combined weight of zero
            raise NoValidExchangesError(
                f"No valid exchanges left for {underlying_index=}"
            )
    else:
        # No excluded exchanges for this expiry, just use the original weighting
        for exchange in exchanges:
            exchange_weights[exchange] = expiry_row[f"expiry_score_{exchange}"]

    return exchange_weights


def get_exchange_columns(df: pd.DataFrame, exchanges: list[str]) -> list[str]:
    exchange_columns = []
    for col in df.columns:
        if any(exchange in col for exchange in exchanges):
            exchange_columns.append(col)
    return exchange_columns


def filter_exchanges(
    df: pd.DataFrame,
    exchanges: list[str],
    conditions: list[Callable[[pd.DataFrame, str], bool]],
    timestamp: int,
    currency: str,
) -> tuple[pd.DataFrame, list[str]]:
    """
    Filters exchanges based on a list of conditions combined with "or".

    :param df: The input DataFrame containing exchange data.
    :param exchanges: A list of exchange names to be evaluated against the conditions.
    :param conditions: A list of callable conditions, where each condition is a function that
        takes the DataFrame and an exchange name as arguments and returns a boolean.
    :param timestamp: timestamp that is being process. Used for logging
    :param currency: currency that is being process. Used for logging
    :return: A tuple containing the modified DataFrame (with the columns corresponding to the filtered exchanges dropped)
        and a list of the filtered exchange names.

    """
    filtered_exchanges = [
        ex
        for ex in exchanges
        if any(condition(df, ex) for condition in conditions)
    ]

    if filtered_exchanges:
        log_filtered_exchanges(df, filtered_exchanges, timestamp, currency)
        df.drop(
            columns=get_exchange_columns(df, filtered_exchanges),
            inplace=True,
        )

    return df, filtered_exchanges


def log_filtered_exchanges(
    df: pd.DataFrame,
    exchanges_to_drop: list[str],
    timestamp: int,
    currency: str,
) -> None:

    first_row = df.iloc[0]

    for ex in exchanges_to_drop:
        scores = {
            f"strikes_quantity_weight_{ex}": first_row[
                f"strikes_quantity_weight_{ex}"
            ],
            f"strikes_smoothed_weight_{ex}": first_row[
                f"strikes_smoothed_weight_{ex}"
            ],
            f"strikes_retained_weight_{ex}": first_row[
                f"strikes_retained_weight_{ex}"
            ],
            f"volumes_weight_{ex}": first_row[f"volumes_weight_{ex}"],
            f"R2_weight_{ex}": first_row[f"R2_weight_{ex}"],
            f"surface_weight_{ex}": first_row[f"surface_weight_{ex}"],
        }

        logging.error(
            f"Excluding {ex=} from composite | {timestamp=} | {currency=}. Scores: {scores}"
        )


def calculate_confidence_score(
    df: pd.DataFrame,
    supercomposite_exchange: str,
    intermediate: CompositeIntermediate,
) -> tuple[pd.DataFrame, float]:
    """
    Calculates the confidence score for the supercomposite_exchange.

    :param df: This is a dataframe whose columns are ListedTenorParams keys and from both the composite and
    the supercomposite_exchange e.g lyra. Each param is suffixed with the exchange name, e.g volume_24h_composite.

    """

    # the total reference volume is the v2composite's volume - we will compare all
    # the volumes of the supercomposite exchange to this volume
    total_volume_24h_total = float(intermediate["scores"]["volume_24h_total"])

    # calculate unique strikes across all expiries across for the supercomposite exchange
    supercomposite_unique_strikes = {
        strike
        for strikes_calib in df[f"strikescalib_{supercomposite_exchange}"]
        if isinstance(strikes_calib, list)
        for strike in strikes_calib
    }

    # calculate the union of strikes of the supercomposite exchange and the composite
    # all rows should contain the same list of strikes
    total_unique_strikes = supercomposite_unique_strikes.union(
        df["total_unique_strikes_v2composite"].iloc[0]
    )

    df.loc[:, f"total_unique_strikes_{supercomposite_exchange}"] = [
        sorted(supercomposite_unique_strikes)
    ] * len(df)

    # sets the total unique strikes for that snapshot
    df.loc[:, "total_unique_strikes"] = [sorted(total_unique_strikes)] * len(df)

    # calculate the number of prefiltered strikes
    df.loc[:, f"strikes_postfilter_total_{supercomposite_exchange}"] = df[
        f"strikescalib_{supercomposite_exchange}"
    ].apply(lambda x: len(x) if isinstance(x, list) else 0)
    # we want the score to have a maximum of 1
    df.loc[:, f"strikes_quantity_score_{supercomposite_exchange}"] = (
        utils_calc.safe_div(
            len(supercomposite_unique_strikes), len(total_unique_strikes)
        )
    )

    df.loc[:, f"strikes_retained_score_{supercomposite_exchange}"] = (
        utils_calc.safe_div(
            df[f"strikes_postfilter_total_{supercomposite_exchange}"]
            .sum()
            .item(),
            df[f"strikes_prefilter_total_{supercomposite_exchange}"]
            .sum()
            .item(),
        )
    )
    df.loc[:, f"strikes_smoothed_score_{supercomposite_exchange}"] = (
        1
        - utils_calc.safe_div(
            df[f"smoothed_strikes_prefilter_total_{supercomposite_exchange}"]
            .sum()
            .item(),
            df[f"strikes_prefilter_total_{supercomposite_exchange}"]
            .sum()
            .item(),
        )
    )
    df.loc[:, f"R2_score_{supercomposite_exchange}"] = df[
        f"R2_{supercomposite_exchange}"
    ].mean()
    df.loc[:, f"volume_24h_score_{supercomposite_exchange}"] = (
        utils_calc.safe_div(
            df.loc[:, f"volume_24h_{supercomposite_exchange}"].sum().item(),
            total_volume_24h_total,
        )
    )

    # for the supercomposite, we should always divide by the number of weigth components
    # we want to contribute to the confidence score
    number_of_score_components = len(df.filter(like="score").columns)

    df.loc[:, "confidence_score"] = utils_calc.safe_div(
        df[f"strikes_quantity_score_{supercomposite_exchange}"]
        + df[f"strikes_smoothed_score_{supercomposite_exchange}"]
        + df[f"R2_score_{supercomposite_exchange}"]
        + df[f"volume_24h_score_{supercomposite_exchange}"]
        + df[f"strikes_retained_score_{supercomposite_exchange}"],
        number_of_score_components,
    )

    confidence_score = df.loc[:, "confidence_score"][0]
    return df, confidence_score


def get_backup_spot_composite(
    df: pd.DataFrame, exchanges: list[str]
) -> np.ndarray | float:  # type: ignore
    # expiries are interpolated beforehand and should all contain values for spot
    spot_values = df[[f"spot_{ex}" for ex in exchanges]].fillna(0)

    # made more robust and not assume all exchanges will have spot present.
    # if there is no spot present then we calculate the spot composite based
    # on available data
    spot_sum = np.sum(spot_values, axis=1)
    non_zero_counts = np.count_nonzero(spot_values, axis=1)

    if np.any(non_zero_counts == 0):
        raise ValueError("No spot value present for one or more expiries")

    return utils_calc.safe_div(spot_sum, non_zero_counts)


def get_forward_composite(
    df: pd.DataFrame, exchanges: list[str], composite_type: str
) -> pd.DataFrame:
    # expiries are interpolated beforehand and should all contain values for forwards

    df_copy = df.loc[:, [f"forward_{ex}" for ex in exchanges]].fillna(0)

    for ex in exchanges:
        df[f"spot_cy_{ex}"] = (1 / df.loc[:, "expiry"]) * np.log(
            df_copy.loc[:, f"forward_{ex}"]
            / df.loc[:, f"spot_{composite_type}"]
        )
        df.replace([np.inf, -np.inf], 0, inplace=True)

    df[f"spot_cy_{composite_type}"] = sum(
        df.loc[:, f"spot_cy_{ex}"] * df.loc[:, f"surface_weight_{ex}"]
        for ex in exchanges
    )

    df[f"forward_{composite_type}"] = df.loc[
        :, f"spot_{composite_type}"
    ] * np.exp(df.loc[:, f"spot_cy_{composite_type}"] * df.loc[:, "expiry"])
    return df


def strikes_quantity_filter(data: pd.DataFrame, ex: str) -> bool:
    return bool(
        data.iloc[0][f"unique_strikes_ratio_{ex}"]
        < UNIQUE_STRIKES_RATIO_THRESHOLD
    )


def strikes_retained_filter(data: pd.DataFrame, ex: str) -> bool:
    return bool(
        data.iloc[0][f"strikes_retained_ratio_{ex}"]
        < STRIKES_RETAINED_RATIO_THRESHOLD
    )
