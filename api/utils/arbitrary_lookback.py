import functools
import logging
from typing import Optional, cast, get_args

import numpy as np
import pandas as pd
import utils_calc
import utils_general
from calc_helpers import EXCHANGE_RD
from constants import (
    BAD_MONEYNESS_CUTOFF_RATIO,
    CALC_TYPE_TO_COL_SPACE,
    LOOKBACK_COLUMN_NAME,
    SPIKED_PARAMS_COLUMN_NAME,
)
from lambda_types import (
    ArbitraryLookbackVolCalcDetails,
    NDArrayFloat64,
    SmileCtInfo,
    VolSurfaceCalcType,
)
from utils_calc import Model

from utils.common import extract_model_params, model_vol
from utils.lookback import get_qualified_name_prefixs_from_qns
from utils.outputs import (
    create_output_col,
    drop_available_columns_from_df,
    undo_create_output_col,
)
from utils.surface import estimate_strike_from_delta


def identify_and_populate_arbitrary_values_for_smoothing(
    current_and_lookback_results: pd.DataFrame,
    current_result: pd.DataFrame,
    calc_types: list[VolSurfaceCalcType],
    scheduled_version: str = "",
) -> pd.DataFrame:
    """

    This function identified arbitrarily subscribed values that do not have a history,
    and uses the modelparameters to generate a history of vols so that we can smooth
    effectively.

    """

    if not current_and_lookback_results.index.is_unique:
        logging.error(
            "Lookback dataframe must have unique index. Skipping function Arbitrary lookback calculation"
        )
        return current_and_lookback_results

    lookbacks_for_current_result_qns = current_and_lookback_results[
        current_and_lookback_results["qualified_name"].isin(
            current_result["qualified_name"]
        )
    ]
    for calc_type in set(calc_types):
        calc_type_df, params_df = _get_calc_type_and_params_df(
            calc_type=calc_type,
            lookbacks_for_current_results=lookbacks_for_current_result_qns,
            scheduled_version=scheduled_version,
        )

        for model, calc_type_model_df in calc_type_df.groupby("model"):
            try:
                assert model in get_args(Model)
                model = cast(Model, model)
                col_space = CALC_TYPE_TO_COL_SPACE[calc_type]

                model_params_columns = [
                    col
                    for col in params_df.columns
                    if col.startswith(model.lower())
                ]

                assert not any(
                    col
                    for col in calc_type_model_df.columns
                    if isinstance(col, (int, float))
                ), "There are numeric column names in the DataFrame, Arbitrary lookback population will not work"

                calc_type_model_df = calc_type_model_df.rename(
                    columns=functools.partial(
                        _rename_values,
                        suffix=col_space,
                        calc_type=calc_type,
                    )
                )

                calc_type_model_df = _drop_and_merge_on_left(
                    left_df=calc_type_model_df,
                    right_df=params_df,
                    merge_columns=[
                        "timestamp",
                        "qualified_name_prefix",
                    ],
                    extra_cols=[
                        "forward",
                        "expiry",
                        "spot",
                        SPIKED_PARAMS_COLUMN_NAME,
                        *model_params_columns,
                    ],
                )

                calc_type_columns = [
                    col
                    for col in calc_type_model_df.columns
                    if isinstance(col, (int, float))
                ]

                # For each qualified_name, check the non_lookback data for the calc_type values
                # that are being calculated either because they are  subscribed to or are being calculated from the catalog
                # We will then check to see if there is a lookback for each of these strikes
                qn_missing_calc_type_dfs = []
                lookback_vol_calc_info: ArbitraryLookbackVolCalcDetails = (
                    utils_general.nested_dict()
                )
                for qualified_name, qn_grouped_df in calc_type_model_df.groupby(
                    "qualified_name"
                ):
                    assert isinstance(qualified_name, str)  # for mypy
                    try:
                        missing_data_mappings, lookback_vol_calc_info = (
                            _get_missing_ct_columns_and_respective_strikes(
                                qn_df=qn_grouped_df,
                                calc_type=calc_type,
                                calc_type_columns=calc_type_columns,
                                qualified_name=qualified_name,
                                model=model,
                                params_columns=model_params_columns,
                                lookback_vol_calc_info=lookback_vol_calc_info,
                            )
                        )
                        if missing_data_mappings is not None:
                            missing_ct_columns, missing_ct_values = (
                                missing_data_mappings
                            )
                            qn_missing_data_df = qn_grouped_df.loc[
                                missing_ct_columns.index
                            ]
                            qn_missing_data_df[
                                f"missing_{calc_type}_columns"
                            ] = missing_ct_columns
                            qn_missing_data_df[
                                f"missing_{calc_type}_strikes"
                            ] = missing_ct_values
                            qn_missing_calc_type_dfs.append(qn_missing_data_df)

                    except Exception as ex:
                        logging.exception(
                            f"Error {ex}, while trying to populate arbitrary lookbacks. Failed to get missing calc_type columns and values for {qualified_name=}"
                        )

                if not qn_missing_calc_type_dfs:
                    continue

                # reduce excessive logging
                lookback_vol_calc_details: list[str] = [
                    f"{error_type} | {qn} | {details}"
                    for error_type, qn_details in lookback_vol_calc_info.items()
                    for qn, details in qn_details.items()
                ]
                if lookback_vol_calc_details:
                    logging.info(
                        f"Arbitrary Lookback Summary Info for {model=}, {calc_type=}: \n"
                        + "\n".join(lookback_vol_calc_details)
                    )

                # indices here SHOULD NOT be reset during concatenation
                all_calc_type_missing_data = pd.concat(qn_missing_calc_type_dfs)

                try:

                    model_params = extract_model_params(
                        mapping_object=all_calc_type_missing_data, model=model
                    )
                    vols = model_vol(
                        model_params=model_params,
                        forward=np.asarray(
                            all_calc_type_missing_data["forward"]
                        ),
                        expiry=np.asarray(all_calc_type_missing_data["expiry"]),
                        model=model,
                        strike=np.asarray(
                            all_calc_type_missing_data[
                                f"missing_{calc_type}_strikes"
                            ]
                        ),
                    )
                    assert isinstance(
                        vols, np.ndarray
                    ), f"Returned vols must be a numpy array {model=}, {calc_type=}"

                    for (row_idx, df_row), vol_row in zip(
                        all_calc_type_missing_data.iterrows(), vols
                    ):

                        if SPIKED_PARAMS_COLUMN_NAME not in df_row:
                            raise KeyError(
                                f"Column {SPIKED_PARAMS_COLUMN_NAME} does not exist in the row"
                            )
                        if not isinstance(
                            df_row[SPIKED_PARAMS_COLUMN_NAME], bool
                        ):
                            raise ValueError(
                                f"Value in column {SPIKED_PARAMS_COLUMN_NAME} is not a boolean: {df_row[SPIKED_PARAMS_COLUMN_NAME]}"
                            )

                        assert not df_row[
                            SPIKED_PARAMS_COLUMN_NAME
                        ], f"Row cannot contain spike, {df_row}"

                        # assign calculated rows back to the df
                        for idx, ct_column in enumerate(
                            df_row[f"missing_{calc_type}_columns"]
                        ):
                            # create the output_col
                            ct_column = create_output_col(
                                suffix=CALC_TYPE_TO_COL_SPACE[calc_type],
                                col_str=str(ct_column),
                            )
                            current_and_lookback_results.at[
                                row_idx, ct_column
                            ] = vol_row[idx]
                except Exception as e:
                    logging.exception(
                        f"Error {e=}, while trying to calculate and populate Implied Volatilities for arbitrary lookbacks for {model=}"
                    )

            except Exception as ex:
                logging.exception(
                    f"Error {ex=}, while trying to populate arbitrary lookbacks for {model=}, {calc_type=}"
                )

    return current_and_lookback_results


def _get_missing_calc_type_values(
    missing_ct_value_mask: pd.DataFrame,
    ct_values_missing_lookback_for_qn: list[float | int],
    calc_type: VolSurfaceCalcType,
    forwards: pd.Series,  # type: ignore
    model: Model,
    smile_ct_info: Optional[SmileCtInfo] = None,
) -> tuple[pd.Series, pd.Series]:  # type: ignore
    """
    Identify and return missing calc_type column names and converted strike values.

    :param missing_ct_value_mask: The mask indicating missing calc_type values
    :param ct_values_missing_lookback_for_qn: The list of ct values that are missing lookbacks
    :param calc_type: The calc type of the data
    :param smile_ct_info: A dataframe index corresponds to the missing_ct_mask df. This is needed as estimating
        the strikes for the calc type requires knowledge of the modelparameters and expiries.
    :param forwards: The forward values for each row.

    :return: Two Series where each element is a list of missing calc_type column names and missing strike values.
    """

    missing_columns = []
    missing_values = []
    delta_fallback_strikes = None

    # initial check to see whether the deltas that have been subscribed to contain unreasonable strikes.
    # If they do, then avoid calculating a lookback for them
    if calc_type == "smile":
        assert smile_ct_info is not None
        delta_fallback_strikes, available_ct_values = (
            _get_fallback_strikes_for_relevant_delta_columns(
                smile_ct_info=smile_ct_info,
                delta_columns=ct_values_missing_lookback_for_qn,
            )
        )

    for idx, row in missing_ct_value_mask.iterrows():
        assert isinstance(idx, int)
        row_missing_columns: list[float] = []
        row_missing_values: list[float] = []
        forward = forwards.loc[idx]

        for ct_column, is_missing in zip(
            ct_values_missing_lookback_for_qn, row
        ):
            if is_missing:
                row_missing_columns.append(ct_column)

                if calc_type == "moneyness":
                    row_missing_values.append(forward * ct_column)
                elif calc_type == "strike":
                    row_missing_values.append(ct_column)
                elif calc_type == "smile":

                    assert smile_ct_info is not None
                    assert delta_fallback_strikes

                    params_info_row = smile_ct_info["params_df"].loc[idx]
                    _, qn_tokens = utils_general.get_qfn_and_version(
                        qualified_name=params_info_row["qualified_name"]
                    )

                    params = extract_model_params(
                        mapping_object=smile_ct_info["params_df"].loc[idx],
                        model=model,
                    )
                    default = (
                        row_missing_values[-1]
                        if row_missing_values
                        else delta_fallback_strikes[ct_column]
                    )  # use the last calculated strike as a fallback, if we aren't calculating the first in the history
                    strike, _ = estimate_strike_from_delta(
                        delta=ct_column,
                        params=params,  # type: ignore
                        spot=params_info_row["spot"],
                        forward=forward,
                        expiry=params_info_row["expiry"],
                        model=model,
                        r_d=EXCHANGE_RD[qn_tokens[0]],
                        default=default,
                    )
                    row_missing_values.append(strike)

                else:
                    raise NotImplementedError(
                        f"Unsupported calc_type, {calc_type}"
                    )

        missing_columns.append(row_missing_columns)
        missing_values.append(row_missing_values)

    return pd.Series(
        missing_columns, index=missing_ct_value_mask.index
    ), pd.Series(missing_values, index=missing_ct_value_mask.index)


def _get_fallback_strikes_for_relevant_delta_columns(
    smile_ct_info: SmileCtInfo,
    delta_columns: list[float | int],
) -> tuple[dict[float | int, float], list[float | int]]:
    """
    Calculate fallback strikes for given delta columns and determine relevant columns based on moneyness criteria.

    This function processes the provided smile context information and delta columns to calculate fallback strike prices.
    It also filters out columns that do not meet the moneyness criteria, ensuring only relevant columns are included in the output.
    """

    delta_fallback_strikes = {}
    delta_columns_to_calculate = []
    assert smile_ct_info is not None

    _, qn_tokens = utils_general.get_qfn_and_version(
        qualified_name=smile_ct_info["earliest_current_result"][
            "qualified_name"
        ]
    )
    r_d = EXCHANGE_RD[qn_tokens[0]]
    for delta_col in list(delta_columns):  # iterate through copy
        strike = utils_calc.strike_from_delta_vol(
            s=smile_ct_info["earliest_current_result"]["spot"],
            f=smile_ct_info["earliest_current_result"]["forward"],
            r_d=r_d,
            t=smile_ct_info["earliest_current_result"]["expiry"],
            vol=smile_ct_info["earliest_current_result"][delta_col],  # type: ignore
            delta=delta_col,
        )
        moneyness_strike = (
            strike / smile_ct_info["earliest_current_result"]["forward"]
        )
        if moneyness_strike < BAD_MONEYNESS_CUTOFF_RATIO:
            delta_columns_to_calculate.append(delta_col)
            delta_fallback_strikes[delta_col] = strike

    return delta_fallback_strikes, delta_columns_to_calculate


def _get_calc_type_and_params_df(
    calc_type: str,
    lookbacks_for_current_results: pd.DataFrame,
    scheduled_version: str = "",
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Strike calc_types often have less qualified_names as they are not calculated for constant tenors

    """
    calc_type_df = lookbacks_for_current_results[
        lookbacks_for_current_results["qualified_name"].str.contains(calc_type)
    ]
    model_idx = 4 if scheduled_version else 3
    calc_type_df["model"] = calc_type_df["qualified_name"].str.split(
        ".", expand=True
    )[[model_idx]]

    calc_type_df["qualified_name_prefix"] = get_qualified_name_prefixs_from_qns(
        calc_type_df["qualified_name"]
    )
    params_df = lookbacks_for_current_results[
        lookbacks_for_current_results["qualified_name"].str.contains("params")
        & lookbacks_for_current_results["qualified_name_prefix"].isin(
            calc_type_df["qualified_name_prefix"]
        )
    ]
    params_df.dropna(axis="columns")  # remove smile/money columns
    params_df[SPIKED_PARAMS_COLUMN_NAME] = params_df.get(
        # Seems typing system for the default value is off
        [SPIKED_PARAMS_COLUMN_NAME],
        default=False,  # type: ignore
    )

    return calc_type_df, params_df


def calculate_vols(
    model: Model, df: pd.DataFrame, calc_type: str
) -> NDArrayFloat64:
    """
    Calculate strikes based on the model type.
    """

    if model == "spline":
        # spline implementation is not vectorized
        strikes = []
        for _, row in df.iterrows():
            spline_params = extract_model_params(
                mapping_object=row, model=model
            )
            row_strikes = model_vol(
                model_params=spline_params,
                forward=row["forward"],
                model=model,
                expiry=row["expiry"],
                strike=list(row[f"missing_{calc_type}_strikes"]),
            )
            strikes.append(row_strikes)
        return np.array(strikes, dtype=object)
    else:
        vectorized_model_params = extract_model_params(
            mapping_object=df, model=model
        )
        vols = model_vol(
            model_params=vectorized_model_params,
            model=model,
            forward=np.asarray(df["forward"]),
            expiry=np.asarray(df["expiry"]),
            strike=np.asarray(df[f"missing_{calc_type}_strikes"]),
        )
        assert isinstance(
            vols, np.ndarray
        ), "Returned vols must be a numpy array"
        return vols


def _get_missing_ct_columns_and_respective_strikes(
    qn_df: pd.DataFrame,
    calc_type_columns: list[float | int],
    calc_type: VolSurfaceCalcType,
    qualified_name: str,
    model: Model,
    params_columns: list[str],
    lookback_vol_calc_info: ArbitraryLookbackVolCalcDetails,
) -> tuple[Optional[tuple[pd.Series, pd.Series]], ArbitraryLookbackVolCalcDetails]:  # type: ignore
    """
    Function checks a qualified_names calc_type columns to see if any data is missing from the lookback
    e.g a new subscription. It returns a dataframe with all lookback rows that need any single calc_type
    value, e.g strike/moneyness with 2 new columns: the missing calc type columns (60000strike, 100money)
    and the dollar denominated strikes them imply (60_000, forward * 1.0 money)

    """

    # Check for rows where SPIKED_PARAMS_COLUMN_NAME is True and ignore
    num_spiked_rows = None
    full_lookback_len = qn_df[qn_df[LOOKBACK_COLUMN_NAME] == True].shape[0]
    if qn_df[SPIKED_PARAMS_COLUMN_NAME].any():
        num_spiked_rows = qn_df[SPIKED_PARAMS_COLUMN_NAME].sum()
        qn_df = qn_df[qn_df[SPIKED_PARAMS_COLUMN_NAME] != True]

    _from_lookback = qn_df[LOOKBACK_COLUMN_NAME] == True
    current_df = qn_df.loc[~_from_lookback]
    qn_lookback_df = qn_df.loc[_from_lookback]

    # retain only strikes/monies that have been calculated for this qn. Those that haven't
    # will have nan values
    all_available_ct_values_in_current_result = (
        current_df[calc_type_columns].dropna(axis=1, how="all").columns.tolist()
    )
    missing_ct_value_mask = qn_lookback_df[
        all_available_ct_values_in_current_result
    ].isna()

    if np.all(missing_ct_value_mask.values == False):
        # All calculated strikes contain their lookbacks
        return None, lookback_vol_calc_info

    if num_spiked_rows:
        lookback_vol_calc_info["Params Containing Recalibration Spike"][
            qualified_name.replace(f".{calc_type}", "")
        ] = f"{num_spiked_rows} out of {full_lookback_len}"

    missing_ct_value_sum = missing_ct_value_mask.sum()
    missing_ct_value_sum_zero_filtered = missing_ct_value_sum[
        missing_ct_value_sum > 0
    ]
    missing_ct_value_dict = {
        float(key): f"{value} rows missing"  # type: ignore
        for key, value in missing_ct_value_sum_zero_filtered.items()
    }

    # store calc_type values where lookback is missing for logging
    lookback_vol_calc_info["Calc Type Values with Missing Lookback Vols"][
        qualified_name.replace(f".{calc_type}", "")
    ] = missing_ct_value_dict

    smile_ct_info: Optional[SmileCtInfo] = None
    if calc_type == "smile":
        params_df = qn_lookback_df.loc[missing_ct_value_mask.any(axis=1)][
            ["qualified_name", "expiry", "spot", *params_columns]
        ]

        smile_ct_info = {
            "params_df": params_df,
            "earliest_current_result": current_df.iloc[
                0
            ],  # we will use this as a fallback strike
        }

    return (
        _get_missing_calc_type_values(
            missing_ct_value_mask=missing_ct_value_mask,
            ct_values_missing_lookback_for_qn=all_available_ct_values_in_current_result,
            calc_type=calc_type,
            forwards=qn_lookback_df["forward"],
            smile_ct_info=smile_ct_info,
            model=model,
        ),
        lookback_vol_calc_info,
    )


def _drop_and_merge_on_left(
    left_df: pd.DataFrame,
    right_df: pd.DataFrame,
    merge_columns: list[str],
    extra_cols: list[str],
) -> pd.DataFrame:
    """
    We drop columns from the left df as we want to copy over the values from right_df onto left_df.
     We do this to prevent the suffixing of columns with x and y if the columns are present in both
     dataframes. The function preserves the indices of the left dataframe

    """

    left_df = drop_available_columns_from_df(
        df=left_df,
        columns=extra_cols,
    )
    left_df.reset_index(inplace=True)  # creates new col called index
    merged_df = pd.merge(
        left_df,
        right_df[[*merge_columns, *extra_cols]],
        on=merge_columns,
        how="left",
    )
    merged_df.set_index("index", inplace=True)
    return merged_df


def _rename_values(
    col_name: str, suffix: str, calc_type: VolSurfaceCalcType
) -> float | int | str:
    if col_name.endswith(suffix):
        return undo_create_output_col(
            input_col=col_name, suffix=CALC_TYPE_TO_COL_SPACE[calc_type]
        )
    return col_name
