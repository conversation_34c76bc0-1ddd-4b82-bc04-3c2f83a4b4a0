import logging
import math
import multiprocessing
import re
from decimal import ROUND_HALF_UP, Decimal
from typing import Any, Literal, Optional, TypeGuard, cast, overload

import numpy as np
import orjson
import pandas as pd
import utils_calc
import utils_calc.utils_calc_types
from constants import TENOR_TYPES
from lambda_types import (
    CalibrationMetrics,
    CubicSplineVectorized,
    DerivedCalcResult,
    ExpiryType,
    ModelParamsBase,
    ModelParamsTypes,
    ModelParamsVectorized,
    NDArrayFloat64,
    ParamsCalcResult,
    SabrParamsVectorized,
    SviParamsVectorized,
    TenorType,
)
from sklearn.metrics import (  # type: ignore
    mean_absolute_error,
    mean_squared_error,
    r2_score,
)
from utils_calc import CubicSpline, Model, SabrParams, SviParams


# Default Multiprocessing Pool uses daemonic processes which cannot spawn
# new processes. This is a workaround rewritten for modern Python
# see https://stackoverflow.com/questions/6974695/python-process-pool-non-daemonic
class NonDaemonProcess(multiprocessing.Process):
    @property
    def daemon(self) -> bool:
        return False

    @daemon.setter
    def daemon(self, value: Any) -> None:
        pass


class NonDaemonPool(multiprocessing.pool.Pool):
    @staticmethod
    def Process(ctx: Any, *args: Any, **kwds: Any) -> NonDaemonProcess:
        return NonDaemonProcess(*args, **kwds)


def seconds_to_qn_convention(seconds: int) -> tuple[int, str]:
    """
    Round 60s -> 1m due to qn convention, potentially other rounding in the future

    :param seconds: number of seconds (int)
    :return: periods (int) , interval (str)
    """

    if seconds == 60:
        return 1, "minute"
    else:
        return seconds, "second"


def concat_lookback(
    previous_df: pd.DataFrame, new_df: pd.DataFrame
) -> pd.DataFrame:
    """
    Concat new results with the lookback cache

    :param previous_df: Data already in the lookback cache
    :param new_df:
    :return:
    """

    return pd.concat([previous_df, new_df], axis=0).drop_duplicates(
        ["qualified_name", "timestamp"],
        keep="last",
    )


def merge_multi_index_lookback(
    previous_df: pd.DataFrame,
    new_df: pd.DataFrame,
    method: Literal["concat", "combine_first"] = "concat",
) -> pd.DataFrame:
    """
    Merge two lookback DataFrames using the specified method ('concat' or 'combine_first').
    Logs warnings for index modifications and duplicate detections.

    :param previous_df: Data already in the lookback cache.
    :param new_df: New data to be merged into the lookback cache.
    :param method: The method to use for merging.
                   - 'concat' for concatenation with duplicate handling.
                   - 'combine_first' to fill missing values.
    :return: The merged DataFrame.
    :raises ValueError: If an unsupported method is provided.
    """

    combined_df: pd.DataFrame | None = None
    previous_df_copy = previous_df.copy()
    new_df_copy = new_df.copy()

    if not isinstance(previous_df_copy.index, pd.MultiIndex):
        logging.warning(
            "Previous DataFrame does not have a MultiIndex. Setting MultiIndex using 'set_qualified_name_timestamp_index'."
        )
        previous_df_copy = set_qualified_name_timestamp_index(
            df=previous_df_copy
        )

    if not isinstance(new_df_copy.index, pd.MultiIndex):
        logging.warning(
            "New DataFrame does not have a MultiIndex. Setting MultiIndex using 'set_qualified_name_timestamp_index'."
        )
        new_df_copy = set_qualified_name_timestamp_index(df=new_df_copy)

    if method == "concat":
        combined_df = pd.concat([previous_df_copy, new_df_copy])
        duplicated_indices_mask = combined_df.index.duplicated(keep=False)
        num_duplicates = duplicated_indices_mask.sum()

        if num_duplicates > 0:
            duplicated_indices = combined_df.index[
                duplicated_indices_mask
            ].unique()

            logging.warning(
                f"Found {num_duplicates} duplicate indices during concatenation. "
                f"Duplicates will be resolved by keeping the last occurrence. {duplicated_indices=}"
            )

        combined_df = combined_df[~combined_df.index.duplicated(keep="last")]

    elif method == "combine_first":
        combined_df = previous_df_copy.combine_first(new_df_copy)

    else:
        raise ValueError(
            f"Unsupported merge method: '{method}'. Choose 'concat' or 'combine_first'."
        )

    del previous_df_copy
    del new_df_copy

    return combined_df


def is_potential_float_str(s: str) -> bool:
    try:
        float(s)
        return True
    except ValueError:
        return False


def get_unique_merged_list(*args: list[float]) -> list[float]:
    return list({element for list_item in args for element in list_item})


@overload
def model_vol(
    model_params: ModelParamsVectorized,
    forward: NDArrayFloat64,
    expiry: NDArrayFloat64,
    model: Model,
    strike: NDArrayFloat64 | list[list[float]],
) -> Optional[NDArrayFloat64]: ...


@overload
def model_vol(
    model_params: ModelParamsBase,
    forward: float,
    expiry: float,
    model: Model,
    strike: float | NDArrayFloat64 | list[float],
) -> Optional[float | list[float] | NDArrayFloat64]: ...


def model_vol(
    model_params: ModelParamsTypes,
    forward: float | NDArrayFloat64,
    expiry: float | NDArrayFloat64,
    model: Model,
    strike: float | NDArrayFloat64 | list[float] | list[list[float]],
) -> Optional[float | list[float] | NDArrayFloat64]:
    vol = None

    if model == "SABR":
        if isinstance(forward, float):
            model_params = cast(SabrParams, model_params)
        else:
            model_params = cast(SabrParamsVectorized, model_params)

        vol = utils_calc.sabr_vol(
            k=strike,  # type: ignore
            f=forward,
            t=expiry,
            alpha=model_params["sabr_alpha"],
            beta=1,
            rho=model_params["sabr_rho"],
            volvol=model_params["sabr_volvol"],
        )

    elif model == "SVI":
        if isinstance(forward, float):
            model_params = cast(SviParams, model_params)
        else:
            model_params = cast(SviParamsVectorized, model_params)

        vol = utils_calc.svi_vol(  # type: ignore
            a=model_params["svi_a"],
            b=model_params["svi_b"],
            rho=model_params["svi_rho"],
            m=model_params["svi_m"],
            sigma=model_params["svi_sigma"],
            f=forward,
            exp=expiry,
            k=strike,
        )

    elif model == "spline":
        if isinstance(forward, float):
            # no vectorized implementation
            model_params = cast(CubicSpline, model_params)
            if isinstance(strike, float):
                return utils_calc.spline_model_vol([strike], model_params)[0]
            else:
                strike_as_list = list(strike)
                return utils_calc.spline_model_vol(
                    strike_as_list, model_params
                )  # TODO: support np.array type in spline interface
        else:
            model_params = cast(CubicSplineVectorized, model_params)

            assert isinstance(strike, (np.ndarray, list))
            assert len(strike) == len(
                model_params
            ), f"Mismatch between the number of strikes sets and spline parameter sets, {len(strike)=}, {len(model_params)=}"

            vols = []
            for strikes_set, model_params_set in zip(strike, model_params):
                strike_as_list = list(strikes_set)
                vols.append(
                    np.array(
                        utils_calc.spline_model_vol(
                            strike_as_list, model_params_set
                        )
                    )
                )
            return np.array(
                vols, dtype=object
            )  # TODO: support np.array type in spline interface
    else:
        raise NotImplementedError("Unrecognised Volatility Model")

    return vol


def check_calibrated_vols_and_log_error_with_details(
    precalibrated_vols: list[float] | NDArrayFloat64,
    model_predicted_vols: list[float] | NDArrayFloat64,
    tenor: float | int,
    timestamp: int,
    model: Model,
    **kwargs: Any,  # used to log where in the calc the error is occuring
) -> CalibrationMetrics:
    # We store this as 0 to mark failed calibrations
    calib_r2 = 0
    rmse = 0
    mse = 0
    mae = 0
    try:
        # constant tenor R2 score
        calib_r2 = r2_score(precalibrated_vols, model_predicted_vols)
        rmse = mean_squared_error(
            precalibrated_vols, model_predicted_vols, squared=False
        )
        mse = mean_squared_error(
            precalibrated_vols, model_predicted_vols, squared=True
        )
        mae = mean_absolute_error(precalibrated_vols, model_predicted_vols)
    except Exception as e:
        # On some bad calibrations e.g pure skew - no convexity/smile, we get NAN values for deep very OTM strikes
        # We can pass this bad calibration to the smoothing function and estimate a better set
        # of modelparameters.

        logging.warning(
            f"Error calculating calibration metrics for {tenor=}, {model=}, {timestamp=}, {kwargs=}, {calib_r2=}, {rmse=}, {mse=}, {mae=},{e=}. Likely due to a failed calibration"
        )
        # Even if we are to drop NAN values and calculate metrics, it would be meaningless as we would be
        # "correctly" fitting a bad smile, to another bad smile and would result in deceptively high
        # values for the metric

    return calib_r2, rmse, mse, mae


def remove_nan_columns_from_df(df: pd.DataFrame) -> pd.DataFrame:
    nan_cols = df.columns[df.isna().all()]
    df.drop(columns=nan_cols, inplace=True)
    return df


def is_derived_calc_result(
    data: DerivedCalcResult | ParamsCalcResult,
) -> TypeGuard[DerivedCalcResult]:
    return "smile" in data["dfs"] and "moneyness" in data["dfs"]


def is_params_calc_result(
    data: DerivedCalcResult | ParamsCalcResult,
) -> TypeGuard[ParamsCalcResult]:
    return "params" in data["dfs"]


def activate_tenor_types(
    df: pd.DataFrame,
    tenor_types_to_toggle: list[TenorType],
    initialise_tenor_types: bool,
) -> pd.DataFrame:
    """

    Note: This function will overwrite existing Tenor_type flags if not used correctly
    """

    if initialise_tenor_types:
        df[[t.value for t in TENOR_TYPES]] = False

    if tenor_types_to_toggle:
        for tenor_type in tenor_types_to_toggle:
            assert (
                tenor_type in TENOR_TYPES
            ), f"{tenor_type} is not a valid TenorType"
            df[tenor_type.value] = True

    return df


def _expand_arb_lookup_df(arb_lookup_df: pd.DataFrame) -> pd.DataFrame:
    """
    Expands the arb_lookup_df such that each row with a True flag for any expiry type is duplicated
    and adjusted, ensuring that only one flag remains True per row.

    """

    expanded_rows = []

    for tenor_type in TENOR_TYPES:
        true_rows = arb_lookup_df[arb_lookup_df[tenor_type.value]]
        true_rows = activate_tenor_types(
            df=true_rows,
            initialise_tenor_types=True,
            tenor_types_to_toggle=[tenor_type],
        )
        expanded_rows.append(true_rows)
    expanded_df = pd.concat(expanded_rows, ignore_index=True)

    return expanded_df


def create_qualified_name_from_expiry_str(
    exchange: str,
    currency: str,
    model: str,
    expiry_str: ExpiryType,
    freq: str,
    calc_type: str,
    version: Optional[str],
) -> ExpiryType:
    return (
        f"{version + '.' if version else ''}{exchange}.option.{currency}.{model}."
        + expiry_str
        + f".{freq}.{calc_type}"
    )


def _merge_with_arb_lookup_df(
    df: pd.DataFrame,
    arb_lookup_df: pd.DataFrame,
    calc_type: str,
    exchange: str,
    currency: str,
    model: str,
    freq: str,
    version: str,
    timestamp: int,
) -> pd.DataFrame:
    """
    Merges the given DataFrame with the arb_lookup_df on the 'tenor_days' column and adds
    an expiry string. It also generates a qualified name for each row.

    """

    relevant_columns = [
        "tenor_str",
        "tenor_days",
        "iso_expiry",
        TenorType.LISTED_EXPIRY.value,
        TenorType.ARBITRARY_EXPIRY.value,
        TenorType.STANDARD_CONSTANT_MATURITY.value,
        TenorType.ARBITRARY_CONSTANT_MATURITY.value,
    ]
    result_df = pd.merge(
        df, arb_lookup_df[relevant_columns], on="tenor_days", how="left"
    )
    result_df = _create_expiry_str_from_expiry_flags(
        df=result_df, timestamp=timestamp
    )
    result_df["qualified_name"] = create_qualified_name_from_expiry_str(
        exchange=exchange,
        currency=currency,
        model=model,
        expiry_str=result_df["expiry_str"],
        freq=freq,
        calc_type=calc_type,
        version=version,
    )
    result_df.drop(
        columns=["expiry_str", "iso_expiry", "tenor_str"], inplace=True
    )
    return result_df


def _create_expiry_str_from_expiry_flags(
    df: pd.DataFrame, timestamp: int
) -> pd.DataFrame:
    """
    Creates an expiry string for each row based on expiry flags and adds it as a new column.

    """

    def _determine_expiry_str(row: pd.Series) -> str:  # type: ignore
        is_maturity = (
            row[TenorType.ARBITRARY_CONSTANT_MATURITY.value]
            or row[TenorType.STANDARD_CONSTANT_MATURITY.value]
        )
        is_expiry = (
            row[TenorType.LISTED_EXPIRY.value]
            or row[TenorType.ARBITRARY_EXPIRY.value]
        )

        if is_maturity and not is_expiry:
            return str(row["tenor_str"])

        elif is_expiry and not is_maturity:
            return str(row["iso_expiry"])
        else:
            raise NotImplementedError()

    # expiry_str is used in downstream calcs like the theoreticalPrice Calc
    # and the restApi. Take care if modifying
    df["expiry_str"] = "-"
    if len(df) > 0:
        df["expiry_str"] = df.apply(_determine_expiry_str, axis=1)
    return df


def modify_string(s: Any) -> Any:
    """
    Parse and deserialize JSON-like strings, with fallback handling for malformed JSON.

    Args:
        s: Input that may be a JSON string or other data type

    Returns:
        Parsed JSON data if input is a valid/correctable JSON string, otherwise returns input unchanged
    """
    if isinstance(s, str):
        try:
            return cast(list[dict[str, Any]], orjson.loads(s))
        except orjson.JSONDecodeError:
            logging.warning(f"Malformed JSON objects found! Investigate {s=}")
            # fix malformed json objects
            s = s.replace("True", "true")
            s = s.replace("False", "false")
            s = s.replace("NaN", "null")
            s = s.replace("nan", "null")
            s = s.replace("'", '"')

            pattern = r"array\((.*?)\)"
            s = re.sub(pattern, r"\1", s)
            return cast(list[dict[str, Any]], orjson.loads(s))
    else:
        return s


def round_to_nearest_value(
    value: float, nearest: float, strategy: str
) -> float:
    """
    Rounds the value to the nearest multiple of `nearest` based on the rounding strategy.
    """
    if strategy == "floor":
        return math.floor(value / nearest) * nearest
    elif strategy == "ceil":
        return math.ceil(value / nearest) * nearest
    else:
        raise NotImplementedError("Invalid rounding strategy specified.")


def round_with_precision(value: float, precision: int, strategy: str) -> float:
    """
    Rounds the value to a specified number of decimal places using the rounding strategy.
    """
    if strategy == "default":
        return float(
            Decimal(value).quantize(
                Decimal(f'1.{"0" * precision}'), rounding=ROUND_HALF_UP
            )
        )
    else:
        raise NotImplementedError(
            "Invalid rounding strategy for precision specified."
        )


def round_value(
    value: float | int,
    precision: Optional[int] = None,
    round_to_nearest: Optional[float] = None,
    rounding_strategy: str = "default",
) -> float:
    """
    Rounds a value based on the given parameters.

    If both `round_to_nearest` and `precision` are provided, an error is raised.
    If `round_to_nearest` is provided, the function will round to the nearest multiple of that value.
    If `precision` is provided, the function will round to the specified number of decimal places.
    """

    # Assert that both `round_to_nearest` and `precision` are not specified
    if round_to_nearest is not None and precision is not None:
        raise ValueError(
            "Cannot specify both 'round_to_nearest' and 'precision'. Please choose one."
        )

    if round_to_nearest is not None:
        return round_to_nearest_value(
            value, round_to_nearest, rounding_strategy
        )

    elif precision is not None:
        return round_with_precision(value, precision, rounding_strategy)

    # Default rounding strategy
    return round(value)


def set_qualified_name_timestamp_index(df: pd.DataFrame) -> pd.DataFrame:
    """
    Sets the DataFrame index to a MultiIndex using qualified_name and timestamp columns.

    Args:
        df: Input DataFrame containing qualified_name and timestamp columns

    Returns:
        pd.DataFrame: DataFrame with MultiIndex (qualified_name, timestamp)

    Raises:
        ValueError: If qualified_name or timestamp columns are missing

    """
    missing_columns = []
    if "qualified_name" not in df.columns:
        missing_columns.append("qualified_name")
    if "timestamp" not in df.columns:
        missing_columns.append("timestamp")

    if missing_columns:
        error_msg = f"Qualified name and/or timestamp are not in the df columns. {missing_columns=}"
        logging.error(error_msg)
        raise ValueError(error_msg)

    df.set_index(keys=["qualified_name", "timestamp"], inplace=True)
    return df


def get_tenor_mapping_key(exchange: str, currency: str) -> str:
    return f"{exchange}.{currency}"
