import logging
from collections import defaultdict
from typing import Literal, Optional, cast

import numpy as np
import pandas as pd
import utils_calc
import utils_general
from calc_helpers import (
    SMOOTH_PARAMS,
    SPAN_SMOOTH_DEFAULT,
    SPAN_ZSCORE_DEFAULT,
    get_atm_deltas_from_boundaries,
    get_calibration_delta_boundary,
)
from constants import (
    CALC_TYPE_TO_COL_SPACE,
    IDX_SMOOTHING_COUNT_LIMIT,
    LOOKBACK_COLUMN_NAME,
    MAX_LOOKBACK_SMOOTHED_RATIO,
    MAX_SMOOTH_SPAN_SCALING,
    MIN_SMOOTHING_LOOKBACK_RATIO,
    NEXT_ITERATION_LOOKBACK_NAME,
    SPIKED_PARAMS_COLUMN_NAME,
    ZSCORE_RECALIBRATION_MULTIPLIER,
    load_domestic_rates,
)
from lambda_types import (
    SingleParams,
    SmoothConfig,
    SmoothedVolDf,
    SmoothingAndEstimationR<PERSON>ult,
    SmoothingCalcType,
    VolSpace,
    VolSurfaceCalcType,
    ZscoreParams,
)
from utils_calc import DEFAULT_SURFACE_MONEYNESS as MONEYNESS
from utils_calc import Model, extract_model_params, get_domestic_rate

from utils.calibrate import recalibrate_params_post_smoothing
from utils.common import (
    check_calibrated_vols_and_log_error_with_details,
    model_vol,
    remove_nan_columns_from_df,
    set_qualified_name_timestamp_index,
)
from utils.outputs import (
    create_output_col,
    drop_available_columns_from_df,
    undo_create_output_col,
)
from utils.surface import (
    calculate_delta_from_params,
    calculate_moneyness_from_params,
    get_min_max_strike_from_deltas,
)


def get_relevant_moneys_and_deltas(
    params: SingleParams,
    spot: float,
    forward: float,
    expiry: float,
    model: Model,
    exchange: str,
) -> tuple[list[float], list[float]]:
    """
    Function to return an ascending list of moneyness strikes
    that fall within min and max deltas that we calibrate on for each expiry.
    We also return the list of deltas to calibrate on


    """
    step = 0.01
    delta_ranges = get_calibration_delta_boundary(expiry)
    put_wing_boundary = delta_ranges["put_wing_boundary"]
    call_wing_boundary = delta_ranges["call_wing_boundary"]
    relevant_deltas = get_atm_deltas_from_boundaries(
        put_wing_delta=put_wing_boundary, call_wing_delta=call_wing_boundary
    )

    min_max_strikes = get_min_max_strike_from_deltas(
        params=params,
        spot=spot,
        forward=forward,
        expiry=expiry,
        model=model,
        exchange=exchange,
        min_delta=put_wing_boundary,
        max_delta=call_wing_boundary,
    )

    # round to 3dp to encapsulate finer moneynesss strikes to get an accurate estimate of 2nd dp
    min_money = round(min_max_strikes["min_strike"] / forward, 3)
    max_money = round(min_max_strikes["max_strike"] / forward, 3)

    relevant_money = [
        money
        for money in MONEYNESS
        if money >= min_money and money <= max_money
    ]

    if (min_money > max_money) or (  # bad modelparameters usually cause this
        not relevant_money
    ):
        logging.warning(
            f"Invalid min and max strikes derived from delta estimation, falling back to standard moneyness grid, {min_money=}, {max_money=}"
        )
        return MONEYNESS, relevant_deltas

    # we populate more moneyness strikes to aid re-calibrations downstream
    # SVI will fail if we only have 3 vols to calibrate on
    # This can happen on extremely short expiries
    if len(relevant_money) <= 3:
        utils_general.log_bsdebug(
            f"Insufficient relevant monies derived, adding extra monies to left and right wing, {spot=} {forward=} {expiry=} {model=} {exchange=}"
        )

        max_money = max(relevant_money)
        relevant_money.extend([max_money + step, max_money + (step * 2)])

        # Add one more value on the left-hand side
        min_money = min(relevant_money)
        relevant_money.append(min_money - step)

        relevant_money = sorted(i for i in relevant_money)

    return relevant_money, relevant_deltas


def _get_zscore_params(exchange: str, freq: str) -> ZscoreParams:
    params = SMOOTH_PARAMS[freq]["zscore"]
    z_params_dict: ZscoreParams = {
        "window": params["window"],
        "span_smooth": params["span_smooth"].get(
            exchange,
            SPAN_SMOOTH_DEFAULT[freq],
        ),
        "span_zscore": params["span_zscore"].get(
            exchange,
            SPAN_ZSCORE_DEFAULT[freq],
        ),
        "limit": params["limit"].get(exchange, 0),
        "adjust": params["adjust"],
    }

    return z_params_dict


def _get_span_scaling_factor(
    lookback_df: pd.DataFrame,
    max_smooth_ratio: float = MAX_LOOKBACK_SMOOTHED_RATIO,
) -> float:
    """
    Compute a scaling factor for the smoothing span based on the smoothed ratio.
    This helps us break out of prolonged periods of smoothing due to large and
    sharp move in vols

    As smoothed_ratio goes from max_smooth_ratio to 1.0, the scaling factor will
    go from 1.0 down to MAX_SMOOTH_SPAN_SCALING, linearly.
    """
    if max_smooth_ratio >= 1.0:
        raise ValueError(
            "max_smooth_ratio must be less than 1.0 to avoid division by zero"
        )

    smoothed_ratio = lookback_df["smoothed"].sum() / len(lookback_df)

    if smoothed_ratio <= max_smooth_ratio:
        return 1.0  # Below the threshold, scaling factor is 1.0
    elif smoothed_ratio == 1.0:
        return MAX_SMOOTH_SPAN_SCALING  # At or above the max ratio, scaling factor is max_scaling_value
    else:
        # Linearly interpolate between 1.0 and max_scaling_value
        return float(
            1.0
            + (MAX_SMOOTH_SPAN_SCALING - 1.0)
            * (smoothed_ratio - max_smooth_ratio)
            / (1.0 - max_smooth_ratio)
        )


def _get_lookback_df(df: pd.DataFrame, start_idx: int) -> pd.DataFrame:
    relevant_df = df.iloc[start_idx:]

    lookback_df = relevant_df[
        relevant_df[LOOKBACK_COLUMN_NAME].eq(True)
        | relevant_df[NEXT_ITERATION_LOOKBACK_NAME].eq(True)
    ]
    return lookback_df


def _smooth_zscore(
    df: pd.DataFrame,
    relevant_fields: list[str],
    smooth_config: SmoothConfig,
) -> pd.DataFrame:
    """

    :param df: Dataframe to smooth
    :param calc_type: Calc type to lookup smoothing configs for
    :param relevant_fields: List of fields to smooth
    :param smooth_config: A dictionary containing the exchange, model, and frequency we need to
        up smoothing configs for
    :return: Smoothed dataframe, and their respective z scores
    """
    df.loc[df["smoothed"].isna(), "smoothed"] = False
    df[NEXT_ITERATION_LOOKBACK_NAME] = False
    df.reset_index(inplace=True)

    zscore_details = smooth_config["zscore_details"]
    qn: str = df.iloc[0]["qualified_name"]
    window: int = zscore_details["window"]
    span_smooth: int = zscore_details["span_smooth"]
    span_zscore: int = zscore_details["span_zscore"]
    limit: float = zscore_details["limit"]
    adjust_flag: bool = zscore_details["adjust"]

    if not limit or len(df) < MIN_SMOOTHING_LOOKBACK_RATIO * window:
        logging.warning(
            f"Qualified name does not have sufficient datapoints for smoothing and/or zscore limit config missing for exchange, {qn=:}, {len(df)=:}, {limit=:}"
        )
        df = set_qualified_name_timestamp_index(df=df)

        return df

    fields_to_replace = relevant_fields
    fields_idx_to_replace = [df.columns.get_loc(f) for f in fields_to_replace]
    row_idx_smoothed_cols = defaultdict(set)
    idx_smooth_count: dict[int, int] = {}
    exceeded_smooth_count_idxs: set[int] = set()

    try:
        start_idx = 0

        while start_idx < len(df):
            lookback_df = _get_lookback_df(df, start_idx)
            scaling_factor = _get_span_scaling_factor(lookback_df)

            z = utils_calc.zscore_ema(
                df=df.iloc[start_idx:, fields_idx_to_replace],
                span=span_zscore,
            )

            # only check indices that we haven't been smoothed over `IDX_SMOOTHING_COUNT_LIMIT` times
            mask = df.iloc[start_idx:].index.isin(exceeded_smooth_count_idxs)
            z_to_check = z[~mask]
            above_zscore_limit_condition = (
                z_to_check[fields_to_replace].abs().gt(limit)
            )
            exceeded_zscore_limit_idx = cast(
                Optional[int],
                (
                    z_to_check.where(above_zscore_limit_condition)
                    .where(df[LOOKBACK_COLUMN_NAME].eq(False))
                    .where(
                        df[NEXT_ITERATION_LOOKBACK_NAME].eq(False)
                    )  # used to ignore rows when start_idx != 0
                    .first_valid_index()
                ),
            )

            if exceeded_zscore_limit_idx is None:
                break

            lookback_window_start_idx = max(
                exceeded_zscore_limit_idx - window, 0
            )
            if start_idx < lookback_window_start_idx:
                """
                NOTE: Each iteration calculates the zscore & smoothes for the
                whole dataframe beginning from start_idx, which would be either
                0 or the start of the lookback window. It is done on these df
                slices so we don't have to recalculate/iterate on every row,
                but instead "jump" straight to the next idx to smooth. On each
                iteration it may be that exceeded_zscore_limit_idx is at more than
                "window" away from start_idx, so this ensures it is re-checked
                with the correct window.

                This is relevant in backfill/ad-hoc runs where more than one
                new datapoint is calculated, so the df here would contain a
                larger dataset than would have been present in a scheduled
                lambda run. This _can create inconsistencies_ in smoothed output
                between a backfill and what was/would be produced in aws env,
                as the EMA works by incorporating all data made available.
                Continuing here serves to catch some of these edge cases and
                reduce inconsistencies due to larger window (ie. when a
                datapoint may have higher zscore due to it), but it does not
                eliminate them entirely (when datapoints may have lower
                zscores due to it)

                Both scenarios should result in fairly minor divergences that
                can be safely ignored for now, but important to note nonetheless.
                """
                start_idx = lookback_window_start_idx
                # we need to exclude these from smoothing in the subsequent iteration, as they are simply used to
                # initialise the lookback for the data point that has been identified for smoothing. If any of these
                # marked points needed to be smoothed, they would have already been smoothed a previous iteration.
                df.loc[
                    start_idx : start_idx + window - 1,
                    NEXT_ITERATION_LOOKBACK_NAME,
                ] = True
                continue

            above_limit_condition = (
                z.loc[exceeded_zscore_limit_idx, :].abs().gt(limit)
            )
            fields_to_smooth: list[str] = list(
                z.columns[above_limit_condition]  # type: ignore
            )
            fields_to_smooth_iloc = [
                df.columns.get_loc(f) for f in fields_to_smooth
            ]
            exceeded_limit_iloc = df.index.get_loc(exceeded_zscore_limit_idx)

            ts = cast(int, df.iloc[exceeded_limit_iloc]["timestamp"])
            isodate = utils_general.to_iso(ts)

            # store the number of times we smooth each idx
            idx_smooth_count[exceeded_zscore_limit_idx] = (
                idx_smooth_count.get(exceeded_zscore_limit_idx, 0) + 1
            )
            # if we've smoothed an idx more than `IDX_SMOOTHING_COUNT_LIMIT` times, skip and record idx
            if (
                idx_smooth_count[exceeded_zscore_limit_idx]
                > IDX_SMOOTHING_COUNT_LIMIT
            ):
                logging.warning(
                    f"Exceeded smoothing count limit for {qn=:}, {isodate=:}, {ts=:}"
                )
                exceeded_smooth_count_idxs.add(exceeded_zscore_limit_idx)
                continue

            logging.info(
                f"Smoothing by zscore for {qn=:}, {isodate=:}, {ts=:}",
            )

            scaled_span = span_smooth * scaling_factor
            df.iloc[start_idx:, fields_to_smooth_iloc] = utils_calc.smooth_ema(
                df=df.iloc[start_idx:, fields_to_smooth_iloc],
                idx_to_smooth=exceeded_zscore_limit_idx,
                fields_to_replace=fields_to_smooth,
                adjust=adjust_flag,
                span=scaled_span,  # type: ignore
            )

            df.loc[exceeded_zscore_limit_idx, "smoothed"] = True
            row_idx_smoothed_cols[exceeded_zscore_limit_idx].update(
                fields_to_smooth
            )

        if row_idx_smoothed_cols:
            df.loc[:, "smoothed_columns"] = df.index.map(  # type: ignore
                lambda x: list(row_idx_smoothed_cols[x])
            )

    except Exception as e:
        logging.exception(f"Failed to smooth {qn=:}, {e=:}")

    df = drop_available_columns_from_df(
        df=df, columns=[NEXT_ITERATION_LOOKBACK_NAME]
    )
    df = set_qualified_name_timestamp_index(df=df)

    return df


def smooth_calc_type_chunk(
    chunk: list[pd.DataFrame],
    freq: str,
    start: str,
    smoothing_calc_types: list[SmoothingCalcType],
    estimate_params: bool = False,
) -> SmoothingAndEstimationResult:
    # qualified_name_prefix -> Calctype -> smoothed/unsmoothed_timeseries
    smoothing_results = cast(
        SmoothingAndEstimationResult, utils_general.nested_dict()
    )

    for qualified_name_prefix_grouped_df in chunk:
        qualified_name_prefix = qualified_name_prefix_grouped_df[
            "qualified_name_prefix"
        ].iloc[0]
        qualified_name_prefix_grouped_df.loc[
            qualified_name_prefix_grouped_df[LOOKBACK_COLUMN_NAME].eq(False),
            "smoothed",
        ] = False
        qualified_name_prefix_grouped_df.sort_index(
            level="timestamp", inplace=True
        )

        if not _df_has_valid_non_lookback_data(
            df=qualified_name_prefix_grouped_df,
            qualified_name_prefix=qualified_name_prefix,
            start=start,
        ):
            continue

        for calc_type in smoothing_calc_types:
            calc_type_df = qualified_name_prefix_grouped_df[
                qualified_name_prefix_grouped_df.index.get_level_values(
                    "qualified_name"
                ).str.contains(calc_type)
            ]

            non_lookback_df = calc_type_df[
                calc_type_df[LOOKBACK_COLUMN_NAME].eq(False)
            ]

            if calc_type == "params":
                # we collect the pre-smoothed params suffixed data as they may need to be
                # re-estimated from smoothed vols below
                smoothing_results[qualified_name_prefix][
                    calc_type
                ] = non_lookback_df
                continue

            elif calc_type == "strike":
                # TenorTypes.STANDARD_CONSTANT_TENOR strikes for the scheduled system have been filtered out prior to
                # this function so no redundant smoothing is happening.
                # We want to allow TenorTypes.STANDARD_CONSTANT_TENOR tenor strikes to be smoothed
                # as a user may subscribe to this in the live system.
                if non_lookback_df.empty:
                    # Either:
                    #   - standard_constant_tenor ONLY in the scheduled system,
                    #   - arbitrary_expiry with no subscribed strikes
                    #   - arbitrary_constant_maturity with no subscribed strikes
                    continue

            # narrow down typing - params not in volsurface Calc types
            calc_type = cast(VolSurfaceCalcType, calc_type)

            try:

                # get qualified_name from the first row
                calc_type_qn = next(
                    iter(calc_type_df.index.get_level_values("qualified_name"))
                )
                version, qn_tokens = utils_general.get_qfn_and_version(
                    calc_type_qn
                )
                e, m = qn_tokens[0], cast(Model, qn_tokens[3])
                smooth_config = construct_smoothing_config(
                    exchange=e, model=m, freq=freq
                )

                smoothed_calc_type_df = smooth_calc_type(
                    calc_type_df=calc_type_df,
                    calc_type=calc_type,
                    smooth_config=smooth_config,
                )

                smoothing_results[qualified_name_prefix][
                    calc_type
                ] = smoothed_calc_type_df

            except Exception:
                logging.exception(
                    f"Error smoothing timeseries, {qualified_name_prefix=:}, chunk_start={start}"
                )

    if estimate_params:
        smoothing_results = estimate_params_helper(
            smoothing_results=smoothing_results, start=start, freq=freq
        )

    return smoothing_results


def smooth_calc_type(
    calc_type_df: pd.DataFrame,
    calc_type: VolSurfaceCalcType,
    smooth_config: SmoothConfig,
) -> pd.DataFrame:
    def _get_cols_to_smooth(space: str) -> list[str]:
        # "moneyness" and "delta" were part of the columns but are not wanted in this use case. These held
        # respective surfaces for listed bundles. These columns have been removed in the lookback_window.py
        # but can re-occur depending on changes upstream
        numerical_cols = (
            calc_type_df.filter(like=space)
            .select_dtypes(include=["number"])
            .columns.tolist()
        )
        # we want to smooth whatever columns suffixed with "space" are present within the dataframe as these may include
        # strikes that have been arbitrarily subscribed to + atm.
        if not numerical_cols:
            raise ValueError(
                f"No numerical columns found with the keyword '{space}'"
            )

        if space == "delta":
            # smooth the atm as well
            numerical_cols.append("atm")

        return numerical_cols

    # Smooth out the vols
    calc_type_df = _smooth_zscore(
        df=calc_type_df,
        smooth_config=smooth_config,
        relevant_fields=_get_cols_to_smooth(
            space=CALC_TYPE_TO_COL_SPACE[calc_type]
        ),
    )

    return calc_type_df


def estimate_params_helper(
    smoothing_results: SmoothingAndEstimationResult,
    start: str,
    freq: str,
) -> SmoothingAndEstimationResult:
    """
    Note: This function modifies smoothing_results

    """
    for qualified_name_prefix, calc_type_to_timeseries in list(
        smoothing_results.items()
    ):
        (
            version,
            qualified_name_prefix_tokens,
        ) = utils_general.get_qfn_and_version(qualified_name_prefix)
        exchange, model = (
            qualified_name_prefix_tokens[0],
            cast(Model, qualified_name_prefix_tokens[3]),
        )
        smooth_config = construct_smoothing_config(
            exchange=exchange, model=model, freq=freq
        )
        calc_type_to_timeseries["params"][SPIKED_PARAMS_COLUMN_NAME] = False

        try:
            calc_type_to_timeseries["params"] = estimate_new_params(
                qn_grouped_params_df=calc_type_to_timeseries["params"],
                moneyness_vol_df=calc_type_to_timeseries["moneyness"].copy(),
                delta_vol_df=calc_type_to_timeseries["smile"].copy(),
                smooth_config=smooth_config,
            )

        except Exception as err:
            logging.exception(
                f"Error estimating new Params, {qualified_name_prefix=:}, {err=:}, chunk_start={start}"
            )

    return smoothing_results


def estimate_new_params(
    qn_grouped_params_df: pd.DataFrame,
    moneyness_vol_df: pd.DataFrame,
    delta_vol_df: pd.DataFrame,
    smooth_config: SmoothConfig,
) -> SmoothedVolDf:
    """
    This function estimates a new set of smoothed model params, by takings the
     already existing model the delta and moneyness timeseries that are passed in,
     looking for any set of smoothed vols from either timeseries, and then recalibrates
     a new estimate of the model parameters that result in these smoothed vols.

    """

    # An edge case can exist when we are backfilling with a large chunk (timeseries). Because we use the last datapoint in the chunk (smoothing_row) below,
    # the forward price, time to expiry and spot may differ from the start of the chunk (one timestamp to another), and the datapoint we are considering (smoothing_row).
    # Therefore, the deltas returned in first datapoint in our chunk could consider the 50 to 150 money, whereas the deltas returned in the last
    # datapoint in the chunk could consider the 40 to 160 money.
    def _apply_strikes_vols_helper(
        row: pd.Series,  # type: ignore
        vol_space: Literal["delta", "money"],
        exchange: str,
    ) -> pd.Series:  # type: ignore
        result = get_strikes_and_vols_from_vol_space_mappings(
            row, vol_space, exchange
        )
        return pd.Series(
            [result.get("strikes"), result.get("vols")],
            index=["strikes", "vols"],
        )

    m, e = (
        smooth_config["model"],
        smooth_config["exchange"],
    )
    smoothing_row = qn_grouped_params_df.iloc[-1]
    qn_grouped_params_df[SPIKED_PARAMS_COLUMN_NAME] = False
    relevant_moneys, relevant_deltas = get_relevant_moneys_and_deltas(
        params=smoothing_row,
        spot=smoothing_row["spot"],
        forward=smoothing_row["forward"],
        expiry=smoothing_row["expiry"],
        model=m,
        exchange=e,
    )
    relevant_base_cols = [
        "smoothed",
        LOOKBACK_COLUMN_NAME,
    ]
    aux_cols = ["spot", "forward", "expiry"]

    # truncate smile and moneyness columns for smoothing-related calculations in this function
    relevant_deltas_cols = (
        [create_output_col("delta", str(f)) for f in relevant_deltas]
        + relevant_base_cols
        + ["atm"]
    )
    relevant_moneys_cols = [
        create_output_col("money", str(f)) for f in relevant_moneys
    ] + relevant_base_cols

    # only timestamps remain as levels
    moneyness_vol_df = moneyness_vol_df[relevant_moneys_cols].reset_index(
        level="qualified_name"
    )
    delta_vol_df = delta_vol_df[relevant_deltas_cols].reset_index(
        level="qualified_name"
    )
    qn_grouped_params_df = qn_grouped_params_df.reset_index(
        level="qualified_name"
    )

    # Retain only vols that are smoothed and not from lookback
    moneyness_vol_df_smoothed = moneyness_vol_df[
        moneyness_vol_df["smoothed"].eq(True)
        & moneyness_vol_df[LOOKBACK_COLUMN_NAME].eq(False)
    ]
    delta_vol_df_smoothed = delta_vol_df[
        delta_vol_df["smoothed"].eq(True)
        & delta_vol_df[LOOKBACK_COLUMN_NAME].eq(False)
    ]

    if moneyness_vol_df_smoothed.empty and delta_vol_df_smoothed.empty:
        return qn_grouped_params_df.set_index(
            "qualified_name", append=True
        ).reorder_levels(
            ["qualified_name", "timestamp"]
        )  # no recalibration needed, early return

    # recalibration needed

    for df in [moneyness_vol_df_smoothed, delta_vol_df_smoothed]:
        df.loc[:, aux_cols] = qn_grouped_params_df.loc[df.index, aux_cols]

    moneyness_vol_df_smoothed["vol_space"] = "money"
    delta_vol_df_smoothed["vol_space"] = "delta"

    # apply strikes_to_vols
    if not moneyness_vol_df_smoothed.empty:
        moneyness_vol_df_smoothed[["strikes", "vols"]] = (
            moneyness_vol_df_smoothed.apply(
                _apply_strikes_vols_helper, args=("money", e), axis=1
            )
        )
    if not delta_vol_df_smoothed.empty:
        delta_vol_df_smoothed[["strikes", "vols"]] = (
            delta_vol_df_smoothed.apply(
                _apply_strikes_vols_helper, args=("delta", e), axis=1
            )
        )

    # clean the df
    moneyness_vol_df_smoothed = remove_nan_columns_from_df(
        moneyness_vol_df_smoothed
    )
    delta_vol_df_smoothed = remove_nan_columns_from_df(delta_vol_df_smoothed)

    # Find the overlapping smoothed indices
    overlapping_indices = moneyness_vol_df_smoothed.index.intersection(
        delta_vol_df_smoothed.index
    )

    if not overlapping_indices.empty:
        # For overlapping indices, we want to recalibrate on both, and choose which has the lowest MAE
        # The reasoning behind this is that we trust our smoothing process, but want to choose the best calibration
        # The MAE measures this well
        moneyness_to_recalibrate = moneyness_vol_df_smoothed.loc[
            overlapping_indices
        ]
        delta_to_recalibrate = delta_vol_df_smoothed.loc[overlapping_indices]

        # Recalibrate params for smoothed moneyness vols
        recalibrated_params_moneyness = moneyness_to_recalibrate.apply(  # type: ignore
            recalibrate_params_post_smoothing, args=(m, e), axis=1
        )
        recalibrated_params_moneyness = calculate_recalibration_metrics(
            recalibrated_params_moneyness, moneyness_to_recalibrate, m
        )

        # Recalibrate params for smoothed delta vols
        recalibrated_params_delta = delta_to_recalibrate.apply(  # type: ignore
            recalibrate_params_post_smoothing, args=(m, e), axis=1
        )
        recalibrated_params_delta = calculate_recalibration_metrics(
            recalibrated_params_delta, delta_to_recalibrate, m
        )

        # Use their Mean Absolute Errors values to create a mask
        MAE_mask = (
            recalibrated_params_moneyness.loc[:, "MAE"]
            < recalibrated_params_delta.loc[:, "MAE"]
        )

        # Select the lowest MAE from the two vol spaces
        selected_from_moneyness = recalibrated_params_moneyness[MAE_mask]
        selected_from_delta = recalibrated_params_delta[~MAE_mask]

        # Concatenate the selected rows, one will be empty
        overlapping_recalibrated_params = pd.concat(
            [selected_from_moneyness, selected_from_delta]
        )
    else:
        overlapping_recalibrated_params = pd.DataFrame()

    # Find non-overlapping indices
    moneyness_vol_df_smoothed.drop(overlapping_indices, inplace=True)
    delta_vol_df_smoothed.drop(overlapping_indices, inplace=True)

    if not moneyness_vol_df_smoothed.empty or not delta_vol_df_smoothed.empty:
        # recalibrate non overlapping indices
        non_overlapping_vols_to_recalibrate = pd.concat(
            [
                moneyness_vol_df_smoothed,
                delta_vol_df_smoothed,
            ]
        )

        # Recalibrate params for non-overlapping smoothed vols
        non_overlapping_params = non_overlapping_vols_to_recalibrate.apply(  # type: ignore
            recalibrate_params_post_smoothing, args=(m, e), axis=1
        )
        non_overlapping_params = calculate_recalibration_metrics(
            non_overlapping_params, non_overlapping_vols_to_recalibrate, m
        )
        recalibrated_params = pd.concat(
            [non_overlapping_params, overlapping_recalibrated_params]
        )
    else:
        recalibrated_params = overlapping_recalibrated_params

    recalibrated_params.loc[:, [*aux_cols, "qualified_name"]] = (
        qn_grouped_params_df.loc[
            recalibrated_params.index, [*aux_cols, "qualified_name"]
        ]
    )
    recalibrated_params["smoothed"] = True

    # add R2 value for the recalibrated params
    qn_grouped_params_df["R2_smoothcalib"] = recalibrated_params["R2"]
    qn_grouped_params_df["MAE_smoothcalib"] = recalibrated_params["MAE"]

    # add spiked-params flag to underlying modelparams if present
    recalibrated_params = _add_spiked_params_flag(
        recalibrated_params=recalibrated_params,
        relevant_moneys=relevant_moneys,
        relevant_deltas=relevant_deltas,
        moneyness_vol_df=moneyness_vol_df,
        delta_vol_df=delta_vol_df,
        smooth_config=smooth_config,
    )

    # Update the original DataFrame with recalibrated_params
    qn_grouped_params_df.update(recalibrated_params)

    qn_grouped_params_df = set_qualified_name_timestamp_index(
        qn_grouped_params_df.reset_index()
    )

    return qn_grouped_params_df


def calculate_recalibration_metrics(
    recalibrated_params: pd.DataFrame,
    vols_to_recalibrate: pd.DataFrame,
    m: Model,
) -> pd.DataFrame:
    """
    Calculate the R^2, MSE, RMSE and MAE value by comparing the newly smoothed vols to
    the implied vols derived from the calibrated parameters.


    """

    assert (recalibrated_params.index == vols_to_recalibrate.index).all()

    for (tstamp_vol_idx, vol_row), (tstamp_params_idx, params_row) in zip(
        vols_to_recalibrate.iterrows(), recalibrated_params.iterrows()
    ):
        vols_row_vol_space = vol_row["vol_space"]
        expiry = params_row["expiry"]
        forward = params_row["forward"]
        model_params = extract_model_params(params_row, m)

        assert vols_row_vol_space == params_row["vol_space"]

        strikes = vol_row["strikes"]
        smoothed_vols = vol_row["vols"]

        predicted_vols = cast(
            list[float],
            model_vol(
                model_params=model_params,
                forward=forward,
                expiry=expiry,
                model=m,
                strike=np.array(strikes),
            ),
        )
        (
            calib_r2,
            rmse,
            mse,
            mae,
        ) = check_calibrated_vols_and_log_error_with_details(
            precalibrated_vols=smoothed_vols,
            model_predicted_vols=predicted_vols,
            tenor=params_row["expiry"],
            timestamp=cast(int, tstamp_vol_idx),
            model=m,
            process="recalibration metrics",
        )
        # add calibration metrics to params row
        recalibrated_params.at[tstamp_params_idx, "R2"] = calib_r2
        recalibrated_params.at[tstamp_params_idx, "RMSE"] = rmse
        recalibrated_params.at[tstamp_params_idx, "MSE"] = mse
        recalibrated_params.at[tstamp_params_idx, "MAE"] = mae

        vol_error = np.asarray(smoothed_vols) - predicted_vols
        calibration_deviation = dict(
            zip(vol_row.filter(like=vols_row_vol_space).index.values, vol_error)
        )
        recalibrated_params.at[tstamp_params_idx, "smooth_vol_diff"] = [
            calibration_deviation
        ]
        recalibrated_params.at[tstamp_params_idx, "max_smooth_vol_diff"] = max(
            abs(value) for value in calibration_deviation.values()
        )

    return recalibrated_params


def get_strikes_and_vols_from_vol_space_mappings(
    mapping_object: SingleParams,
    vol_space: Literal["delta", "money"],
    exchange: str,
) -> dict[str, list[float]]:
    if isinstance(mapping_object, pd.Series):
        vol_space_series = mapping_object.filter(like=vol_space)
        vols = cast(list[float], vol_space_series.to_list())
        cols_output = list(vol_space_series.index.values)
    else:
        key_vals = {
            str(k): v for k, v in mapping_object.items() if vol_space in str(k)
        }
        cols_output = list(key_vals.keys())
        vols = cast(list[float], list(key_vals.values()))

    cols_numeric = [
        undo_create_output_col(input_col=col, suffix=vol_space)
        for col in cols_output
    ]

    if vol_space == "delta":
        strikes = [
            utils_calc.strike_from_delta_vol(
                s=cast(float, mapping_object["spot"]),
                f=cast(float, mapping_object["forward"]),
                r_d=get_domestic_rate(
                    load_domestic_rates(),
                    cast(float, mapping_object["expiry"]) * 365,
                ),
                t=cast(float, mapping_object["expiry"]),
                vol=vol,
                delta=delta,
            )
            for vol, delta in zip(vols, cols_numeric)
        ]
    elif vol_space == "money":
        forward = cast(float, mapping_object["forward"])
        strikes = [forward * moneyness for moneyness in cols_numeric]
    else:
        raise NotImplementedError("Unrecognised Volatility space")

    assert len(strikes) == len(vols)

    return {"vols": vols, "strikes": strikes}


def _add_spiked_params_flag(
    recalibrated_params: pd.DataFrame,
    relevant_moneys: list[float],
    relevant_deltas: list[float],
    moneyness_vol_df: pd.DataFrame,
    delta_vol_df: pd.DataFrame,
    smooth_config: SmoothConfig,
) -> pd.DataFrame:
    """

    This function aims to ensure that recalibration errors, which might introduce spikes in the recalibrated parameters,
    are identified. It checks the z-score of the newly estimated points against their historical values
    and removes any points exceeding a predefined threshold.

    :param recalibrated_params: The dataframe containing re-estimated model parameters post smoothing.
    :param relevant_moneys: A list of relevant moneyness values used for recalibration.
    :param relevant_deltas: A list of relevant delta values used for recalibration.
    :param moneyness_vol_df: The dataframe containing smoothed volatility data in moneyness space.
    :param delta_vol_df: The dataframe containing smoothed volatility data in delta space.
    :param smooth_config: A dictionary containing smoothing configurations for the moneyness and delta volatility
        timeseries that are passed in to the function

    """

    def _get_zscore(
        df: pd.DataFrame,
        relevant_fields: list[float],
        span_zscore: int,
        vol_space: VolSpace,
    ) -> pd.DataFrame:
        relevant_cols = [
            create_output_col(vol_space, str(f)) for f in relevant_fields
        ]
        return utils_calc.zscore_ema(
            df=df.loc[:, relevant_cols],
            span=span_zscore,
        )

    zscore_details = smooth_config["zscore_details"]
    recalibrated_params[SPIKED_PARAMS_COLUMN_NAME] = False

    recalibrated_params_on_smoothed_vol_space_vols: dict[
        VolSpace, pd.DataFrame
    ] = {
        "delta": recalibrated_params[
            recalibrated_params["vol_space"].eq("delta")
        ],
        "money": recalibrated_params[
            recalibrated_params["vol_space"].eq("money")
        ],
    }

    recalibrated_vols_dict: dict[VolSpace, pd.DataFrame] = {
        "money": (
            recalibrated_params_on_smoothed_vol_space_vols["money"].apply(
                calculate_moneyness_from_params,
                args=(smooth_config["model"], relevant_moneys),
                axis=1,
            )
            if not recalibrated_params_on_smoothed_vol_space_vols["money"].empty
            else pd.DataFrame()
        ),
        "delta": (
            recalibrated_params_on_smoothed_vol_space_vols["delta"].apply(
                calculate_delta_from_params,
                args=(
                    smooth_config["model"],
                    smooth_config["exchange"],
                    relevant_deltas,
                ),
                axis=1,
            )
            if not recalibrated_params_on_smoothed_vol_space_vols["delta"].empty
            else pd.DataFrame()
        ),
    }

    moneyness_vol_df.update(recalibrated_vols_dict["money"])
    delta_vol_df.update(recalibrated_vols_dict["delta"])

    # The multiplier is applied to relax our acceptance criteria of a recalibration spike.
    # The z-score of the recalibrated vols (those that are implied by params that have been recalibrated from smoothed vols)
    # are allowed to be ZSCORE_RECALIBRATION_MULTIPLIER greater than the z-score smoothing limit
    threshold = zscore_details["limit"] * ZSCORE_RECALIBRATION_MULTIPLIER

    zscore_money = _get_zscore(
        moneyness_vol_df,
        relevant_moneys,
        zscore_details["span_zscore"],
        "money",
    )
    zscore_delta = _get_zscore(
        delta_vol_df, relevant_deltas, zscore_details["span_zscore"], "delta"
    )
    # vol_df and zscore indices match up -
    # we use vol_df to grab the indices we care about
    money_subset = zscore_money.loc[
        moneyness_vol_df.index.isin(
            recalibrated_params_on_smoothed_vol_space_vols["money"].index
        )
    ]
    delta_subset = zscore_delta.loc[
        delta_vol_df.index.isin(
            recalibrated_params_on_smoothed_vol_space_vols["delta"].index
        )
    ]

    # Identify where z-scores exceed the threshold
    exceed_money = money_subset.abs().gt(threshold)
    exceed_delta = delta_subset.abs().gt(threshold)

    above_limit = {
        "money": exceed_money.any(axis=1),
        "delta": exceed_delta.any(axis=1),
    }

    # Align the indices of the boolean Series with their corresponding DataFrame indices
    money_indexer = above_limit["money"].reindex(
        moneyness_vol_df.index, fill_value=False
    )
    delta_indexer = above_limit["delta"].reindex(
        delta_vol_df.index, fill_value=False
    )

    # Use the reindexed boolean Series to select indices
    timestamps_to_remove = (
        pd.concat(
            [
                moneyness_vol_df.loc[money_indexer],
                delta_vol_df.loc[delta_indexer],
            ]
        )
        .index.unique()
        .tolist()
    )

    qn = next(iter(recalibrated_params["qualified_name"]))

    if not timestamps_to_remove:
        return recalibrated_params

    # Build a consolidated log message for each timestamp
    for ts in timestamps_to_remove:
        # For each timestamp, gather exceeded columns by vol space
        for vol_space, subset, exceed_mask in [
            ("money", money_subset, exceed_money),
            ("delta", delta_subset, exceed_delta),
        ]:
            if ts not in subset.index:
                continue

            ts_exceed = exceed_mask.loc[ts]
            exceeded_columns = ts_exceed.index[ts_exceed]

            # Create a condensed dictionary of exceeded columns with (zscore, exceeded_by)
            exceeded_info = {}
            for col in exceeded_columns:
                col_zscore = subset.loc[ts, col]
                diff = abs(col_zscore) - threshold
                exceeded_info[col] = (
                    f"Z: {round(float(abs(col_zscore)), 4)}",
                    f"Diff: {round(float(diff), 4)}",
                )

            logging.warning(
                "SPIKES ENCOUNTERED POST-RECALIBRATION, EXCLUDING FROM STREAM OUTPUT: timestamp=%s, vol_space=%s, qualified_name=%s, base_zlimit=%s, spike_zlimit_threshold=%.4f, exceeded_columns=%s",
                ts,
                vol_space,
                qn,
                zscore_details["limit"],
                threshold,
                exceeded_info,
            )

    # Mark columns to be dropped later inside post smoothing processing function
    recalibrated_params.loc[
        recalibrated_params.index.isin(timestamps_to_remove),
        SPIKED_PARAMS_COLUMN_NAME,
    ] = True

    return recalibrated_params


def _is_expired_option(qn_prefix: str, timestamp_iso: str) -> bool:
    tenor_or_iso_exp = qn_prefix.split(".")[-2]
    if ":" in tenor_or_iso_exp:
        exp_days = utils_general.convert_expiry_to_days(
            expiry=tenor_or_iso_exp, timestamp=timestamp_iso
        )
        return exp_days < 0
    return False


def _df_has_valid_non_lookback_data(
    df: pd.DataFrame, qualified_name_prefix: str, start: str
) -> bool:
    non_lookback_df = df[df[LOOKBACK_COLUMN_NAME].eq(False)]
    if non_lookback_df.empty:
        # look for why its empty and log
        if _is_expired_option(
            qn_prefix=qualified_name_prefix,
            timestamp_iso=start,
        ):
            logging.warning(
                f"Expired option encountered, skipping {qualified_name_prefix=}, chunk_start={start}"
            )

        else:
            logging.error(
                f"Encountered qualified name with no modelparameters present in interval. Likely removed from modelParamsCalc {qualified_name_prefix=}. Investigate"
            )

        return False
    return True


def construct_smoothing_config(
    exchange: str, model: Model, freq: str
) -> SmoothConfig:
    zscore_details: ZscoreParams = _get_zscore_params(
        exchange=exchange, freq=freq
    )
    smooth_config: SmoothConfig = {
        "exchange": exchange,
        "model": model,
        "freq": freq,
        "zscore_details": zscore_details,
    }
    return smooth_config
