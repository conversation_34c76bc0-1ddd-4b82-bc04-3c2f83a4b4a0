import logging
from collections import defaultdict
from collections.abc import Iterable
from datetime import UTC, datetime, timezone
from typing import Any, Literal, cast

import utils_general
from block_stream import ConsumerBaseFunctionClass, Subscriber
from constants import SMOOTHING_CALC_TYPES, TENOR_PRECISION
from lambda_types import Expiry<PERSON>rb<PERSON>ields, TenorExpiryArbFields, TenorType
from stream_types import (
    TYPES,
    Expiry,
    ExpirySubInfo,
    FlexInternalMessageValues,
    IVTypeToSub,
    MessageValueKeys,
    SingleSubscribeInfo,
    StreamCalcTypes,
    SubscribeMessage,
    Totals,
)
from utils.common import round_value
from utils_general.aggregate_logger import (
    LoggingRecord,
    collate_logs,
    get_agg_logger,
)


class SubscriptionMessageLogs(LoggingRecord):
    command: str
    calculator: str

    subscriber_id: str
    instruments_str: str


class ActiveFunction(ConsumerBaseFunctionClass[SubscribeMessage, Subscriber]):
    def __init__(self, command: SubscribeMessage) -> None:
        self._expiry_subscriber_map: dict[Expiry, ExpirySubInfo] = defaultdict(
            lambda: {
                "subscribers": {},
                "totals": Totals(
                    strikes=defaultdict(set),
                    deltas=defaultdict(set),
                    moneyness=defaultdict(set),
                ),
            }
        )
        # We default to calculating 4 types, params and IV releated series:
        # 1. If a subscription message contains an additional type which is not related to this function we will
        #    add it to the calc types and calculate for all expiries for ease (e.g. butterfly).
        # 2. Uniform calc_types across expiries simplify the management of subscriptions. It's reasonable to assume
        #    that if an exchange subscribes to a specific calculation type (like 'butterfly') for one expiry, they would
        #    likely require it for most expiries. Adjusting 'calc_types' individually for each exchange
        #    contradicts this assumption and leads to unnecessary complexity as we would have to link 1 type to 1 expiry.
        # Therefore, types is be predetermined and the output should be consumed based on permissions to ensure
        # a more streamlined and manageable subscription process.
        self._calc_types = SMOOTHING_CALC_TYPES.copy()
        super().__init__(command)

        # After init as it accesses self._subscribers
        if "command" in command and command["command"] == "subscribe":
            self.subscribe(command)

    def _get_raw_msg_vals(
        self, message: SubscribeMessage
    ) -> FlexInternalMessageValues:
        raw_msg: FlexInternalMessageValues = {}
        if message["type"] == "params":
            raw_msg["params"] = True
        vals = message.get("values")
        if vals is not None:
            for k, v in vals.items():
                k_casted = cast(MessageValueKeys, k)
                if k_casted != "params":
                    assert isinstance(v, list)
                    raw_msg[k_casted] = set(v)
        return raw_msg

    def _update_expiries_totals(
        self,
        message: SubscribeMessage,
        type: Literal["add", "remove"] = "add",
    ) -> None:
        totals = self._expiry_subscriber_map[message["expiry"]]["totals"]
        values = {
            k: v for k, v in message.get("values", {}).items() if k != "params"
        }

        for k, v in values.items():
            assert isinstance(v, list)
            # Params should always be calculated so no need to keep totals
            if k in totals and k != "params":
                k_casted = cast(TYPES, k)
                for val in v:
                    if type == "add":
                        totals[k_casted][val].add(message["subscriber_id"])
                    else:
                        totals[k_casted][val].remove(message["subscriber_id"])
                        if len(totals[k_casted][val]) == 0:
                            del totals[k_casted][val]

        self._expiry_subscriber_map[message["expiry"]]["totals"] = totals

    def subscribe(self, message: SubscribeMessage) -> None:
        message_str = "Subscribed to"
        raw_msg = self._get_raw_msg_vals(message)
        subscribers_details: SingleSubscribeInfo = self._expiry_subscriber_map[
            message["expiry"]
        ]["subscribers"].get(message["subscriber_id"], {})
        # As strikes are purely arbitrary subscriptions only calc if needed
        if message["type"] == "strike":
            self._calc_types.add("strike")
        # Subscriber has subscribed to new expiry
        if not subscribers_details:
            subscribers_details = raw_msg
            _add_msg_to_consolidate(message_str=message_str, msg=message)
        else:
            for k, v in raw_msg.items():
                k_casted = cast(MessageValueKeys, k)
                if (
                    k_casted in subscribers_details
                    and subscribers_details[k_casted]
                    and isinstance(v, Iterable)
                ):
                    k_casted = cast(TYPES, k)
                    existing_values = set(subscribers_details[k_casted])
                    new_values = set(v)
                    if not new_values.issubset(existing_values):
                        subscribers_details[k_casted].update(new_values)
                        _add_msg_to_consolidate(
                            message_str=message_str, msg=message
                        )
                elif isinstance(v, Iterable):
                    subscribers_details[k_casted] = set(v)
                    _add_msg_to_consolidate(
                        message_str=message_str, msg=message
                    )
                if (
                    message["type"] == "params"
                    and "params" not in subscribers_details
                ):
                    subscribers_details["params"] = True
        self._update_expiries_totals(message)

        self._expiry_subscriber_map[message["expiry"]]["subscribers"][
            message["subscriber_id"]
        ] = subscribers_details
        # Update subscribers heartbeat date to stop them being cleansed
        self._subscribers[message["subscriber_id"]] = {
            "last_heartbeat": datetime.now(timezone.utc),
        }

    def unsubscribe(self, message: SubscribeMessage) -> int:
        message_str = "Unsubscribed to"
        if message["subscriber_id"] not in self._subscribers:
            logging.warning(
                f"Unable to unsubscribe. Not subscribed: {message['subscriber_id']}"
            )
            return len(self._expiry_subscriber_map)

        mapping = self._expiry_subscriber_map.get(message["expiry"])
        if not mapping:
            now = utils_general.to_iso(datetime.now(tz=timezone.utc))
            exp = utils_general.convert_expiry_to_days(message["expiry"], now)
            if exp > 0:
                raise RuntimeError("Unable to find expiry in subscriber map.")
            return len(self._expiry_subscriber_map)

        if message["subscriber_id"] not in mapping["subscribers"]:
            logging.warning(
                f"Unable to unsubscribe: {message['subscriber_id']}  not subscribed to {message['expiry']}."
            )
            return len(self._expiry_subscriber_map)
        try:
            raw_msg = self._get_raw_msg_vals(message)
            subscribers_details: SingleSubscribeInfo = (
                self._expiry_subscriber_map[message["expiry"]]["subscribers"][
                    message["subscriber_id"]
                ]
            )

            for k, v in raw_msg.items():
                k_casted = cast(MessageValueKeys, k)
                if (
                    k_casted in subscribers_details
                    and subscribers_details[k_casted]
                    and isinstance(v, set)
                ):
                    k_casted = cast(TYPES, k)
                    values_to_remove = subscribers_details[k_casted] & v
                    if values_to_remove:
                        subscribers_details[k_casted] = (
                            subscribers_details[k_casted] - v
                        )
                        _add_msg_to_consolidate(
                            message_str=message_str, msg=message
                        )
                    if len(subscribers_details[k_casted]) == 0:
                        del subscribers_details[k_casted]

                elif isinstance(v, bool):
                    del subscribers_details[k_casted]
                    _add_msg_to_consolidate(
                        message_str=message_str, msg=message
                    )

            self._update_expiries_totals(message=message, type="remove")

            if len(subscribers_details) == 0:
                del self._expiry_subscriber_map[message["expiry"]][
                    "subscribers"
                ][message["subscriber_id"]]
                if (
                    len(
                        self._expiry_subscriber_map[message["expiry"]][
                            "subscribers"
                        ]
                    )
                    == 0
                ):
                    del self._expiry_subscriber_map[message["expiry"]]
            else:
                self._expiry_subscriber_map[message["expiry"]]["subscribers"][
                    message["subscriber_id"]
                ] = subscribers_details

            # As strikes are purely arbitrary subscriptions only calc if needed
            if message["type"] == "strike" and "strike" in self._calc_types:
                found_strike = False
                for data in self._expiry_subscriber_map.values():
                    if len(data["totals"].get("strikes", set())) > 0:
                        found_strike = True
                        break
                if not found_strike:
                    self._calc_types.remove("strike")
        except Exception as err:
            raise RuntimeError(
                f"Unable to unsubscribe {message['expiry']=} from cache"
            ) from err

        return len(self._expiry_subscriber_map)

    def remove_expiry(self, expiry: str) -> int:
        """
        Returns the length of remaining expiries
        """
        if expiry not in self._expiry_subscriber_map:
            raise RuntimeError("Expiry not found in subscriber map")
        del self._expiry_subscriber_map[expiry]
        utils_general.log_bsdebug(f"Removed {expiry=}")
        return len(self._expiry_subscriber_map)

    def get_expiries(self) -> list[str]:
        return list(self._expiry_subscriber_map.keys())

    def _convert_totals_to_readable(self, totals: Totals) -> ExpiryArbFields:
        """
        Converts mapping of values & set of sub_ids to list of values.
        e.g. {"strikes": { 30000: {sub_1, sub_2}, 32500: {sub_2}}} -> { "strikes": [30000,32500]}
        """
        converted_totals = ExpiryArbFields()
        for k, v in totals.items():
            keys = cast(IVTypeToSub, v).keys()
            converted_totals.update(k, list(keys) if keys else [])
        return converted_totals

    def get_expiries_in_days(
        self, timestamp: int | float
    ) -> TenorExpiryArbFields:
        expiry_days_n_totals = TenorExpiryArbFields()

        # sister code for the below block in prep_data.py where the below expiry_days_n_totals are merged into a larger
        # TenorExpiryArbField object depending on the subscription messages params.
        for s, obj in list(self._expiry_subscriber_map.items()):
            try:
                totals = self._convert_totals_to_readable(obj["totals"])
                if utils_general.is_constant_maturity(s):
                    totals.update(
                        {
                            TenorType.ARBITRARY_CONSTANT_MATURITY.value: True,
                            "tenor_str": s,
                        }
                    )
                    exp_days = round_value(
                        utils_general.convert_maturity_string_to_day_or_minutes(
                            s, "days"
                        ),
                        TENOR_PRECISION,
                    )

                    expiry_days_n_totals.add_expiry_arb_fields(exp_days, totals)
                else:
                    totals.update(
                        {
                            TenorType.ARBITRARY_EXPIRY.value: True,
                            "iso_expiry": s,
                        }
                    )
                    exp_days = utils_general.convert_expiry_to_days(
                        s, timestamp
                    )

                    if exp_days <= 0:
                        self.remove_expiry(s)
                    else:
                        exp_days = round_value(exp_days, TENOR_PRECISION)
                        expiry_days_n_totals.add_expiry_arb_fields(
                            exp_days, totals
                        )

            except ValueError as e:
                logging.exception(
                    f"Failed to convert expiry {s}, removing from list. err={e}"
                )
                self.remove_expiry(s)
        return expiry_days_n_totals

    def get_expiries_totals(self) -> dict[str, ExpiryArbFields]:
        expiries_totals: dict[str, ExpiryArbFields] = {}
        for s, obj in list(self._expiry_subscriber_map.items()):
            totals = self._convert_totals_to_readable(obj["totals"])
            if utils_general.is_constant_maturity(s):
                totals.update(
                    {
                        TenorType.ARBITRARY_CONSTANT_MATURITY.value: True,
                        "tenor_str": s,
                    }
                )
            else:
                totals.update(
                    {TenorType.ARBITRARY_EXPIRY.value: True, "iso_expiry": s}
                )
            expiries_totals[s] = totals
        return expiries_totals

    def execute(self) -> Any:
        return super().execute()  # type: ignore

    def get_total_executions(self) -> int:
        return len(self.get_expiries())

    def get_calc_types(self) -> set[StreamCalcTypes]:
        return self._calc_types


@collate_logs()
def _add_msg_to_consolidate(message_str: str, msg: SubscribeMessage) -> None:
    subscription_data_attributes: dict[str, Any] = {
        "exchange": msg.get("exchange", "blockscholes"),
        "function_id": msg.get("function_id"),
        "model": msg.get("model"),
        "base_asset": msg.get("base_asset"),
        "expiry": msg.get("expiry"),
        "type": msg.get("type"),
        "values": msg.get("values", None),
    }

    agg_logger = get_agg_logger()
    key_data = utils_general.json_loads(subscription_data_attributes)
    agg_logger.info(
        SubscriptionMessageLogs(
            key=message_str,
            command=msg["command"],
            calculator=msg["calculator"],
            subscriber_id=msg["subscriber_id"],
            timestamp=int(datetime.now(UTC).timestamp() * 1e9),
            instruments_str=key_data,
            message=None,
            extra={},
        ),
        structure=["key", "command", "calculator", "subscriber_id"],
        value=key_data,
    )
