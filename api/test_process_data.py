import json
from datetime import UTC, datetime
from typing import Any, cast
from unittest.mock import patch

import pandas as pd
import pytest
from lambda_types import ParallelCalcPreppedSlice, ParallelCalcPreppedSliceOrig
from process_data import _smooth, process_chunk, process_data

from datagrabber import CatalogAssetType


@pytest.mark.parametrize("smoothing", [True, False])
@pytest.mark.parametrize("asset_class", ["spot", "perpetual", "future"])
def test_process_data_returns_mid_smoothing(
    snapshot: Any,
    asset_class: CatalogAssetType,
    smoothing: bool,
) -> None:
    now = datetime.now(tz=UTC).replace(second=0, microsecond=0)
    timestamp = int(now.timestamp() * 1e9)
    qn = f"deribit.{asset_class}.BTC-USD.1m.mid.px"
    data: ParallelCalcPreppedSlice = {
        "qualified_name": qn,
        "instrument": "BTC-USD",
        "bid": 2.0,
        "ask": 6.0,
        "quotes_ffilled": False,
        "smoothed": False,
        "stubbed": False,
        "from_lookback": False,
        "timestamp": timestamp,
    }
    result = process_chunk(
        chunk=[data],
        periods=1,
        interval="minute",
        freq="1m",
        smoothing_enabled=smoothing,
    )

    assert all(item["timestamp"] == timestamp for item in result)

    # Delete timestamp for snapshot since this will change every time the
    # test is run.
    for item in result:
        del item["timestamp"]

    snapshot.assert_match(json.dumps(result, indent=2), "result.json")


def create_data(freq: str, instrument: str) -> ParallelCalcPreppedSlice:
    return {
        "qualified_name": f"deribit.option.{instrument}.{freq}.mid.px",
        "instrument": instrument,
        "ask": 0.038,
        "quotes_ffilled": False,
        "smoothed": False,
        "from_lookback": False,
        "timestamp": 1693317600000000000,
        "stubbed": False,
        "bid": 0.036,
        "mid": 0,
        "index": 26036.17,
    }


@pytest.mark.parametrize("smoothing", [True, False])
@pytest.mark.parametrize(
    "freq, data",
    [
        (freq, create_data(freq, instrument))
        for freq in ["1m", "live"]
        for instrument in ["BTC-15SEP23-26500-P", "BTC-15SEP23-26500d5-P"]
    ],
)
def test_process_data_with_options_and_one_sample_returns_mid(
    snapshot: Any, freq: str, smoothing: bool, data: ParallelCalcPreppedSlice
) -> None:
    result = process_chunk(
        chunk=[data],
        periods=1,
        interval="minute" if freq == "1m" else "second",
        freq=freq,
        smoothing_enabled=smoothing,
    )

    snapshot.assert_match(json.dumps(result, indent=2), "result.json")


def _convert_prepped_slice(
    data: ParallelCalcPreppedSliceOrig,
) -> list[ParallelCalcPreppedSlice]:
    return [
        {
            "qualified_name": data["qualified_name"],
            "instrument": data["instrument"],
            **item,
        }
        for item in data["snaps_prices"].values()
    ]


def test_process_data_with_options_returns_smoothed_data() -> None:
    # fmt:off
    data: ParallelCalcPreppedSliceOrig = {
        "qualified_name": "deribit.option.BTC-15SEP23-30000-C.1h.mid.px",
        "instrument": "BTC-15SEP23-30000-C",
        "snaps_prices": {
            1693144800000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693144800000000000, "stubbed": False, "mid": 0.03475, "bid": 0.0335, "index": 26068.69},
            1693148400000000000: {"ask": 0.0345, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693148400000000000, "stubbed": False, "mid": 0.03325, "bid": 0.032, "index": 26140.75},
            1693152000000000000: {"ask": 0.035, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693152000000000000, "stubbed": False, "mid": 0.03375, "bid": 0.0325, "index": 26112.57},
            1693155600000000000: {"ask": 0.034, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693155600000000000, "stubbed": False, "mid": 0.03275, "bid": 0.0315, "index": 26155.3},
            1693159200000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693159200000000000, "stubbed": False, "mid": 0.03525, "bid": 0.034, "index": 26057.28},
            1693162800000000000: {"ask": 0.035, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693162800000000000, "stubbed": False, "mid": 0.03375, "bid": 0.0325, "index": 26096.07},
            1693166400000000000: {"ask": 0.0345, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693166400000000000, "stubbed": False, "mid": 0.03325, "bid": 0.032, "index": 26115.44},
            1693170000000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693170000000000000, "stubbed": False, "mid": 0.03425, "bid": 0.033, "index": 26083.92},
            1693173600000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693173600000000000, "stubbed": False, "mid": 0.035, "bid": 0.034, "index": 26060.08},
            1693177200000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693177200000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26079.75},
            1693180800000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693180800000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26089.92},
            1693184400000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693184400000000000, "stubbed": False, "mid": 0.03575, "bid": 0.035, "index": 26013.66},
            1693188000000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693188000000000000, "stubbed": False, "mid": 0.03575, "bid": 0.035, "index": 26016.1},
            1693191600000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693191600000000000, "stubbed": False, "mid": 0.03575, "bid": 0.035, "index": 26017.96},
            1693195200000000000: {"ask": 0.037, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693195200000000000, "stubbed": False, "mid": 0.03625, "bid": 0.0355, "index": 25999.56},
            1693198800000000000: {"ask": 0.037, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693198800000000000, "stubbed": False, "mid": 0.036, "bid": 0.035, "index": 26012.69},
            1693202400000000000: {"ask": 0.0385, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693202400000000000, "stubbed": False, "mid": 0.0375, "bid": 0.0365, "index": 25950.82},
            1693206000000000000: {"ask": 0.04, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693206000000000000, "stubbed": False, "mid": 0.03925, "bid": 0.0385, "index": 25933.14},
            1693209600000000000: {"ask": 0.041, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693209600000000000, "stubbed": False, "mid": 0.0405, "bid": 0.04, "index": 25918.98},
            1693213200000000000: {"ask": 0.04, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693213200000000000, "stubbed": False, "mid": 0.039, "bid": 0.038, "index": 25953.26},
            1693216800000000000: {"ask": 0.0405, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693216800000000000, "stubbed": False, "mid": 0.04, "bid": 0.0395, "index": 25910.93},
            1693220400000000000: {"ask": 0.0405, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693220400000000000, "stubbed": False, "mid": 0.0395, "bid": 0.0385, "index": 25935.23},
            1693224000000000000: {"ask": 0.039, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693224000000000000, "stubbed": False, "mid": 0.03825, "bid": 0.0375, "index": 25982.72},
            1693227600000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693227600000000000, "stubbed": False, "mid": 0.03475, "bid": 0.034, "index": 26121.83},
            1693231200000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693231200000000000, "stubbed": False, "mid": 0.03525, "bid": 0.0345, "index": 26090.69},
            1693234800000000000: {"ask": 0.035, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693234800000000000, "stubbed": False, "mid": 0.0345, "bid": 0.034, "index": 26123.24},
            1693238400000000000: {"ask": 0.037, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693238400000000000, "stubbed": False, "mid": 0.03625, "bid": 0.0355, "index": 26085.9},
            1693242000000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693242000000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26135.22},
            1693245600000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693245600000000000, "stubbed": False, "mid": 0.03475, "bid": 0.034, "index": 26133.18},
            1693249200000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693249200000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26134.56},
            1693252800000000000: {"ask": 0.0395, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693252800000000000, "stubbed": False, "mid": 0.03875, "bid": 0.038, "index": 25981.29},
            1693256400000000000: {"ask": 0.039, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693256400000000000, "stubbed": False, "mid": 0.03825, "bid": 0.0375, "index": 25980.12},
            1693260000000000000: {"ask": 0.0395, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693260000000000000, "stubbed": False, "mid": 0.0385, "bid": 0.0375, "index": 25970.81},
            1693263600000000000: {"ask": 0.0375, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693263600000000000, "stubbed": False, "mid": 0.0365, "bid": 0.0355, "index": 26046.06},
            1693267200000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693267200000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26105.42},
            1693270800000000000: {"ask": 0.0345, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693270800000000000, "stubbed": False, "mid": 0.03375, "bid": 0.033, "index": 26126.17},
            1693274400000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693274400000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26091.53},
            1693278000000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693278000000000000, "stubbed": False, "mid": 0.03475, "bid": 0.034, "index": 26083.09},
            1693281600000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693281600000000000, "stubbed": False, "mid": 0.03525, "bid": 0.0345, "index": 26065.31},
            1693285200000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693285200000000000, "stubbed": False, "mid": 0.0355, "bid": 0.0345, "index": 26043.67},
            1693288800000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693288800000000000, "stubbed": False, "mid": 0.035, "bid": 0.034, "index": 26044.25},
            1693292400000000000: {"ask": 0.038, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693292400000000000, "stubbed": False, "mid": 0.037, "bid": 0.036, "index": 25999.34},
            1693296000000000000: {"ask": 0.039, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693296000000000000, "stubbed": False, "mid": 0.038, "bid": 0.037, "index": 25993.05},
            1693299600000000000: {"ask": 0.04, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693299600000000000, "stubbed": False, "mid": 0.03925, "bid": 0.0385, "index": 25947.96},
            1693303200000000000: {"ask": 0.039, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693303200000000000, "stubbed": False, "mid": 0.03825, "bid": 0.0375, "index": 25974.72},
            1693306800000000000: {"ask": 0.0395, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693306800000000000, "stubbed": False, "mid": 0.03875, "bid": 0.038, "index": 25963.78},
            1693310400000000000: {"ask": 0.04, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693310400000000000, "stubbed": False, "mid": 0.039, "bid": 0.038, "index": 25961.8},
            1693314000000000000: {"ask": 0.038, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693314000000000000, "stubbed": False, "mid": 0.03725, "bid": 0.0365, "index": 26030.88},
            # end of lookback window
            1693317600000000000: {"ask": 1.0, "quotes_ffilled": False, "smoothed": False, "from_lookback": False, "timestamp": 1693317600000000000, "stubbed": False, "bid": 1.0, "index": 26036.17},
            1693321200000000000: {"ask": 10.0, "quotes_ffilled": False, "smoothed": False, "from_lookback": False, "timestamp": 1693321200000000000, "stubbed": False, "bid": 10.0, "index": 27379.89},
        }
    }
    # fmt:on

    chunk = _convert_prepped_slice(data)

    result = process_chunk(
        chunk=chunk,
        periods=1,
        interval="hour",
        freq="1h",
        smoothing_enabled=True,
    )

    smoothed_results = cast(
        list[dict[str, Any]], [item for item in result if item["smoothed"]]
    )
    assert len(smoothed_results) == 2
    assert all(pd.notna(item["mid"]) for item in smoothed_results)
    assert all(item["mid"] < item["raw_mid"] for item in smoothed_results)


def test_process_data_with_options_respects_smoothing_limit() -> None:
    # fmt:off
    data: ParallelCalcPreppedSliceOrig = {
        "qualified_name": "deribit.option.BTC-15SEP23-30000-C.1h.mid.px",
        "instrument": "BTC-15SEP23-30000-C",
        "snaps_prices": {
            1693144800000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693144800000000000, "stubbed": False, "mid": 0.03475, "bid": 0.0335, "index": 26068.69},
            1693148400000000000: {"ask": 0.0345, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693148400000000000, "stubbed": False, "mid": 0.03325, "bid": 0.032, "index": 26140.75},
            1693152000000000000: {"ask": 0.035, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693152000000000000, "stubbed": False, "mid": 0.03375, "bid": 0.0325, "index": 26112.57},
            1693155600000000000: {"ask": 0.034, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693155600000000000, "stubbed": False, "mid": 0.03275, "bid": 0.0315, "index": 26155.3},
            1693159200000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693159200000000000, "stubbed": False, "mid": 0.03525, "bid": 0.034, "index": 26057.28},
            1693162800000000000: {"ask": 0.035, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693162800000000000, "stubbed": False, "mid": 0.03375, "bid": 0.0325, "index": 26096.07},
            1693166400000000000: {"ask": 0.0345, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693166400000000000, "stubbed": False, "mid": 0.03325, "bid": 0.032, "index": 26115.44},
            1693170000000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693170000000000000, "stubbed": False, "mid": 0.03425, "bid": 0.033, "index": 26083.92},
            1693173600000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693173600000000000, "stubbed": False, "mid": 0.035, "bid": 0.034, "index": 26060.08},
            1693177200000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693177200000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26079.75},
            1693180800000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693180800000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26089.92},
            1693184400000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693184400000000000, "stubbed": False, "mid": 0.03575, "bid": 0.035, "index": 26013.66},
            1693188000000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693188000000000000, "stubbed": False, "mid": 0.03575, "bid": 0.035, "index": 26016.1},
            1693191600000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693191600000000000, "stubbed": False, "mid": 0.03575, "bid": 0.035, "index": 26017.96},
            1693195200000000000: {"ask": 0.037, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693195200000000000, "stubbed": False, "mid": 0.03625, "bid": 0.0355, "index": 25999.56},
            1693198800000000000: {"ask": 0.037, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693198800000000000, "stubbed": False, "mid": 0.036, "bid": 0.035, "index": 26012.69},
            1693202400000000000: {"ask": 0.0385, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693202400000000000, "stubbed": False, "mid": 0.0375, "bid": 0.0365, "index": 25950.82},
            1693206000000000000: {"ask": 0.04, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693206000000000000, "stubbed": False, "mid": 0.03925, "bid": 0.0385, "index": 25933.14},
            1693209600000000000: {"ask": 0.041, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693209600000000000, "stubbed": False, "mid": 0.0405, "bid": 0.04, "index": 25918.98},
            1693213200000000000: {"ask": 0.04, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693213200000000000, "stubbed": False, "mid": 0.039, "bid": 0.038, "index": 25953.26},
            1693216800000000000: {"ask": 0.0405, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693216800000000000, "stubbed": False, "mid": 0.04, "bid": 0.0395, "index": 25910.93},
            1693220400000000000: {"ask": 0.0405, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693220400000000000, "stubbed": False, "mid": 0.0395, "bid": 0.0385, "index": 25935.23},
            1693224000000000000: {"ask": 0.039, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693224000000000000, "stubbed": False, "mid": 0.03825, "bid": 0.0375, "index": 25982.72},
            1693227600000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693227600000000000, "stubbed": False, "mid": 0.03475, "bid": 0.034, "index": 26121.83},
            1693231200000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693231200000000000, "stubbed": False, "mid": 0.03525, "bid": 0.0345, "index": 26090.69},
            1693234800000000000: {"ask": 0.035, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693234800000000000, "stubbed": False, "mid": 0.0345, "bid": 0.034, "index": 26123.24},
            1693238400000000000: {"ask": 0.037, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693238400000000000, "stubbed": False, "mid": 0.03625, "bid": 0.0355, "index": 26085.9},
            1693242000000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693242000000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26135.22},
            1693245600000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693245600000000000, "stubbed": False, "mid": 0.03475, "bid": 0.034, "index": 26133.18},
            1693249200000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693249200000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26134.56},
            1693252800000000000: {"ask": 0.0395, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693252800000000000, "stubbed": False, "mid": 0.03875, "bid": 0.038, "index": 25981.29},
            1693256400000000000: {"ask": 0.039, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693256400000000000, "stubbed": False, "mid": 0.03825, "bid": 0.0375, "index": 25980.12},
            1693260000000000000: {"ask": 0.0395, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693260000000000000, "stubbed": False, "mid": 0.0385, "bid": 0.0375, "index": 25970.81},
            1693263600000000000: {"ask": 0.0375, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693263600000000000, "stubbed": False, "mid": 0.0365, "bid": 0.0355, "index": 26046.06},
            1693267200000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693267200000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26105.42},
            1693270800000000000: {"ask": 0.0345, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693270800000000000, "stubbed": False, "mid": 0.03375, "bid": 0.033, "index": 26126.17},
            1693274400000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693274400000000000, "stubbed": False, "mid": 0.0345, "bid": 0.0335, "index": 26091.53},
            1693278000000000000: {"ask": 0.0355, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693278000000000000, "stubbed": False, "mid": 0.03475, "bid": 0.034, "index": 26083.09},
            1693281600000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693281600000000000, "stubbed": False, "mid": 0.03525, "bid": 0.0345, "index": 26065.31},
            1693285200000000000: {"ask": 0.0365, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693285200000000000, "stubbed": False, "mid": 0.0355, "bid": 0.0345, "index": 26043.67},
            1693288800000000000: {"ask": 0.036, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693288800000000000, "stubbed": False, "mid": 0.035, "bid": 0.034, "index": 26044.25},
            1693292400000000000: {"ask": 0.038, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693292400000000000, "stubbed": False, "mid": 0.037, "bid": 0.036, "index": 25999.34},
            1693296000000000000: {"ask": 0.039, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693296000000000000, "stubbed": False, "mid": 0.038, "bid": 0.037, "index": 25993.05},
            1693299600000000000: {"ask": 0.04, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693299600000000000, "stubbed": False, "mid": 0.03925, "bid": 0.0385, "index": 25947.96},
            1693303200000000000: {"ask": 0.039, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693303200000000000, "stubbed": False, "mid": 0.03825, "bid": 0.0375, "index": 25974.72},
            1693306800000000000: {"ask": 0.0395, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693306800000000000, "stubbed": False, "mid": 0.03875, "bid": 0.038, "index": 25963.78},
            1693310400000000000: {"ask": 0.04, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693310400000000000, "stubbed": False, "mid": 0.039, "bid": 0.038, "index": 25961.8},
            1693314000000000000: {"ask": 0.038, "quotes_ffilled": False, "smoothed": False, "from_lookback": True, "timestamp": 1693314000000000000, "stubbed": False, "mid": 0.03725, "bid": 0.0365, "index": 26030.88},
            # end of lookback window
            1693317600000000000: {"ask": 1e100, "quotes_ffilled": False, "smoothed": False, "from_lookback": False, "timestamp": 1693317600000000000, "stubbed": False, "bid": 1e100, "index": 26036.17},
            1693321200000000000: {"ask": 10.0, "quotes_ffilled": False, "smoothed": False, "from_lookback": False, "timestamp": 1693321200000000000, "stubbed": False, "bid": 10.0, "index": 27379.89},
            1693324800000000000: {"ask": 1.0, "quotes_ffilled": False, "smoothed": False, "from_lookback": False, "timestamp": 1693324800000000000, "stubbed": False, "bid": 1.0, "index": 27379.89},
        }
    }
    # fmt:on

    chunk = _convert_prepped_slice(data)

    result = process_chunk(
        chunk=chunk,
        periods=1,
        interval="hour",
        freq="1h",
        smoothing_enabled=True,
    )

    assert cast(float, result[0]["mid"]) > 3.33e45


def test_process_data_with_options_ffilled_data_is_not_smoothed() -> None:
    # fmt:off
    data: ParallelCalcPreppedSliceOrig = {
        "qualified_name": "deribit.option.BTC-15SEP23-30000-C.1h.mid.px",
        "instrument": "BTC-15SEP23-30000-C",
        "snaps_prices": {
            1693144800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693144800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26068.69},
            1693148400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693148400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26140.75},
            1693152000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693152000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26112.57},
            1693155600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693155600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26155.3},
            1693159200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693159200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26057.28},
            1693162800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693162800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26096.07},
            1693166400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693166400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26115.44},
            1693170000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693170000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26083.92},
            1693173600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693173600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26060.08},
            1693177200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693177200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26079.75},
            1693180800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693180800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26089.92},
            1693184400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693184400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26013.66},
            1693188000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693188000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26016.1},
            1693191600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693191600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26017.96},
            1693195200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693195200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25999.56},
            1693198800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693198800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26012.69},
            1693202400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693202400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25950.82},
            1693206000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693206000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25933.14},
            1693209600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693209600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25918.98},
            1693213200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693213200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25953.26},
            1693216800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693216800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25910.93},
            1693220400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693220400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25935.23},
            1693224000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693224000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25982.72},
            1693227600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693227600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26121.83},
            1693231200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693231200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26090.69},
            1693234800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693234800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26123.24},
            1693238400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693238400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26085.9},
            1693242000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693242000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26135.22},
            1693245600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693245600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26133.18},
            1693249200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693249200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26134.56},
            1693252800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693252800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25981.29},
            1693256400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693256400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25980.12},
            1693260000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693260000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25970.81},
            1693263600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693263600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26046.06},
            1693267200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693267200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26105.42},
            1693270800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693270800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26126.17},
            1693274400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693274400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26091.53},
            1693278000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693278000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26083.09},
            1693281600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693281600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26065.31},
            1693285200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693285200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26043.67},
            1693288800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693288800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26044.25},
            1693292400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693292400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25999.34},
            1693296000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693296000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25993.05},
            1693299600000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693299600000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25947.96},
            1693303200000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693303200000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25974.72},
            1693306800000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693306800000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25963.78},
            1693310400000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693310400000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 25961.8},
            1693314000000000000: {"ask": 0.01, "quotes_ffilled": True, "smoothed": False, "from_lookback": True, "timestamp": 1693314000000000000, "stubbed": False, "mid": 0.01, "bid": 0.01, "index": 26030.88},
            # end of lookback window
            1693317600000000000: {"ask": 1.0, "quotes_ffilled": False, "smoothed": False, "from_lookback": False, "timestamp": 1693317600000000000, "stubbed": False, "mid": 0, "bid": 1.0, "index": 26036.17},
            1693321200000000000: {"ask": 10.0, "quotes_ffilled": False, "smoothed": False, "from_lookback": False, "timestamp": 1693321200000000000, "stubbed": False, "mid": 0, "bid": 10.0, "index": 27379.89},
        }
    }
    # fmt:on

    chunk = _convert_prepped_slice(data)

    result = process_chunk(
        chunk=chunk,
        periods=1,
        interval="hour",
        freq="1h",
        smoothing_enabled=True,
    )

    assert not any(item["smoothed"] for item in result)
    assert [item["mid"] for item in result] == [
        item["raw_mid"] for item in result
    ]


def test_process_data_with_options_lookback_window_is_not_returned() -> None:
    data: ParallelCalcPreppedSliceOrig = {
        # fmt: off
        "qualified_name": "deribit.option.BTC-26JAN24-38000-C.live.mid.px",
        "instrument": "BTC-26JAN24-38000-C",
        "snaps_prices": {
            1700514240000000000: {
                "mid": 0.1015,
                "quotes_ffilled": False,
                "smoothed": False,
                "from_lookback": True,
                "timestamp": 1700514240000000000,
                "stubbed": False,
                "bid": 0.101,
                "ask": 0.102,
                "index": 37485.26,
            },
            1700514300000000000: {
                "mid": 0.1015,
                "quotes_ffilled": True,
                "smoothed": False,
                "from_lookback": True,
                "timestamp": 1700514300000000000,
                "stubbed": False,
                "bid": 0.101,
                "ask": 0.102,
                "index": 37472.15,
            },
            1700514360000000000: {
                "mid": 0.10125,
                "quotes_ffilled": False,
                "smoothed": False,
                "from_lookback": True,
                "timestamp": 1700514360000000000,
                "stubbed": False,
                "bid": 0.101,
                "ask": 0.102,
                "index": 37490.27,
            },
            1700514420000000000: {
                "bid": 0.101,
                "quotes_ffilled": False,
                "smoothed": False,
                "from_lookback": False,
                "timestamp": 1700514420000000000,
                "stubbed": False,
                "ask": 0.102,
                "index": 37490.87,
            },
        },
        # fmt: on
    }

    chunk = _convert_prepped_slice(data)

    result = process_chunk(
        chunk=chunk,
        periods=1,
        interval="minute",
        freq="1m",
        smoothing_enabled=True,
    )

    assert all(item["from_lookback"] == False for item in result)


@pytest.mark.parametrize("smoothing", [True, False])
def test_process_data_with_options_replaces_timestamp(smoothing: bool) -> None:
    data: ParallelCalcPreppedSliceOrig = {
        # fmt: off
        "qualified_name": "deribit.option.BTC-26JAN24-38000-C.live.mid.px",
        "instrument": "BTC-26JAN24-38000-C",
        "snaps_prices": {
            1700514240000000000: {
                "mid": 0.1015,
                "quotes_ffilled": False,
                "smoothed": False,
                "from_lookback": True,
                "timestamp": 1700514240000000000,
                "stubbed": False,
                "bid": 0.101,
                "ask": 0.102,
                "index": 37485.26,
            },
            1700514300000000000: {
                "mid": 0.1015,
                "quotes_ffilled": True,
                "smoothed": False,
                "from_lookback": True,
                "timestamp": 1700514300000000000,
                "stubbed": False,
                "bid": 0.101,
                "ask": 0.102,
                "index": 37472.15,
            },
            1700514360000000000: {
                "mid": 0.10125,
                "quotes_ffilled": False,
                "smoothed": False,
                "from_lookback": True,
                "timestamp": 1700514360000000000,
                "stubbed": False,
                "bid": 0.101,
                "ask": 0.102,
                "index": 37490.27,
            },
            1700514420000000000: {
                "bid": 0.101,
                "quotes_ffilled": False,
                "smoothed": False,
                "from_lookback": False,
                "timestamp": 1700514420000000000,
                "stubbed": False,
                "ask": 0.102,
                "index": 37490.87,
            },
        },
        # fmt: on
    }

    chunk = _convert_prepped_slice(data)

    result = process_chunk(
        chunk=chunk,
        periods=1,
        interval="minute",
        freq="live",
        snapshot_ts=1234,
        smoothing_enabled=smoothing,
    )

    assert all(item["timestamp"] == 1234 for item in result)


def test_smooth_function_intermittent_valueError() -> None:
    # fmt: off
    data = [{'bid': 0.0039, 'bid_ffilled': False, 'quotes_ffilled': False, 'smoothed': False, 'from_lookback': True,'timestamp': 1697450400000000000, 'stubbed': False, 'ask': 0.0045, 'ask_ffilled': False, 'mid': 0.0042,'mid_ffilled': False, 'index': 27779.33, 'strike': 28250, 'isodate': '2023-10-16T10:00:00', 'itm': False,'window_itm': False, 'raw_mid': 0.0042, 'window_ffill_ratio': 0.0},
     {'bid': 0.0039, 'bid_ffilled': False, 'quotes_ffilled': False, 'smoothed': False, 'from_lookback': True,'timestamp': 1697454000000000000, 'stubbed': False, 'ask': 0.0043, 'ask_ffilled': False,'mid': 0.0040999999999999, 'mid_ffilled': False, 'index': 27778.24, 'strike': 28250,'isodate': '2023-10-16T11:00:00', 'itm': False, 'window_itm': False, 'raw_mid': 0.0040999999999999995,'window_ffill_ratio': 0.0},
     {'bid': 0.0038, 'bid_ffilled': False, 'quotes_ffilled': False, 'smoothed': False, 'from_lookback': True,'timestamp': 1697457600000000000, 'stubbed': False, 'ask': 0.0044, 'ask_ffilled': False, 'mid': 0.0041,'mid_ffilled': False, 'index': 27757.5, 'strike': 28250, 'isodate': '2023-10-16T12:00:00', 'itm': False,'window_itm': False, 'raw_mid': 0.0041, 'window_ffill_ratio': 0.0},
     {'bid': 0.0046, 'bid_ffilled': False, 'quotes_ffilled': False, 'smoothed': False, 'from_lookback': True,'timestamp': 1697461200000000000, 'stubbed': False, 'ask': 0.0055, 'ask_ffilled': False,'mid': 0.0043899416883304, 'mid_ffilled': False, 'index': 27897.69, 'strike': 28250,'isodate': '2023-10-16T13:00:00', 'itm': False, 'window_itm': False, 'raw_mid': 0.00505,'window_ffill_ratio': 0.0},
     {'bid': 0.011, 'bid_ffilled': False, 'quotes_ffilled': False, 'smoothed': False, 'from_lookback': False,'timestamp': 1697464800000000000, 'stubbed': False, 'ask': 0.0115, 'ask_ffilled': False, 'mid': 0.01125,'mid_ffilled': False, 'index': 28006.1, 'strike': 28250, 'isodate': '2023-10-16T14:00:00', 'itm': False,'window_itm': False, 'raw_mid': 0.01125, 'window_ffill_ratio': 0.0}]
    # fmt: on
    qn = "v-00002.deribit.option.BTC-18OCT23-28250-C.1h.mid.px"
    field = "mid"
    freq = "1h"

    with patch("logging.exception") as mock_logging:
        _smooth(
            qn=qn, df=pd.DataFrame.from_records(data), field=field, freq=freq
        )
        mock_logging.assert_not_called()


def test_process_data_multiprocessing(snapshot: Any) -> None:
    """
    Tests the top-level process_data function.

    This function is used by the scheduled app to batch-process data, using
    multiprocessing to chunk the input. As a result it is slower to run over
    small input sizes due to the multiprocessing overhead, hence the above
    tests use the process_chunk function instead. However it's useful to have
    one test to validate the basic behaviour for process_data.
    """
    asset_class = "spot"

    now = datetime.now(tz=UTC).replace(second=0, microsecond=0)
    timestamp = int(now.timestamp() * 1e9)
    qn = f"deribit.{asset_class}.BTC-USD.1m.mid.px"
    data: ParallelCalcPreppedSlice = {
        "qualified_name": qn,
        "instrument": "BTC-USD",
        "bid": 2.0,
        "ask": 6.0,
        "quotes_ffilled": False,
        "smoothed": False,
        "stubbed": False,
        "from_lookback": False,
        "timestamp": timestamp,
    }
    result = process_data(
        periods=1,
        interval="minute",
        freq="1m",
        chunk=[data],
        smoothing_enabled=True,
    )

    assert all(item["timestamp"] == timestamp for item in result)

    # Delete timestamp for snapshot since this will change every time the
    # test is run.
    for item in result:
        del item["timestamp"]

    snapshot.assert_match(json.dumps(result, indent=2), "result.json")
