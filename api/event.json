{"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "debug": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["deribit"], "currency_pairs": [{"base": "BTC", "quote": "BTC"}]}], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "2024-07-09T00:00:00Z", "end": "2024-07-09T00:00:00Z"}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "blockscholes-test-prod", "s3_object_suffix": "", "s3_object_prefix": "thomas_gaps"}}}}}