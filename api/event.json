{"body": {"calc": {"version": "1.0.0", "type": "volSmileCalc", "args": {"consistent_read": false, "exchanges": ["deribit"], "currencies": ["BTC"], "models": ["SVI"], "types": ["params", "smile", "moneyness", "strike"], "include_listed_expiries": true, "estimate_params": true, "smooth": true, "debug": false, "frequency": {"interval": "hour", "periods": 1}, "date_range": {"absolute": {"start": "2024-08-11T21:00:00.000Z", "end": "2024-08-11T21:01:00.000Z"}}}, "output_options": {"version": "v-00004", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "blockscholes-test-prod", "s3_object_suffix": "", "s3_object_prefix": "30-day-backfill-test-hourly-720-snaps"}}}}}