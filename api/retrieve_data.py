import logging
from collections import defaultdict
from typing import cast

import utils_general
from calc_helpers import SMOOTH_PARAMS, get_tenors
from constants import (
    READ_OPERATIONS_LIMIT,
    SMOOTHING_CALC_TYPES,
    TARGET_SNAPSHOTS,
)
from datagrabber import GrabParams, get_instruments, grab
from lambda_types import (
    AllQueryResults,
    CalcType,
    OptionContractDetails,
    ParamsQueryResult,
    RawSuperExchangeMap,
    SnapshotDates,
    TenorMode,
    TenorType,
)
from utils.common import modify_string
from utils.lookback import generate_lookback_timestamps
from utils_calc import Model
from utils_general import from_iso


def make_query(
    qualified_name_matches: list[str],
    snapshot_dates: SnapshotDates,
    fields: list[str],
    debug: bool = False,
    consistent_read: bool = False,
) -> GrabParams:
    if "range" in snapshot_dates:
        date_filter = {
            "BETWEEN": {
                "field": "timestamp",
                "start": int(snapshot_dates["range"]["start"]),
                "end": int(snapshot_dates["range"]["end"]),
            }
        }

    elif "timestamps" in snapshot_dates:
        date_filter = {
            "IN": {
                "field": "timestamp",
                "value": snapshot_dates["timestamps"].copy(),
            }
        }

    else:
        raise ValueError(
            "snapshot_dates must contain either range or timestamps"
        )

    return cast(
        GrabParams,
        {
            "aws_stage": "",
            "entity_type": "timeseries_debug" if debug else "timeseries",
            "fields": fields,
            "consistent_read": consistent_read,
            "filter": {
                "AND": [
                    {
                        "IN": {
                            "field": "qualified_name",
                            "value": qualified_name_matches.copy(),
                        }
                    },
                    date_filter,
                ]
            },
        },
    )


def construct_queries(
    qualified_names: list[str],
    snapshot_dates: SnapshotDates,
    fields: list[str],
    debug: bool = False,
    consistent_read: bool = False,
) -> list[GrabParams]:
    queries: list[GrabParams] = []
    qualified_name_matches: list[str] = []

    def add_query() -> None:
        queries.append(
            make_query(
                qualified_name_matches,
                snapshot_dates,
                fields,
                debug,
                consistent_read,
            )
        )

    if "range" in snapshot_dates:
        read_limit = READ_OPERATIONS_LIMIT
    else:
        read_limit = 1

    for qn in qualified_names:
        qualified_name_matches.append(qn)
        if len(qualified_name_matches) == read_limit:
            add_query()
            qualified_name_matches = []
    if qualified_name_matches:
        add_query()
    return queries


def _decompose_exchanges(
    exchanges: list[str],
) -> tuple[RawSuperExchangeMap, list[str]]:
    raw_to_super_exchange_mapping: RawSuperExchangeMap = defaultdict(set)
    all_exchanges: set[str] = set()

    for e in exchanges:
        if e.endswith("supercomposite"):
            raw_exchanges = [e.split("-")[0], "v2composite"]
        else:
            raw_exchanges = [e]

        for raw in raw_exchanges:
            raw_to_super_exchange_mapping[raw].add(e)

        all_exchanges.update(raw_exchanges)

    return raw_to_super_exchange_mapping, list(all_exchanges)


def retrieve_data(
    start: str,
    end: str,
    currencies: list[str],
    exchanges: list[str],
    models: list[Model],
    calc_types: list[CalcType],
    freq: str,
    interval: str,
    include_listed_expiries: bool,
    tenor_mode: TenorMode,
    version: str = "",
    debug: bool = False,
    consistent_read: bool = False,
    fetch_lookback_data: bool = True,
) -> tuple[
    list[ParamsQueryResult],
    list[AllQueryResults],
    dict[str, OptionContractDetails],
    RawSuperExchangeMap,
]:
    v = f"{version}." if version else ""
    qn_freq = "1m" if freq == "live" else freq
    qn_prefixes = [
        f"{v}{e}.option.{c}.{m}"
        for c in currencies
        for m in models
        for e in exchanges
    ]
    qns = [f"{prefix}.{qn_freq}.params" for prefix in qn_prefixes]

    queries = []
    if TARGET_SNAPSHOTS:
        queries.extend(
            construct_queries(
                qns,
                {"timestamps": TARGET_SNAPSHOTS},
                [],
                debug,
                consistent_read,
            )
        )
    else:
        snapshot_dates: SnapshotDates = {
            "range": {
                "start": from_iso(start).timestamp() * 1e9,
                "end": from_iso(end).timestamp() * 1e9,
            }
        }
        queries = construct_queries(
            qns,
            snapshot_dates,
            [],
            debug,
            consistent_read,
        )
    data = cast(list[ParamsQueryResult], grab(queries))

    lookback_data: list[AllQueryResults] = []
    if fetch_lookback_data:
        # The end of the lookback should be the start of our chunk
        lookback_data = _retrieve_lookback_data(
            qn_prefixes=qn_prefixes,
            lookback_end=start,
            freq=freq,
            interval=interval,
            calc_types=calc_types,
            debug=debug,
            consistent_read=consistent_read,
            tenor_mode=tenor_mode,
        )

    instruments: dict[str, OptionContractDetails] = {}
    exchange_map: RawSuperExchangeMap = {}
    if include_listed_expiries:
        exchange_map, raw_exchanges = _decompose_exchanges(exchanges)
        instruments_raw = get_instruments(
            fields=[
                "strike",
                "expiry",
                "qualified_name",
                "baseAsset",
                "instrument",
                "listing",
                "availableSince",
            ],
            start=start,
            end=end,
            exchanges=raw_exchanges,
            asset_types=["option"],
            base_assets=currencies,
        )
        for i in instruments_raw:
            new_inst: OptionContractDetails = {
                "expiry": i["expiry"],
                "baseAsset": i["baseAsset"],
                "qualified_name": i["qualified_name"],
                "listing": i["listing"],
                "availableSince": i["availableSince"],
                # below 2 are not available on Synthetic currencies
                "instrument_name": i.get(  # artifact leaked from blockstream
                    "instrument", f'{i["baseAsset"]}.{i["expiry"]}'
                ),
                "strike": i.get("strike"),
            }

            instruments[new_inst["instrument_name"]] = new_inst

    return (
        data,
        lookback_data,
        instruments,
        exchange_map,
    )


def retrieve_data_for_qns(
    snapshot_dates: SnapshotDates,
    qualified_names: list[str],
    debug: bool = False,
    consistent_read: bool = False,
) -> list[AllQueryResults]:
    queries = construct_queries(
        qualified_names, snapshot_dates, [], debug, consistent_read
    )

    results = cast(list[AllQueryResults], grab(queries))
    return results


def _retrieve_lookback_data(
    qn_prefixes: list[str],
    lookback_end: str,
    freq: str,
    interval: str,
    calc_types: list[CalcType],
    tenor_mode: TenorMode,
    debug: bool = False,
    consistent_read: bool = False,
) -> list[AllQueryResults]:
    lookback_calc_types = list({*calc_types}.intersection(SMOOTHING_CALC_TYPES))
    if not lookback_calc_types:
        logging.info("no calc_types to lookback, return empty lookback data")
        return []

    # 1- Generate date range based on lookback window
    smooth_freq = "20s" if freq == "live" else freq
    lookback_window_size = SMOOTH_PARAMS[smooth_freq]["zscore"]["window"]

    # All timestamps of lookback data
    snapshot_timestamps = list(
        generate_lookback_timestamps(
            lookback_end, lookback_window_size, interval
        )
    )
    snapshot_dates: SnapshotDates = {
        "range": {
            "end": max(snapshot_timestamps),
            "start": min(snapshot_timestamps),
        }
    }

    # 2- Generate lookback qualified_names
    lookback_qns: list[str] = []
    qn_freq = "1m" if freq == "live" else freq

    # Add listed tenors
    lookback_qns.extend(
        [
            f"{qn_prefix}.listed.{qn_freq}.{calc}"
            for calc in lookback_calc_types
            for qn_prefix in qn_prefixes
        ]
    )

    # Add tenors from defined tenor_mapping
    for pref in qn_prefixes:
        _, tokens = utils_general.get_qfn_and_version(pref)
        tenors = get_tenors(
            freq=qn_freq,
            mode=tenor_mode,
            exchange=tokens[0],
            currency=tokens[2],
        )
        lookback_qns.extend(
            [
                f"{pref}.{utils_general.convert_tenor_days_to_constant_maturity(t)}.{qn_freq}.{calc}"
                for calc in lookback_calc_types
                for t in tenors
            ]
        )

    queries = construct_queries(
        qualified_names=lookback_qns,
        snapshot_dates=snapshot_dates,
        fields=[],
        debug=debug,
        consistent_read=consistent_read,
    )

    # 3- Extract listed results
    grab_results = grab(queries)
    logging.info("--Retrieved lookback data--")
    return _transform_results(
        grab_results, qn_freq=qn_freq, is_live_version=(freq == "live")
    )


def _transform_results(
    results: list[dict[str, object]], qn_freq: str, is_live_version: bool
) -> list[AllQueryResults]:
    """
    Extract listed result if needed
    """
    transform: list[AllQueryResults] = []
    try:
        for result in results:
            qn = cast(str, result.get("qualified_name"))
            if ".listed." not in qn:
                if is_live_version:
                    result["qualified_name"] = qn.replace(
                        f".{qn_freq}.", ".live."
                    )

                if "params" in qn and "spline" in qn:
                    result["spline_info"] = modify_string(result["spline_info"])

                result.update(
                    {
                        TenorType.LISTED_EXPIRY.value: False,
                        TenorType.ARBITRARY_EXPIRY.value: False,
                        TenorType.ARBITRARY_CONSTANT_MATURITY.value: False,
                        TenorType.STANDARD_CONSTANT_MATURITY.value: True,
                    }
                )
                transform.append(cast(AllQueryResults, result))

            else:
                calc_type = qn.split(".")[-1]
                if calc_type not in result:
                    logging.error(
                        f"invalid result, calc_type '{calc_type}' not found: {result}"
                    )
                    continue
                for r in modify_string(cast(str, result[calc_type])):
                    qn = r["qualified_name"]
                    if is_live_version:
                        r["qualified_name"] = qn.replace(
                            f".{qn_freq}.", ".live."
                        )

                    if calc_type == "params" and "spline" in qn:
                        r["spline_info"] = modify_string(r["spline_info"])

                    r.update(
                        {
                            TenorType.LISTED_EXPIRY.value: True,
                            TenorType.ARBITRARY_EXPIRY.value: False,
                            TenorType.ARBITRARY_CONSTANT_MATURITY.value: False,
                            TenorType.STANDARD_CONSTANT_MATURITY.value: False,
                        }
                    )
                    r["timestamp"] = result["timestamp"]
                    transform.append(cast(AllQueryResults, r))

    except Exception as e:
        logging.exception(
            f"Error trying to extract params inside retrieved listed params. error: {e}"
        )

    return transform
