import warnings

warnings.simplefilter(action="ignore", category=FutureWarning)

import logging
from datetime import datetime

import pandas as pd
import numpy as np
import math
import utils_calc
from calc_helpers import (
    create_strike_df,
    calculate_calibration_metrics,
    calculate_model_vol_and_deviation,
    get_calib_indexes,
)
from utils_calc import get_domestic_rate
from typing import Any
from constants import load_domestic_rates


def _spline_parameter_calc_single_expiration(
    query: utils_calc.CalibrationQuery,
) -> None | tuple[pd.DataFrame, pd.DataFrame.index, utils_calc.spline.CubicSpline]:
    if len(query["strikes"]) < 3:
        logging.warning(
            f"spline_parameter_calc there is not enough strikes for calibration, query={query}",
        )
        return None

    if strike ==

    strike_df = create_strike_df(query, overwrite_deep_wing=True)
    calib_indexes = get_calib_indexes(strike_df, query, True, True)

    if len(calib_indexes) < 3:
        logging.warning(
            f"spline_parameter_calc there is not enough calib_indexes for calibration, query={query}",
        )
        return None

    market_vols = strike_df.loc[calib_indexes, "mid_vol"].values
    strikes = strike_df.loc[calib_indexes, "strike"].values

    query_for_calibration = query.copy()
    query_for_calibration["LNvols"] = market_vols  # both resorted by strike
    query_for_calibration["strikes"] = strikes  # both resorted by strike

    calib_params = utils_calc.spline.perform_spline_calibration(query_for_calibration)

    strike_df = calculate_model_vol_and_deviation(
        strike_df, query, calib_params, "spline"
    )

    if strike_df["model_vol"].isnull().sum() > 0:
        logging.error(
            f"spline_parameter_calc failed to produced non-negative total_var, query={query}, calib_params={calib_params}",
        )
        return None

    return strike_df, calib_indexes, calib_params


def prepare_final_dataframe(results: list[dict[str, Any]]) -> pd.DataFrame:
    expanded_results = [
        {**r["calib_params"], **r["calib_info"], **r["base_info"]} for r in results
    ]
    final_df = pd.DataFrame(expanded_results)
    final_df.sort_values("expiry", inplace=True)
    final_df.reset_index(drop=True, inplace=True)

    # TODO(ibazhov): move to metric calcualtion or somewhere else, unify for all model
    def calculate_atm_vol(row):
        spline_info = utils_calc.spline.CubicSpline(spline_info=row["spline_info"])
        return utils_calc.spline_model_vol([row["forward"]], spline_info)[0]

    final_df["atm_vol"] = final_df.apply(calculate_atm_vol, axis=1)

    return final_df


def spline_parameter_calc(
    base_df: pd.DataFrame,
    queries: list[utils_calc.CalibrationQuery],
) -> pd.DataFrame | None:
    results = []

    for index, query in enumerate(queries):
        single_expiration_result = _spline_parameter_calc_single_expiration(query)
        if single_expiration_result is None:
            continue

        strike_df, calib_indexes, calib_params = single_expiration_result
        calib_params["expiry"] = query["expiry"]
        results.append(
            {
                "calib_params": calib_params,
                "calib_info": calculate_calibration_metrics(
                    strike_df, calib_indexes, query, "spline"
                ),
                "base_info": base_df.iloc[index].to_dict(),
            }
        )

    if len(results) == 0:
        raise Exception("No calib params generated.")

    return prepare_final_dataframe(results)


def arb_check_delta_spline(model_params: pd.DataFrame, exchange: str):
    model_params = model_params.sort_values("expiry")
    timestamp = model_params.iloc[0]["timestamp"]
    date = datetime.utcfromtimestamp(timestamp // 1e9)
    model_params["strike_put_1"] = 0
    model_params["strike_call_1"] = 0
    model_params["rd"] = (model_params["expiry"] * 365).apply(
        lambda x: get_domestic_rate(load_domestic_rates(), x)
    )

    for index, row in model_params.iterrows():
        s, f, t = row["spot"], row["forward"], row["expiry"]
        spline_info = row["spline_info"]

        for sign in [-1, 1]:
            strike_delta = 0.01 * sign
            label = "strike_put_1" if sign < 0 else "strike_call_1"
            try:
                model_params.at[index, label] = utils_calc.estimate_strike_from_delta(
                    "spline", row, s, f, t, strike_delta, row["rd"]
                )
            except Exception:
                strike_to_use = "min_strike" if sign < 0 else "max_strike"
                model_params.at[index, label] = row[strike_to_use]

    moneyness = list(range(1, 1101, 1))
    moneyness_str = [str(x) for x in moneyness]
    columns_df = ["underlying_index"] + moneyness_str
    total_vars_strike = pd.DataFrame(
        np.zeros((len(model_params["underlying_index"]), len(columns_df))) * np.nan,
        columns=columns_df,
    )
    total_vars_strike = total_vars_strike.set_index("underlying_index")
    total_vars_strike.index = model_params["underlying_index"]

    for index, row in model_params.iterrows():
        f, t = row["forward"], row["expiry"]
        spline_info = row["spline_info"]
        min_strike, max_strike = row["min_strike"], row["max_strike"]
        min_money, max_money = math.ceil((min_strike / f) * 100), math.floor(
            (max_strike / f) * 100
        )
        moneyness_sub = list(range(max(10, min_money), min(1100, max_money) + 1, 1))
        moneyness_str = [str(x) for x in moneyness_sub]
        strikes = np.array([f * (x / 100) for x in moneyness_sub])
        total_vars = utils_calc.spline_model_vol(
            strikes, utils_calc.spline.CubicSpline(spline_info=spline_info)
        )
        total_vars = [(x**2) * t for x in total_vars]
        total_vars_strike.loc[row["underlying_index"], moneyness_str] = total_vars

    if all(total_vars_strike.apply(lambda x: x.dropna().is_monotonic, axis=0)):
        strikes_arbitrage = pd.DataFrame()
    else:
        logging.warning(
            f"L1: Arbitrage found in traded strikes, timestamp={timestamp}, date={date}",
        )
        strikes_arbitrage = total_vars_strike

    moneyness = list(range(1, 1101, 1))
    moneyness_str = [str(x) for x in moneyness]
    columns_df = ["underlying_index"] + moneyness_str
    total_vars_money = pd.DataFrame(
        np.zeros((len(model_params["underlying_index"]), len(columns_df))) * np.nan,
        columns=columns_df,
    )
    total_vars_money = total_vars_money.set_index("underlying_index")
    total_vars_money.index = model_params["underlying_index"]

    for index, row in model_params.iterrows():
        f, t = row["forward"], row["expiry"]
        spline_info = row["spline_info"]
        min_strike, max_strike = row["strike_put_1"], row["strike_call_1"]
        min_money, max_money = math.ceil((min_strike / f) * 100), math.floor(
            (max_strike / f) * 100
        )
        moneyness_sub = list(range(max(10, min_money), min(1100, max_money) + 1, 1))
        moneyness_str = [str(x) for x in moneyness_sub]
        strikes = np.array([f * (x / 100) for x in moneyness_sub])
        total_vars = utils_calc.spline_model_vol(
            strikes, utils_calc.spline.CubicSpline(spline_info=spline_info)
        )
        total_vars = [(x**2) * t for x in total_vars]
        total_vars_money.loc[row["underlying_index"], moneyness_str] = total_vars

    if all(total_vars_money.apply(lambda x: x.dropna().is_monotonic, axis=0)):
        moneyness_arbitrage = []
    else:
        logging.log(
            15,
            f"L2: Arbitrage found from delta -1 to 1, timestamp={timestamp}, date={date}",
        )
        moneyness_bool = list(
            ((total_vars_money.apply(lambda x: x.dropna().is_monotonic, axis=0)))
        )
        moneyness_arbitrage = list(np.where(~np.array(moneyness_bool))[0])

    return strikes_arbitrage, moneyness_arbitrage
