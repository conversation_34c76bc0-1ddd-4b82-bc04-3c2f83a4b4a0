import logging
import math
import warnings
from datetime import datetime, timezone
from functools import partial
from typing import cast, get_type_hints

import numpy as np
import utils_calc
import utils_calc.utils_calc_constants
import utils_calc.utils_calc_helpers
import utils_calc.utils_calc_svi
import utils_general
from calc_helpers import (
    DELTAS_STR,
    EXTRA_MONEYNESS_LESS_STRIKES,
    MIN_STRIKES,
    MONEYNESS_STR,
    finalize_result_df,
)
from constants import (
    CALIB_R2_THRESHOLD,
    EXPIRY_PRECISION,
    TENOR_PRECISION,
    TWO_MINUTES_IN_YEARS,
    load_domestic_rates,
)
from lambda_types import (
    ListedTenorParamsSVI,
    NDArrayFloat64,
    ParamsCalcResult,
    TenorExpiryArbFields,
)
from pandas import DataFrame
from utils.calibrate import fit_spline_with_exception
from utils.common import (
    check_calibrated_vols_and_log_error_with_details,
    round_value,
)
from utils.extrapolate import get_strikes_and_vols_to_extrapolate
from utils.interpolate import get_strikes_to_interpolate
from utils_calc import DEFAULT_SURFACE_MONEYNESS as MONEYNESS
from utils_calc import get_domestic_rate
from utils_calc.utils_calc_types import (
    CalibrationQuery,
    SviCalibrationReferenceParams,
    SviParams,
)

warnings.simplefilter(action="ignore", category=FutureWarning)


def handle_sparse_strikes(
    strikes: list[float],
    vols: list[float],
    forward: float,
    spot: float,
    exp: float,
    rd: float,
    t: float,
    tenors_params: DataFrame,
) -> tuple[list[float], list[float], list[float]]:
    """
    Handles the augmentation of strikes and recalculation of vols when strikes_narrow is too small.
    Returns updated (strikes_narrow, vols_narrow, deltas).
    """
    strikes_to_vol_dict = dict(zip(strikes, vols))
    extra_strikes = [m * forward for m in EXTRA_MONEYNESS_LESS_STRIKES]

    if extra_strikes:
        ref_row = utils_calc.get_closest_expiry_ref_params(
            tenors_params[list(get_type_hints(SviParams).keys())],
            exp,
            utils_calc.INTERPOLATED_EXPIRY_CALENDAR_ARB_THRESHOLD,
        )

        a = ref_row["svi_a"]
        b = ref_row["svi_b"]
        rho = ref_row["svi_rho"]
        m = ref_row["svi_m"]
        sigma = ref_row["svi_sigma"]
        param_expiry = ref_row["expiry"]

        extra_vols = cast(
            NDArrayFloat64,
            utils_calc.svi_vol(
                a,
                b,
                rho,
                m,
                sigma,
                forward,
                param_expiry,
                np.array(extra_strikes),
            ),
        )

        strikes_to_vol_dict.update(dict(zip(extra_strikes, extra_vols)))

    return get_strikes_and_vols_to_extrapolate(
        expiry=exp,
        forward=forward,
        spot=spot,
        strikes_to_vol=strikes_to_vol_dict,
        r_d=rd,
    )


def svi_tenor_params(
    snap: list[ListedTenorParamsSVI],
    exchange: str,
    currency: str,
    expiry_iv_info: TenorExpiryArbFields,
) -> ParamsCalcResult:
    tenors_days = sorted(expiry_iv_info.keys())
    df_vol_matrix_pre = DataFrame(
        np.zeros((len(tenors_days), len(DELTAS_STR))),
        columns=DELTAS_STR,
        index=tenors_days,
    )

    df_vol_matrix_money = DataFrame(
        np.zeros((len(tenors_days), len(MONEYNESS_STR))),
        columns=MONEYNESS_STR,
        index=tenors_days,
    )

    snap_data = DataFrame(snap)
    snap_data["expiry"] = snap_data["expiry"].apply(
        partial(round_value, precision=EXPIRY_PRECISION)
    )
    # snap_data = snap_data[snap_data["R2"] > 0.9]
    # snap_data = snap_data[snap_data["expiry"] > (2 / 365)]
    all_listed_expiries = snap_data["expiry"].tolist()
    all_listed_tenors = sorted(
        round_value(x * 365, TENOR_PRECISION) for x in all_listed_expiries
    )
    listed_tenor_expiry_map = dict(zip(all_listed_tenors, all_listed_expiries))
    max_exp_listed = max(all_listed_expiries)
    min_exp_listed = min(all_listed_expiries)
    spot = np.mean(snap_data["spot"])
    timestamp = int(snap_data["timestamp"].iloc[0])
    runtime = datetime.now(tz=timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.000Z")

    # max_forward = snap_data[snap_data["expiry"] == max_exp_listed].iloc[0]["forward"]
    # c_yield = (1 / max_exp_listed) * math.log(max_forward / spot)

    ### Yields ############
    c_yield_s, c_yield_left, c_yield_right = utils_calc.calc_convenience_yield(
        snap_data, spot
    )

    #########################

    df_vol_matrix_pre["atm"] = 0
    df_vol_matrix_pre["lower_bordering_expiry"] = None
    df_vol_matrix_pre["upper_bordering_expiry"] = None

    tenors_params_svi = DataFrame(
        columns=[
            "tenor_days",
            "forward",
            "svi_a",
            "svi_b",
            "svi_rho",
            "svi_m",
            "svi_sigma",
            "atm_vol",
            "expiry",
            "rd",
        ]
    )
    tenor_completed = []
    tenor_days_without_listed_expiries = sorted(
        set(tenors_days).difference(all_listed_tenors)
    )

    tenor_days_only_listed_expiries = sorted(
        set(tenors_days).intersection(all_listed_tenors)
    )

    for tenor in tenor_days_only_listed_expiries:
        exp = listed_tenor_expiry_map[tenor]
        tenor_completed.append(tenor)
        svi_Date = snap_data[snap_data["expiry"] == exp].iloc[0]
        df_vol_matrix_pre.loc[tenor, "lower_bordering_expiry"] = svi_Date[
            "underlying_index"
        ]
        df_vol_matrix_pre.loc[tenor, "upper_bordering_expiry"] = svi_Date[
            "underlying_index"
        ]
        forward = svi_Date["forward"]
        a = svi_Date["svi_a"]
        b = svi_Date["svi_b"]
        rho = svi_Date["svi_rho"]
        m = svi_Date["svi_m"]
        sigma = svi_Date["svi_sigma"]
        atm_vol = svi_Date["atm_vol"]
        rd = get_domestic_rate(load_domestic_rates(), tenor)

        tenors_params_svi = tenors_params_svi.append(  # type: ignore
            {
                "tenor_days": tenor,
                "forward": forward,
                "svi_a": a,
                "svi_b": b,
                "svi_rho": rho,
                "svi_m": m,
                "svi_sigma": sigma,
                "atm_vol": atm_vol,
                "expiry": exp,
                "rd": rd,
            },
            ignore_index=True,
        )

    for tenor in tenor_days_without_listed_expiries:
        try:
            exp = tenor / 365
            if exp < min_exp_listed:
                continue
            if exp > max_exp_listed:
                continue
            else:
                # Tenor interpolation
                (
                    lower_bordering_expiry,
                    upper_bordering_expiry,
                ) = utils_calc.get_bordering_expiries(
                    snap_df=snap_data, target_expiry=exp
                )

                if (
                    lower_bordering_expiry is None
                    or upper_bordering_expiry is None
                ):
                    raise Exception(
                        f"Both bordering Listed expiries must be present: "
                        f"{lower_bordering_expiry=}"
                        f"{upper_bordering_expiry=}"
                    )

                df_vol_matrix_pre.loc[tenor, "lower_bordering_expiry"] = (
                    lower_bordering_expiry["underlying_index"]
                )
                df_vol_matrix_pre.loc[tenor, "upper_bordering_expiry"] = (
                    upper_bordering_expiry["underlying_index"]
                )

                # interpolate forward
                frac = (exp - lower_bordering_expiry["expiry"]) / (
                    upper_bordering_expiry["expiry"]
                    - lower_bordering_expiry["expiry"]
                )
                forward_inter = pow(
                    lower_bordering_expiry["forward"], 1 - frac
                ) * pow(upper_bordering_expiry["forward"], frac)

                (
                    lower_bordering_strikes,
                    interpolated_tenor_strikes,
                    upper_bordering_strikes,
                ) = get_strikes_to_interpolate(
                    upper_expiry=upper_bordering_expiry,
                    lower_expiry=lower_bordering_expiry,
                    model="SVI",
                    spot=spot,
                    interpolated_forward=forward_inter,
                    exchange=exchange,
                )

                lower_bordering_svi_vars = utils_calc.svi_var(
                    lower_bordering_expiry["svi_a"],
                    lower_bordering_expiry["svi_b"],
                    lower_bordering_expiry["svi_rho"],
                    lower_bordering_expiry["svi_m"],
                    lower_bordering_expiry["svi_sigma"],
                    np.array(lower_bordering_strikes),
                    lower_bordering_expiry["forward"],
                )
                upper_bordering_svi_vars = utils_calc.svi_var(
                    upper_bordering_expiry["svi_a"],
                    upper_bordering_expiry["svi_b"],
                    upper_bordering_expiry["svi_rho"],
                    upper_bordering_expiry["svi_m"],
                    upper_bordering_expiry["svi_sigma"],
                    np.array(upper_bordering_strikes),
                    upper_bordering_expiry["forward"],
                )
                interpolated_svi_vars = [
                    (a * (1 - frac)) + (b * frac)
                    for a, b in zip(
                        np.asarray(lower_bordering_svi_vars),
                        np.asarray(upper_bordering_svi_vars),
                    )
                ]
                interpolated_vols = np.sqrt(
                    np.array(interpolated_svi_vars) / exp
                )

                ####################### SVI Parameters - API Calibration ###################################################
                rd = get_domestic_rate(load_domestic_rates(), exp * 365)
                query: CalibrationQuery = {
                    "expiry": exp,
                    "forward": forward_inter,
                    "spot": spot,
                    "domestic_rate": rd,
                    "test_type": "strikes",
                    "vol_test_type": "vol_lognormal",
                    "model": "SVI",
                    "LNvols": interpolated_vols,
                    "strikes": list(interpolated_tenor_strikes),
                    "biv": interpolated_vols,
                    "aiv": interpolated_vols,
                }

                ################ SVI Parameters Error Re-Calibration #######################################################
                ref_param = utils_calc.get_closest_expiry_ref_params(
                    tenors_params_svi[list(get_type_hints(SviParams).keys())],
                    exp,
                    utils_calc.INTERPOLATED_EXPIRY_CALENDAR_ARB_THRESHOLD,
                )
                calibration_result = (
                    utils_calc.calibrate_svi_with_recalibrations(
                        query, ref_param
                    )
                )
                svi_parameters = calibration_result["svi_parameters"]

                ##########################################################################################################
                a, b, rho, m, sigma = (
                    svi_parameters["svi_a"],
                    svi_parameters["svi_b"],
                    svi_parameters["svi_rho"],
                    svi_parameters["svi_m"],
                    svi_parameters["svi_sigma"],
                )
                ##########################################################################################
                predicted_vols = cast(
                    NDArrayFloat64,
                    utils_calc.svi_vol(
                        a,
                        b,
                        rho,
                        m,
                        sigma,
                        forward_inter,
                        exp,
                        interpolated_tenor_strikes,
                    ),
                )

                # constant tenor R2 score
                # TODO: bad calibrations will have nans in vols. We need to fix the underlying SVI calibration or return sensible fallback values
                # returns 0 for bad calibrations. Listed expiries have nan's so we use 0 to distinguish
                (
                    calib_r2,
                    _,
                    _,
                    _,
                ) = check_calibrated_vols_and_log_error_with_details(
                    precalibrated_vols=interpolated_vols,
                    model_predicted_vols=predicted_vols,
                    tenor=tenor,
                    timestamp=timestamp,
                    model="SVI",
                    process="tenor interpolation",
                )

                tenors_params_svi = tenors_params_svi.append(  # type: ignore
                    {
                        "tenor_days": tenor,
                        "forward": forward_inter,
                        "svi_a": a,
                        "svi_b": b,
                        "svi_rho": rho,
                        "svi_m": m,
                        "svi_sigma": sigma,
                        "atm_vol": utils_calc.svi_vol(
                            a,
                            b,
                            rho,
                            m,
                            sigma,
                            forward_inter,
                            exp,
                            forward_inter,
                        ),
                        "expiry": exp,
                        "R2_calib": calib_r2,
                        "rd": rd,
                    },
                    ignore_index=True,
                )

                tenor_completed.append(tenor)

            # Add SVI jump wing params
            utils_calc.add_svi_jw_params(tenors_params_svi)

        except Exception as e:
            logging.exception(
                f"Error interpolating SVI params {exchange=}, tenor={tenor}, timestamp={utils_general.to_iso(timestamp)}, e={e}"
            )
            # drop failed tenor from surface skeleton
            df_vol_matrix_pre = df_vol_matrix_pre.drop([tenor])

    ### Fill Moneyness Surface for Interpolated Tenors######################
    for tenor in tenor_completed:
        temp = tenors_params_svi[tenors_params_svi["tenor_days"] == tenor].iloc[
            0
        ]
        forward = temp["forward"]
        a = temp["svi_a"]
        b = temp["svi_b"]
        rho = temp["svi_rho"]
        m = temp["svi_m"]
        sigma = temp["svi_sigma"]
        strikes = np.array(MONEYNESS) * forward
        tenor_vols = cast(
            NDArrayFloat64,
            utils_calc.svi_vol(
                a,
                b,
                rho,
                m,
                sigma,
                forward,
                tenor / 365,
                strikes,
            ),
        )
        df_vol_matrix_money.loc[tenor, MONEYNESS_STR] = tenor_vols

    ######################################################################################################################################################

    #### Extrapolation Left ############
    # TENOR MUST BE SORTED FOR THIS TO WORK
    df_vol_matrix_money = utils_calc.extrapolate_left(df_vol_matrix_money)

    ###################################
    """
    series_t = df_vol_matrix_money.index.values

    #### Extrapolation Right ############
    df_vol_matrix_money = df_vol_matrix_money**2
    df_vol_matrix_money = df_vol_matrix_money.mul(series_t / 365, axis=0)
    df_vol_matrix_money = df_vol_matrix_money.replace(to_replace=0, method="ffill")
    df_vol_matrix_money = df_vol_matrix_money.div(series_t / 365, axis=0)
    df_vol_matrix_money = df_vol_matrix_money ** (1 / 2)
    ###################################
    """
    ###### Info for Interpolated and Extrapolated Tenors ##########
    tenor_in_ex = [t for t in tenors_days if t not in tenor_completed]
    min_inter = min(tenor_completed)
    max_inter = max(tenor_completed)
    tenor_in_ex_right = [x for x in tenor_in_ex if x > max_inter]

    ############################################################

    df_vol_matrix_money, tenor_in_ex = fit_spline_with_exception(
        df_vol_matrix_money=df_vol_matrix_money,
        tenor_completed=tenor_completed,
        right_side_tenors=tenor_in_ex_right,
        tenors_to_extrapolate=tenor_in_ex,
        timestamp=timestamp,
        exchange=exchange,
        currency=currency,
    )

    ############ Vol Surface - Moneyness ######################

    sorted_tenor_in_left_ex = sorted(
        [x for x in tenor_in_ex if x < min_inter], reverse=True
    )
    for t in sorted_tenor_in_left_ex:
        try:
            exp = t / 365
            c_yield = c_yield_left if t < min_inter else c_yield_right

            spot_shifted = (
                spot if t < min_inter else c_yield_s.iloc[-1]["forward"]
            )

            t_shifted = (
                exp if t < min_inter else (exp) - c_yield_s.iloc[-1]["expiry"]
            )

            assert t > 0
            forward = spot_shifted * (math.exp(c_yield * t_shifted))

            df_vol_matrix_pre.loc[t, "upper_bordering_expiry"] = snap_data[
                snap_data["expiry"] == listed_tenor_expiry_map[min_inter]
            ].iloc[0]["underlying_index"]

            vols = df_vol_matrix_money.loc[t, MONEYNESS_STR].values.tolist()  # type: ignore
            strikes = np.array(MONEYNESS) * forward

            # In the future we may want to use custom set of strikes to calibrate
            # E.g Longer tenors using a different set of strikes
            # We may want to anchor some deltas in certain regions of the wings and atm like in modelparams
            # calculate the grid based on the deltas and volatilites
            rd = get_domestic_rate(load_domestic_rates(), exp * 365)
            (
                strikes_narrow,
                vols_narrow,
                deltas,
            ) = get_strikes_and_vols_to_extrapolate(
                expiry=exp,
                forward=forward,
                spot=spot,
                strikes_to_vol=dict(zip(strikes, vols)),
                r_d=rd,
            )
            if len(strikes_narrow) < MIN_STRIKES:
                logging.warning(
                    f"Got less than required strikes for SVI calibration. Adding more strikes {t=}, {strikes_narrow=}"
                )

                strikes_narrow, vols_narrow, deltas = handle_sparse_strikes(
                    strikes,
                    vols,
                    forward,
                    spot,
                    exp,
                    rd,
                    t,
                    tenors_params_svi,
                )

            query = {
                "expiry": exp,
                "forward": forward,
                "spot": spot,
                "domestic_rate": rd,
                "test_type": "delta",
                "vol_test_type": "vol_lognormal",
                "model": "SVI",
                "LNvols": vols_narrow,
                "strikes": strikes_narrow,
                "biv": vols_narrow,
                "aiv": vols_narrow,
            }

            ref_param = utils_calc.get_closest_expiry_ref_params(
                tenors_params_svi[list(get_type_hints(SviParams).keys())],
                exp,
                utils_calc.INTERPOLATED_EXPIRY_CALENDAR_ARB_THRESHOLD,
            )

            calibration_result = utils_calc.calibrate_svi_with_recalibrations(
                query, ref_param
            )

            svi_parameters = calibration_result["svi_parameters"]

            a, b, rho, m, sigma = (
                svi_parameters["svi_a"],
                svi_parameters["svi_b"],
                svi_parameters["svi_rho"],
                svi_parameters["svi_m"],
                svi_parameters["svi_sigma"],
            )

            predicted_vols = cast(
                NDArrayFloat64,
                utils_calc.svi_vol(
                    a, b, rho, m, sigma, forward, exp, np.array(strikes_narrow)
                ),
            )

            (
                calib_r2,
                rmse,
                mse,
                mae,
            ) = check_calibrated_vols_and_log_error_with_details(
                precalibrated_vols=vols_narrow,
                model_predicted_vols=predicted_vols,
                tenor=t,
                timestamp=timestamp,
                model="SVI",
                process="left side tenor extrapolation",
            )

            ############################ Error Handling for Extraolated Tenors, Get previous calib values ############################
            ############################ Error Handling, Get previous calib values ###################################################
            if (
                np.isinf(np.asarray(list(svi_parameters.values()))).any()
                or np.isnan(np.asarray(list(svi_parameters.values()))).any()
            ):

                log_fn = (
                    logging.warning
                    if exp < TWO_MINUTES_IN_YEARS
                    else logging.error
                )

                log_fn(
                    "SVI calibration failed for extrapolated tenor - fallback to previous tenor values. "
                    f"{exchange=} | t={exp} | {currency=} | {utils_general.to_iso(timestamp)} | "
                    f"{timestamp} | {query=} | {ref_param=}"
                )

                lower_fallback_expiry_row, upper_fallback_expiry_row = (
                    utils_calc.get_bordering_expiries(
                        snap_df=tenors_params_svi, target_expiry=exp
                    )
                )

                if lower_fallback_expiry_row is not None:
                    pre_sol = lower_fallback_expiry_row
                elif upper_fallback_expiry_row is not None:
                    pre_sol = upper_fallback_expiry_row
                else:
                    raise NotImplementedError(
                        f"No bordering calibrated expiries found. Investigate! {exchange=} | t={exp} | {currency} | {utils_general.to_iso(timestamp)} | {timestamp}"
                    )

                (
                    svi_parameters["svi_a"],
                    svi_parameters["svi_b"],
                    svi_parameters["svi_rho"],
                    svi_parameters["svi_m"],
                    svi_parameters["svi_sigma"],
                ) = (
                    pre_sol["svi_a"],
                    pre_sol["svi_b"],
                    pre_sol["svi_rho"],
                    pre_sol["svi_m"],
                    pre_sol["svi_sigma"],
                )
            ###########################################################################################################################

            if calib_r2 < CALIB_R2_THRESHOLD or math.isnan(calib_r2):

                logging.warning(
                    f"Calibration R2 < {CALIB_R2_THRESHOLD} in left hand extrapolation, falling back to parameter scaling"
                )

                # Scaling SVI parameters from the reference expiry
                (
                    svi_parameters["svi_a"],
                    svi_parameters["svi_b"],
                    svi_parameters["svi_rho"],
                    svi_parameters["svi_m"],
                    svi_parameters["svi_sigma"],
                ) = (
                    ref_param["svi_a"] * (exp / ref_param["expiry"]),
                    ref_param["svi_b"] * (exp / ref_param["expiry"]),
                    ref_param["svi_rho"],
                    ref_param["svi_m"],
                    ref_param["svi_sigma"],
                )

                a, b, rho, m, sigma = (
                    svi_parameters["svi_a"],
                    svi_parameters["svi_b"],
                    svi_parameters["svi_rho"],
                    svi_parameters["svi_m"],
                    svi_parameters["svi_sigma"],
                )

                fallback_r2_calib = _get_fallback_r2(
                    df=tenors_params_svi, ref_param=ref_param
                )
                tenors_params_svi = tenors_params_svi.append(  # type: ignore
                    {
                        "tenor_days": t,
                        "forward": forward,
                        "svi_a": a,
                        "svi_b": b,
                        "svi_rho": rho,
                        "svi_m": m,
                        "svi_sigma": sigma,
                        "atm_vol": utils_calc.svi_vol(
                            a, b, rho, m, sigma, forward, exp, forward
                        ),
                        "expiry": exp,
                        "R2_calib": fallback_r2_calib,
                        "rd": rd,
                    },
                    ignore_index=True,
                )
                # Add SVI jump wing params
                utils_calc.add_svi_jw_params(tenors_params_svi)

            else:
                tenors_params_svi = tenors_params_svi.append(  # type: ignore
                    {
                        "tenor_days": t,
                        "forward": forward,
                        "svi_a": a,
                        "svi_b": b,
                        "svi_rho": rho,
                        "svi_m": m,
                        "svi_sigma": sigma,
                        "atm_vol": utils_calc.svi_vol(
                            a, b, rho, m, sigma, forward, exp, forward
                        ),
                        "expiry": exp,
                        "R2_calib": calib_r2,
                        "rd": rd,
                    },
                    ignore_index=True,
                )
                # Add SVI jump wing params
                utils_calc.add_svi_jw_params(tenors_params_svi)

        except Exception as e:
            logging.exception(
                f"Error calculating listed expiry param during left hand extrapolation for {exchange=}, tenor={t}, timestamp={utils_general.to_iso(timestamp)}, e={e}"
            )
            # drop failed tenor from surface skeleton
            df_vol_matrix_pre = df_vol_matrix_pre.drop([t])

    for t in tenor_in_ex_right:
        try:
            exp = t / 365

            c_yield = c_yield_left if t < min_inter else c_yield_right

            spot_shifted = (
                spot if t < min_inter else c_yield_s.iloc[-1]["forward"]
            )

            t_shifted = (
                exp if t < min_inter else (exp) - c_yield_s.iloc[-1]["expiry"]
            )

            assert t > 0
            forward = spot_shifted * (math.exp(c_yield * t_shifted))

            df_vol_matrix_pre.loc[t, "lower_bordering_expiry"] = snap_data[
                snap_data["expiry"] == listed_tenor_expiry_map[max_inter]
            ].iloc[0]["underlying_index"]

            vols = df_vol_matrix_money.loc[t, MONEYNESS_STR].values.tolist()  # type: ignore
            strikes = np.array(MONEYNESS) * forward
            rd = get_domestic_rate(load_domestic_rates(), exp * 365)
            (
                strikes_narrow,
                vols_narrow,
                deltas,
            ) = get_strikes_and_vols_to_extrapolate(
                expiry=exp,
                forward=forward,
                spot=spot,
                strikes_to_vol=dict(zip(strikes, vols)),
                r_d=rd,
            )

            query = {
                "expiry": exp,
                "forward": forward,
                "spot": spot,
                "domestic_rate": rd,
                "test_type": "delta",
                "vol_test_type": "vol_lognormal",
                "model": "SVI",
                "LNvols": vols_narrow,
                "strikes": strikes_narrow,
                "biv": vols_narrow,
                "aiv": vols_narrow,
            }

            ref_param = utils_calc.get_closest_expiry_ref_params(
                tenors_params_svi[list(get_type_hints(SviParams).keys())],
                exp,
                utils_calc.INTERPOLATED_EXPIRY_CALENDAR_ARB_THRESHOLD,
            )

            calibration_result = utils_calc.calibrate_svi_with_recalibrations(
                query, ref_param
            )

            svi_parameters = calibration_result["svi_parameters"]

            a, b, rho, m, sigma = (
                svi_parameters["svi_a"],
                svi_parameters["svi_b"],
                svi_parameters["svi_rho"],
                svi_parameters["svi_m"],
                svi_parameters["svi_sigma"],
            )

            predicted_vols = cast(
                NDArrayFloat64,
                utils_calc.svi_vol(
                    a, b, rho, m, sigma, forward, exp, np.array(strikes_narrow)
                ),
            )

            (
                calib_r2,
                rmse,
                mse,
                mae,
            ) = check_calibrated_vols_and_log_error_with_details(
                precalibrated_vols=vols_narrow,
                model_predicted_vols=predicted_vols,
                tenor=t,
                timestamp=timestamp,
                model="SVI",
                process="right side tenor extrapolation",
            )

            ############################ Error Handling for Extraolated Tenors, Get previous calib values ############################
            ############################ Error Handling, Get previous calib values ###################################################
            if (
                np.isinf(np.asarray(list(svi_parameters.values()))).any()
                or np.isnan(np.asarray(list(svi_parameters.values()))).any()
            ):
                logging.error(
                    f"SVI calibration failed for extrapolated tenor - fallback to previous tenor values. {exchange=} | t={exp} | {currency} | {utils_general.to_iso(timestamp)} | {timestamp} | {query} | {ref_param}"
                )
                lower_fallback_expiry_row, upper_fallback_expiry_row = (
                    utils_calc.get_bordering_expiries(
                        snap_df=tenors_params_svi, target_expiry=exp
                    )
                )

                if lower_fallback_expiry_row is not None:
                    pre_sol = lower_fallback_expiry_row
                else:
                    raise NotImplementedError(
                        f"No bordering calibrated expiries found. Investigate! {exchange=} | t={exp} | {currency} | {utils_general.to_iso(timestamp)} | {timestamp}"
                    )

                (
                    svi_parameters["svi_a"],
                    svi_parameters["svi_b"],
                    svi_parameters["svi_rho"],
                    svi_parameters["svi_m"],
                    svi_parameters["svi_sigma"],
                ) = (
                    pre_sol["svi_a"],
                    pre_sol["svi_b"],
                    pre_sol["svi_rho"],
                    pre_sol["svi_m"],
                    pre_sol["svi_sigma"],
                )
            ###########################################################################################################################

            if calib_r2 < CALIB_R2_THRESHOLD or math.isnan(calib_r2):
                logging.warning(
                    f"Calibration R2 < {CALIB_R2_THRESHOLD} in right hand extrapolation, falling back to parameter scaling"
                )

                # Scaling SVI parameters from the reference expiry

                (
                    svi_parameters["svi_a"],
                    svi_parameters["svi_b"],
                    svi_parameters["svi_rho"],
                    svi_parameters["svi_m"],
                    svi_parameters["svi_sigma"],
                ) = (
                    ref_param["svi_a"] * (exp / ref_param["expiry"]),
                    ref_param["svi_b"] * (exp / ref_param["expiry"]),
                    ref_param["svi_rho"],
                    ref_param["svi_m"],
                    ref_param["svi_sigma"],
                )

                a, b, rho, m, sigma = (
                    svi_parameters["svi_a"],
                    svi_parameters["svi_b"],
                    svi_parameters["svi_rho"],
                    svi_parameters["svi_m"],
                    svi_parameters["svi_sigma"],
                )

                fallback_r2_calib = _get_fallback_r2(
                    df=tenors_params_svi, ref_param=ref_param
                )
                tenors_params_svi = tenors_params_svi.append(  # type: ignore
                    {
                        "tenor_days": t,
                        "forward": forward,
                        "svi_a": a,
                        "svi_b": b,
                        "svi_rho": rho,
                        "svi_m": m,
                        "svi_sigma": sigma,
                        "atm_vol": utils_calc.svi_vol(
                            a, b, rho, m, sigma, forward, exp, forward
                        ),
                        "expiry": exp,
                        "R2_calib": fallback_r2_calib,
                        "rd": rd,
                    },
                    ignore_index=True,
                )
                # Add SVI jump wing params
                utils_calc.add_svi_jw_params(tenors_params_svi)

            else:
                tenors_params_svi = tenors_params_svi.append(  # type: ignore
                    {
                        "tenor_days": t,
                        "forward": forward,
                        "svi_a": a,
                        "svi_b": b,
                        "svi_rho": rho,
                        "svi_m": m,
                        "svi_sigma": sigma,
                        "atm_vol": utils_calc.svi_vol(
                            a, b, rho, m, sigma, forward, exp, forward
                        ),
                        "expiry": exp,
                        "R2_calib": calib_r2,
                        "rd": rd,
                    },
                    ignore_index=True,
                )
                # Add SVI jump wing params
                utils_calc.add_svi_jw_params(tenors_params_svi)

        except Exception as e:
            logging.exception(
                f"Error calculating listed expiry param during right hand extrapolation for {exchange=}, tenor={t}, timestamp={utils_general.to_iso(timestamp)}, e={e}"
            )
            # drop failed tenor from surface skeleton
            df_vol_matrix_pre = df_vol_matrix_pre.drop([t])

    tenors_params_svi["spot"] = spot

    tenors_params_svi = finalize_result_df(
        df=tenors_params_svi,
        calc_type="params",
        runtime=runtime,
        timestamp=timestamp,
    )

    return {
        "model": "SVI",
        "exchange": exchange,
        "currency": currency,
        "timestamp": timestamp,
        "dfs": {
            "params": tenors_params_svi,
        },
        "df_vol_matrix_pre": df_vol_matrix_pre,
    }


def _get_fallback_r2(
    df: DataFrame, ref_param: SviCalibrationReferenceParams
) -> float:

    try:
        fallback_r2_calib = df.loc[
            df["expiry"] == ref_param["expiry"], "R2_calib"
        ].iloc[0]
    except (KeyError, IndexError):
        # in case the ref_param is a listed expiry which does not have R2_calib
        fallback_r2_calib = np.nan

    return float(fallback_r2_calib)
