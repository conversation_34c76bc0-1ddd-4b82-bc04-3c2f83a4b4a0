from typing import Dict, List, Optional, Tuple
from unittest.mock import MagicMock

import numpy as np
import pandas as pd
import pytest
from calc_helpers import create_strike_df
from pytest import MonkeyPatch
from svi_calc import _extend_data_spline, _spline_neumann, svi_parameter_calc
from utils_calc import CalibrationQuery


@pytest.fixture
def get_test_fwd_and_spot() -> Tuple[List[float], float]:
    return [1005, 1006, 1007, 1008, 1009], 1000


@pytest.fixture
def base_df(get_test_fwd_and_spot: Tuple[List[float], float]):
    fwds, spot = get_test_fwd_and_spot
    num_entries = len(fwds)
    return pd.DataFrame(
        {
            "timestamp": [1707502980000000000] * len(fwds),
            "spot": [spot for _ in fwds],
            "forward": fwds,
            "underlying_index": [chr(65 + i) for i in range(num_entries)],
        }
    )


@pytest.fixture
def queries(get_test_fwd_and_spot: Tuple[List[float], float]):
    # Mock some CalibrationQuery objects
    fwds, spot = get_test_fwd_and_spot
    return [
        CalibrationQuery(
            {
                "expiry": 0.000001 * fwd,
                "forward": fwd,
                "spot": spot,
                "domestic_rate": 0,
                "model": "SVI",
                "test_type": "delta",
                "vol_test_type": "",
                "LNvols": [0.3] * 5,
                "strikes": [fwd - 200, fwd - 100, fwd, fwd + 100, fwd + 200],
                "biv": [0.29] * 5,
                "aiv": [0.31] * 5,
            }
        )
        for fwd in fwds
    ]


@pytest.fixture()
def patch_create_strike_df(monkeypatch: MonkeyPatch):
    create_strike_mock = MagicMock()
    monkeypatch.setattr("api.retrieve_data.grab", create_strike_mock)
    return create_strike_mock


def test_svi_parameter_calc_excluding_some_params(
    monkeypatch: MonkeyPatch,
    base_df: pd.DataFrame,
    queries: List[CalibrationQuery],
):
    def create_strike_mock(q: CalibrationQuery):
        strike_df = pd.DataFrame()

        strike_df["strike"] = q["strikes"]
        strike_df["bid_price"] = q["biv"]
        strike_df["ask_price"] = q["aiv"]
        strike_df["mid_price"] = [0] * len(q["strikes"])
        strike_df["ask_vol"] = 0
        strike_df["bid_vol"] = 0
        strike_df["mid_vol"] = 0
        strike_df["call_delta"] = [0.1, 0.2, 0.5, 0.8, 0.9]
        return strike_df

    def create_calculate_calibration_metrics_mock(
        strike_df: pd.DataFrame,
        calib_indexes: pd.Index,
        query: CalibrationQuery,
        method_used: str,
        include_metrics_by_delta_region: bool = False,
    ):
        return {"R2_full_strike_range": 0.9}

    monkeypatch.setattr("svi_calc.create_strike_df", create_strike_mock)

    def mock_get_calib_indexes(
        strike_df: pd.DataFrame,
        query: CalibrationQuery,
        use_extended_pillars: bool = False,
        filter_first_strikes_for_convexity: bool = False,
    ):
        if (
            query == queries[1] or query == queries[2]
        ):  # Mock returning less than 3 so continues
            return []
        else:
            return [1, 2, 3, 4]

    monkeypatch.setattr("svi_calc.get_calib_indexes", mock_get_calib_indexes)
    monkeypatch.setattr(
        "svi_calc.calculate_calibration_metrics",
        create_calculate_calibration_metrics_mock,
    )

    def mock_svi(
        strike_df: pd.DataFrame,
        calib_indexes: pd.Index,
        query: CalibrationQuery,
        method: str,
        in_spread_calibration: bool,
        ref_params: Optional[Dict[str, float]] = None,
        with_inverse_spread_weights: bool = False,
    ):
        return {
            "svi_parameters": {
                "expiry": query["expiry"],
                "svi_a": 0.1,
                "svi_b": 0,
                "svi_rho": 0,
                "svi_m": 0,
                "svi_sigma": 0,
            },
            "method_used": "none",
            "success": 1,
        }

    monkeypatch.setattr("svi_calc._calibrate_svi", mock_svi)
    monkeypatch.setattr("svi_calc.svi_atm_var", MagicMock(return_value=[0]))
    queries = sorted(queries, key=lambda d: d["expiry"])
    base_df["expiry"] = pd.DataFrame(queries)["expiry"]
    base_df.set_index("expiry", inplace=True)
    base_df.sort_index(ascending=True, inplace=True)

    result = svi_parameter_calc(base_df, queries)

    assert result["underlying_index"].equals(pd.Series(["A", "D", "E"]))
    assert result["forward"].equals(pd.Series([1005, 1008, 1009]))
    assert result["expiry"].equals(
        pd.Series([queries[0]["expiry"], queries[3]["expiry"], queries[4]["expiry"]])
    )

    def create_calculate_calibration_metrics_mock_small_R2(
        strike_df: pd.DataFrame,
        calib_indexes: pd.Index,
        query: CalibrationQuery,
        method_used: str,
        include_metrics_by_delta_region: bool = False,
    ):
        if query["expiry"] == queries[3]["expiry"]:
            return {"R2_full_strike_range": 0.8}
        else:
            return {"R2_full_strike_range": 0.4}

    monkeypatch.setattr(
        "svi_calc.calculate_calibration_metrics",
        create_calculate_calibration_metrics_mock_small_R2,
    )
    result = svi_parameter_calc(base_df, queries)
    assert result["underlying_index"].equals(pd.Series(["D"]))
    assert result["forward"].equals(pd.Series([1008]))
    assert result["expiry"].equals(pd.Series([queries[3]["expiry"]]))


def test_spline_neumann():
    x = np.linspace(1, 10, 100)
    y = [t**4 for t in x]
    spl = _spline_neumann(x, y, 1.0)
    test_vals = [spl(x_test).item() for x_test in np.linspace(3, 7, 25)]
    expected_vals = [
        80.99999999560217,
        100.5559719997036,
        123.4563174906794,
        150.06248718908842,
        180.75358771348073,
        215.92712151128174,
        255.99999999563644,
        301.40771254559166,
        352.60432650636824,
        410.06248718908853,
        474.27224536445834,
        545.7420476320599,
        624.999999995602,
        712.5927864248813,
        809.0856688555515,
        915.0624871891557,
        1031.1242363485965,
        1157.8903070860142,
        1295.9999999955892,
        1446.1111936375416,
        1608.9003445379562,
        1785.062487189086,
        1975.309560666164,
        2180.3718998734894,
        2400.999999995673,
    ]
    assert np.allclose(test_vals, expected_vals, atol=1e-6)


def test_svi_parameter_calc(
    sorted_queries: List[CalibrationQuery], sorted_base_df: pd.DataFrame
):
    base_df = sorted_base_df
    queries = sorted_queries
    res = svi_parameter_calc(base_df, queries)
    expected_res = {
        "expiry": [0.027, 0.046, 0.065, 0.142],
        "svi_a": [0.00237342, 0.00089383, 0.00186579, 0.00781307],
        "svi_b": [0.04072746, 0.06089445, 0.07276188, 0.12660285],
        "svi_rho": [-0.4804231, -0.18989715, -0.21635704, -0.17326227],
        "svi_m": [-0.02428728, 0.00578462, 0.01846431, 0.04846188],
        "svi_sigma": [0.0533299, 0.10620441, 0.13061499, 0.19628085],
    }
    for key in expected_res.keys():
        assert np.allclose(expected_res[key], list(res[key]), atol=1e-4)


def test_svi_parameter_calc_2(
    sorted_queries2: List[CalibrationQuery], sorted_base_df2: pd.DataFrame
):
    base_df = sorted_base_df2
    queries = sorted_queries2
    res = svi_parameter_calc(base_df, queries)
    expected_res_2 = {
        "expiry": [0.0007990867579908676, 0.09121004566210046, 0.4747716894977169],
        "svi_a": [1.906588116448157e-05, 0.011196408417592316, 0.03730684724917154],
        "svi_b": [0.008735011825345675, 0.09422821891890944, 0.3197027786174951],
        "svi_rho": [-0.022101557468974, -0.2935854243962203, -0.06766221672057923],
        "svi_m": [9.652960410301862e-05, -0.0613535522380427, -0.023332758407496174],
        "svi_sigma": [0.0015741683922737277, 0.2712255705335705, 0.5707117497483373],
    }
    for key in expected_res_2.keys():
        assert np.allclose(expected_res_2[key], list(res[key]), atol=1e-4)


def test_svi_parameter_calc_with_data_extension(
    sorted_queries: List[CalibrationQuery],
):
    queries = sorted_queries
    assert (
        max(create_strike_df(queries[0])["call_delta"]) < 0.9
        and min(create_strike_df(queries[0])["call_delta"]) > 0.1
    )
    assert len((queries[0]["strikes"])) == 7.0

    new_query = _extend_data_spline(queries[0])
    new_strike_df = create_strike_df(new_query)
    # first query has only 0.14-0.89 delta available. so initially its length will be 7 and after extension data it will have 0.01 - 0.99 delta
    expected_strikes = [
        11384.722403373866,
        13835.005795598154,
        14938.371321045894,
        15000.0,
        15500.0,
        16000.0,
        16500.0,
        17000.0,
        17500.0,
        18000.0,
        18438.32899110561,
        19111.265851420063,
        20666.94123915347,
    ]
    assert np.allclose(
        sorted(new_query["strikes"]), sorted(expected_strikes), atol=1e-4
    )
    expected_deltas = [
        0.9898773356325801,
        0.9498771706459642,
        0.8998775802693264,
        0.8961625931829301,
        0.8368491584086399,
        0.863407128438468,
        0.5798369759345272,
        0.4009006742062807,
        0.24795594892817902,
        0.1489223194474503,
        0.09999993200597303,
        0.05000017747898751,
        0.010000137737902896,
    ]
    assert np.allclose(
        sorted(list(new_strike_df["call_delta"])), sorted(expected_deltas), atol=1e-4
    )
    # second queries and so on should not have any data extension
    for index in [1, 2, 3]:
        new_query = _extend_data_spline(queries[index])
        new_strike_df = create_strike_df(new_query)
        # first query has only 0.14-0.89 delta available. so initially its length will be 7 and after extension data it will have 0.01 - 0.99 delta
        assert (
            max(new_strike_df["call_delta"]) > 0.9
            and min(new_strike_df["call_delta"]) < 0.1
        )
        lhs = new_query["strikes"]
        rhs = queries[index]["strikes"]
        assert lhs == rhs
