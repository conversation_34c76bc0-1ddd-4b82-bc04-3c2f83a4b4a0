import logging
from typing import cast

import numpy as np
import pandas as pd
import pytest
from constants import LOOKBACK_COLUMN_NAME, SPIKED_PARAMS_COLUMN_NAME
from lambda_types import VolSurfaceCalcType
from pandas._testing import assert_frame_equal
from utils.arbitrary_lookback import (
    identify_and_populate_arbitrary_values_for_smoothing,
)


@pytest.fixture
def input_fixture() -> dict[str, pd.DataFrame]:
    # fmt: off
    current_result = [
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.01095890411, 'forward': 63323.75, 'svi_rho': -0.3792102010621424, 'svi_a': 0.0007657525805969175, 'spot': 63235.496235239676, 'svi_b': 0.0225280083034741, 'svi_sigma': 0.054003418599337924, 'atm_vol': 0.415965608949725, 'contains_recalib_spike': np.nan, 'svi_m': -0.01687717892074818, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'from_lookback': False, '61000strike': 0.4523415376074195, '66000strike': 0.4333235785228331,
         '696969strike': 1.775799527726476}, # arb_values with no lookback
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, '100money': 0.41596560894192564, '80money': 0.8179096800280996, '120money': 0.5821192654059997,
         '277money': 1.180890526689011}, # arb_values with no lookback
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False,
         "3d5delta": 0.486326312234048},  # arb_values with no lookback

        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.002739726027, 'forward': 63257.54813861788, 'svi_rho': 0.148439952971213, 'svi_a': 0.00023317104820170384, 'spot': 63235.496235239676, 'svi_b': 0.009490116334020892, 'svi_sigma': 0.02171158944581066, 'atm_vol': 0.41478540034441846, 'contains_recalib_spike': np.nan, 'svi_m': 0.017100257365180446, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'from_lookback': False, '61000strike': 0.507382104921187, '66000strike': 0.4623033929377359, '696969strike': 3.0923962106695506},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, '277money': 2.0176582415844933, '100money': 0.4147854003744904, '80money': 0.8928320107277017, '120money': 0.8644593729997618},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, "3d5delta": 0.1}
    ]

    lookback_data  = [
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.010962709285, 'forward': 63323.75, 'svi_rho': -0.4141646328901876, 'svi_a': 0.000801429377357313, 'spot': 63239.422231016244, 'svi_b': 0.022112415347691806, 'svi_sigma': 0.05084441366960688, 'atm_vol': 0.4085247173736632, 'contains_recalib_spike': False, 'svi_m': -0.01781071505957095, 'expiry_str': '2024-07-05T08:00:00Z', 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.010960806697, 'forward': 63316.25, 'svi_rho': -0.42561767551672347, 'svi_a': 0.0008402853772529233, 'spot': 63233.14128656461, 'svi_b': 0.02193943888749022, 'svi_sigma': 0.05014943608742622, 'atm_vol': 0.40964969107003174, 'contains_recalib_spike': False, 'svi_m': -0.01904590941394013, 'expiry_str': '2024-07-05T08:00:00Z', 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'from_lookback': True, '61000strike': 0.4464901384463531, '66000strike': 0.425509804408031, '696969strike': np.nan},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'from_lookback': True, '61000strike': 0.4460811593833499, '66000strike': 0.4271468437940875, '696969strike': np.nan},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True, '277money': np.nan, '100money': 0.40852471736671503, '80money': 0.8193384801225349, '120money': 0.5678184451309949},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True, '277money': np.nan, '100money': 0.40964969107205124, '80money': 0.819278950230451, '120money': 0.5661150240538926},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile','qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820680000000000,'expiry': 0.010962709284627397, 'from_lookback': True, "3d5delta": np.nan},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile','qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'timestamp': 1719820740000000000,'expiry': 0.01096080669710685, 'from_lookback': True,  "3d5delta": np.nan},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202, 'forward': 63260.51558484025, 'svi_rho': -0.17923184376835205, 'svi_a': 0.0002940442056225631, 'spot': 63239.422231016244, 'svi_b': 0.007977080284092004, 'svi_sigma': 0.018951541515816336, 'atm_vol': 0.4040888376438661, 'contains_recalib_spike': False, 'svi_m': 0.0015701873900868897, 'expiry_str': '2024-07-02T08:00:00Z', 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.002741628615, 'forward': 63253.91904832017, 'svi_rho': 0.07995049465032875, 'svi_a': 0.0001904591996133537, 'spot': 63233.14128656461, 'svi_b': 0.009565467141848387, 'svi_sigma': 0.0262434454540807, 'atm_vol': 0.4115472950692524, 'contains_recalib_spike': False, 'svi_m': 0.014022198579339746, 'expiry_str': '2024-07-02T08:00:00Z', 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'from_lookback': True, '61000strike': 0.5003152563396541, '66000strike': 0.4655825996461777, '696969strike': np.nan},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'from_lookback': True, '61000strike': 0.503387478354699, '66000strike': 0.4609993652643708, '696969strike': np.nan},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True, '100money': 0.40408883767592413, '80money': 0.9380722021577146, '120money': 0.7358087054906611},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True, '100money': 0.4115472950629692, '80money': 0.9142365366187051, '120money': 0.8430322921524035},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True, "3d5delta": np.nan},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True,  "3d5delta": np.nan},
    ]
    # fmt: on

    return {
        "current_result": pd.DataFrame(current_result),
        "lookback_data": pd.DataFrame(lookback_data),
    }


def test_identify_arbitrary_values_for_smoothing(
    input_fixture: dict[str, pd.DataFrame], caplog: pytest.LogCaptureFixture
) -> None:
    """ """

    # fmt: off
    expected_result = [
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.01095890411, 'forward': 63323.75, 'svi_rho': -0.3792102010621424, 'svi_a': 0.0007657525805969175, 'spot': 63235.496235239676, 'svi_b': 0.0225280083034741, 'svi_sigma': 0.054003418599337924, 'atm_vol': 0.415965608949725, 'svi_m': -0.01687717892074818, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820800000000000, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4523415376074195, '66000strike': 0.4333235785228331, '696969strike': 1.775799527726476},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '277money': 1.180890526689011, '100money': 0.41596560894192564, '80money': 0.8179096800280996, '120money': 0.5821192654059997},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '3d5delta': 0.486326312234048},

        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.002739726027, 'forward': 63257.54813861788, 'svi_rho': 0.148439952971213, 'svi_a': 0.00023317104820170384, 'spot': 63235.496235239676, 'svi_b': 0.009490116334020892, 'svi_sigma': 0.02171158944581066, 'atm_vol': 0.41478540034441846, 'svi_m': 0.017100257365180446, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820800000000000, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.507382104921187, '66000strike': 0.4623033929377359, '696969strike': 3.0923962106695506},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '277money': 2.0176582415844933, '100money': 0.4147854003744904, '80money': 0.8928320107277017, '120money': 0.8644593729997618},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '3d5delta': 0.1},

        # lookback results - arb_cols should be populated
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.010962709285, 'forward': 63323.75, 'svi_rho': -0.4141646328901876, 'svi_a': 0.000801429377357313, 'spot': 63239.422231016244, 'svi_b': 0.022112415347691806, 'svi_sigma': 0.05084441366960688, 'atm_vol': 0.4085247173736632, 'contains_recalib_spike': False, 'svi_m': -0.01781071505957095, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'expiry_str': '2024-07-05T08:00:00Z'},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.010960806697, 'forward': 63316.25, 'svi_rho': -0.42561767551672347, 'svi_a': 0.0008402853772529233, 'spot': 63233.14128656461, 'svi_b': 0.02193943888749022, 'svi_sigma': 0.05014943608742622, 'atm_vol': 0.40964969107003174, 'contains_recalib_spike': False, 'svi_m': -0.01904590941394013, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'expiry_str': '2024-07-05T08:00:00Z'},
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820680000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4464901384463531, '66000strike': 0.425509804408031,
            '696969strike': 1.711557005852037}, # populated arb_values
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820740000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4460811593833499, '66000strike': 0.4271468437940875,
            '696969strike': 1.690345229269062}, # populated arb_values
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '100money': 0.40852471736671503, '80money': 0.8193384801225349, '120money': 0.5678184451309949,
            '277money': 1.1404383593600842}, # populated arb_values
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '100money': 0.40964969107205124, '80money': 0.819278950230451, '120money': 0.5661150240538926,
            '277money': 1.1279853702794376}, # populated arb_values
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m',
         '3d5delta': 0.47395927535140714}, # populated arb_values
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m',
         '3d5delta': 0.47462137796023446}, # populated arb_values

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202, 'forward': 63260.51558484025, 'svi_rho': -0.17923184376835205, 'svi_a': 0.0002940442056225631, 'spot': 63239.422231016244, 'svi_b': 0.007977080284092004, 'svi_sigma': 0.018951541515816336, 'atm_vol': 0.4040888376438661, 'contains_recalib_spike': False, 'svi_m': 0.0015701873900868897, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'expiry_str': '2024-07-02T08:00:00Z'},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.002741628615, 'forward': 63253.91904832017, 'svi_rho': 0.07995049465032875, 'svi_a': 0.0001904591996133537, 'spot': 63233.14128656461, 'svi_b': 0.009565467141848387, 'svi_sigma': 0.0262434454540807, 'atm_vol': 0.4115472950692524, 'contains_recalib_spike': False, 'svi_m': 0.014022198579339746, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'expiry_str': '2024-07-02T08:00:00Z'},
        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820680000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.5003152563396541, '66000strike': 0.4655825996461777,
         '696969strike': 2.414521709925129}, # populated arb_values
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820740000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.503387478354699, '66000strike': 0.4609993652643708,
         '696969strike': 3.009746323826699}, # populated arb_values
        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '100money': 0.40408883767592413, '80money': 0.9380722021577146, '120money': 0.7358087054906611,
            '277money': 1.5922888950965868}, # populated arb_values
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '100money': 0.4115472950629692, '80money': 0.9142365366187051, '120money': 0.8430322921524035,
            '277money': 1.963864355802979}, # populated arb_values
        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m',
            '3d5delta': 0.47167553458171424}, # populated arb_values
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m',
            '3d5delta': 0.46780109380515156}, # populated arb_values

    ]
    # fmt: on
    with caplog.at_level(logging.INFO):
        lookback_result = identify_and_populate_arbitrary_values_for_smoothing(
            current_and_lookback_results=pd.concat(
                [
                    input_fixture["current_result"],
                    input_fixture["lookback_data"],
                ],
                ignore_index=True,
            ),
            current_result=input_fixture["current_result"],
            calc_types=[
                "moneyness",
                "strike",
                "smile",
            ],
            scheduled_version="",
        )

    assert_frame_equal(
        lookback_result.sort_index(axis=1),
        pd.DataFrame(expected_result).sort_index(axis=1),
    )
    assert "Arbitrary Lookback Summary Info" in caplog.text


def test_identify_arbitrary_values_for_smoothing_spiked_params(
    input_fixture: dict[str, pd.DataFrame], caplog: pytest.LogCaptureFixture
) -> None:
    """ """

    # fmt: off
    expected_result = [
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.01095890411, 'forward': 63323.75, 'svi_rho': -0.3792102010621424, 'svi_a': 0.0007657525805969175, 'spot': 63235.496235239676, 'svi_b': 0.0225280083034741, 'svi_sigma': 0.054003418599337924, 'atm_vol': 0.415965608949725, 'svi_m': -0.01687717892074818, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m'},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820800000000000, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4523415376074195, '66000strike': 0.4333235785228331, '696969strike': 1.775799527726476},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '277money': 1.180890526689011, '100money': 0.41596560894192564, '80money': 0.8179096800280996, '120money': 0.5821192654059997},
        {'tenor_days': 4.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820800000000000, 'expiry': 0.010958904109589041, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '3d5delta': 0.486326312234048},

        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820800000000000, 'expiry': 0.002739726027, 'forward': 63257.54813861788, 'svi_rho': 0.148439952971213, 'svi_a': 0.00023317104820170384, 'spot': 63235.496235239676, 'svi_b': 0.009490116334020892, 'svi_sigma': 0.02171158944581066, 'atm_vol': 0.41478540034441846, 'svi_m': 0.017100257365180446, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m'},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820800000000000, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.507382104921187, '66000strike': 0.4623033929377359, '696969strike': 3.0923962106695506},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '277money': 2.0176582415844933, '100money': 0.4147854003744904, '80money': 0.8928320107277017, '120money': 0.8644593729997618},
        {'tenor_days': 1.0, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820800000000000, 'expiry': 0.0027397260273972603, 'from_lookback': False, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '3d5delta': 0.1},

        # lookback results - arb_cols should be populated for one timestamp only
        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.010962709285, 'forward': 63323.75, 'svi_rho': -0.4141646328901876, 'svi_a': 0.000801429377357313, 'spot': 63239.422231016244, 'svi_b': 0.022112415347691806, 'svi_sigma': 0.05084441366960688, 'atm_vol': 0.4085247173736632, 'contains_recalib_spike': True, 'svi_m': -0.01781071505957095, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'expiry_str': '2024-07-05T08:00:00Z'},
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.010960806697, 'forward': 63316.25, 'svi_rho': -0.42561767551672347, 'svi_a': 0.0008402853772529233, 'spot': 63233.14128656461, 'svi_b': 0.02193943888749022, 'svi_sigma': 0.05014943608742622, 'atm_vol': 0.40964969107003174, 'contains_recalib_spike': False, 'svi_m': -0.01904590941394013, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', 'expiry_str': '2024-07-05T08:00:00Z'},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820680000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4464901384463531, '66000strike': 0.425509804408031,
            '696969strike': np.nan}, # lookback data not populated
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.strike', 'timestamp': 1719820740000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '61000strike': 0.4460811593833499, '66000strike': 0.4271468437940875,
            '696969strike': 1.690345229269062},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '100money': 0.40852471736671503, '80money': 0.8193384801225349, '120money': 0.5678184451309949,
            '277money': np.nan}, # lookback data not populated
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.moneyness', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m', '100money': 0.40964969107205124, '80money': 0.819278950230451, '120money': 0.5661150240538926,
            '277money': 1.1279853702794376},

        {'tenor_days': 4.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820680000000000, 'expiry': 0.010962709284627397, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m',
            '3d5delta': np.nan}, # lookback data not populated
        {'tenor_days': 4.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m.smile', 'timestamp': 1719820740000000000, 'expiry': 0.01096080669710685, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-05T08:00:00Z.1m',
            '3d5delta': 0.47462137796023446},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202, 'forward': 63260.51558484025, 'svi_rho': -0.17923184376835205, 'svi_a': 0.0002940442056225631, 'spot': 63239.422231016244, 'svi_b': 0.007977080284092004, 'svi_sigma': 0.018951541515816336, 'atm_vol': 0.4040888376438661, 'contains_recalib_spike': True, 'svi_m': 0.0015701873900868897, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'expiry_str': '2024-07-02T08:00:00Z'},
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.params', 'timestamp': 1719820740000000000, 'expiry': 0.002741628615, 'forward': 63253.91904832017, 'svi_rho': 0.07995049465032875, 'svi_a': 0.0001904591996133537, 'spot': 63233.14128656461, 'svi_b': 0.009565467141848387, 'svi_sigma': 0.0262434454540807, 'atm_vol': 0.4115472950692524, 'contains_recalib_spike': False, 'svi_m': 0.014022198579339746, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', 'expiry_str': '2024-07-02T08:00:00Z'},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820680000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.5003152563396541, '66000strike': 0.4655825996461777,
            '696969strike': np.nan}, # lookback data not populated
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.strike', 'timestamp': 1719820740000000000, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '61000strike': 0.503387478354699, '66000strike': 0.4609993652643708,
            '696969strike': 3.009746323826699},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '100money': 0.40408883767592413, '80money': 0.9380722021577146, '120money': 0.7358087054906611,
            '277money': np.nan}, # lookback data not populated
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.moneyness', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True, 'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m', '100money': 0.4115472950629692, '80money': 0.9142365366187051, '120money': 0.8430322921524035,
            '277money': 1.963864355802979},

        {'tenor_days': 1.001388888889, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820680000000000, 'expiry': 0.002743531202435616, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m',
            '3d5delta': np.nan}, # lookback data not populated
        {'tenor_days': 1.000694444444, 'qualified_name': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m.smile', 'timestamp': 1719820740000000000, 'expiry': 0.0027416286149150684, 'from_lookback': True,'qualified_name_prefix': 'deribit.option.BTC.SVI.2024-07-02T08:00:00Z.1m',
            '3d5delta': 0.46780109380515156},

    ]
    # fmt: on

    input_data = pd.concat(
        [
            input_fixture["current_result"],
            input_fixture["lookback_data"],
        ],
        ignore_index=True,
    )
    input_data.loc[
        input_data["qualified_name"].str.contains("params")
        & input_data[LOOKBACK_COLUMN_NAME]
        & input_data["timestamp"].eq(1719820680000000000),
        SPIKED_PARAMS_COLUMN_NAME,
    ] = True

    calc_types = cast(
        list[VolSurfaceCalcType], ["moneyness", "strike", "smile"]
    )
    with caplog.at_level(logging.INFO):
        lookback_result = identify_and_populate_arbitrary_values_for_smoothing(
            current_and_lookback_results=input_data,
            current_result=input_fixture["current_result"],
            calc_types=calc_types,
            scheduled_version="",
        )

    unique_qns = input_data["qualified_name"].unique()
    non_params_qns = [name for name in unique_qns if "params" not in name]

    info_messages = [
        record.message
        for record in caplog.records
        if record.levelno == logging.INFO
    ]

    assert len(info_messages) == len(calc_types)
    assert all(qn.rstrip(".")[0] in caplog.text for qn in non_params_qns)
    assert_frame_equal(
        lookback_result.sort_index(axis=1),
        pd.DataFrame(expected_result).sort_index(axis=1),
    )
