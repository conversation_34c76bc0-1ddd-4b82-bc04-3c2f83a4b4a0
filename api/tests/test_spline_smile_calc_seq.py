from typing import Generator
from unittest.mock import patch

import numpy as np
import pandas as pd
import pytest
from spline_smile_calc_seq import (
    _extrapolate_call_wing_if_needed,
    _extrapolate_put_wing_if_needed,
)

from .helpers import compare_dataframes_row_by_row


class TestSplineSmileCalcSeq:
    # fmt: off
    @pytest.fixture(autouse=True)
    def setup(self) -> Generator[None, None, None]:
        self.strikes = [2861.728043837481, 2987.44087547516, 3059.7506633079606, 3106.9058458380227, 3141.2936245157057, 3242.0425238043513, 3297.2137399000476, 3336.117119984305, 3367.2352862045163, 3394.1228294570956, 3418.732247601005, 3442.032551457587, 3464.68515159531, 3487.1616342347834, 3487.856954962455, 3510.6822461411616, 3534.430033206204, 3559.751177555023, 3587.537113059512, 3619.2058084760138, 3657.1083762342, 3705.8308938919977, 3778.0274796752287, 3918.806750976937, 3970.495560911182, 4037.9113933690137, 4135.474086061929, 4321.310259753039]
        self.vols = [1.1473725166674236, 0.9939990083011018, 0.9112280751456445, 0.8630598992436357, 0.8295431205647903, 0.7324525940491252, 0.6882632411670032, 0.6645219265876783, 0.6516641616573009, 0.6449870787270279, 0.6412169796592415, 0.6395634161305709, 0.6391855133996753, 0.640244228714853, 0.6403024488939066, 0.6433013746801919, 0.6486709758905334, 0.6567797598663975, 0.668432929383751, 0.6837713012363499, 0.7037441574719401, 0.734225249654599, 0.7868868111478565, 0.8952998899097611, 0.9384392353072255, 0.9937927057425036, 1.0730506678933074, 1.226960438589845]
        self.spot = 3477.8134899890288
        self.forward = 3482.868122605276
        self.expiry = 0.00547945205479
        self.rd = 0.0
        yield
        del self.strikes
        del self.vols

    # fmt: on
    def test_spline_smile_calc_seq(self) -> None:
        expected_output = {
            "strikes": [
                2861.728043837481,
                2987.44087547516,
                3059.7506633079606,
                3106.9058458380227,
                3141.2936245157057,
                3242.0425238043513,
                3297.2137399000476,
                3336.117119984305,
                3367.2352862045163,
                3394.1228294570956,
                3418.732247601005,
                3442.032551457587,
                3464.68515159531,
                3487.1616342347834,
                3487.856954962455,
                3510.6822461411616,
                3534.430033206204,
                3559.751177555023,
                3587.537113059512,
                3619.2058084760138,
                3657.1083762342,
                3705.8308938919977,
                3778.0274796752287,
                3918.806750976937,
                3970.495560911182,
                4037.9113933690137,
                4135.474086061929,
                4321.310259753039,
                4507.146433444149,
                4878.818780826369,
            ],
            "vols": [
                1.1473725166674236,
                0.9939990083011018,
                0.9112280751456445,
                0.8630598992436357,
                0.8295431205647903,
                0.7324525940491252,
                0.6882632411670032,
                0.6645219265876783,
                0.6516641616573009,
                0.6449870787270279,
                0.6412169796592415,
                0.6395634161305709,
                0.6391855133996753,
                0.640244228714853,
                0.6403024488939066,
                0.6433013746801919,
                0.6486709758905334,
                0.6567797598663975,
                0.668432929383751,
                0.6837713012363499,
                0.7037441574719401,
                0.734225249654599,
                0.7868868111478565,
                0.8952998899097611,
                0.9384392353072255,
                0.9937927057425036,
                1.0730506678933074,
                1.226960438589845,
                1.3581305798038903,
                1.5756425409903108,
            ],
        }
        with patch("logging.exception") as mock_log:
            test_result = _extrapolate_call_wing_if_needed(
                strikes=self.strikes,
                vols=self.vols,
                spot=self.spot,
                forward=self.forward,
                exp=self.expiry,
                rd=self.rd,
            )

        mock_log.assert_not_called()
        compare_dataframes_row_by_row(
            pd.DataFrame([expected_output["strikes"]]),
            pd.DataFrame([test_result[0]]),
        )

        compare_dataframes_row_by_row(
            pd.DataFrame([expected_output["vols"]]),
            pd.DataFrame([test_result[1]]),
        )

    def test_extrapolate_put_wing_if_needed(self) -> None:
        expected_min_strikes = [
            2575.55,  # Approximate extrapolated strikes below original min
            2819.185,
        ]
        expected_min_vols = [
            1.456,  # Approximate extrapolated vols, based on slope
            1.163,
        ]

        # Trim original arrays to only two lowest strikes for test
        base_strikes = self.strikes[:2].copy()
        base_vols = self.vols[:2].copy()

        with patch("logging.exception") as mock_log:
            result_strikes, result_vols = _extrapolate_put_wing_if_needed(
                strikes=base_strikes.copy(),
                vols=base_vols.copy(),
                spot=self.spot,
                forward=self.forward,
                exp=self.expiry,
                rd=self.rd,
            )

        mock_log.assert_not_called()

        # Check that new strikes are added at the beginning
        assert result_strikes[0] < base_strikes[0]
        assert len(result_strikes) > len(base_strikes)

        # Check that the original strikes are preserved in order at the tail
        assert result_strikes[-2:] == base_strikes

        # Check increasing vols toward lower strikes (downward skew)
        assert result_vols[0] > result_vols[1] > result_vols[2]

        # Check closeness of extrapolated strikes and vols
        for i, (exp_s, res_s) in enumerate(
            zip(expected_min_strikes, result_strikes[:2])
        ):
            assert np.isclose(
                exp_s, res_s, rtol=0.02
            ), f"Strike {i} mismatch: {exp_s} vs {res_s}"

        for i, (exp_v, res_v) in enumerate(
            zip(expected_min_vols, result_vols[:2])
        ):
            assert np.isclose(
                exp_v, res_v, rtol=0.02
            ), f"Vol {i} mismatch: {exp_v} vs {res_v}"

        # Ensure original lowest strike is present in the result
        assert base_strikes[0] in result_strikes
