from typing import cast

import numpy as np
import pandas as pd
import pytest
import utils_general
from constants import TENOR_TYPES
from lambda_types import (
    DerivedCalcResult,
    ExchangeCurrencyToTimestampTenorExpiryArbFields,
    ExpiryArbFields,
    ParamsCalcResult,
)
from utils.common import is_derived_calc_result, merge_multi_index_lookback
from utils.outputs import handle_tenor_intersections, serialize_columns

from .helpers import compare_dataframes_row_by_row
from .test_types import HandleTenorIntersectionTestData


@pytest.fixture()
def handle_tenor_intersections_data() -> HandleTenorIntersectionTestData:
    # fmt: off
    params_result: ParamsCalcResult = {
        "currency": "BTC",
        "model": "SVI",
        "exchange": "deribit",
        "timestamp": 1716451200000000000,
        "dfs": {
            "params": pd.DataFrame([
                {"expiry": 0.0027397260273972603, 'tenor_days': 1.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:20.000Z'},
                {"expiry": 2*0.0027397260273972603, 'tenor_days': 2.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:20.000Z'},
                {"expiry": 8*0.0027397260273972603, 'tenor_days': 8.0,  'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:20.000Z'},
            ]),
        },
        "df_vol_matrix_pre": pd.DataFrame()
    }
    derived_result: DerivedCalcResult = {
        "currency": "BTC",
        "model": "SVI",
        "exchange": "deribit",
        "timestamp": 1716451200000000000,
        "dfs": {
            "strike": pd.DataFrame([
                {"expiry": 0.0027397260273972603, 'tenor_days': 1.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:28.000Z'},
                {"expiry": 2*0.0027397260273972603, 'tenor_days': 2.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:28.000Z'},
                {"expiry": 8*0.0027397260273972603, 'tenor_days': 8.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:28.000Z'},
            ]),
            "smile": pd.DataFrame([
                {"expiry": 0.0027397260273972603, 'tenor_days': 1.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:28.000Z'},
                {"expiry": 2*0.0027397260273972603, 'tenor_days': 2.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:28.000Z'},
                {"expiry": 8*0.0027397260273972603, 'tenor_days': 8.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z', 'runtime': '2024-06-28T10:33:28.000Z'},
            ]),
            "moneyness": pd.DataFrame([
                {"expiry": 0.0027397260273972603, 'tenor_days': 1.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z','runtime': '2024-06-28T10:33:28.000Z'},
                {"expiry": 2*0.0027397260273972603, 'tenor_days': 2.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z','runtime': '2024-06-28T10:33:28.000Z'},
                {"expiry": 8*0.0027397260273972603, 'tenor_days': 8.0, 'timestamp': 1716451200000000000, 'isodate': '2024-05-23T08:00:00.000000Z','runtime': '2024-06-28T10:33:28.000Z'},
            ])
        },
    }

    # fmt: on
    exchange_curr_timestamp_to_expiry_info = cast(
        ExchangeCurrencyToTimestampTenorExpiryArbFields,
        {
            "deribit": {
                "BTC": {
                    1716451200000000000: {
                        1.0: ExpiryArbFields(
                            strikes=set(),
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=True,
                            arbitrary_expiry=False,
                            standard_constant_maturity=True,
                            arbitrary_constant_maturity=False,
                            iso_expiry="2024-05-23T08:00:00Z",
                            tenor_str="1d",
                        ),
                        2.0: ExpiryArbFields(
                            strikes=set(),
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=True,
                            arbitrary_expiry=False,
                            standard_constant_maturity=True,
                            arbitrary_constant_maturity=False,
                            iso_expiry="2024-05-24T08:00:00Z",
                            tenor_str="2d",
                        ),
                        8.0: ExpiryArbFields(
                            strikes=set(),
                            deltas=set(),
                            moneyness=set(),
                            listed_expiry=True,
                            arbitrary_expiry=False,
                            standard_constant_maturity=False,
                            arbitrary_constant_maturity=False,
                            iso_expiry="2024-05-30T08:00:00Z",
                            tenor_str="",
                        ),
                    }
                }
            }
        },
    )

    return {
        "calculation_results": [params_result, derived_result],
        "exchange_curr_timestamp_to_expiry_info": exchange_curr_timestamp_to_expiry_info,
    }


@pytest.mark.parametrize("freq", ["1h", "live"])
def test_handle_tenor_intersections(
    freq: str,
    handle_tenor_intersections_data: HandleTenorIntersectionTestData,
) -> None:

    def _assert_expiry_str(
        result_dict: ParamsCalcResult | DerivedCalcResult,
    ) -> None:
        for _, df in result_dict["dfs"].items():
            assert isinstance(df, pd.DataFrame)
            for _, row in df.iterrows():
                tenor = row["tenor_days"]

                qualified_name = row["qualified_name"]
                _, qn_tokens = utils_general.get_qfn_and_version(qualified_name)

                listed_or_arb_listed = (
                    row["listed_expiry"] or row["arbitrary_expiry"]
                )
                standard_or_arb_constant = (
                    row["standard_constant_maturity"]
                    or row["arbitrary_constant_maturity"]
                )

                arb_fields = handle_tenor_intersections_data[
                    "exchange_curr_timestamp_to_expiry_info"
                ][qn_tokens[0]][qn_tokens[2]][row["timestamp"]][tenor]

                if listed_or_arb_listed:
                    assert arb_fields.iso_expiry == qn_tokens[-3]
                if standard_or_arb_constant:
                    assert arb_fields.tenor_str == qn_tokens[-3]
        return

    result = handle_tenor_intersections(
        results=handle_tenor_intersections_data["calculation_results"],
        exchange_curr_timestamp_to_expiry_info=handle_tenor_intersections_data[
            "exchange_curr_timestamp_to_expiry_info"
        ],
        vol_surface_calc_types=["strike", "smile", "moneyness"],
        freq=freq,
        include_listed_expiries=True,
        version="",
    )
    additional_columns = [*[t.value for t in TENOR_TYPES], "qualified_name"]
    for res, inp in zip(
        result, handle_tenor_intersections_data["calculation_results"]
    ):

        for key in ["currency", "model", "exchange", "timestamp"]:
            assert res[key] == inp[key]  # type: ignore

        if is_derived_calc_result(res):
            assert res["dfs"]["strike"]["qualified_name"].is_unique
            qualified_names = res["dfs"]["strike"]["qualified_name"]
            _assert_expiry_str(res)
            if freq != "live":
                for qname in qualified_names:
                    assert "d." not in qname

        for _, df in res["dfs"].items():
            assert isinstance(df, pd.DataFrame)
            _assert_expiry_str(res)
            for col in additional_columns:
                assert col in df.columns


class TestCombineLookback:
    # fmt: off
    def test_same_dataframes(self)-> None:
        old_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6},
        ])

        new_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6},
        ])

        combined = merge_multi_index_lookback(old_lookback, new_lookback, method='combine_first')
        compare_dataframes_row_by_row(combined.reset_index(), old_lookback)

    def test_new_column_non_nan_values_only(self)-> None:
        old_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6},
        ])

        new_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5, 'value3': 9},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6, 'value3': 10},
        ])

        expected = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5, 'value3': 9},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6, 'value3': 10},
        ])

        combined = merge_multi_index_lookback(old_lookback, new_lookback, method='combine_first')
        compare_dataframes_row_by_row(combined.reset_index(), expected)

    def test_new_row_only(self)-> None:
        old_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6},
        ])

        new_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6},
            {'qualified_name': 'name3', 'timestamp': 3, 'value1': 3, 'value2': 7},
        ])

        expected = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6},
            {'qualified_name': 'name3', 'timestamp': 3, 'value1': 3, 'value2': 7},
        ])

        combined = merge_multi_index_lookback(old_lookback, new_lookback, method='combine_first')
        compare_dataframes_row_by_row(combined.reset_index(), expected)

    def test_old_with_nan_new_with_values(self)-> None:
        old_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': np.nan, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': np.nan},
        ])

        new_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6},
        ])

        expected = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6},
        ])

        combined = merge_multi_index_lookback(old_lookback, new_lookback, method='combine_first')
        compare_dataframes_row_by_row(combined.reset_index(), expected)

    def test_combination(self)-> None:
        old_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': np.nan, 'value2': 5},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': np.nan},
        ])

        new_lookback = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5, 'value3': 9},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6, 'value3': 10},
            {'qualified_name': 'name3', 'timestamp': 3, 'value1': 3, 'value2': 7, 'value3': 11},
        ])

        expected = pd.DataFrame([
            {'qualified_name': 'name1', 'timestamp': 1, 'value1': 1, 'value2': 5, 'value3': 9},
            {'qualified_name': 'name2', 'timestamp': 2, 'value1': 2, 'value2': 6, 'value3': 10},
            {'qualified_name': 'name3', 'timestamp': 3, 'value1': 3, 'value2': 7, 'value3': 11},
        ])

        combined = merge_multi_index_lookback(old_lookback, new_lookback, method='combine_first')
        compare_dataframes_row_by_row(combined.reset_index(), expected)
    # fmt: on


def test_serialze_spline_info_column() -> None:
    # fmt: off
    input_df = pd.DataFrame(
        [
            {'forward': 2.203250355419547, 'spline_info':
                [
                    {'constant_coef': 8.740937194666532, 'cube_coef': 0.0, 'linear_coef': -7.9537620385140855, 'square_coef': 0.0, 'x_left': 0.0, 'x_right': 2.05},
                    {'constant_coef': 0.787175156152446, 'cube_coef': -4.772021251776632e-06, 'linear_coef': -0.19399419606132023, 'square_coef': 0.0, 'x_left': 2.05, 'x_right': 2.1},
                    {'constant_coef': 0.593176188069874, 'cube_coef': 0.03817152265140548, 'linear_coef': -0.19400851212507383, 'square_coef': -1.4316063755329646e-05, 'x_left': 2.1, 'x_right': 2.15},
                    {'constant_coef': 0.4373248825324504, 'cube_coef': 0.037179416330293195, 'linear_coef': -0.07952257629836876, 'square_coef': -1.3942088421929768e-05, 'x_left': 2.15, 'x_right': 2.2},
                    {'constant_coef': 0.3949677804759522, 'cube_coef': -0.004119333574592762, 'linear_coef': 0.03198778851566667, 'square_coef': 1.541615443214054e-06, 'x_left': 2.2, 'x_right': 2.25},
                    {'constant_coef': 0.42283777703246933, 'cube_coef': 0.06687977447016198, 'linear_coef': 0.019632871022774816, 'square_coef': -2.5077178065724438e-05, 'x_left': 2.25, 'x_right': 2.3},
                    {'constant_coef': 0.5093253453473404, 'cube_coef': -0.05845308741628964, 'linear_coef': 0.2202220400771313, 'square_coef': 2.1898884688341346e-05, 'x_left': 2.3, 'x_right': 2.35},
                    {'constant_coef': 0.6711161968928713, 'cube_coef': 0.22436064748269613, 'linear_coef': 0.08981315119527819, 'square_coef': -0.0006727389685637306, 'x_left': 2.35, 'x_right': 2.4500000000000006},
                    {'constant_coef': 0.9846172566022825, 'cube_coef': -0.22887654357755566, 'linear_coef': 0.3807748078531196, 'square_coef': 8.57434710921286e-05, 'x_left': 2.4500000000000006, 'x_right': 2.500000000000001},
                    {'constant_coef': 1.1366012643489394, 'cube_coef': 0.9091493275321961, 'linear_coef': -0.6113666718747264, 'square_coef': -0.0027274479825966317, 'x_left': 2.500000000000001, 'x_right': 2.6000000000000014},
                    {'constant_coef': 1.4316564720238154, 'cube_coef': 0.0, 'linear_coef': 54.87628678367313, 'square_coef': 0.0, 'x_left': 2.6000000000000014, 'x_right': 5.200000000000003}
                ]
            },
            {'forward': 2.203195880483028, 'spline_info':
                [
                    {'constant_coef': 8.445334090306712, 'cube_coef': 0.0, 'linear_coef': -6.921735471521239, 'square_coef': 0.0, 'x_left': 0.0, 'x_right': 1.847599804683305},
                    {'constant_coef': 1.5235986187854735, 'cube_coef': -1.5792683421806408e-10, 'linear_coef': -0.22673085596018033, 'square_coef': 0.0, 'x_left': 1.847599804683305, 'x_right': 1.9081204482287368},
                    {'constant_coef': 1.2968677626673664, 'cube_coef': 1.1808575351978584e-06, 'linear_coef': -0.13680972934287208, 'square_coef': -1.7250031592608694e-10, 'x_left': 1.9081204482287368, 'x_right': 1.9446386927823562},
                    {'constant_coef': 1.1600592140095292, 'cube_coef': -4.528350136819e-06, 'linear_coef': -0.0987436842130976, 'square_coef': 2.487643865466252e-10, 'x_left': 1.9446386927823562, 'x_right': 1.9709967551204364},
                    {'constant_coef': 1.0613110016950593, 'cube_coef': 9.79649943995382e-06, 'linear_coef': -0.0774471980672376, 'square_coef': -2.5951787850383827e-10, 'x_left': 1.9709967551204364, 'x_right': 1.9916672141162517},
                    {'constant_coef': 0.9838735998677437, 'cube_coef': -6.129120097014526e-05, 'linear_coef': -0.24386530515009539, 'square_coef': 5.052480463544616e-08, 'x_left': 1.9916672141162517, 'x_right': 2.0567789480957064},
                    {'constant_coef': 0.7399470540414828, 'cube_coef': 0.0018518881283787572, 'linear_coef': -0.13393578378235074, 'square_coef': -2.535024628206177e-07, 'x_left': 2.0567789480957064, 'x_right': 2.0925127079034977},
                    {'constant_coef': 0.607862904885048, 'cube_coef': 0.0014695387287330189, 'linear_coef': -0.08195709445648999, 'square_coef': -5.233829361507376e-08, 'x_left': 2.0925127079034977, 'x_right': 2.1153248347011893},
                    {'constant_coef': 0.5273752968189974, 'cube_coef': 0.006822174960154847, 'linear_coef': -0.06460042516584975, 'square_coef': -1.4045189818291583e-07, 'x_left': 2.1153248347011893, 'x_right': 2.1343280586682556},
                    {'constant_coef': 0.4695969061614041, 'cube_coef': 0.0043095830273242215, 'linear_coef': -0.03855364754954826, 'square_coef': -5.9143931593357443e-08, 'x_left': 2.1343280586682556, 'x_right': 2.150928425535626},
                    {'constant_coef': 0.4353527824952486, 'cube_coef': 0.0015093974209383194, 'linear_coef': -0.02305025154954384, 'square_coef': -1.50769848768232e-08, 'x_left': 2.150928425535626, 'x_right': 2.165860811171096},
                    {'constant_coef': 0.41381191328965833, 'cube_coef': 0.002312446295577229, 'linear_coef': -0.0171326516058067, 'square_coef': -1.8280425952847584e-08, 'x_left': 2.165860811171096, 'x_right': 2.179673041241313},
                    {'constant_coef': 0.39899168969900284, 'cube_coef': 0.004240499464577931, 'linear_coef': -0.00966568222109346, 'square_coef': -2.8564161025134662e-08, 'x_left': 2.179673041241313, 'x_right': 2.1927677006745308},
                    {'constant_coef': 0.39356647837832615, 'cube_coef': 0.0025893844014323736, 'linear_coef': 0.002986680848833439, 'square_coef': -1.628581658854743e-08, 'x_left': 2.1927677006745308, 'x_right': 2.205566343494195},
                    {'constant_coef': 0.3991425273427751, 'cube_coef': -4.866407895915664e-07, 'linear_coef': 6.7833940308874736e-06, 'square_coef': 7.679898151319022e-22, 'x_left': 2.205566343494195, 'x_right': 2.2055744160037114},
                    {'constant_coef': 0.39914882409601693, 'cube_coef': -0.0009970197775311395, 'linear_coef': 0.008546421510755603, 'square_coef': 6.510553080489244e-09, 'x_left': 2.2055744160037114, 'x_right': 2.2185342049075847},
                    {'constant_coef': 0.40669823233979446, 'cube_coef': 0.0009616075137850248, 'linear_coef': 0.005646608231071608, 'square_coef': -6.593754198619099e-09, 'x_left': 2.2185342049075847, 'x_right': 2.231706825670841},
                    {'constant_coef': 0.41330644149089674, 'cube_coef': -0.0026874255166817384, 'linear_coef': 0.008966412598779906, 'square_coef': 2.1392597426475804e-08, 'x_left': 2.231706825670841, 'x_right': 2.2455510844467104},
                    {'constant_coef': 0.4195854499655926, 'cube_coef': 0.006793516490079455, 'linear_coef': 0.0010236809473701774, 'square_coef': -7.847912007692378e-08, 'x_left': 2.2455510844467104, 'x_right': 2.261225089861909},
                    {'constant_coef': 0.4274025689239221, 'cube_coef': -0.006692756420667468, 'linear_coef': 0.022619764569852423, 'square_coef': 9.124872819026035e-08, 'x_left': 2.261225089861909, 'x_right': 2.277789334655379},
                    {'constant_coef': 0.4433296683218355, 'cube_coef': 0.042784751444588126, 'linear_coef': 0.0028955080137927935, 'square_coef': -8.624390863664136e-07, 'x_left': 2.277789334655379, 'x_right': 2.2966595090896775},
                    {'constant_coef': 0.4890090653411296, 'cube_coef': -0.09047103617724325, 'linear_coef': 0.20380117185950766, 'square_coef': 6.827831901825943e-06, 'x_left': 2.2966595090896775, 'x_right': 2.3259610037028864},
                    {'constant_coef': 0.6023460288552944, 'cube_coef': 0.13156470673351509, 'linear_coef': -0.07373224816078293, 'square_coef': -1.2876700831927798e-05, 'x_left': 2.3259610037028864, 'x_right': 2.3579213589082784},
                    {'constant_coef': 0.6601656107271969, 'cube_coef': -0.7276053700992672, 'linear_coef': 1.190769254077444, 'square_coef': 0.0036375403681625147, 'x_left': 2.3579213589082784, 'x_right': 2.4765038621474664},
                    {'constant_coef': 1.1269670350735355, 'cube_coef': 0.447420861579901, 'linear_coef': -0.5993304861081565, 'square_coef': -0.0005046628369590945, 'x_left': 2.4765038621474664, 'x_right': 2.548672978971852},
                    {'constant_coef': 0.9745527477083261, 'cube_coef': 0.25141957409136634, 'linear_coef': 0.8870291549269574, 'square_coef': -0.000484560409310366, 'x_left': 2.548672978971852, 'x_right': 2.6349570411975782},
                    {'constant_coef': 2.112516916317334, 'cube_coef': 0.027245036585880077, 'linear_coef': 1.9664644464634586, 'square_coef': -8.94527338459775e-05, 'x_left': 2.6349570411975782, 'x_right': 2.7383970219959033},
                    {'constant_coef': 4.106136946632824, 'cube_coef': -0.11967319584932148, 'linear_coef': 4.643254902013228, 'square_coef': 0.004694741429567288, 'x_left': 2.7383970219959033, 'x_right': 2.9729152617066648},
                    {'constant_coef': 8.6344133942263, 'cube_coef': -0.11431424680998906, 'linear_coef': 4.293624797324399, 'square_coef': 0.004963999496579347, 'x_left': 2.9729152617066648, 'x_right': 3.2074335014174262},
                    {'constant_coef': 12.818687944237299, 'cube_coef': -0.5675807694331939, 'linear_coef': 7.921220111775179, 'square_coef': 0.16766576335556876, 'x_left': 3.2074335014174262, 'x_right': 3.676469980838949},
                    {'constant_coef': 20.33999304993485, 'cube_coef': -0.2722907306308796, 'linear_coef': 13.10761866037347, 'square_coef': -0.31140084521721895, 'x_left': 3.676469980838949, 'x_right': 4.614542939681995},
                    {'constant_coef': 32.86392013446021, 'cube_coef': 0.24109131554894525, 'linear_coef': 23.335889556092784, 'square_coef': -4.776421665570883, 'x_left': 4.614542939681995, 'x_right': 6.490688857368086},
                    {'constant_coef': 51.664479340531074, 'cube_coef': 0.0, 'linear_coef': 50.185867640470995, 'square_coef': 0.0, 'x_left': 6.490688857368086, 'x_right': 12.981377714736173}
                ]
             }
        ]
    )
    # fmt: on

    expected_result = [
        # fmt:off
        {'forward': 2.203250355419547, 'spline_info': '[{"constant_coef":8.740937194666532,"cube_coef":0.0,"linear_coef":-7.9537620385140855,"square_coef":0.0,"x_left":0.0,"x_right":2.05},{"constant_coef":0.787175156152446,"cube_coef":-4.772021251776632e-6,"linear_coef":-0.19399419606132023,"square_coef":0.0,"x_left":2.05,"x_right":2.1},{"constant_coef":0.593176188069874,"cube_coef":0.03817152265140548,"linear_coef":-0.19400851212507383,"square_coef":-0.000014316063755329646,"x_left":2.1,"x_right":2.15},{"constant_coef":0.4373248825324504,"cube_coef":0.037179416330293195,"linear_coef":-0.07952257629836876,"square_coef":-0.000013942088421929768,"x_left":2.15,"x_right":2.2},{"constant_coef":0.3949677804759522,"cube_coef":-0.004119333574592762,"linear_coef":0.03198778851566667,"square_coef":1.541615443214054e-6,"x_left":2.2,"x_right":2.25},{"constant_coef":0.42283777703246933,"cube_coef":0.06687977447016198,"linear_coef":0.019632871022774816,"square_coef":-0.000025077178065724438,"x_left":2.25,"x_right":2.3},{"constant_coef":0.5093253453473404,"cube_coef":-0.05845308741628964,"linear_coef":0.2202220400771313,"square_coef":0.000021898884688341346,"x_left":2.3,"x_right":2.35},{"constant_coef":0.6711161968928713,"cube_coef":0.22436064748269613,"linear_coef":0.08981315119527819,"square_coef":-0.0006727389685637306,"x_left":2.35,"x_right":2.4500000000000006},{"constant_coef":0.9846172566022825,"cube_coef":-0.22887654357755566,"linear_coef":0.3807748078531196,"square_coef":0.0000857434710921286,"x_left":2.4500000000000006,"x_right":2.500000000000001},{"constant_coef":1.1366012643489394,"cube_coef":0.9091493275321961,"linear_coef":-0.6113666718747264,"square_coef":-0.0027274479825966317,"x_left":2.500000000000001,"x_right":2.6000000000000014},{"constant_coef":1.4316564720238154,"cube_coef":0.0,"linear_coef":54.87628678367313,"square_coef":0.0,"x_left":2.6000000000000014,"x_right":5.200000000000003}]'},
        {'forward': 2.203195880483028, 'spline_info': '[{"constant_coef":8.445334090306712,"cube_coef":0.0,"linear_coef":-6.921735471521239,"square_coef":0.0,"x_left":0.0,"x_right":1.847599804683305},{"constant_coef":1.5235986187854735,"cube_coef":-1.5792683421806408e-10,"linear_coef":-0.22673085596018033,"square_coef":0.0,"x_left":1.847599804683305,"x_right":1.9081204482287368},{"constant_coef":1.2968677626673664,"cube_coef":1.1808575351978584e-6,"linear_coef":-0.13680972934287208,"square_coef":-1.7250031592608694e-10,"x_left":1.9081204482287368,"x_right":1.9446386927823562},{"constant_coef":1.1600592140095292,"cube_coef":-4.528350136819e-6,"linear_coef":-0.0987436842130976,"square_coef":2.487643865466252e-10,"x_left":1.9446386927823562,"x_right":1.9709967551204364},{"constant_coef":1.0613110016950593,"cube_coef":9.79649943995382e-6,"linear_coef":-0.0774471980672376,"square_coef":-2.5951787850383827e-10,"x_left":1.9709967551204364,"x_right":1.9916672141162517},{"constant_coef":0.9838735998677437,"cube_coef":-0.00006129120097014526,"linear_coef":-0.24386530515009539,"square_coef":5.052480463544616e-8,"x_left":1.9916672141162517,"x_right":2.0567789480957064},{"constant_coef":0.7399470540414828,"cube_coef":0.0018518881283787572,"linear_coef":-0.13393578378235074,"square_coef":-2.535024628206177e-7,"x_left":2.0567789480957064,"x_right":2.0925127079034977},{"constant_coef":0.607862904885048,"cube_coef":0.0014695387287330189,"linear_coef":-0.08195709445648999,"square_coef":-5.233829361507376e-8,"x_left":2.0925127079034977,"x_right":2.1153248347011893},{"constant_coef":0.5273752968189974,"cube_coef":0.006822174960154847,"linear_coef":-0.06460042516584975,"square_coef":-1.4045189818291583e-7,"x_left":2.1153248347011893,"x_right":2.1343280586682556},{"constant_coef":0.4695969061614041,"cube_coef":0.0043095830273242215,"linear_coef":-0.03855364754954826,"square_coef":-5.9143931593357443e-8,"x_left":2.1343280586682556,"x_right":2.150928425535626},{"constant_coef":0.4353527824952486,"cube_coef":0.0015093974209383194,"linear_coef":-0.02305025154954384,"square_coef":-1.50769848768232e-8,"x_left":2.150928425535626,"x_right":2.165860811171096},{"constant_coef":0.41381191328965833,"cube_coef":0.002312446295577229,"linear_coef":-0.0171326516058067,"square_coef":-1.8280425952847584e-8,"x_left":2.165860811171096,"x_right":2.179673041241313},{"constant_coef":0.39899168969900284,"cube_coef":0.004240499464577931,"linear_coef":-0.00966568222109346,"square_coef":-2.8564161025134662e-8,"x_left":2.179673041241313,"x_right":2.1927677006745308},{"constant_coef":0.39356647837832615,"cube_coef":0.0025893844014323736,"linear_coef":0.002986680848833439,"square_coef":-1.628581658854743e-8,"x_left":2.1927677006745308,"x_right":2.205566343494195},{"constant_coef":0.3991425273427751,"cube_coef":-4.866407895915664e-7,"linear_coef":6.7833940308874736e-6,"square_coef":7.679898151319022e-22,"x_left":2.205566343494195,"x_right":2.2055744160037114},{"constant_coef":0.39914882409601693,"cube_coef":-0.0009970197775311395,"linear_coef":0.008546421510755603,"square_coef":6.510553080489244e-9,"x_left":2.2055744160037114,"x_right":2.2185342049075847},{"constant_coef":0.40669823233979446,"cube_coef":0.0009616075137850248,"linear_coef":0.005646608231071608,"square_coef":-6.593754198619099e-9,"x_left":2.2185342049075847,"x_right":2.231706825670841},{"constant_coef":0.41330644149089674,"cube_coef":-0.0026874255166817384,"linear_coef":0.008966412598779906,"square_coef":2.1392597426475804e-8,"x_left":2.231706825670841,"x_right":2.2455510844467104},{"constant_coef":0.4195854499655926,"cube_coef":0.006793516490079455,"linear_coef":0.0010236809473701774,"square_coef":-7.847912007692378e-8,"x_left":2.2455510844467104,"x_right":2.261225089861909},{"constant_coef":0.4274025689239221,"cube_coef":-0.006692756420667468,"linear_coef":0.022619764569852423,"square_coef":9.124872819026035e-8,"x_left":2.261225089861909,"x_right":2.277789334655379},{"constant_coef":0.4433296683218355,"cube_coef":0.042784751444588126,"linear_coef":0.0028955080137927935,"square_coef":-8.624390863664136e-7,"x_left":2.277789334655379,"x_right":2.2966595090896775},{"constant_coef":0.4890090653411296,"cube_coef":-0.09047103617724325,"linear_coef":0.20380117185950766,"square_coef":6.827831901825943e-6,"x_left":2.2966595090896775,"x_right":2.3259610037028864},{"constant_coef":0.6023460288552944,"cube_coef":0.13156470673351509,"linear_coef":-0.07373224816078293,"square_coef":-0.000012876700831927798,"x_left":2.3259610037028864,"x_right":2.3579213589082784},{"constant_coef":0.6601656107271969,"cube_coef":-0.7276053700992672,"linear_coef":1.190769254077444,"square_coef":0.0036375403681625147,"x_left":2.3579213589082784,"x_right":2.4765038621474664},{"constant_coef":1.1269670350735355,"cube_coef":0.447420861579901,"linear_coef":-0.5993304861081565,"square_coef":-0.0005046628369590945,"x_left":2.4765038621474664,"x_right":2.548672978971852},{"constant_coef":0.9745527477083261,"cube_coef":0.25141957409136634,"linear_coef":0.8870291549269574,"square_coef":-0.000484560409310366,"x_left":2.548672978971852,"x_right":2.6349570411975782},{"constant_coef":2.112516916317334,"cube_coef":0.027245036585880077,"linear_coef":1.9664644464634586,"square_coef":-0.0000894527338459775,"x_left":2.6349570411975782,"x_right":2.7383970219959033},{"constant_coef":4.106136946632824,"cube_coef":-0.11967319584932148,"linear_coef":4.643254902013228,"square_coef":0.004694741429567288,"x_left":2.7383970219959033,"x_right":2.9729152617066648},{"constant_coef":8.6344133942263,"cube_coef":-0.11431424680998906,"linear_coef":4.293624797324399,"square_coef":0.004963999496579347,"x_left":2.9729152617066648,"x_right":3.2074335014174262},{"constant_coef":12.818687944237299,"cube_coef":-0.5675807694331939,"linear_coef":7.921220111775179,"square_coef":0.16766576335556876,"x_left":3.2074335014174262,"x_right":3.676469980838949},{"constant_coef":20.33999304993485,"cube_coef":-0.2722907306308796,"linear_coef":13.10761866037347,"square_coef":-0.31140084521721895,"x_left":3.676469980838949,"x_right":4.614542939681995},{"constant_coef":32.86392013446021,"cube_coef":0.24109131554894525,"linear_coef":23.335889556092784,"square_coef":-4.776421665570883,"x_left":4.614542939681995,"x_right":6.490688857368086},{"constant_coef":51.664479340531074,"cube_coef":0.0,"linear_coef":50.185867640470995,"square_coef":0.0,"x_left":6.490688857368086,"x_right":12.981377714736173}]'}
        # fmt:on
    ]

    result = serialize_columns(input_df, ["spline_info"])
    compare_dataframes_row_by_row(
        pd.DataFrame(expected_result).reset_index(), result.reset_index()
    )
