import asyncio
from datetime import datetime, timedelta, timezone
from functools import partial
from typing import Any, Callable, Generator, Optional, cast, get_args
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest
from lambda_types import ExpiryArbFields
from run_stream_flex import predicate
from stream_flex_function import ActiveFunction
from stream_flex_sub_manager import FlexConsumerSubscriptionManager
from stream_types import SubscribeMessage
from utils_general.aggregate_logger import setup_agg_logger

from .helpers import (
    _get_arb_output_for_flex,
    sub_type_to_message_value_helper,
    sub_types_cast_helper,
)
from .test_types import SubOverides, TestMessageTypes


@pytest.fixture
def iso_7d() -> str:
    return (datetime.now(tz=timezone.utc) + timedelta(days=7)).strftime(
        "%Y-%m-%dT%H:%M:%S.000Z"
    )


@pytest.fixture
def iso_14d() -> str:
    return (datetime.now(tz=timezone.utc) + timedelta(days=14)).strftime(
        "%Y-%m-%dT%H:%M:%S.000Z"
    )


@pytest.fixture
def predicate_fn() -> Callable[[str], bool]:
    return partial(
        predicate,
        instruments=[],
        exchange="EXCHANGE",
        currency="BASE_ASSET",
        model="MODEL",
    )


SubFactory = Callable[[Optional[SubOverides]], SubscribeMessage]


@pytest.fixture
def mock_time() -> Generator[Mock, None, None]:
    with patch("time.time", return_value=1234567890) as mock:
        yield mock


@pytest.fixture
def sub_message_factory(iso_7d: Optional[str]) -> SubFactory:
    def create_sub_message(
        args: Optional[SubOverides] = None,
    ) -> SubscribeMessage:
        setup_agg_logger()
        fields: dict[str, Any] = {
            "type": "params",
            "subscriber_id": "sub_1",
            "expiry": iso_7d,
            "command": "subscribe",
            "calculator": "test",
            "exchange": "EXCHANGE",
            "model": "MODEL",
            "base_asset": "BTC",
            "function_id": "1",
        }
        if args is not None:
            for k, v in args.items():
                fields[k] = v
        return cast(SubscribeMessage, fields)

    return create_sub_message


class TestFlexConsumerSubscriptionManager:
    @pytest.fixture(autouse=True)
    def flex_consumer_subscription_manager(self) -> Generator[None, None, None]:
        setup_agg_logger()
        # this is dynamically assigned
        self.FlexConsumerSubscriptionManager = FlexConsumerSubscriptionManager
        yield
        del self.FlexConsumerSubscriptionManager

    @pytest.mark.asyncio
    async def test_subscribe(
        self,
        sub_message_factory: SubFactory,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        caplog.set_level(15)
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        init_msg = sub_message_factory(None)
        await sub_manager.subscribe(msg=init_msg)

        result_function = sub_manager.get_function(
            f"{init_msg['exchange']}.{init_msg['base_asset']}.{init_msg['model']}"
        )
        assert result_function is not None
        assert (
            result_function.get_expiries()
            == ActiveFunction(init_msg).get_expiries()
        )
        assert sub_manager.get_active_function_count() == 1
        assert (
            "Subscription added for sub_1 to EXCHANGE.BTC.MODEL" in caplog.text
        )

    @pytest.mark.asyncio
    async def test_get_undefined_fn(
        self, sub_message_factory: SubFactory, caplog: pytest.LogCaptureFixture
    ) -> None:
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        assert sub_manager.get_function("UNKNOWN") is None

    @pytest.mark.asyncio
    async def test_unsubscribe_unknown_fn(
        self, sub_message_factory: SubFactory, caplog: pytest.LogCaptureFixture
    ) -> None:
        caplog.set_level(15)
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        init_msg = sub_message_factory(None)
        await sub_manager.unsubscribe(msg=init_msg)

        assert (
            "Unable to unsubscribe sub_1, internal function not found: EXCHANGE.BTC.MODEL"
            in caplog.text
        )

    @pytest.mark.asyncio
    async def test_unsubscribe_known_fn(
        self, sub_message_factory: SubFactory, caplog: pytest.LogCaptureFixture
    ) -> None:
        caplog.set_level(15)
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        init_msg = sub_message_factory(None)
        await sub_manager.subscribe(msg=init_msg)
        assert sub_manager.get_active_function_count() == 1

        await sub_manager.unsubscribe(msg=init_msg)

        assert sub_manager.get_active_function_count() == 0
        assert "Unsubscribed: sub_1 from EXCHANGE.BTC.MODEL" in caplog.text

    @pytest.mark.asyncio
    async def test_expiry_watcher_remove_expiry(
        self, sub_message_factory: SubFactory, caplog: pytest.LogCaptureFixture
    ) -> None:
        caplog.set_level(15)
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        past_day_str = (
            datetime.now(tz=timezone.utc) - timedelta(days=1)
        ).isoformat()
        init_msg = sub_message_factory({"expiry": past_day_str})
        await sub_manager.subscribe(msg=init_msg)
        future_day_str = (
            datetime.now(tz=timezone.utc) + timedelta(days=1)
        ).isoformat()
        future_msg = sub_message_factory({"expiry": future_day_str})
        await sub_manager.subscribe(msg=future_msg)

        # Removes one
        try:
            await asyncio.wait_for(
                sub_manager.expiry_watcher(refresh_interval=0.1), timeout=0.15
            )
        except asyncio.TimeoutError:
            pass

        result_function = sub_manager.get_function(
            f"{init_msg['exchange']}.{init_msg['base_asset']}.{init_msg['model']}"
        )
        assert result_function is not None
        assert sub_manager.get_active_function_count() == 1
        assert result_function.get_expiries() == [future_day_str]
        assert f"Removed expiry='{past_day_str}'" in caplog.text

    @pytest.mark.asyncio
    async def test_expiry_watcher_remove_fn(
        self, sub_message_factory: SubFactory, caplog: pytest.LogCaptureFixture
    ) -> None:
        caplog.set_level(15)
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        init_msg = sub_message_factory(
            {
                "expiry": (
                    datetime.now(tz=timezone.utc) - timedelta(days=1)
                ).isoformat()
            }
        )
        await sub_manager.subscribe(msg=init_msg)
        assert sub_manager.get_active_function_count() == 1

        try:
            await asyncio.wait_for(
                sub_manager.expiry_watcher(refresh_interval=0.1), timeout=0.15
            )
        except asyncio.TimeoutError:
            pass

        assert sub_manager.get_active_function_count() == 0
        assert (
            "Removed running function as all expiries were in the past EXCHANGE.BTC.MODEL"
            in caplog.text
        )

    def test_predicate_invalid_qfn(
        self, predicate_fn: Callable[[Any], bool]
    ) -> None:
        tick = {"q": "EXCHANGE.option.BASE_ASSET"}
        assert not predicate_fn(tick)

    def test_predicate_valid_qfn(
        self, predicate_fn: Callable[[Any], bool]
    ) -> None:
        tick = {"q": "EXCHANGE.option.BASE_ASSET.MODEL.live.params"}
        assert predicate_fn(tick)

    def test_predicate_valid_catalog_qfn(
        self, predicate_fn: Callable[[Any], bool]
    ) -> None:
        tick = {
            "q": "EXCHANGE.option.contracts",
            "instrument": "BASE_ASSET_USD_EXPIRY_STRIKE_C",
            "baseAsset": "BASE_ASSET",
            "strike": 5,
        }
        assert predicate_fn(tick)

    def test_predicate_invalid_catalog_qfn(
        self, predicate_fn: Callable[[Any], bool]
    ) -> None:
        tick = {
            "q": "EXCHANGE.option.contracts",
            "instrument": "BASE_ASSET_USD_EXPIRY",
        }
        tick_without_strike = {
            "q": "EXCHANGE.option.contracts",
            "instrument": "BASE_ASSET_USD_EXPIRY_STRIKE_C",
            "baseAsset": "BASE_ASSET",
        }
        assert not predicate_fn(tick)
        assert not predicate_fn(tick_without_strike)

    @pytest.mark.asyncio
    async def test_get_run_vars(
        self, sub_message_factory: SubFactory, iso_7d: str
    ) -> None:
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        msg = sub_message_factory(None)
        result = {
            "calc_types": sorted(["smile", "params", "moneyness", "strike"]),
            "subscribed_arbitrary_info": {
                "EXCHANGE": {
                    "BTC": {
                        msg["expiry"]: _get_arb_output_for_flex(
                            {"iso_expiry": iso_7d}
                        )
                    }
                }
            },
        }
        await sub_manager.subscribe(sub_message_factory(None))
        assert sub_manager.get_run_vars("EXCHANGE.BTC.MODEL") == result

    @pytest.mark.asyncio
    @pytest.mark.parametrize("sub_type", get_args(TestMessageTypes))
    async def test_get_run_vars_multiple(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
        iso_14d: str,
        sub_type: TestMessageTypes,
    ) -> None:
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        msg_1 = sub_message_factory(None)
        await sub_manager.subscribe(msg_1)

        message_value_type = sub_type_to_message_value_helper(sub_type)
        values = sub_types_cast_helper(sub_type, [1])
        msg_2 = sub_message_factory(
            {
                "type": sub_type,
                "expiry": iso_14d,
                "subscriber_id": "sub_2",
                "values": values,
            }
        )
        # arb constant maturity
        msg_3 = sub_message_factory(
            {
                "type": sub_type,
                "expiry": "7d5d",
                "subscriber_id": "sub_69",
                "values": values,
            }
        )
        await sub_manager.subscribe(msg_2)
        await sub_manager.subscribe(msg_3)

        base = ExpiryArbFields(arbitrary_expiry=True, iso_expiry=iso_14d)
        base3 = ExpiryArbFields(
            arbitrary_constant_maturity=True, tenor_str="7d5d"
        )
        base.update(message_value_type, [1])
        base3.update(message_value_type, [1])

        calc_types = ["smile", "params", "moneyness", "strike"]

        result = {
            "calc_types": sorted(calc_types),
            "subscribed_arbitrary_info": {
                "EXCHANGE": {
                    "BTC": {
                        msg_1["expiry"]: _get_arb_output_for_flex(
                            {"iso_expiry": msg_1["expiry"]}
                        ),
                        iso_14d: base,
                        "7d5d": base3,
                    }
                }
            },
        }

        assert sub_manager.get_run_vars("EXCHANGE.BTC.MODEL") == result

    @pytest.mark.asyncio
    async def test_removal_of_strike_calc_type(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
    ) -> None:
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
        )
        values = sub_types_cast_helper("strike", [1000, 2000])
        base_msg: SubOverides = {
            "type": "strike",
            "expiry": iso_7d,
            "values": values,
        }
        msg = sub_message_factory(base_msg)
        sub_2_msg = sub_message_factory({**base_msg, "subscriber_id": "sub_2"})
        # Other so run_vars are not none after removing the sub_2 msg
        other_msg = sub_message_factory(
            {**base_msg, "type": "smile", "values": {"deltas": [0.99]}}
        )

        await sub_manager.subscribe(msg)
        await sub_manager.subscribe(sub_2_msg)
        await sub_manager.subscribe(other_msg)

        result = {
            "calc_types": sorted(["smile", "params", "moneyness", "strike"]),
            "subscribed_arbitrary_info": {
                "EXCHANGE": {
                    "BTC": {
                        msg["expiry"]: _get_arb_output_for_flex(
                            {
                                "iso_expiry": msg["expiry"],
                                "deltas": [0.99],
                                "strikes": [1000, 2000],
                            }
                        ),
                    }
                }
            },
        }

        assert sub_manager.get_run_vars("EXCHANGE.BTC.MODEL") == result

        # unsubscribe sub_1 strike
        await sub_manager.unsubscribe(msg)
        assert sub_manager.get_run_vars("EXCHANGE.BTC.MODEL") == result

        # unsubscribe sub_2 strike
        result = {
            "calc_types": sorted(["smile", "params", "moneyness"]),
            "subscribed_arbitrary_info": {
                "EXCHANGE": {
                    "BTC": {
                        msg["expiry"]: _get_arb_output_for_flex(
                            {"iso_expiry": msg["expiry"], "deltas": [0.99]}
                        ),
                    }
                }
            },
        }
        await sub_manager.unsubscribe(sub_2_msg)

        assert sub_manager.get_run_vars("EXCHANGE.BTC.MODEL") == result

    @pytest.mark.asyncio
    async def test_subscribe_to_unsubscribed_function_removed_from_pending_removal(
        self,
        sub_message_factory: SubFactory,
        iso_7d: str,
        mock_time: MagicMock,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        caplog.set_level(15)
        sub_manager = self.FlexConsumerSubscriptionManager(
            calculator="test",
            FunctionClass=ActiveFunction,
            time_to_remove_cache_secs=60,
        )
        values = sub_types_cast_helper("strike", [1000, 2000])
        base_msg: SubOverides = {
            "type": "strike",
            "expiry": iso_7d,
            "values": values,
        }
        msg = sub_message_factory(base_msg)
        sub_2_msg = sub_message_factory({**base_msg, "subscriber_id": "sub_2"})
        other_msg = sub_message_factory(
            {**base_msg, "type": "smile", "values": {"deltas": [0.99]}}
        )

        await sub_manager.subscribe(msg)
        await sub_manager.subscribe(sub_2_msg)
        await sub_manager.subscribe(other_msg)

        assert sub_manager.get_active_function_count() == 1

        await sub_manager.unsubscribe(msg=msg)
        await sub_manager.unsubscribe(msg=sub_2_msg)
        await sub_manager.unsubscribe(msg=other_msg)

        # active function is still 1 as we have defined time_to_remove_cache_secs
        assert sub_manager.get_active_function_count() == 1
        assert sub_manager.get_run_vars("EXCHANGE.BTC.MODEL") == {
            "calc_types": sorted(["smile", "params", "moneyness"]),
            "subscribed_arbitrary_info": {
                "EXCHANGE": {
                    "BTC": {}  # no expires as no one is subscribed to this
                }
            },
        }
        assert (
            "Adding function 'EXCHANGE.BTC.MODEL' to pending removal"
            in caplog.text
        )

        assert sub_manager._pending_to_remove == {
            "EXCHANGE.BTC.MODEL": 1234567890
        }

        # subscribe once again
        await sub_manager.subscribe(msg)
        assert (
            "Subscription received for internal_function_id='EXCHANGE.BTC.MODEL' that is pending removal."
            in caplog.text
        )
        # assert that is empty again
        assert sub_manager._pending_to_remove == {}
