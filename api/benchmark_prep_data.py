import cProfile
import pstats
from random import random

from lambda_types import PxResult
from prep_data import blockstream_prep_data
from utils_general.utils_general_types import InstrumentsDetailsMap

NAMES = [
    "deribit.option.BTC-26JAN24-38000-C.tick.bid.px",
    "deribit.option.BTC-26JAN24-38000-C.tick.ask.px",
    "deribit.option.BTC-26JAN24-38000-C.live.mid.px",
]

instruments: InstrumentsDetailsMap = {
    "deribit.option.BTC-26JAN24-38000-C": {
        "baseAsset": "BTC",
        "quoteAsset": "BTC",
        "instrument": "BTC-26JAN24-38000-C",
        "qualified_name": "deribit.option.contracts",
    }
}
STARTING_TIMESTAMP = 1700495700000000000


def init_data() -> list[PxResult]:
    data: list[PxResult] = []

    for name in NAMES:
        for i in range(4000):
            t = STARTING_TIMESTAMP + i
            px = random()

            data.append(
                {
                    "qualified_name": name,
                    "timestamp": t,
                    "px": px,
                }
            )

    return data


def benchmark(iterations: int) -> None:
    data = init_data()

    profiler = cProfile.Profile()
    profiler.enable()

    for _ in range(iterations):
        blockstream_prep_data(
            raw_data=data,
            instruments=instruments,
            snapshot_ts=1700495885800000000,
        )

    profiler.disable()

    stats = pstats.Stats(profiler).sort_stats("cumulative")
    stats.print_stats(10)


ITERATIONS = 10

if __name__ == "__main__":
    benchmark(ITERATIONS)
