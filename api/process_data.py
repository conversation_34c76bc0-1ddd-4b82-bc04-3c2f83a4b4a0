import functools
import logging
import math
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from multiprocessing.connection import Connection
from numbers import Number
from typing import Any, Callable, <PERSON><PERSON>, TypedDict, cast

import numpy as np
import pandas as pd
import utils_general
from constants import (
    DAYS_IN_YEAR,
    FIVE_MIN_IN_YEARS,
    HARD_CAPS_CONFIG,
    MIN_FUTURE_CURVE_DATAPOINTS,
    NUM_WORKERS,
    PRICE_DECIMAL_PRECISION,
    load_domestic_rates,
)
from numpy.typing import NDArray
from scipy.special import ndtr
from typings import (
    FutureCurveData,
    OptionPriceMap,
    PxQueryResult,
    Snapshot,
    TheoreticalPrice,
    ValidOptionType,
)
from utils_calc import (
    CubicSpline,
    Model,
    calc_convenience_yield,
    get_domestic_rate,
    option_price_greeks,
    sabr_vol,
    spline_model_vol,
    svi_vol,
)
from utils_general import to_datetime


def process_data(
    data: list[Snapshot],
    frequency: str,
    version: str = "",
    greeks: bool = False,
) -> list[TheoreticalPrice]:
    return utils_general.parallel_process(
        data_slices=data,
        num_workers=NUM_WORKERS,
        process_chunk_fn=functools.partial(
            _process_chunk_and_send_results,
            frequency=frequency,
            version=version,
            greeks=greeks,
        ),
        chunk_data=True,
    )


def _process_chunk_and_send_results(
    frequency: str,
    chunk: list[Snapshot],
    conn: Connection,
    version: str = "",
    greeks: bool = False,
) -> None:
    try:
        results = process_chunk(
            chunk=chunk,
            frequency=frequency,
            version=version,
            greeks=greeks,
        )
        conn.send(results)
    except Exception as e:
        conn.send({"error": str(e), "details": ""})
        logging.exception(f"Error processing data chunk, e={e}")
    conn.close()


def process_chunk(
    chunk: list[Snapshot],
    frequency: str,
    version: str = "",
    greeks: bool = False,
    **_: Any,
) -> list[TheoreticalPrice]:
    results: list[TheoreticalPrice] = []

    for snapshot in chunk:
        try:
            additional_data, result = _calculate_theoretical_price(snapshot, greeks)
        except Exception as ex:
            logging.error(
                f"Failed to calculate theoretical price for Snapshot. error: {ex}"
            )
            continue

        v = f"{version}." if version and frequency != "live" else ""
        model = f".{snapshot.data['model']}" if snapshot.asset_class == "option" else ""
        suffix = "pxs" if snapshot.asset_class == "option" else "px"

        # TODO: for backward compatibility of lambda version - ensure restAPI can handle the new format & backfill carried out
        if (
            frequency != "live"
            and snapshot.asset_class == "option"
            and isinstance(result, dict)
        ):
            for option_type in result:
                result[option_type] = result[option_type]["strikes"]

        # TODO: for backward compatibility we remove model from qfn if it's SVI
        if model == ".SVI":
            model = ""

        qualified_name = f"{v}{snapshot.exchange}.{snapshot.asset_class}.{snapshot.instrument_name}{model}.{frequency}.theoretical.{suffix}"
        results.append(
            {
                "qualified_name": qualified_name,
                "timestamp": snapshot.timestamp,
                suffix: result,
                **additional_data,
            }
        )

    return results


OptionKey = str | float
OptionPrice = float
OptionGreeksTuple = Tuple[float, float, float, float, float, float, float]


class TPricerOutputByType(TypedDict, total=False):
    moneyness: dict[OptionKey, OptionPrice] | dict[OptionKey, OptionGreeksTuple]
    strikes: dict[OptionKey, OptionPrice] | dict[OptionKey, OptionGreeksTuple]


def _calculate_theoretical_price(
    snapshot: Snapshot,
    output_greeks: bool = False,
) -> tuple[dict[str, Any], float | OptionPriceMap]:
    if snapshot.asset_class == "perpetual":
        if not snapshot.data or not isinstance(snapshot.data, float):
            raise ValueError(
                f"failed to calculate theoretical price for {snapshot.instrument_name}, "
                f"invalid perpetual price: {snapshot.data}"
            )
        return {}, snapshot.data
    elif snapshot.asset_class == "future":
        expiry_in_days = snapshot.data["expiry_in_days"]
        spot: float = snapshot.data["spot"]
        rate = snapshot.data["spline"](expiry_in_days)

        px = spot * math.exp(rate * expiry_in_days)
        if math.isnan(px):
            raise ValueError(
                f"failed to calculate theoretical price for '{snapshot.instrument_name}' "
                f"with: {spot=} - {rate=} - {expiry_in_days=}"
            )
        return {
            "annualised_rate": rate * DAYS_IN_YEAR,
            "intermediate": {
                "expiry_in_days": expiry_in_days,
                "spot": spot,
            },
        }, round(px, PRICE_DECIMAL_PRECISION)
    elif snapshot.asset_class == "option":
        expiry_in_days = snapshot.data["expiry_in_days"]
        domestic_rate = get_domestic_rate(load_domestic_rates(), expiry_in_days)
        foreign_rate: float = (
            domestic_rate - snapshot.data["spline"](expiry_in_days) * DAYS_IN_YEAR
        )
        expiry_in_years = expiry_in_days / DAYS_IN_YEAR
        spot = snapshot.data["spot"]
        fwd: float | None = None

        theoretical_prices_result: dict[str, TPricerOutputByType] = {}
        for option_type, strikes_list in snapshot.data["strikes_by_type"].items():
            static_strikes: list[float] = sorted(
                filter(lambda s: isinstance(s, Number), strikes_list)
            )
            dynamic_strikes = list(
                filter(lambda s: s in ("atm_spot", "atm_forward"), strikes_list)
            )

            strikes_raw = static_strikes.copy()
            if dynamic_strikes:
                for s in dynamic_strikes:
                    if s == "atm_spot":
                        strikes_raw.append(spot)
                    elif s == "atm_forward":
                        fwd = _get_forward(
                            spot, domestic_rate, foreign_rate, expiry_in_years
                        )
                        strikes_raw.append(fwd)
                    else:
                        logging.warning(f"Unknown dynamic strike found: {s}")

            strikes = np.array(strikes_raw)
            strike_labels = [*static_strikes, *dynamic_strikes]

            fwd = _get_forward(spot, domestic_rate, foreign_rate, expiry_in_years)

            strike_output = _calculate_option_prices_and_greeks(
                option_type=option_type,
                spot=spot,
                expiry_in_years=expiry_in_years,
                domestic_rate=domestic_rate,
                foreign_rate=foreign_rate,
                strikes=strikes,
                strike_labels=strike_labels,
                iv_list=get_iv_from_params(
                    snapshot.data["model"], strikes, snapshot.data["params"]
                ),
                fwd=fwd,
                ref_fwd=snapshot.data["params"].get("forward"),
                output_greeks=output_greeks,
            )

            if option_type not in theoretical_prices_result:
                theoretical_prices_result[option_type] = {}
            theoretical_prices_result[option_type]["strikes"] = strike_output

        for option_type, moneyness_list in snapshot.data.get(
            "moneyness_by_type", {}
        ).items():
            if fwd is None:
                fwd = _get_forward(spot, domestic_rate, foreign_rate, expiry_in_years)

            money_strikes = np.array(list(moneyness_list)) * fwd
            money_output = _calculate_option_prices_and_greeks(
                option_type=option_type,
                spot=spot,
                expiry_in_years=expiry_in_years,
                domestic_rate=domestic_rate,
                foreign_rate=foreign_rate,
                strikes=money_strikes,
                strike_labels=moneyness_list,
                iv_list=get_iv_from_params(
                    snapshot.data["model"], money_strikes, snapshot.data["params"]
                ),
                fwd=fwd,
                ref_fwd=snapshot.data["params"].get("forward"),
                output_greeks=output_greeks,
            )
            if option_type not in theoretical_prices_result:
                theoretical_prices_result[option_type] = {}
            theoretical_prices_result[option_type]["moneyness"] = money_output

        intermediate = {
            "annualised_domestic_rate": domestic_rate,
            "annualised_foreign_rate": foreign_rate,
            "spot": spot,
            "params_timestamp": snapshot.data["params_ts"],
        }
        if fwd:
            intermediate["forward"] = fwd
        return {
            "intermediate": intermediate,
        }, theoretical_prices_result
    else:
        raise NotImplementedError(f"Unsupported asset class {snapshot.asset_class}")


def get_iv_from_params(
    model: Model, strikes: NDArray[np.float64], params: dict[str, Any]
) -> NDArray[np.float64]:
    if model == "SVI":
        vol = svi_vol(
            a=params["svi_a"],
            b=params["svi_b"],
            rho=params["svi_rho"],
            m=params["svi_m"],
            sigma=params["svi_sigma"],
            f=params["forward"],
            exp=params["expiry"],
            k=strikes,
        )
    elif model == "SABR":
        vol = sabr_vol(
            k=strikes,
            f=params["forward"],
            t=params["expiry"],
            alpha=params["sabr_alpha"],
            beta=1,  # Default to 1 to align with modelparam
            rho=params["sabr_rho"],
            volvol=params["sabr_volvol"],
        )
    elif model == "spline":
        vol = np.array(
            spline_model_vol(
                strikes=cast(list[float], strikes),
                params=cast(CubicSpline, params),
            ),
            dtype=np.float64,
        )
    else:
        raise ValueError(f"Unsupported model: {model}")

    return cast(NDArray[np.float64], vol)


def _get_forward(
    spot: float, domestic_rate: float, foreign_rate: float, expiry_in_years: float
) -> float:
    return spot * math.exp((domestic_rate - foreign_rate) * expiry_in_years)


def get_foreign_rate(
    spot: float,
    domestic_rate: float,
    forward: float,
    expiry_in_years: float,
) -> float:
    """
    Calculate the foreign interest rate using the formula:
    forward = spot * exp((domestic_rate - foreign_rate) * expiry_in_years)
    Rearranging gives us:
    foreign_rate = domestic_rate - (log(forward / spot) / expiry_in_years)
    """
    if spot <= 0 or forward <= 0:
        raise ValueError("Spot and forward prices must be greater than zero.")
    if expiry_in_years <= 0:
        raise ValueError("Expiry in years must be greater than zero.")

    return domestic_rate - (float(np.log(forward / spot)) / expiry_in_years)


def price_option_vectorized(
    option_type: int,
    spot: float,
    expiry_in_years: float,
    domestic_rate: float,
    foreign_rate: float,
    strike: NDArray[np.float64],
    implied_vol: NDArray[np.float64],
    fwd: float | None = None,
    ref_fwd: float | None = None,
) -> NDArray[np.float64]:
    """
    Calculate the option price for a list of strikes

    Following same format as: https://github.com/blockscholes/vixCalc/blob/main/api/calc_vix.py#L75

    :param option_type: option type. 1 for a Call and -1 for a Put
    :param spot: underlying spot price
    :param fwd: underlying forward price
    :param expiry_in_years: time to expiry (express in years)
    :param domestic_rate: domestic interest rate
    :param foreign_rate: foreign interest rate
    :param strike: list of strikes price to calculate from
    :param implied_vol: implied volatility associated with the strikes
    :param ref_fwd: reference fwd price
    :return: option price
    """
    # For options close to expiry, we simply return the difference between the spot and strike; 0 if negative
    if expiry_in_years < FIVE_MIN_IN_YEARS:
        if option_type == 1:
            prices = spot - strike
        else:
            prices = strike - spot
        prices[prices < 0] = 0
        return prices

    if fwd is None:
        fwd = _get_forward(spot, domestic_rate, foreign_rate, expiry_in_years)

    if ref_fwd:
        # calculate relative diff between ref_fwd and fwd
        diff = (ref_fwd - fwd) / ref_fwd
        if diff > 0.01:
            logging.warning(
                f"Relative difference between ref_fwd and fwd is too high: {diff}. fwd_from_params: {ref_fwd}, fwd: {fwd} {spot=} {foreign_rate=} days={expiry_in_years * DAYS_IN_YEAR}, estimated_expiry={utils_general.to_iso(datetime.now(timezone.utc) + timedelta(days=expiry_in_years * DAYS_IN_YEAR))}"
            )

    sub = implied_vol * math.sqrt(expiry_in_years)

    d_p = (np.log(fwd / strike) + ((implied_vol**2) / 2) * expiry_in_years) / sub
    d_m = (np.log(fwd / strike) - ((implied_vol**2) / 2) * expiry_in_years) / sub

    # Note: we start using scipy.special.ndtr instead of scipy.stats.norm.cdf for performance.
    # See discussion: https://github.com/scipy/scipy/issues/9855
    prices = (
        option_type
        * math.exp(-domestic_rate * expiry_in_years)
        * (fwd * ndtr(option_type * d_p) - strike * ndtr(option_type * d_m))
    )

    return np.round(prices, decimals=PRICE_DECIMAL_PRECISION)


def calculate_future_yield_curve_map(
    raw_data: list[PxQueryResult],
) -> dict[str, Callable[[float], float]]:
    if len(raw_data) == 0:
        raise RuntimeError("Failed to prepare yield curve data, no data is available")

    spot_data: dict[str, float] = {}
    futures_by_currency_pair: dict[str, list[FutureCurveData]] = defaultdict(list)
    for data in raw_data:
        qfn = data["qualified_name"].split(".")
        if qfn[1] == "spot":
            spot_data[qfn[2]] = data["px"]
        elif qfn[1] == "future":
            # Get instrument currency pair from qfn
            inst = qfn[2].split("_")
            currency_pair = f"{inst[0]}_{inst[1]}"

            # Get date from timestamp and expiry
            future_date = datetime.fromtimestamp(
                data["timestamp"] // 1e9, tz=timezone.utc
            )

            expiry = inst[2].strip("-SYN")
            if utils_general.is_constant_maturity(expiry):
                continue
            else:
                # Calculate days to expiry
                days_to_expiry = (
                    to_datetime(expiry) - future_date
                ).total_seconds() / 86400

            futures_by_currency_pair[currency_pair].append(
                {
                    "currency_pair": currency_pair,
                    "days_to_expiry": days_to_expiry,
                    "price": data["px"],
                }
            )

    if not spot_data:
        raise RuntimeError(
            "Failed to prepare yield curve data, no spot data is available"
        )
    elif not futures_by_currency_pair:
        raise RuntimeError(
            "Failed to prepare yield curve data, no future data is available"
        )

    results: dict[str, Callable[[float], float]] = {}
    for currency_pair, data in futures_by_currency_pair.items():
        if currency_pair in spot_data:
            try:
                results[currency_pair] = calc_yield_curve_fn(
                    data, spot_data[currency_pair]
                )
            except Exception as ex:
                logging.exception(
                    f"Failed to calculate yield curve for '{currency_pair}' futures. error: {ex}"
                )
        else:
            logging.error(
                f"Cannot calculate yield curve for '{currency_pair}' futures without the spot price"
            )

    return results


def _validate_and_filter_yields(
    future_data: list[FutureCurveData],
) -> list[FutureCurveData]:
    filter_curve = []

    base_asset = (
        future_data[0].get("currency_pair", "").split("_")[0]
        if len(future_data) > 0
        else ""
    )
    hard_cap_config = (
        HARD_CAPS_CONFIG[base_asset]
        if base_asset in HARD_CAPS_CONFIG
        else HARD_CAPS_CONFIG["default"]
    )

    for data in future_data:
        hard_cap = hard_cap_config.get_near_interest_rate_hard_cap(
            data["days_to_expiry"]
        )

        if hard_cap.min > data["daily_yield"] or data["daily_yield"] > hard_cap.max:
            logging.warning(
                f"Excluding future datapoint '{data}' from yield curve as it's outside min/max hard cap. "
                f"'{data['daily_yield']}' outside ({hard_cap.min}, {hard_cap.max}). Initial Yield Curve: '{future_data}'"
            )
            continue

        filter_curve.append(data)

    return filter_curve


def calc_yield_curve_fn(
    future_data: list[FutureCurveData], spot: float
) -> Callable[[float], float]:
    if len(future_data) < MIN_FUTURE_CURVE_DATAPOINTS:
        raise Exception(
            f"Insufficient datapoints to calculate yield curve: '{len(future_data)}' datapoints, "
            f"'{MIN_FUTURE_CURVE_DATAPOINTS}' required"
        )

    # Sort data by days to expiry
    future_data.sort(key=lambda row: row["days_to_expiry"])

    # Calculate the yield for each expiration
    for data in future_data:
        data["daily_yield"] = (1 / data["days_to_expiry"]) * math.log(
            data["price"] / spot
        )

    future_data = _validate_and_filter_yields(future_data)

    if len(future_data) < MIN_FUTURE_CURVE_DATAPOINTS:
        raise Exception(
            f"Insufficient datapoints to calculate yield curve after yield validation: "
            f"'{len(future_data)}' datapoints, '{MIN_FUTURE_CURVE_DATAPOINTS}' required"
        )

    _, _, c_yield_right = calc_convenience_yield(
        snapshot=pd.DataFrame(future_data).rename(
            columns={"days_to_expiry": "expiry", "price": "forward"}
        ),
        spot=spot,
    )

    msg = "\n".join(
        [
            f"{d['days_to_expiry']:.2f} - {d['price']} - {d['daily_yield'] * DAYS_IN_YEAR}"
            for d in future_data
        ]
    )
    logging.info(
        f"Successfully calculate yield curve for '{future_data[0].get('currency_pair')}' with '{len(future_data)}' datapoints, spot: '{spot}'\n{msg}"
    )

    return functools.partial(
        yield_fn, spot=spot, data=future_data, c_yield_right=c_yield_right
    )


def yield_fn(
    expiry: float, spot: float, c_yield_right: float, data: list[FutureCurveData]
) -> float:
    lower_expiry = data[0]["days_to_expiry"]
    higher_expiry = data[-1]["days_to_expiry"]
    if expiry < lower_expiry:
        # For an expiry lower than the lowest listed expiry, we return the left yield in the curve.
        return cast(float, data[0]["daily_yield"])
    elif expiry > higher_expiry:
        # Extrapolate: For an expiry higher than the highest listed expiry, we extrapolate using the following fn
        return extrapolate_right(expiry, c_yield_right, data)
    else:
        return interpolate_expiry(spot, expiry, data)


def interpolate_expiry(
    spot: float, expiry: float, data: list[FutureCurveData]
) -> float:
    idx = 0
    # Find the nearest data point to our expiry.
    # The dataframe is already ordered by expiry, and the index has been reset.
    for ex in data:
        if ex["days_to_expiry"] > expiry:
            break
        elif ex["days_to_expiry"] == expiry:
            # If the expiry we are looking for is already in the curve, return the corresponding yield
            return cast(float, data[idx]["daily_yield"])
        idx += 1

    frac = (expiry - data[idx - 1]["days_to_expiry"]) / (
        data[idx]["days_to_expiry"] - data[idx - 1]["days_to_expiry"]
    )

    # If any of the yields is negative, then we need to interpolate using future prices
    if data[idx - 1]["daily_yield"] < 0 or data[idx]["daily_yield"] < 0:
        interpolated_px = pow(data[idx - 1]["price"], 1 - frac) * pow(
            data[idx]["price"], frac
        )
        # return the yield associated with the interpolated price
        return (1 / expiry) * math.log(interpolated_px / spot)

    return float(
        pow(data[idx - 1]["daily_yield"], 1 - frac)
        * pow(data[idx]["daily_yield"], frac)
    )


def extrapolate_right(
    expiry: float, c_yield_right: float, data: list[FutureCurveData]
) -> float:
    """
    To extrapolate yields to the right part of the curve, we use the right convenience yield, which is calculated from
    the latest two datapoints in the curve (see the function calc_right_convenience_yield).

    The problem with this approach is that we cannot use this yield with the current spot price to calculate the
    new theoretical price:

        spot * e ^ (c_right_yield * expiry)

    As this would apply the same yield to the entire period of time, we need to start extrapolating from the
    latest available future price (let's assume it is T):

        future_T * e ^ (c_right_yield * (expiry - T))

    What we need to consider here, is that the future_T is not considering changes in the spot price.
    So we cannot use the future price at T, as it's not reflecting changes in the underlying spot.
    What we need to do here is recalculate the theoretical price at T with the current spot, and then use
    this to extrapolate using the convenience right yield.

    What we need to consider here is that the future_T is not considering changes in the spot price.
    Therefore, we cannot use the future price at T, as it does not reflect changes in the underlying spot.
    What we need to do here is recalculate the theoretical price at T with the current spot and then use this to
    extrapolate using the convenience right yield:

        theoretical_future_T = spot * e ^ (yield_T * T)

    Then the theoretical future price for the extrapolated expiry would be:

        theoretical_future_T * e ^ (c_right_yield * (expiry - T))

    But instead of calculating two theoretical prices, we could simplify these two equations as follows:

        Let's assume that x is the theoretical future price for the expiry we are looking for,
        and T is the latest available expiry in the curve, then:

            1) theoretical_future_T = spot * e ^ (yield_T * T)
            2) x = theoretical_future_T * e ^ (c_right_yield * (expiry - T))

        Replacing:

            3) x = spot * e ^ (yield_T * T) * e ^ (c_right_yield * (expiry - T))
            4) x = spot * e ^ (yield_T * T + c_right_yield * (expiry - T))

        We could now simplify the exponent to the form -> y * expiry; where y is the yield returned by this function:

            y * expiry = yield_T * T + c_right_yield * (expiry - T)

            y * expiry = [yield_T * T / expiry + c_right_yield - c_right_yield * T / expiry] * expiry

        Then,

            y = yield_T * T / expiry + c_right_yield - c_right_yield * T / expiry

    :param expiry: expiry to extrapolate the yield
    :param c_yield_right: right convenience yield,
    :param data: yield curve data
    :return: yield to be used in the theoretical price calculation
    """
    if len(data) == 1:
        # if we have only one datapoint in the curve, return right yield which is equal to only yield in the curve
        return c_yield_right

    return float(
        data[-1]["daily_yield"] * data[-1]["days_to_expiry"] / expiry
        + c_yield_right
        - c_yield_right * data[-1]["days_to_expiry"] / expiry
    )


def _calculate_option_prices_and_greeks(
    option_type: ValidOptionType,
    spot: float,
    expiry_in_years: float,
    domestic_rate: float,
    foreign_rate: float,
    strikes: NDArray[np.float64],
    strike_labels: list[float | str],
    iv_list: NDArray[np.float64],
    fwd: float,
    ref_fwd: float | None,
    output_greeks: bool,
) -> dict[OptionKey, OptionPrice] | dict[OptionKey, OptionGreeksTuple]:
    prices = price_option_vectorized(
        option_type=1 if option_type == "C" else -1,
        spot=spot,
        expiry_in_years=expiry_in_years,
        domestic_rate=domestic_rate,
        foreign_rate=foreign_rate,
        strike=strikes,
        implied_vol=iv_list,
        fwd=fwd,
        ref_fwd=ref_fwd,
    )

    # Create base result dictionary with strike prices
    result = dict(zip(strike_labels, prices))

    if output_greeks:
        greeks = option_price_greeks(
            spot=spot,
            forward=fwd,
            strike=strikes,
            vol=iv_list,
            exp=expiry_in_years,
            r_d=domestic_rate,
            op_type=option_type,
            currency="domestic",
        )

        # Update result dictionary with greek values
        result = {}
        for i, strike_label in enumerate(strike_labels):
            result[strike_label] = (
                prices[i],
                greeks.delta[i],
                greeks.gamma[i],
                greeks.vega[i],
                greeks.theta[i],
                greeks.volga[i],
                greeks.vanna[i],
            )

    return result
