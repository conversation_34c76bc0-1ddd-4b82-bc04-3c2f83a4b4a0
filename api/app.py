import json
import logging
import os
import pathlib
import sys
import traceback
from datetime import datetime, timezone
from typing import Any, cast

import orjson
import pandas as pd
import utils_aws
import utils_general
from aws_lambda_typing import context as ctx
from aws_lambda_typing import events
from constants import LOOKBACK_COLUMN_NAME, S3, TENOR_TYPES
from lambda_types import (
    CalcArgs,
    PreviousResults,
    ProcessDataSnapshot,
    VolSmileCalc,
)
from prep_data import prep_data_lambda
from process_data import process_data
from retrieve_data import retrieve_data
from utils.common import merge_multi_index_lookback
from utils.outputs import (
    drop_available_columns_from_df,
    serialize_columns,
)

pd.set_option("display.max_rows", None)
pd.set_option("display.max_columns", None)
utils_general.setup_python_logger(os.getenv("LOG_LEVEL", "INFO"))


def lambda_handler(
    event: events.APIGatewayProxyEventV1, context: ctx.Context
) -> Any:
    input = utils_general.json_loads(event["body"])
    try:
        open_sockets = utils_general.socket_fds()
        logging.info(f"START: {len(open_sockets)} open sockets")

        calc: VolSmileCalc = input["calc"]
        # validate_input(calc)
        args: CalcArgs = calc["args"]
        do_store = calc["output_options"]["do_store"]
        output_type = calc["output_options"]["type"]
        output_version: str = calc["output_options"].get("version", "")
        currencies = args["currencies"]
        exchanges = args["exchanges"]
        interval = args["frequency"]["interval"]
        periods = int(args["frequency"]["periods"])
        types = args["types"]
        models = args["models"]
        smooth = bool(args.get("smooth", False))
        include_listed_expiries = bool(
            args.get("include_listed_expiries", False)
        )
        estimate_params = bool(args.get("estimate_params", True))
        consistent_read = bool(args.get("consistent_read", False))
        debug = bool(args.get("debug", False))
        tenor_mode = args.get("tenor_mode", "standard")
        previous_result = cast(
            PreviousResults,
            (
                {"df": args["previous_result"]}
                if "previous_result" in args
                else {"df": None}
            ),
        )
        args["previous_result"] = (
            None  # avoid issue on output json serialisation
        )
        include_result_response = bool(
            args.get("include_result_response", False)
        )

        assert do_store and output_type == "csv"
        assert "composite" not in exchanges or (
            "params" in types and "smile" in types
        )

        start, end = utils_aws.get_calc_date_range(args["date_range"])
        time_condition = args["date_range"].get("time_condition", None)
        freq = f"{periods}{utils_general.INTERVAL_TO_LETTER[interval]}"

        retrieval_start = datetime.now(tz=timezone.utc).timestamp()
        lqn = (
            f"{output_version + '.' if output_version else ''}"
            + "+".join(exchanges)
            + "."
            + "+".join(currencies)
            + "."
            + "+".join(models)
        )

        # only retrieve lookback data if previous result is not present in input args
        fetch_lookback_data = previous_result["df"] is None
        raw_params_data, lookback_data, instruments, exchange_map = (
            retrieve_data(
                start=start,
                end=end,
                currencies=currencies,
                exchanges=list(exchanges),
                models=models,
                calc_types=types,
                freq=freq,
                interval=interval,
                include_listed_expiries=include_listed_expiries,
                version=output_version,
                tenor_mode=tenor_mode,
                debug=debug,
                consistent_read=consistent_read,
                fetch_lookback_data=fetch_lookback_data,
            )
        )

        retrieval_end = datetime.now(tz=timezone.utc).timestamp()
        logging.info(
            f"{lqn} data retrieval took {int(retrieval_end - retrieval_start)}s"
        )
        snapshot: ProcessDataSnapshot = prep_data_lambda(
            raw_params_data=raw_params_data,
            lookback_data=lookback_data,
            instruments=instruments,
            estimate_params=estimate_params,
            calc_types=types,
            exchange_map=exchange_map,
            include_listed_expiries=include_listed_expiries,
            tenor_mode=tenor_mode,
            freq=freq,
            start=start,
            end=end,
            time_condition=time_condition,
        )
        if not snapshot.params:
            raise ValueError(
                f"{lqn} retrieved data was empty, {start=}, {end=}"
            )

        # update previous result if present
        if not fetch_lookback_data:
            snapshot.previous_results = previous_result

        process_data_result = process_data(
            chunk=snapshot,
            interval=interval,
            periods=periods,
            smooth=smooth,
            estimate_params=estimate_params,
            scheduled_version=output_version,
            include_listed_expiries=include_listed_expiries,
        )

        result = process_data_result["calc_output"]
        lookback_result = process_data_result["lookback_result"]

        # Tenor_type columns unused in scheduled system
        result = drop_available_columns_from_df(
            df=result, columns=[t.value for t in TENOR_TYPES]
        )
        logging.info(
            f"{lqn} data processing took {int(datetime.now(tz=timezone.utc).timestamp() - retrieval_end)}s"
        )
        if result.empty:
            raise ValueError(f"{lqn} processed data was empty")

        first_cols = [
            "qualified_name",
            "timestamp",
            "isodate",
            "smoothed",
            "to_smooth",
            "window_fully_marked_to_smooth",
            LOOKBACK_COLUMN_NAME,
            "tenor_days",
        ]
        cols = list(filter(lambda f: f in result.columns, first_cols)) + list(
            set(result.columns) - set(first_cols)
        )
        s3_obj_key = f"volSmileCalc/{do_store.get('s3_object_prefix', '')}_{'+'.join(exchanges)}_{'+'.join(currencies)}_{'+'.join(models)}_{'+'.join(types)}_{freq}_{start}_{end}_{tenor_mode}_{output_version}_{do_store.get('s3_object_suffix', '')}.csv.gz"
        destination = f"s3://{do_store['s3_bucket']}/{s3_obj_key}"
        logging.info(
            f"Storing volsurface: https://s3.console.aws.amazon.com/s3/object/{do_store['s3_bucket']}/{s3_obj_key}"
        )

        # serialize the spline_info column here to avoid having to deserialize it again
        # when running in backfill/live mode where chunks from one `process_data` run are passed to the next
        serialized_result = serialize_columns(
            result, ["spline_info", "intermediate"]
        )
        utils_aws.put_s3_object_sync(
            S3,
            do_store["s3_bucket"],
            s3_obj_key,
            serialized_result[cols].to_csv(index=False),
        )

        # Including results as separate param to avoid json
        # serialisation (which doesn't work on DataFrames)
        if include_result_response and smooth:
            assert lookback_result["df"] is not None
            if LOOKBACK_COLUMN_NAME in result.columns:
                result = result[~result[LOOKBACK_COLUMN_NAME].eq(True)]
            addtl_response_params = {
                "result_df": merge_multi_index_lookback(
                    lookback_result["df"], result
                )
            }
        elif include_result_response:
            addtl_response_params = {"result_df": pd.DataFrame()}
        else:
            addtl_response_params = {}

        return {
            "statusCode": 200,
            "headers": utils_aws.lambda_response_headers("GET"),
            "body": orjson.dumps(
                {
                    "input": input,
                    "output": {
                        "status": 0,
                        "msg": "Success",
                        "results": "",
                        "storage_destination": destination,
                    },
                }
            ).decode("utf-8"),
            **addtl_response_params,
        }

    except Exception as e:
        logging.exception(f"Error e={e} while processing event={event}")
        return {
            "statusCode": 500,
            "headers": utils_aws.lambda_response_headers("GET"),
            "body": orjson.dumps(
                {
                    "input": input,
                    "output": {
                        "status": 1,
                        "msg": traceback.format_exc(),
                        "results": "",
                    },
                }
            ).decode("utf-8"),
        }


if __name__ == "__main__":
    event = cast(
        events.APIGatewayProxyEventV1,
        json.load(
            open(f"{pathlib.Path(__file__).parent.resolve()}/event.json")
        ),
    )
    context = cast(ctx.Context, None)
    lambda_handler(event, context)
    sys.exit()
