import logging
from typing import Any, Callable, cast

import numpy as np
import pandas as pd
import utils_general
from calc_helpers import estimate_missing_expiries, params_composite
from constants import (
    COMMON_METRIC_COLUMNS,
    DAYS_IN_ONE_YEAR,
    LYRA_SCORES_THRESHOLDS,
    MODEL_SPECIFIC_COLUMNS,
    QUALITY_R2_THRESHOLD,
    THRESHOLD_NO_LYRA,
    THRESHOLD_ONLY_LYRA,
)
from typings import (
    CalcType,
    CompositeIntermediate,
    ExpiryState,
    ListedTenorParams,
)
from utils.common import (
    SSMConfigSettings,
    get_expiry_iso_str_from_expiry_params,
    get_min_expiries_per_exchange,
)
from utils.composite import (
    calculate_confidence_score,
    calculate_exchange_surface_weights,
    calculate_expiry_scores,
    filter_exchanges,
    get_backup_spot_composite,
    get_forward_composite,
    get_R2_market,
    strikes_quantity_filter,
    strikes_retained_filter,
)
from utils.futures import populate_futures_indices


def create_exchange_df(
    model_params: list[ListedTenorParams], exchange: str, currency: str
) -> pd.DataFrame:
    exchange_df = pd.DataFrame(model_params, copy=False)
    exchange_df["is_SYN"] = exchange_df["underlying_index"].str.contains("SYN")

    # add identifiers for each entry in column
    # we will merge on these columns, so we exclude their suffixing
    exchange_df.columns = pd.Index(
        [
            (
                param_key + "_" + exchange
                if param_key not in ["underlying_index", "expiry", "timestamp"]
                else param_key
            )
            for param_key in exchange_df.columns
        ]
    )
    exchange_df.loc[:, "underlying_index"] = exchange_df[
        "underlying_index"
    ].replace(to_replace=r"SYN.", value="", regex=True)
    exchange_df.loc[:, f"expiry_state_{exchange}"] = ExpiryState.LISTED.value

    # add appropriate underlying_index col
    exchange_df = _format_underlying_indices(exchange_df, exchange, currency)

    return exchange_df


def handle_expiry_mismatch(df: pd.DataFrame, exchanges: list[str]) -> None:
    expiry_columns = [f"expiry_{ex}" for ex in exchanges]
    df.loc[:, "expiry"] = df.loc[:, expiry_columns].bfill(axis=1).iloc[:, 0]
    df.drop(columns=expiry_columns, inplace=True)


def merge_dataframes(
    to_merge_df: dict[str, pd.DataFrame],
) -> pd.DataFrame | None:
    merged_df = None
    for df in to_merge_df.values():
        if merged_df is None:
            merged_df = df
        else:
            merged_df = pd.merge(
                merged_df,
                df,
                on=["underlying_index", "expiry", "timestamp"],
                how="outer",
            )

    return merged_df


def add_exchanges_present_columns(
    df: pd.DataFrame, exchanges: list[str], column_suffix: str
) -> pd.DataFrame:
    def _get_exchange_names_from_expiry_state(
        row: "pd.Series[Any]", predicate: Callable[[Any], bool]
    ) -> list[str]:
        """
        Returns a list of exchange names for which the column name starts with "expiry_state_"
        and the corresponding value in the row equals the provided flag_value.
        """
        exchange_flags = [
            str(col)
            for col, value in row.items()
            if str(col).startswith("expiry_state_") and predicate(value)
        ]
        exchange_names = [name.split("_")[2] for name in exchange_flags]
        return exchange_names

    # exchanges that have the expiry present have expiry_state as listed
    df.loc[:, f"exchanges_present_{column_suffix}"] = df.apply(
        lambda row: _get_exchange_names_from_expiry_state(
            row, lambda x: x == ExpiryState.LISTED.value
        ),
        axis=1,
    )
    # estimated expiries should not have the ExpiryState as listed
    df.loc[:, f"estimated_exchanges_{column_suffix}"] = df.apply(
        lambda row: _get_exchange_names_from_expiry_state(
            row,
            lambda x: x
            not in [ExpiryState.LISTED.value, ExpiryState.EXCLUDED.value],
        ),
        axis=1,
    )

    df = _add_USDC_exchanges(df)

    return df


def create_merged_df_from_row(
    qn_dict: dict[str, list[ListedTenorParams]], currency: str
) -> tuple[pd.DataFrame, list[str]] | None:
    # function takes a row a dataframe frame and creates a merged dataframe from each exchange's modelParams
    # this merged dataframe will create a new column and extend it using the exchanges name. eg svi_a_deribit, svi_a_bybit

    to_merge_df = {}
    min_expiries = get_min_expiries_per_exchange(currency)
    for qn, model_params in qn_dict.items():
        version, qn_tokens = utils_general.get_qfn_and_version(qn)
        exchange = qn_tokens[0]

        # check if the exchange has an entry present at that particular timestamp
        if model_params:
            if len(model_params) < min_expiries:
                param_ts = model_params[0]["timestamp"]
                logging.error(
                    f"Removing {qn} from composite as not enough params found, {len(model_params)=} {param_ts} ({utils_general.to_iso(param_ts)})"
                )
                # Wont be enough to extrapolate expiries on to match other exchanges
                continue
            to_merge_df[exchange] = create_exchange_df(
                model_params, exchange, currency
            )
        else:
            logging.error(
                f"Empty listed expiries for {qn}, composite will exclude {exchange}"
            )

    exchanges_present_in_snap = list(to_merge_df.keys())
    merged_df = merge_dataframes(to_merge_df)

    if merged_df is None:
        return None

    merged_df.loc[:, "tenor_days"] = (
        merged_df.loc[:, "expiry"] * DAYS_IN_ONE_YEAR
    )
    merged_df.sort_values("expiry", inplace=True)
    merged_df.reset_index(drop=True)

    return merged_df, exchanges_present_in_snap


def _add_USDC_exchanges(df: pd.DataFrame) -> pd.DataFrame:
    def _apply_is_USDC(row: "pd.Series[Any]") -> list[str]:
        exchange_flags = [
            str(col)
            for col, value in row.items()
            if str(col).startswith("USDC_flag") and value == 1
        ]
        exchange_names = [name.split("_")[2] for name in exchange_flags]
        return exchange_names

    df.loc[:, "USDC_exchanges"] = df.apply(_apply_is_USDC, axis=1)
    return df


def _format_underlying_indices(
    df: pd.DataFrame, exchange: str, currency: str
) -> pd.DataFrame:
    """
    Function to unify underlying_index names from different exchanges. Assumes underlyings as this is an instrument representing the composite.

    """

    def _normalise_underlying_index(row: "pd.Series[Any]") -> str:
        iso_str = get_expiry_iso_str_from_expiry_params(row)
        return f"{currency}_USD_{iso_str}"

    underlying_index_cols = df.filter(like="underlying_index").columns

    # we mark columns that have USDC present
    df.loc[:, f"USDC_flag_{exchange}"] = (
        df.loc[:, underlying_index_cols]
        .apply(lambda col: col.str.contains("_USDC"))
        .any(axis=1)
        .astype(int)
    )

    for col in underlying_index_cols:
        df[col] = df.apply(
            _normalise_underlying_index,
            axis=1,
        )

    return df


def get_metric_columns(
    model: str, fallback_to_forward_composite: bool = False
) -> list[str]:
    """
    Returns the list of metric columns for a given model.

    :param model: The volatility surface model, e.g., "SVI" or "spline".
    :param fallback_to_forward_composite: Whether to include 'spot_cy' in output.
    :return: List of column names relevant for the model.
    """
    if model not in MODEL_SPECIFIC_COLUMNS:
        raise ValueError(f"Unknown model: {model}")

    metric_columns = COMMON_METRIC_COLUMNS + MODEL_SPECIFIC_COLUMNS[model]

    if fallback_to_forward_composite:
        metric_columns.append("spot_cy")

    return metric_columns


def create_composite(
    model_params_dict: dict[str, list[ListedTenorParams]],
    spot_index: float,
    futures_indices: dict[str, float],
    timestamp: int,
    model: str,
    exclusion_config: SSMConfigSettings,
    debug: bool = False,
) -> tuple[list[ListedTenorParams] | None, CompositeIntermediate | None]:
    isodate = utils_general.to_iso(timestamp)
    column_suffix: CalcType = "v2composite"
    intermediate: CompositeIntermediate | None = None
    composite_params_records: list[ListedTenorParams] | None = None

    if len(model_params_dict) == 0:
        logging.error(
            f"No exchanges with model params to construct composite, {timestamp=:}, {isodate=:}"
        )
        return None, None

    qn = next(iter(model_params_dict.keys()))
    version, qn_tokens = utils_general.get_qfn_and_version(qn)
    currency = qn_tokens[2]

    try:
        df_and_exchanges_present = create_merged_df_from_row(
            model_params_dict, currency
        )
        if df_and_exchanges_present is None:
            logging.error(
                f"No exchanges with model params to construct composite, {currency=:}, {timestamp=:}, {isodate=:}"
            )
            return None, None

        merged_df, exchanges_in_snap = df_and_exchanges_present

        merged_df = calculate_exchange_surface_weights(
            df=merged_df,
            exchanges=exchanges_in_snap,
            static_penalties=exclusion_config.static_exchange_penalties,
        )
        merged_df, removed_exchanges = filter_exchanges(
            df=merged_df,
            exchanges=exchanges_in_snap,
            conditions=[strikes_quantity_filter, strikes_retained_filter],
            timestamp=timestamp,
            currency=currency,
        )
        if removed_exchanges:
            # Rebalance and recalculate weights for the remaining exchanges. We will now only consider
            # the volumes, total strikes, total avg R2, for the REMAINING exchanges
            exchanges_in_snap = list(
                set(exchanges_in_snap) - set(removed_exchanges)
            )
            if not exchanges_in_snap:
                logging.error(
                    f"No valid exchanges with model params to construct composite after filtering, {currency=}, {timestamp=}, {isodate=}"
                )
                return None, None

            merged_df = calculate_exchange_surface_weights(
                df=merged_df,
                exchanges=exchanges_in_snap,
                static_penalties=exclusion_config.static_exchange_penalties,
            )

        # filter_R2
        merged_df = _reset_bad_r2_params(merged_df, exchanges_in_snap)

        # Interpolate and extrapolate missing expiries
        for exchange in exchanges_in_snap:
            merged_df = estimate_missing_expiries(
                df=merged_df,
                sparse_exchange=exchange,
                currency=currency,
                model=model,
                exclusion_config=exclusion_config,
            )

        # Calculate expiry-specific weights
        merged_df = calculate_expiry_scores(
            df=merged_df,
            exchanges=exchanges_in_snap,
            currency=currency,
            exclusion_config=exclusion_config,
        )

        # get exchanges present in snap
        merged_df = add_exchanges_present_columns(
            merged_df, exchanges_in_snap, column_suffix
        )

        merged_df = get_R2_market(merged_df, exchanges_in_snap)
        # inheriting scores from constituent exchanges
        merged_df.loc[:, f"strikes_prefilter_total_{column_suffix}"] = (
            merged_df[
                [f"strikes_prefilter_total_{ex}" for ex in exchanges_in_snap]
            ].sum(axis=1)
        )
        merged_df.loc[:, f"volume_24h_{column_suffix}"] = merged_df[
            [f"volume_24h_{ex}" for ex in exchanges_in_snap]
        ].sum(axis=1)
        merged_df.loc[:, f"strikes_postfilter_total_{column_suffix}"] = (
            merged_df[
                [f"strikes_postfilter_total_{ex}" for ex in exchanges_in_snap]
            ].sum(axis=1)
        )
        merged_df.loc[
            :, f"smoothed_strikes_prefilter_total_{column_suffix}"
        ] = merged_df[
            [
                f"smoothed_strikes_prefilter_total_{ex}"
                for ex in exchanges_in_snap
            ]
        ].sum(
            axis=1
        )

        # add spot composite to dataframe
        if spot_index:
            merged_df.loc[:, f"spot_{column_suffix}"] = spot_index
        else:
            logging.warning(
                f"Blockscholes Spot index not available, creating spot index from exchange modelparameters, {timestamp=}, {currency=}"
            )
            merged_df.loc[:, f"spot_{column_suffix}"] = (
                get_backup_spot_composite(merged_df, exchanges_in_snap)
            )

        merged_df, fallback_to_forward_composite = populate_futures_indices(
            df=merged_df,
            exchanges=exchanges_in_snap,
            futures_indices=futures_indices,
            column_suffix=column_suffix,
            timestamp=timestamp,
            currency=currency,
        )

        merged_df = params_composite(
            composite_snapshot=merged_df,
            composite_target_type=column_suffix,
            exchanges=exchanges_in_snap,
            currency=currency,
            model=model,
            debug=debug,
            exclusion_config=exclusion_config,
        )

        # remove composite names from the column names
        merged_df.rename(
            columns=lambda x: x.replace("_v2composite", ""), inplace=True
        )

        metric_columns = get_metric_columns(
            model, fallback_to_forward_composite
        )

        exchange_common_cols = [
            "R2",
            "R2_weight",
            "strikes_postfilter_total",
            "strikes_quantity_weight",
            "strikes_prefilter_total",
            "smoothed_strikes_prefilter_total",
            "strikes_retained_weight",
            "strikes_smoothed_weight",
            "volume_24h",
            "volumes_weight",
            "surface_weight",
            "expiry_score",
            "expiry_state",
        ]

        exchange_common_cols = [
            col + "_" + exchange
            for exchange in exchanges_in_snap
            for col in exchange_common_cols
        ]

        # convert nan's to none
        merged_df.replace({np.nan: None}, inplace=True)

        # filter out intermediate columns
        composite_df = merged_df.loc[:, metric_columns].sort_values("expiry")
        composite_params_records = cast(
            list[ListedTenorParams], composite_df.to_dict(orient="records")
        )

        intermediate_regex = "|".join(["weight", "surface"])
        identifiers = ["expiry", "underlying_index"]

        # save intermediate metrics
        exchange_metric_cols = [
            col
            for col in (
                exchange_common_cols
                + identifiers
                + [
                    "R2_model",
                    "R2_market",
                    "exchanges_present",
                    "estimated_exchanges",
                    "USDC_exchanges",
                ]
            )
            if col in merged_df.columns
        ]
        # regex filters out columns that have any of `intermediate_regex`
        exchange_metrics_df = (
            merged_df.loc[:, exchange_metric_cols]
            .filter(regex=f"^(?!.*({intermediate_regex})).*$")
            .sort_values("expiry")
        )

        # save surface weights
        weights_df = merged_df.loc[:, exchange_common_cols].filter(
            regex=f"{intermediate_regex}"
        )
        weights_df[
            [
                "total_unique_strikes",
                "volume_24h_total",
                "fallback_to_forward_composite",
            ]
        ] = merged_df[
            [
                "total_unique_strikes",
                "volume_24h_total",
                "fallback_to_forward_composite",
            ]
        ]

        # Check if all rows are equal.
        all_rows_equal = weights_df.eq(weights_df.iloc[0]).all(axis=None)
        assert (
            all_rows_equal
        ), "Weights are not the same along the composite surface"

        exchange_metrics_records = exchange_metrics_df.to_dict(orient="records")
        intermediate = {
            "scores": weights_df.iloc[0].to_dict(),
            "exchange_metrics": exchange_metrics_records,
        }

    except Exception as e:
        logging.exception(
            f"Error calculating v2composite, {currency=:}, {timestamp=:}, {isodate=:}, {e=:}"
        )
        return None, None

    return composite_params_records, intermediate


def create_supercomposite(
    model_params_dict: dict[str, list[ListedTenorParams]],
    intermediate_metrics_dict: CompositeIntermediate,
    supercomposite_exchange: str,
    timestamp: int,
    model: str,
    exclusion_config: SSMConfigSettings,
    debug: bool = False,
) -> tuple[list[ListedTenorParams] | None, CompositeIntermediate | None]:
    isodate = utils_general.to_iso(timestamp)
    column_suffix = supercomposite_exchange + "-supercomposite"
    qn = next(iter(model_params_dict.keys()))
    version, qn_tokens = utils_general.get_qfn_and_version(qn)
    currency: str = qn_tokens[2]

    supercomposite_records: list[ListedTenorParams] | None = None
    intermediate: CompositeIntermediate | None = None

    try:
        df_and_exchanges_present = create_merged_df_from_row(
            model_params_dict, currency
        )

        if (
            not df_and_exchanges_present
            or "v2composite" not in df_and_exchanges_present[1]
        ):
            logging.error(
                f"No v2composite params available, timestamp={timestamp}, currency={currency}, isodate={isodate}"
            )
            return None, None

        merged_df, exchanges_in_snap = df_and_exchanges_present

        metric_columns = get_metric_columns(model)

        score_columns = [
            f"strikes_quantity_score_{supercomposite_exchange}",
            f"R2_score_{supercomposite_exchange}",
            f"volume_24h_score_{supercomposite_exchange}",
            f"strikes_retained_score_{supercomposite_exchange}",
            f"strikes_smoothed_score_{supercomposite_exchange}",
            f"surface_weight_{supercomposite_exchange}",
            f"total_unique_strikes_{supercomposite_exchange}",
            "surface_weight_v2composite",
            "confidence_score",
            "confidence_score_ref",
        ]

        exchange_common_cols = [
            "R2",
            "strikes_postfilter_total",
            "strikes_prefilter_total",
            "smoothed_strikes_prefilter_total",
        ]
        extra_metrics: list[str] = []

        # extract metrics from intermediate dictionary
        # we nees to suffix it with _v2composite to make it compatible with functions downstream
        merged_df["total_unique_strikes_v2composite"] = [
            intermediate_metrics_dict["scores"]["total_unique_strikes"]
        ] * len(merged_df)

        temp_df = pd.DataFrame(
            intermediate_metrics_dict["exchange_metrics"]
        ).set_index("underlying_index")
        merged_df.loc[:, "volume_24h_v2composite"] = merged_df[
            "underlying_index"
        ].map(temp_df.filter(like="volume").sum(axis=1))
        merged_df["R2_v2composite"] = merged_df["underlying_index"].map(
            temp_df["R2_market"]
        )

        # no calibration for a certain timestamp
        if supercomposite_exchange not in exchanges_in_snap:
            logging.error(
                f"No model params for {supercomposite_exchange=:}, {currency=:}, {timestamp=:}, {isodate=:}"
            )

            # we are returning only the v2 composite, so confidence score is 0
            # and v2composite surface weight is 1
            merged_df.loc[:, "confidence_score"] = 0
            merged_df.loc[:, f"surface_weight_{supercomposite_exchange}"] = 0
            merged_df.loc[:, "surface_weight_v2composite"] = 1

            merged_df.rename(
                columns=lambda x: (
                    x.replace("_v2composite", "")
                    if (x.endswith("_v2composite") and x not in score_columns)
                    else x
                ),
                inplace=True,
            )

            (
                composite_fallback_records,
                intermediate,
            ) = save_supercomposite_data(
                merged_df,
                metric_columns,
                exchange_common_cols,
                extra_metrics,
                [
                    "confidence_score",
                    f"surface_weight_{supercomposite_exchange}",
                    "surface_weight_v2composite",
                ],  # this will be the only score column present
                timestamp,
            )
            return (
                composite_fallback_records,
                intermediate,
            )

        # calculate weights
        merged_df, confidence_score = calculate_confidence_score(
            merged_df, supercomposite_exchange, intermediate_metrics_dict
        )
        # we want to track the confidence scores value without the influence of
        # clipping by the individual score thresholds
        merged_df.loc[:, "confidence_score_ref"] = confidence_score

        # our composite will already be based on filtered out R2 values
        # so we should only filter out R2 values for the supercomposite exchange
        merged_df = _reset_bad_r2_params(merged_df, exchanges_in_snap)

        # Interpolate and extrapolate missing expiries
        # this also handles cases where supercomposite exchange has expiries
        # that the v2composite doesn't
        for exchange in exchanges_in_snap:
            merged_df = estimate_missing_expiries(
                df=merged_df,
                sparse_exchange=exchange,
                currency=currency,
                model=model,
                exclusion_config=exclusion_config,
            )

        # Note: expiry weights are intentionally not calculated for the supercomposite as they instead rely on the
        # confidence scores rather than surface weights.

        # get exchanges present in snap
        merged_df = add_exchanges_present_columns(
            merged_df, exchanges_in_snap, column_suffix
        )

        # Create a mask for each condition and combine them
        # Scores are suffixed with the exchange name
        mask = pd.concat(
            [
                merged_df[col + f"_{supercomposite_exchange}"] < threshold
                for col, threshold in LYRA_SCORES_THRESHOLDS.items()
            ],
            axis=1,
        ).any(axis=1)

        # if any row in the mask is True, set the overall confidence score to THRESHOLD_NO_LYRA.
        if mask.any():
            confidence_score = THRESHOLD_NO_LYRA
            merged_df.loc[:, "confidence_score"] = confidence_score

        if confidence_score >= THRESHOLD_ONLY_LYRA:
            # all the weight gets assigned the supercomposite exchnage e.g lyra
            # this is for storage only and is not used in any calcs downstream
            merged_df.loc[:, f"surface_weight_{supercomposite_exchange}"] = 1
            merged_df.loc[:, "surface_weight_v2composite"] = 0

            # returned values will only be from column that do not have _{exchange} which will be lyra data
            # we need to save score columns but they are also suffixed with the exchange name
            # so we exclude columns that are in score_columns
            merged_df.rename(
                columns=lambda x: (
                    x.replace(f"_{supercomposite_exchange}", "")
                    if (
                        x.endswith(f"_{supercomposite_exchange}")
                        and x not in score_columns
                    )
                    else x
                ),
                inplace=True,
            )

        elif confidence_score <= THRESHOLD_NO_LYRA:
            # all the weight gets assigned our composite
            # this is for storage only and is not used in any calcs downstream
            merged_df.loc[:, f"surface_weight_{supercomposite_exchange}"] = 0
            merged_df.loc[:, "surface_weight_v2composite"] = 1

            # we need to return composite data and all its consituents
            merged_df.rename(
                columns=lambda x: (
                    x.replace("_v2composite", "")
                    if (x.endswith("_v2composite") and x not in score_columns)
                    else x
                ),
                inplace=True,
            )

        elif (
            confidence_score < THRESHOLD_ONLY_LYRA
            and confidence_score > THRESHOLD_NO_LYRA
        ):
            final_weight = (confidence_score - THRESHOLD_NO_LYRA) / (
                THRESHOLD_ONLY_LYRA - THRESHOLD_NO_LYRA
            )
            # we will only ever be dealing with 2 "exchanges" in the supercomposite
            # the surface weight here is unlike the confidence score, and determines
            # how we weight the composite when we are within the confidence interval
            merged_df.loc[:, f"surface_weight_{supercomposite_exchange}"] = (
                final_weight
            )
            merged_df.loc[:, "surface_weight_v2composite"] = 1 - final_weight
            merged_df = get_R2_market(merged_df, exchanges_in_snap)
            # calculate new spot composite
            merged_df.loc[:, f"spot_{column_suffix}"] = (
                get_backup_spot_composite(merged_df, exchanges_in_snap)
            )

            # calculate forward
            merged_df = get_forward_composite(
                merged_df, exchanges_in_snap, column_suffix
            )

            merged_df = params_composite(
                composite_snapshot=merged_df,
                composite_target_type=column_suffix,
                exchanges=exchanges_in_snap,
                currency=currency,
                model=model,
                debug=debug,
            )

            merged_df.rename(
                columns=lambda x: (
                    x.replace(f"_{column_suffix}", "")
                    if x.endswith(f"_{column_suffix}")
                    and x not in score_columns
                    else x
                ),
                inplace=True,
            )

            metric_columns.extend(["R2_market"])
            if debug:
                metric_columns.append("R2_model")
            exchange_common_cols.append("volume_24h")
            extra_metrics = [
                "exchanges_present",
                "estimated_exchanges",
                "USDC_exchanges",
            ]

            # if we are returning the supercomposite, then we want to
            # return metrics for individual exchanges
            exchange_common_cols = [
                col + "_" + exchange
                for exchange in exchanges_in_snap
                for col in exchange_common_cols
            ]

        # The expiry estimation process only populates calibration-related columns (e.g., "svi_a", "forward", "R2").
        # This leaves surface-wide 'score' and total_unique_strike columns with NaNs. The following line fills these NaN values.
        colums_to_fill = [
            *merged_df.filter(like="score").columns.tolist(),
            f"total_unique_strikes_{supercomposite_exchange}",
        ]
        scores_df = merged_df.loc[:, colums_to_fill]
        backfilled = scores_df.fillna(method="bfill")
        fully_filled = backfilled.fillna(method="ffill")
        merged_df.loc[:, colums_to_fill] = fully_filled

        (
            supercomposite_records,
            intermediate,
        ) = save_supercomposite_data(
            merged_df,
            metric_columns,
            exchange_common_cols,
            extra_metrics,
            score_columns,
            timestamp,
        )

    except Exception as e:
        logging.exception(
            f"Error calculating supercomposite, {currency=:}, {timestamp=:}, {isodate=:}, {e=:}"
        )

    return (supercomposite_records, intermediate)


def save_supercomposite_data(
    merged_df: pd.DataFrame,
    metric_columns: list[str],
    exchange_common_cols: list[str],
    extra_metrics: list[str],
    score_columns: list[str],
    timestamp: int,
) -> tuple[list[ListedTenorParams], CompositeIntermediate]:
    # convert nan's to none
    merged_df.replace({np.nan: None}, inplace=True)

    # filter out intermediate columns
    supercomposite_df = merged_df.loc[:, metric_columns].sort_values("expiry")
    supercomposite_records = cast(
        list[ListedTenorParams], supercomposite_df.to_dict(orient="records")
    )

    intermediate_patterns = "|".join(["weight", "surface", "score"])

    identifiers = [
        "expiry",
        "underlying_index",
    ]
    # save intermediate metrics
    # regex filters out columns that have any of `intermediate_patterns`
    exchange_metrics_df = (
        merged_df.loc[:, exchange_common_cols + identifiers + extra_metrics]
        .filter(regex=f"^(?!.*({intermediate_patterns})).*$")
        .sort_values("expiry")
    )
    exchange_metrics_df.loc[:, "timestamp"] = [timestamp] * len(
        exchange_metrics_df
    )

    # save surface weights
    weights_df = merged_df.loc[:, score_columns]

    # Check if all rows are equal
    all_rows_equal = weights_df.eq(weights_df.iloc[0]).all(axis=None)
    assert (
        all_rows_equal
    ), "Weights are not the same along the composite surface"

    exchange_metrics_records = exchange_metrics_df.to_dict(orient="records")
    intermediate: CompositeIntermediate = {
        "scores": weights_df.iloc[0].to_dict(),
        "exchange_metrics": exchange_metrics_records,
    }

    return supercomposite_records, intermediate


def _reset_bad_r2_params(
    df: pd.DataFrame, exchanges: list[str]
) -> pd.DataFrame:
    """
    This function filters expiries of by R2 by setting the values of the params as nans.
    Downstream interpolation function will interpret these are missing params and will interpolate
    appropriately.

    """

    R2_columns = [f"R2_{ex}" for ex in exchanges]
    to_filter_R2_mask = df[R2_columns] < QUALITY_R2_THRESHOLD
    # we want to keep this column present
    columns_to_keep = ["weight", "spot"]

    for exchange in exchanges:
        exchange_mask = to_filter_R2_mask[f"R2_{exchange}"]
        indices = exchange_mask[exchange_mask].index.tolist()
        exchange_cols = [
            col
            for col in df.columns
            if col.endswith(exchange)
            and not any(keep in col for keep in columns_to_keep)
        ]
        # set as nan so their values don't contibute to calcs downstream
        df.loc[indices, exchange_cols] = np.nan

    return df
