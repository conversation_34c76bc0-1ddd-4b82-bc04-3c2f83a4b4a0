import asyncio
import json
import logging
import math
import os
import time
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from functools import partial
from typing import Any, cast

import utils_general
from block_stream.agent import Agent
from block_stream.channel import Channel
from block_stream.consumer import (
    Consumer,
    Tick<PERSON>alc<PERSON>ache,
    WaitFor<PERSON>et<PERSON>alculationCache,
    auto_scaling_cache_manager,
    cancel_all_tasks,
)
from block_stream.typings import InstrumentsDetailsMap
from block_stream.utils.metrics import CallableMetric, MetricWorker, RateMetric
from calc_helpers import SMOOTH_PARAMS
from constants import (
    BLOCKSTREAM_ENDPOINT,
    LOOKBACK_COLUMN_NAME,
    METRIC_S,
    SMOOTHING_CALC_TYPES,
    SPIKED_PARAMS_COLUMN_NAME,
    TENOR_TYPES,
    UNIX_MINUTE_NS,
)
from lambda_types import (
    CalcType,
    ParamsCalcData,
    PreviousResults,
    ProcessDataResult,
    TenorMode,
)
from prep_data import get_calculated_data_from_raw_data, prep_data
from process_data import process_data
from retrieve_data import retrieve_data
from stream_flex_function import ActiveFunction
from stream_flex_sub_manager import (
    FlexConsumerSubscriptionManager,
    expiry_metric,
)
from utils.common import merge_multi_index_lookback, round_value
from utils.outputs import undo_create_output_col
from utils_calc import DEFAULT_SURFACE_DELTAS, DEFAULT_SURFACE_MONEYNESS
from utils_calc.utils_calc_types import Model
from utils_general.aggregate_logger import get_agg_logger, setup_agg_logger

utils_general.setup_python_logger(os.environ.get("LOG_LEVEL", logging.INFO))

agent = Agent("VolSmileFlexStream", endpoint=BLOCKSTREAM_ENDPOINT)
tick_data_stream = agent.channel(
    "tickData",
    auto_flush_s=None,
    seconds_per_checkpoint=None,
    consumer_mode="fanout",
    output_mode="multiple",
)

subscription_manager = FlexConsumerSubscriptionManager(
    calculator="volSmileFlex",
    FunctionClass=ActiveFunction,
    time_to_remove_cache_secs=60 * 5,
)

calculation_rate_metric = RateMetric()
metric_worker = MetricWorker(
    METRIC_S,
    {
        "calculation_per_s": calculation_rate_metric,
        "active_functions": CallableMetric(
            subscription_manager.get_active_function_count
        ),
        "total_expiries": expiry_metric,
    },
    component_name="VolSmileFlexStream",
)


def _create_calc_cache(
    function_id: str,
    caches_output: dict[str, ParamsCalcData],
    c_read: bool,
    version: str,
    c_types: list[CalcType],
    tenor_mode: TenorMode,
    estimate_params: bool,
    interval: str,
    periods: int,
    smooth: bool,
) -> WaitForSetCalculationCache:
    exchange, currency, model = function_id.split(".")
    model = cast(Model, model)

    start = utils_general.to_iso(
        datetime.now(tz=timezone.utc) - timedelta(minutes=5)
    )
    end = utils_general.to_iso(datetime.now(tz=timezone.utc))

    raw_params_data, lookback_data, instruments, exchange_map = retrieve_data(
        start=start,
        end=end,
        currencies=[currency],
        exchanges=[exchange],
        models=[model],
        calc_types=c_types,
        freq="live",
        interval="minute",
        include_listed_expiries=True,
        version=version,
        debug=False,
        consistent_read=c_read,
        tenor_mode=tenor_mode,
    )

    interval_delta_ns = 60 * 1e9
    lookback_window_size = SMOOTH_PARAMS["20s"]["zscore"]["window"]
    lookback_window_ns = lookback_window_size * interval_delta_ns
    for d in lookback_data:
        _, qn_tokens = utils_general.get_qfn_and_version(d["qualified_name"])
        d["qualified_name"] = ".".join(qn_tokens)
    calculated_data = get_calculated_data_from_raw_data(
        raw_params_data=raw_params_data, lookback_data=lookback_data
    )
    calculated_data["params"] = []
    # We are using the cache to store last listed params data along with keeping the lookback timeseries updated
    tick_cache = TickCalcCache(
        predicate_fn=partial(
            predicate,
            exchange=exchange,
            currency=currency,
            model=model,
        ),
        preloaded_calc_input={},
        prefetched_instruments=cast(InstrumentsDetailsMap, instruments),
        preloaded_calc_output=cast(dict[str, Any], calculated_data),
        tick_calc_fn=process_calc_tick,
        cleanup_fn=partial(clean_up, lookback_window_ns=lookback_window_ns),
        return_instruments=True,
    )

    calc_cache = WaitForSetCalculationCache(
        name=function_id,
        caches=[tick_cache],
        exchange_map=exchange_map,
        exchanges=[exchange],
        include_listed_expiries=True,
        smooth=smooth,
        interval=interval,
        periods=periods,
        tenor_mode=tenor_mode,
        freq="live",
        estimate_params=estimate_params,
        generate_dynamic_params=partial(
            subscription_manager.get_run_vars, function_id
        ),
        set_to_wait_for={f"{exchange}.option.{currency}.{model}.live.params"},
        timestamp_to_qfn_map={},
    )
    calc_cache.on_clean_up()

    # Add calculated_data reference to shared dict to allow updating lookback_df inside _put_output
    caches_output[function_id] = calculated_data

    return calc_cache


def process_calc_tick(
    tick: Any,
    calc_input: dict[str, Any],
    calc_output: dict[str, Any],
    *args: Any,
) -> dict[str, Any]:
    """
    Note: This function process each tick update accepted by the cache (listed params).
    A reference to the calc_output is shared with the _put_output fn to allow updating lookback_df after calculation
    """

    calc_output["params"] = [tick]

    return calc_output


def predicate(
    tick: Any,
    instruments: dict[str, Any],
    exchange: str,
    currency: str,
    model: str,
) -> bool:
    qn = tick["q"].split(".")
    if (
        len(qn) == 6
        and qn[0] == exchange
        and qn[1] == "option"
        and qn[2] == currency
        and qn[3] == model
        and qn[-1] == "params"
    ):
        return True
    elif (  # Option contracts needed for strike output
        len(qn) == 3
        and qn[0] == exchange
        and qn[2] == "contracts"
        and tick.get("baseAsset") == currency
        and tick.get("strike")
    ):
        return True
    return False


async def _put_output(
    output_data_stream: Channel,
    result: list[Any] | ProcessDataResult,
    caches_output: dict[str, ParamsCalcData],
) -> None:
    result = cast(ProcessDataResult, result)
    rows = result["calc_output"].to_dict(orient="records")

    if rows:

        processed_qns = []

        qfn_split = rows[0]["qualified_name"].split(".")
        timestamp = rows[0]["timestamp"]

        # exchange.currency.model
        key = f"{qfn_split[0]}.{qfn_split[2]}.{qfn_split[3]}"
        for row in rows:
            json_v = utils_general.json_loads(row)
            # Refrain from allowing spiked params to be sent
            contains_spikes = row.get(SPIKED_PARAMS_COLUMN_NAME, False)
            if contains_spikes and not math.isnan(contains_spikes):
                utils_general.log_bsdebug(
                    "Skipping params qfn= %s", json_v.get("qualified_name")
                )
                continue

            qfn: str = row["qualified_name"]
            clean_stream_output(qfn=qfn, json_value=json_v)

            if scheduled_version and qfn.startswith(scheduled_version):
                qfn = qfn[len(scheduled_version) + 1 :]

            r = {
                "q": qfn,
                "v": json_v,
                "t": row["timestamp"],
            }

            processed_qns.append(qfn)

            await output_data_stream.put(r, key=key)

        await output_data_stream.flush()

        aggregated_qns = aggregate_qns_and_calc_type_for_logging(processed_qns)
        logging.info(
            "Stream output successful for key: %s, at timestamp: %s. Details: %s",
            key,
            timestamp,
            aggregated_qns,
        )

        calculation_rate_metric.add()

        if key not in caches_output:
            logging.error(f"invalid key {key}")
        else:
            update_lookback_df(
                results=result,
                previous_ref=caches_output[key]["previous_results"],
                timestamp=timestamp,
            )
    else:
        logging.error("No data to put in output stream")


def clean_stream_output(qfn: str, json_value: dict[str, Any]) -> None:
    # Drop all NaNs - this causes trouble for the streamed data
    nans = [
        k
        for k, v in json_value.items()
        if isinstance(v, float) and math.isnan(v)
    ]
    for k in nans:
        if should_log_and_delete(qfn=qfn, k=k):
            log_and_delete_key(qfn=qfn, key=k, json_v=json_value)
        else:
            del json_value[k]

    # remove TenorTypes
    for t in TENOR_TYPES:
        if t.value in json_value:
            del json_value[t.value]


def should_log_and_delete(qfn: str, k: str) -> bool:
    if qfn.endswith("moneyness") and "money" in k:
        if (
            undo_create_output_col(suffix="money", input_col=k)
            in DEFAULT_SURFACE_MONEYNESS
        ):
            return True
    elif (
        any(qfn.endswith(suffix) for suffix in ["smile", "skew", "butterfly"])
        and "delta" in k
    ):
        if (
            undo_create_output_col(suffix="delta", input_col=k)
            in DEFAULT_SURFACE_DELTAS
        ):
            return True
    elif qfn.endswith("params") and ("sabr_" in k or "svi_" in k):
        return True
    return False


def log_and_delete_key(qfn: str, key: str, json_v: Any) -> None:
    logging.warning(
        f"Removing Key {key} from json due to having NaN value {qfn=}."
    )
    del json_v[key]


def aggregate_qns_and_calc_type_for_logging(
    qualified_names: list[str],
) -> dict[str, list[str]]:
    """
    Aggregates qualified names into a dictionary based on currency and unique_tenor,
    and logs the structure.

    :param qualified_names: The list of processed qualified names.
    :return: A dictionary mapping each `currency.unique_tenor` to a list of corresponding `qn_suffix` values.

    """
    qn_dict = defaultdict(list)

    for qfn in qualified_names:
        qn_parts = utils_general.get_qfn_and_version(qfn)[1]

        currency = qn_parts[2]
        unique_tenor = qn_parts[4]
        calc_type = qn_parts[6]

        log_key = f"{currency}.{unique_tenor}"
        qn_dict[log_key].append(calc_type)

    aggregated_qns = dict(qn_dict)

    return aggregated_qns


def update_lookback_df(
    results: ProcessDataResult,
    previous_ref: PreviousResults,
    timestamp: int,
) -> None:

    calc_output = results["calc_output"]
    calc_output = calc_output[
        calc_output["qualified_name"].str.endswith(tuple(SMOOTHING_CALC_TYPES))
    ]

    # Set lookback to True
    calc_output.loc[:, LOOKBACK_COLUMN_NAME] = True

    # Timestamp rounded to the previous minute, required by live data with
    # frequency < 1m for smoothing, as highest precision stored data is 1m
    record_t_rounded = int(
        round_value(
            value=timestamp,
            round_to_nearest=UNIX_MINUTE_NS,
            rounding_strategy="floor",
        )
    )

    # Update cache with new results which are used for smoothing in subsequent iterations.
    # The cache may already contain qualified name + timestamp pairs same as current results
    # from previous iterations due to running at 20 sec frequency but smoothing on minute data.
    # In this case, data is overwritten with the new results.
    calc_output["timestamp"] = record_t_rounded
    if previous_ref["df"] is None:
        if (
            results["lookback_result"]["df"] is None
            or results["lookback_result"]["df"].empty
        ):
            previous_ref["df"] = calc_output
        else:
            previous_ref["df"] = merge_multi_index_lookback(
                results["lookback_result"]["df"], calc_output
            )
    else:
        if (
            results["lookback_result"]["df"] is not None
            and not results["lookback_result"]["df"].empty
        ):
            # the lookback_dfs returned by different processes may differ due to the processing
            # of different subscription messages. We need our lookback to be a union of all lookbacks returned
            # from all processes
            new_lookback_df = merge_multi_index_lookback(
                previous_df=previous_ref["df"],
                new_df=results["lookback_result"]["df"],
                method="combine_first",
            )
            previous_ref["df"] = merge_multi_index_lookback(
                new_lookback_df, calc_output
            )
        else:
            logging.error(
                "Lookback df returned from process data is empty! Falling back to using previous lookback instead"
            )
            previous_ref["df"] = merge_multi_index_lookback(
                previous_ref["df"], calc_output
            )


def clean_up(
    calc_input: dict[str, Any],
    calc_output: dict[str, Any],
    expired_inst: set[str],
    lookback_window_ns: int,
) -> tuple[dict[str, Any], dict[str, Any]]:
    if (
        calc_output["previous_results"]["df"] is None
        or calc_output["previous_results"]["df"].empty
    ):
        logging.warning("empty dataframe to clean")
        return calc_input, calc_output

    last_valid_timestamp = (
        int(
            round_value(
                value=time.time_ns(),
                round_to_nearest=UNIX_MINUTE_NS,
                rounding_strategy="floor",
            )
        )
        - lookback_window_ns
    )
    before_rows = calc_output["previous_results"]["df"].shape[0]
    # Slice and update cache to remove stale items, prevent memory leak
    calc_output["previous_results"]["df"] = calc_output["previous_results"][
        "df"
    ][
        calc_output["previous_results"]["df"].index.get_level_values(
            "timestamp"
        )
        >= last_valid_timestamp
    ]

    after_rows = (
        calc_output["previous_results"]["df"].shape[0]
        if calc_output["previous_results"]["df"] is not None
        else 0
    )
    logging.info(
        f"successful cleaning cache at {utils_general.to_iso(last_valid_timestamp)}: {calc_output['previous_results']['df'].shape} - row deleted: {before_rows - after_rows}"
    )
    return calc_input, calc_output


if __name__ == "__main__":
    setup_agg_logger()
    with get_agg_logger() as logger:
        frequency_seconds: int = int(os.environ.get("frequency_seconds", "30"))
        tenor_mode: TenorMode = cast(TenorMode, os.environ.get("tenor_mode"))
        smooth: bool = os.environ.get("smooth") == "True"
        consistent_read: bool = os.environ.get("consistent_read") == "True"
        scheduled_version: str | None = os.environ.get("scheduled_version")
        calc_types = list(SMOOTHING_CALC_TYPES)
        estimate_params = bool(os.environ.get("estimate_params", True))

        # Keyed by asset type for ease of catalog construction
        fix_target_jobs: list[str] = json.loads(
            os.environ.get("FIXED_TARGET_JOBS", "[]")
        )
        # exchange.currency.model -> ParamsCalcData
        shared_caches_output: dict[str, ParamsCalcData] = {}
        while True:
            try:
                scaling_cache = auto_scaling_cache_manager.AutoScalingCacheManager(
                    target_jobs=subscription_manager.get_target_jobs(),
                    create_cache_fn=partial(
                        _create_calc_cache,
                        caches_output=shared_caches_output,
                        c_read=consistent_read,
                        version=scheduled_version,
                        c_types=calc_types,  # This defines accepted datapoints from stream
                        tenor_mode=tenor_mode,
                        estimate_params=estimate_params,
                        interval="second",
                        periods=frequency_seconds,
                        smooth=smooth,
                    ),
                    data_set_freq_key="frequency_seconds",
                    allow_dynamic_target_jobs=True,
                )

                # Associate scaling_cache and fix_target_jobs list with subscription_manager
                # This would allow subscription_manager to modify the caches running in the consumer based on subscriptions
                subscription_manager.set_auto_scaling_cache_manager(
                    auto_scaling_cache_manager=scaling_cache,
                    fix_target_jobs=fix_target_jobs,
                )

                consumer = Consumer(
                    consumer_name="volSmileFlex",
                    input_data_stream=tick_data_stream,
                    output_data_stream=tick_data_stream,
                    mode="wait-for-set",
                    frequency_ns=int(frequency_seconds * 1e9),
                    caches=scaling_cache,
                    prep_data=prep_data,
                    process_chunk_helper=process_data,  # type: ignore
                    put_output=partial(
                        _put_output,
                        caches_output=shared_caches_output,
                    ),
                    clean_up_interval_secs=300,
                    metric_seconds=METRIC_S,
                    allowed_calc_lag_metric_stages=[
                        "start_lag",
                        "start_calc_lag",
                        "prep_data_lag",
                        "process_data_lag",
                        "put_output_lag",
                        "total_lag",
                    ],
                    copy_data_on_calc=True,
                )

                loop = asyncio.get_event_loop()
                loop.run_until_complete(
                    asyncio.gather(
                        subscription_manager.async_run(),
                        subscription_manager.expiry_watcher(),
                        subscription_manager.cleaning_target_jobs_watchdog(),
                        consumer.async_run(),
                        metric_worker.run(),
                    )
                )

            except Exception as e:
                logging.exception(f"Run failed: {e}")
                cancel_all_tasks()
                time.sleep(10)  # Time to recover, reduce log spew
