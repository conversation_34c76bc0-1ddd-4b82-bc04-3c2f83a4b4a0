import logging
import math
import warnings
from datetime import datetime, timezone
from functools import partial

import numpy as np
import pandas as pd
import utils_calc
import utils_calc.utils_calc_types
import utils_general
from calc_helpers import DELTAS_STR, MONEYNESS_STR, finalize_result_df
from constants import (
    EXPIRY_PRECISION,
    EXPIRY_TOLERANCE,
    SPLINE_EXTRAPOLATION_DELTA,
    TENOR_PRECISION,
    load_domestic_rates,
)
from lambda_types import (
    ListedTenorParamsSpline,
    NDArrayFloat64,
    ParamsCalcResult,
    TenorExpiryArbFields,
)
from utils.calibrate import fit_spline_with_exception
from utils.common import (
    check_calibrated_vols_and_log_error_with_details,
    round_value,
)
from utils.extrapolate import get_strikes_and_vols_to_extrapolate
from utils.interpolate import get_strikes_to_interpolate
from utils_calc import (
    DEFAULT_SURFACE_MONEYNESS,
    CalibrationQuery,
    CubicSpline,
    Model,
    get_domestic_rate,
)

warnings.simplefilter(action="ignore", category=RuntimeWarning)


def _interpolate_vol(
    frac: float,
    exp: float,
    lower_bordering_expiry: pd.Series,  # type: ignore
    upper_bordering_expiry: pd.Series,  # type: ignore
    lower_bordering_strikes: NDArrayFloat64,
    upper_bordering_strikes: NDArrayFloat64,
) -> NDArrayFloat64:
    lower_bordering_vols = utils_calc.spline_model_vol(
        lower_bordering_strikes.tolist(),
        CubicSpline(spline_info=lower_bordering_expiry["spline_info"]),
    )
    upper_bordering_vols = utils_calc.spline_model_vol(
        upper_bordering_strikes.tolist(),
        CubicSpline(spline_info=upper_bordering_expiry["spline_info"]),
    )

    lower_bordering_vars: NDArrayFloat64 = lower_bordering_expiry[
        "expiry"
    ] * np.square(np.array(lower_bordering_vols))

    upper_bordering_vars: NDArrayFloat64 = upper_bordering_expiry[
        "expiry"
    ] * np.square(np.array(upper_bordering_vols))
    interpolated_vars: NDArrayFloat64 = (
        1 - frac
    ) * lower_bordering_vars + frac * upper_bordering_vars
    interpolated_vols: NDArrayFloat64 = np.sqrt(interpolated_vars / exp)

    return interpolated_vols


def _extrapolate_call_wing_if_needed(
    strikes: list[float],
    vols: list[float],
    spot: float,
    forward: float,
    exp: float,
    rd: float,
) -> tuple[list[float], list[float]]:
    """
    Extrapolates the call wing by appending strikes at higher values to reach target delta.

    For call wings, especially at longer expiries, we often need to append many high-strike
    OTM call points to reach target delta. To prevent extrapolated vols from growing too aggressively
    as strikes increase rapidly, we start with a reasonably estimated total variance slope and
    gradually reduce (trim) it if convergence stalls. We also accelerate the strike spacing
    to avoid excessive looping.
    """
    if len(vols) <= 1:
        return strikes, vols

    max_strike = strikes[-1]
    second_max_strike = strikes[-2]
    strike_step = max_strike - second_max_strike
    assert strike_step > 0
    vol_at_max_strike = vols[-1]
    vol_at_second_max_strike = vols[-2]
    max_strike_total_var = (vol_at_max_strike**2) * exp
    second_max_strike_total_var = (vol_at_second_max_strike**2) * exp

    total_var_slope = (max_strike_total_var - second_max_strike_total_var) / (
        np.log(max_strike / second_max_strike)
    )
    total_var_slope = np.clip(total_var_slope, 0, 2)

    min_delta = utils_calc.option_delta(
        spot,
        forward,
        max_strike,
        exp,
        rd,
        vol_at_max_strike,
        1,
    )

    new_strike_counter = 0
    trim_slope = False
    new_strike = max_strike
    try:
        while (
            min_delta > SPLINE_EXTRAPOLATION_DELTA and new_strike_counter < 50
        ):
            new_strike_counter += 1
            new_strike_last = new_strike
            new_strike = strike_step + new_strike_last
            strike_step += strike_step  # accelerating strike increments
            if new_strike_counter >= 10 or total_var_slope >= 1.0:
                trim_slope = True  # Trim var slope if its too high or min delta is not reached in some iterations
            if trim_slope:
                total_var_slope = 0.9 * total_var_slope
            new_total_var = max_strike_total_var + total_var_slope * (
                np.log(new_strike / new_strike_last)
            )
            new_vol = np.sqrt(new_total_var / exp)
            max_strike_total_var = new_total_var
            new_delta = utils_calc.option_delta(
                spot,
                forward,
                new_strike,
                exp,
                rd,
                new_vol,
                1,
            )
            vols.append(new_vol)
            strikes.append(new_strike)
            min_delta = min(min_delta, new_delta)

    except Exception as e:
        logging.exception(f"Call wing extrapolation failed for {exp=}: {e}")

    return strikes, vols


def _extrapolate_put_wing_if_needed(
    strikes: list[float],
    vols: list[float],
    spot: float,
    forward: float,
    exp: float,
    rd: float,
) -> tuple[list[float], list[float]]:
    """
    Extrapolates the put wing by prepending strikes at lower values to reach target delta.

    Put wings often require just a few extra deep OTM strikes, typically in shorter expiries or
    edge cases. Since strike values approach zero in these scenarios, the logic is more conservative
    using small geometric steps downward and early checks for invalid steps.
    If the extrapolated skew appears unrealistically upward (positive variance slope), it is forcibly
    corrected to a small negative value.
    """

    MAX_NEW_STRIKES = 50

    if len(vols) <= 1:
        return strikes, vols

    min_strike, vol_min = strikes[0], vols[0]
    second_min_strike, vol_second = strikes[1], vols[1]
    strike_step = second_min_strike - min_strike
    assert strike_step > 0

    var1 = (vol_min**2) * exp
    var2 = (vol_second**2) * exp

    # Log-moneyness slope
    slope = (var2 - var1) / np.log(second_min_strike / min_strike)

    # If upward skew, correct it to a small negative slope
    if slope > 0.0:
        logging.warning(
            f"Put skew slope positive ({slope:.4f}); adjusting to -0.01"
        )
        slope = -0.01

    slope = np.clip(slope, -1.0, 0.0)

    new_strike = min_strike
    put_delta = utils_calc.option_delta(
        spot, forward, new_strike, exp, rd, vol_min, -1
    )

    if abs(put_delta) <= SPLINE_EXTRAPOLATION_DELTA:
        return strikes, vols

    new_strike_counter = 0

    def _next_put_strike(
        k_prev: float,
        strike_step: float,
        gap_ln: float = 0.3,
        min_factor: float = 0.90,
    ) -> float:
        """
        Computes the next lower strike with geometric/logarithmic spacing,
        constrained by strike_step (e.g., from original grid).
        """
        # Candidate based on log decrement
        k_prop = k_prev * np.exp(-gap_ln)

        # Enforce a minimum reduction either as % or absolute step
        k_prop = min(k_prop, k_prev - strike_step)
        k_prop = max(k_prop, k_prev * min_factor)

        if k_prop < 1e-4 or k_prop >= k_prev:
            raise StopIteration("Strike too small or non-decreasing")

        return float(k_prop)

    try:
        while (
            abs(put_delta) > SPLINE_EXTRAPOLATION_DELTA
            and new_strike_counter < MAX_NEW_STRIKES
        ):
            new_strike_counter += 1
            new_strike_last = new_strike

            # Step down geometrically to get next strike
            try:
                new_strike = _next_put_strike(new_strike_last, strike_step)
            except StopIteration:
                logging.warning(
                    "Next strike computed too close to zero during put wing extrapolation. Stopping."
                )
                break

            # Extrapolate using total variance slope
            new_total_var = var1 + slope * np.log(new_strike / min_strike)
            new_vol = np.sqrt(new_total_var / exp)

            # Update state
            put_delta = utils_calc.option_delta(
                spot, forward, new_strike, exp, rd, new_vol, -1
            )

            # Prepend new vol and strike
            vols.insert(0, new_vol)
            strikes.insert(0, new_strike)

            # Roll forward anchor strike/var
            min_strike = new_strike
            var1 = new_total_var

    except Exception as e:
        logging.exception(f"Put wing extrapolation failed for {exp=}: {e}")

    return strikes, vols


def _calculate_vol_to_calibrate(
    snap_data: pd.DataFrame,
    exp: float,
    model: Model,
    spot: float,
    exchange: str,
) -> tuple[float, list[float], float, float, list[float]]:
    (
        lower_bordering_expiry,
        upper_bordering_expiry,
    ) = utils_calc.get_bordering_expiries(snap_df=snap_data, target_expiry=exp)

    if lower_bordering_expiry is None or upper_bordering_expiry is None:
        raise Exception(
            f"Both bordering Listed expiries must be present: "
            f"{lower_bordering_expiry=}"
            f"{upper_bordering_expiry=}"
        )

    # interpolate forward
    frac = (exp - lower_bordering_expiry["expiry"]) / (
        upper_bordering_expiry["expiry"] - lower_bordering_expiry["expiry"]
    )
    forward_inter = utils_calc.interpolate_forward(
        upper_bounding_expiry=upper_bordering_expiry["expiry"],
        upper_bounding_forward=upper_bordering_expiry["forward"],
        lower_bounding_expiry=lower_bordering_expiry["expiry"],
        lower_bounding_forward=lower_bordering_expiry["forward"],
        target_expiry=exp,
    )

    (
        lower_bordering_strikes,
        interpolated_tenor_strikes,
        upper_bordering_strikes,
    ) = get_strikes_to_interpolate(
        upper_expiry=upper_bordering_expiry,
        lower_expiry=lower_bordering_expiry,
        model=model,
        spot=spot,
        interpolated_forward=forward_inter,
        exchange=exchange,
    )

    interpolated_vols = _interpolate_vol(
        frac,
        exp,
        lower_bordering_expiry,
        upper_bordering_expiry,
        lower_bordering_strikes,
        upper_bordering_strikes,
    )

    if np.isnan(interpolated_vols).any():
        logging.warning(
            f"Interpolated tenor strikes has nan values: {interpolated_vols=}, {exchange=}, {exp=}, lower_bordering_expiry={lower_bordering_expiry.to_dict()}, upper_bordering_expiry={upper_bordering_expiry.to_dict()}"
        )

    not_bad_strikes = ~np.isnan(interpolated_vols)
    interpolated_tenor_strikes = interpolated_tenor_strikes[not_bad_strikes]
    interpolated_vols = interpolated_vols[not_bad_strikes]

    interpolated_tenor_strikes_as_list, interpolated_vols_as_list = (
        _extrapolate_call_wing_if_needed(
            interpolated_tenor_strikes.tolist(),
            interpolated_vols.tolist(),
            spot,
            forward_inter,
            exp,
            get_domestic_rate(load_domestic_rates(), exp * 365),
        )
    )

    interpolated_tenor_strikes_as_list, interpolated_vols_as_list = (
        _extrapolate_put_wing_if_needed(
            interpolated_tenor_strikes_as_list,
            interpolated_vols_as_list,
            spot,
            forward_inter,
            exp,
            get_domestic_rate(load_domestic_rates(), exp * 365),
        )
    )

    return (
        forward_inter,
        interpolated_vols_as_list,
        lower_bordering_expiry["underlying_index"],
        upper_bordering_expiry["underlying_index"],
        interpolated_tenor_strikes_as_list,
    )


def _calibrate_parameters(
    exp: float,
    forward: float,
    spot: float,
    rd: float,
    vols: list[float],
    strikes: list[float],
) -> utils_calc.CubicSpline:
    query: CalibrationQuery = {
        "expiry": exp,
        "forward": forward,
        "spot": spot,
        "domestic_rate": rd,
        "test_type": "strikes",
        "vol_test_type": "vol_lognormal",
        "model": "spline",
        "LNvols": vols,
        "strikes": list(strikes),
        "biv": vols,
        "aiv": vols,
    }

    return utils_calc.perform_spline_calibration(query=query)


def _process_calibrated_parameters(
    tenor: float | int,
    strikes: list[float],
    vols: list[float] | NDArrayFloat64,
    forward: float,
    model: Model,
    parameters: CubicSpline,
    process: str,
    timestamp: int,
) -> dict[str, float | list[utils_calc.utils_calc_types.CubicSplineSegment]]:
    predicted_vols = utils_calc.spline_model_vol(strikes, parameters)
    atm_vol = utils_calc.spline_model_vol([forward], parameters)[0]

    (
        calib_r2,
        _,
        _,
        _,
    ) = check_calibrated_vols_and_log_error_with_details(
        precalibrated_vols=vols,
        model_predicted_vols=predicted_vols,
        tenor=tenor,
        timestamp=timestamp,
        model=model,
        process=process,
    )

    exp_res = {
        "tenor_days": tenor,
        "forward": forward,
        "atm_vol": atm_vol,
        "R2_calib": calib_r2,
        "spline_info": parameters["spline_info"],
        "rd": get_domestic_rate(load_domestic_rates(), tenor),
    }

    return exp_res


def _fill_moneyness_tenor_surface(
    tenor_completed: list[float],
    tenors_params: pd.DataFrame,
    df_vol_matrix_money: pd.DataFrame,
    tenors_days: list[float],
    exchange: str,
    currency: str,
    timestamp: int,
) -> tuple[pd.DataFrame, list[float]]:
    for tenor in tenor_completed:
        temp = tenors_params[tenors_params["tenor_days"] == tenor].iloc[0]
        forward = temp["forward"]
        strikes = np.array(DEFAULT_SURFACE_MONEYNESS) * forward
        tenor_vols = np.array(
            utils_calc.spline_model_vol(
                strikes, CubicSpline(spline_info=temp["spline_info"])
            )
        )
        df_vol_matrix_money.loc[tenor, MONEYNESS_STR] = tenor_vols

    df_vol_matrix_money = utils_calc.extrapolate_left(df_vol_matrix_money)

    tenor_in_ex = [t for t in tenors_days if t not in tenor_completed]
    max_inter = max(tenor_completed)
    tenor_in_ex_right = [x for x in tenor_in_ex if x > max_inter]

    df_vol_matrix_money, tenor_in_ex = fit_spline_with_exception(
        df_vol_matrix_money=df_vol_matrix_money,
        tenor_completed=tenor_completed,
        right_side_tenors=tenor_in_ex_right,
        tenors_to_extrapolate=tenor_in_ex,
        timestamp=timestamp,
        exchange=exchange,
        currency=currency,
    )

    return df_vol_matrix_money, tenor_in_ex


def spline_tenor_params(
    snap: list[ListedTenorParamsSpline],
    exchange: str,
    currency: str,
    expiry_iv_info: TenorExpiryArbFields,
) -> ParamsCalcResult:
    # TODO: test that correct params are being passed through
    # we pass in arbitrary expiry tenors to be able to
    # differentiate between arbitrary tenors and listed tenors.
    # extra_tenors is inclusive of arbitrary expiry tenors
    tenors_days = sorted(
        [round_value(x, TENOR_PRECISION) for x in expiry_iv_info.keys()]
    )
    df_vol_matrix_pre = pd.DataFrame(
        np.zeros((len(tenors_days), len(DELTAS_STR))),
        columns=DELTAS_STR,
        index=tenors_days,
    )

    df_vol_matrix_money = pd.DataFrame(
        np.zeros((len(tenors_days), len(MONEYNESS_STR))),
        columns=MONEYNESS_STR,
        index=tenors_days,
    )

    snap_data = pd.DataFrame(snap)
    snap_data["expiry"] = snap_data["expiry"].apply(
        partial(round_value, precision=EXPIRY_PRECISION)
    )

    all_listed_expiries = snap_data["expiry"].tolist()
    all_listed_tenors = sorted(
        round_value(x * 365, TENOR_PRECISION) for x in all_listed_expiries
    )
    listed_tenor_expiry_map = dict(zip(all_listed_tenors, all_listed_expiries))
    max_exp_listed = max(all_listed_expiries)
    min_exp_listed = min(all_listed_expiries)
    spot = np.mean(snap_data["spot"])
    timestamp = int(snap_data["timestamp"].iloc[0])
    runtime = datetime.now(tz=timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.000Z")

    c_yield_s, c_yield_left, c_yield_right = utils_calc.calc_convenience_yield(
        snap_data, spot
    )

    ###########################

    df_vol_matrix_pre["atm"] = 0
    df_vol_matrix_pre["lower_bordering_expiry"] = None
    df_vol_matrix_pre["upper_bordering_expiry"] = None

    columns = [
        "tenor_days",
        "forward",
        "spline_info",
        "atm_vol",
        "expiry",
        "rd",
    ]

    tenors_params = pd.DataFrame(columns=columns)

    tenor_completed = []
    for tenor in tenors_days:
        rd = get_domestic_rate(load_domestic_rates(), tenor)
        exp = tenor / 365
        if exp < (min_exp_listed - EXPIRY_TOLERANCE) or exp > (
            max_exp_listed + EXPIRY_TOLERANCE
        ):
            continue

        listed_expiry = tenor in all_listed_tenors
        if listed_expiry:
            param_line = snap_data[
                snap_data["expiry"] == listed_tenor_expiry_map[tenor]
            ].iloc[0]
            underlying_index_expiry_1 = param_line["underlying_index"]
            underlying_index_expiry_2 = param_line["underlying_index"]
            exp_res = {
                "tenor_days": tenor,
                "forward": param_line["forward"],
                "atm_vol": param_line["atm_vol"],
                "spline_info": param_line["spline_info"],
                "rd": rd,
            }
        else:
            try:
                (
                    forward,
                    interpolated_vols,
                    underlying_index_expiry_1,
                    underlying_index_expiry_2,
                    interpolated_tenor_strikes,
                ) = _calculate_vol_to_calibrate(
                    snap_data,
                    exp,
                    "spline",
                    spot,
                    exchange,
                )
                parameters = _calibrate_parameters(
                    exp,
                    forward,
                    spot,
                    rd,
                    interpolated_vols,
                    interpolated_tenor_strikes,
                )

                exp_res = _process_calibrated_parameters(
                    tenor,
                    interpolated_tenor_strikes,
                    interpolated_vols,
                    forward,
                    "spline",
                    parameters,
                    "tenor interpolation",
                    timestamp,
                )

            except Exception as _:
                logging.exception(
                    f"Error calculating model parameters derived data for {exchange=}, {tenor=}, {timestamp=}, iso={utils_general.to_iso(timestamp)}"
                )
                # drop failed tenor from surface skeleton
                df_vol_matrix_pre = df_vol_matrix_pre.drop([tenor])
                continue

        tenor_completed.append(tenor)
        tenors_params = tenors_params.append(  # type: ignore
            exp_res,
            ignore_index=True,
        )
        df_vol_matrix_pre.loc[tenor, "lower_bordering_expiry"] = (
            underlying_index_expiry_1
        )
        df_vol_matrix_pre.loc[tenor, "upper_bordering_expiry"] = (
            underlying_index_expiry_2
        )

    (
        df_vol_matrix_money,
        tenor_in_ex,
    ) = _fill_moneyness_tenor_surface(
        tenor_completed,
        tenors_params,
        df_vol_matrix_money,
        tenors_days,
        exchange,
        currency,
        timestamp,
    )

    min_inter = min(tenor_completed)
    for tenor in tenor_in_ex:
        try:
            exp = round_value(tenor / 365, EXPIRY_PRECISION)
            c_yield = c_yield_left if tenor < min_inter else c_yield_right
            spot_shifted = (
                spot if tenor < min_inter else c_yield_s.iloc[-1]["forward"]
            )
            t_shifted = (
                exp if tenor < min_inter else exp - c_yield_s.iloc[-1]["expiry"]
            )
            assert tenor > 0
            forward_extrap = spot_shifted * (math.exp(c_yield * t_shifted))

            vols = df_vol_matrix_money.loc[tenor, MONEYNESS_STR].values.tolist()  # type: ignore
            strikes = np.array(DEFAULT_SURFACE_MONEYNESS) * forward_extrap
            # In the future we may want to use custom set of strikes to calibrate
            # E.g Longer tenors using a different set of strikes
            # We may want to anchor some deltas in certain regions of the wings and atm like in modelparams
            # calculate the grid based on the deltas and volatilites
            rd = get_domestic_rate(load_domestic_rates(), exp * 365)
            strikes, vols = _extrapolate_call_wing_if_needed(
                strikes.tolist(),
                vols,
                spot,
                forward_extrap,
                exp,
                rd,
            )

            strikes, vols = _extrapolate_put_wing_if_needed(
                strikes,
                vols,
                spot,
                forward_extrap,
                exp,
                rd,
            )

            (
                strikes_narrow,
                vols_narrow,
                deltas_narrow,
            ) = get_strikes_and_vols_to_extrapolate(
                expiry=exp,
                forward=forward_extrap,
                spot=spot,
                strikes_to_vol=dict(zip(strikes, vols)),
                r_d=rd,
                use_calibration_delta_boundary=False,
            )

            parameters = _calibrate_parameters(
                exp,
                forward_extrap,
                spot,
                rd,
                vols_narrow,
                strikes_narrow,
            )

            exp_res = _process_calibrated_parameters(
                tenor,
                strikes_narrow,
                vols_narrow,
                forward_extrap,
                "spline",
                parameters,
                "tenor extrapolation",
                timestamp,
            )

            tenors_params = tenors_params.append(  # type: ignore
                exp_res,
                ignore_index=True,
            )

        except Exception as _:
            logging.exception(
                f"Error calculating moneyness and/or listed expiry param for {exchange=}, {tenor=}, {timestamp=}, iso={utils_general.to_iso(timestamp)}"
            )
            # drop failed tenor from surface skeleton
            df_vol_matrix_pre = df_vol_matrix_pre.drop([tenor])

    tenors_params["spot"] = spot
    tenors_params["expiry"] = tenors_params["tenor_days"] / 365

    return {
        "model": "spline",
        "exchange": exchange,
        "currency": currency,
        "timestamp": timestamp,
        "dfs": {
            "params": finalize_result_df(
                df=tenors_params,
                calc_type="params",
                runtime=runtime,
                timestamp=timestamp,
            ),
        },
        "df_vol_matrix_pre": df_vol_matrix_pre,
    }
