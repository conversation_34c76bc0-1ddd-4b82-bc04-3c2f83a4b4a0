import functools
import logging
import math
import os
import time
from datetime import datetime, timedelta, timezone
from functools import partial
from typing import Any, Set

import orjson
import pandas as pd
import utils_general
from block_stream import Agent
from block_stream.channel import Channel
from block_stream.consumer import (
    <PERSON>ache,
    Consumer,
    DerivedDataCache,
    HistoricalTickCache,
    WaitForSetCalculationCache,
)
from block_stream.consumer.auto_scaling_cache_manager import (
    AutoScalingCacheManager,
)
from constants import (
    BLOCKSTREAM_ENDPOINT,
    INDEX_PRICE_CLEANUP_SECS,
    METRIC_S,
    RUN_CLEANUP_FNS_AFTER_SECS,
)
from prep_data import prep_data
from process_data import process_chunk_helper
from retrieve_data import retrieve_data
from typings import (
    BSBaseIndexQueryResult,
    ContractDetailsMap,
    CurrencyDataSetMapping,
    DataSet,
    DataType,
    FutureContractDetailsMap,
    Model,
    OptionContractDetailsMap,
    ParamsQueryResult,
    SpotContractDetailsMap,
    VolumeQueryResult,
)
from utils.common import SSMConfigSettings, get_calc_types
from utils.type_guards import (
    is_future_contract,
    is_option_contract,
    is_raw_future,
    is_raw_option_volume,
    is_raw_params,
    is_raw_spot,
    is_spot_contract,
)

utils_general.setup_python_logger(os.getenv("LOG_LEVEL", "INFO"))

agent = Agent(
    "CompositeCalcStream", endpoint=BLOCKSTREAM_ENDPOINT, consumer_mode="fanout"
)

tick_data_stream = agent.channel(
    "tickData",
    auto_flush_s=None,
    seconds_per_checkpoint=None,
    output_mode="multiple",
)


def _build_cache(
    currency_dataset_mapping: CurrencyDataSetMapping,
    data_types: list[DataType],
    models: list[Model],
    scheduled_version: str,
) -> tuple[list[Cache], set[str], dict[int, set[str]]]:
    start = utils_general.to_iso(
        datetime.now(tz=timezone.utc) - timedelta(minutes=5)
    )  # Couple of mins to preload latest model params
    end = utils_general.to_iso(datetime.now(tz=timezone.utc))

    logging.info(
        f"Preloading historic data: {currency_dataset_mapping=:} {models=} {data_types=} {start=:} {end=:}"
    )

    start_time = time.time()
    raw_data, contract_details, params_qfns = retrieve_data(
        start=start,
        end=end,
        currency_dataset_mapping=currency_dataset_mapping,
        data_types=data_types,
        models=models,
        periods=1,
        interval="tick",
        version=scheduled_version,
        consistent_read=True,
        lookup_options={
            "order_by": {"pkey": "asc", "skey": "desc"},
            "limit": 1,
        },  # Preload latest modelParams
    )

    def clean_qn(qn: str) -> str:
        # Live system does not use versioning. Note that not all data will be versioned
        if scheduled_version:
            qn = qn.replace(f"{scheduled_version}.", "")
        return qn.replace(".1m.", ".live.")

    retrieve_time = time.time() - start_time
    volume_list: list[VolumeQueryResult] = []
    params_list: list[ParamsQueryResult] = []
    spot_index_list: list[BSBaseIndexQueryResult] = []
    futures_indices_list: list[BSBaseIndexQueryResult] = []

    live_params_qfns = {clean_qn(qn) for qn in params_qfns if "params" in qn}
    for d in raw_data:
        qn = d["qualified_name"]
        if is_raw_option_volume(d):
            volume_list.append(d)
        else:
            qn = clean_qn(qn)
            d["qualified_name"] = qn
            if is_raw_spot(d):
                spot_index_list.append(d)

            elif is_raw_future(d):
                futures_indices_list.append(d)

            elif is_raw_params(d):
                params_list.append(d)

    (
        spot_contract_details,
        futures_contract_details,
        options_contracts_details,
    ) = parse_contract_details(contract_details)

    logging.info(
        f"Preloaded data in {round(retrieve_time)}s: {len(raw_data)=:}"
    )

    params_pf: dict[int, dict[str, ParamsQueryResult]] = {}
    ordered_ts_set: dict[int, Set[str]] = {}

    for row in params_list:
        t = row["timestamp"]
        qn = row["qualified_name"]
        if t not in params_pf:
            params_pf[t] = {}
            ordered_ts_set[t] = set()
        params_pf[t][qn] = row
        ordered_ts_set[t].add(qn)

    spot_indices_cache = HistoricalTickCache(
        prefetched_ticks=spot_index_list,
        copy_new_data=True,
        time_window=timedelta(seconds=INDEX_PRICE_CLEANUP_SECS),
        prefetched_instruments=spot_contract_details,
        predicate_fn=partial(
            _predicate_bs_index,
            currencies=currency_dataset_mapping.keys(),
            asset_type="spot",
        ),
    )
    futures_indices_cache = HistoricalTickCache(
        prefetched_ticks=futures_indices_list,
        copy_new_data=True,
        time_window=timedelta(seconds=INDEX_PRICE_CLEANUP_SECS),
        prefetched_instruments=futures_contract_details,
        predicate_fn=partial(
            _predicate_bs_index,
            currencies=currency_dataset_mapping.keys(),
            asset_type="future",
        ),
        catalog_filters=[
            {
                "base_assets": list(currency_dataset_mapping.keys()),
                "quote_assets": ["USD"],
                "asset_class": ["future"],
                "exchanges": ["blockscholes"],
                "suffixes": ["index.px"],
            }
        ],
    )
    params_cache = DerivedDataCache(
        static_qualified_names=live_params_qfns,
        prefetched_datapoints=params_pf,
        store_by_timestamp=True,
        copy_new_data=True,
        cleanup_secs=RUN_CLEANUP_FNS_AFTER_SECS,
    )
    catalog_filter_map: dict[str, dict[str, set[str]]] = (
        utils_general.nested_dict()
    )
    for base, base_data_set in currency_dataset_mapping.items():
        for exchange_data in [
            *base_data_set["composite_exchanges"],
            *base_data_set.get("super_composite_exchanges", []),
        ]:
            if (
                exchange_data["original_quote_asset"]
                not in catalog_filter_map[base]
            ):
                catalog_filter_map[base][
                    exchange_data["original_quote_asset"]
                ] = {exchange_data["exchange"]}
            else:
                catalog_filter_map[base][
                    exchange_data["original_quote_asset"]
                ].add(exchange_data["exchange"])

    option_volume_cache = HistoricalTickCache(
        prefetched_ticks=volume_list,
        copy_new_data=True,
        prefetched_instruments=options_contracts_details,
        time_window=timedelta(hours=24),
        catalog_filters=[
            {
                "base_assets": [base_asset],
                "quote_assets": [quote],
                "asset_class": ["option"],
                "exchanges": list(exchanges),
                "suffixes": ["volume.sum"],
            }
            for base_asset, quote_map in catalog_filter_map.items()
            for quote, exchanges in quote_map.items()
        ],
    )
    option_volume_cache.on_clean_up()
    futures_indices_cache.on_clean_up()

    return (
        [
            spot_indices_cache,
            futures_indices_cache,
            params_cache,
            option_volume_cache,
        ],
        live_params_qfns,
        ordered_ts_set,
    )


def parse_contract_details(
    contract_details: ContractDetailsMap,
) -> tuple[
    SpotContractDetailsMap, FutureContractDetailsMap, OptionContractDetailsMap
]:
    spot_contract_details: SpotContractDetailsMap = {}
    option_contract_details: OptionContractDetailsMap = {}
    future_contract_details: FutureContractDetailsMap = {}

    for key, instr_data in contract_details.items():
        if is_spot_contract(instr_data):
            spot_contract_details[key] = instr_data
        elif is_option_contract(instr_data):
            option_contract_details[key] = instr_data
        elif is_future_contract(instr_data):
            future_contract_details[key] = instr_data

    return (
        spot_contract_details,
        future_contract_details,
        option_contract_details,
    )


def _bs_tick_has_valid_base_and_quote(
    instrument: str, currencies: list[str]
) -> bool:
    tokens = instrument.split("_")
    base, quote = tokens[0], tokens[1]
    return base in currencies and quote == "USD"


def _predicate_bs_index(
    tick: Any,
    instrument: dict[str, Any],
    currencies: list[str],
    asset_type: str,
) -> (
    bool
):  # instrument added for compatibility with predicate_fn HistoricalTickCache
    # it is not used because BS instruments do not have catalog entries

    def _round_to_nearest_second(tstamp: int) -> int:
        return int(round(tstamp / 1e9) * 1e9)

    qn_tokens = tick["q"].split(".")
    is_valid = bool(
        len(qn_tokens) == 6
        and qn_tokens[0] == "blockscholes"
        and (qn_tokens[1] == asset_type)
        and _bs_tick_has_valid_base_and_quote(
            instrument=qn_tokens[2], currencies=currencies
        )
        and qn_tokens[-2] == "index"
        and qn_tokens[-1] == "px"
    )

    if is_valid:
        # round the index price of the valid tick to the nearest second
        tick["t"] = _round_to_nearest_second(tick["t"])

    return is_valid


def _get_start_end_time() -> dict[str, str]:
    now = datetime.now(tz=timezone.utc).isoformat()
    return {"start": now, "end": now}


async def _put_output(output_stream: Channel, df: pd.DataFrame) -> None:
    try:
        result_dict = df.to_dict(orient="records")
        for result in result_dict:
            key = ".".join(result["qualified_name"].split(".")[:3])
            # Adjust columns to convention
            result["q"] = result["qualified_name"]
            result["t"] = result["timestamp"]
            result["p"] = utils_general.json_loads(result.get("params", []))
            if not result["p"]:
                continue
            if (
                isinstance(result["p"], float) and math.isnan(result["p"])
            ) or len(result["p"]) == 0:
                continue

            del result["qualified_name"]
            del result["timestamp"]
            del result["params"]

            utils_general.log_bsdebug(
                f"OUTPUTTING: {result['q']=}, {result['t']=}"
            )

            await output_stream.put(result, key=key)
        await output_stream.flush()
    except Exception as e:
        logging.exception(
            f"Error adding output to stream for {df=:}, Error {e=:}"
        )


def _get_calc_cache(
    target_job: str, scheduled_version: str, exclusion_config: SSMConfigSettings
) -> WaitForSetCalculationCache:
    data_set: DataSet = orjson.loads(target_job)
    currency_dataset_mapping = data_set["currency_dataset_mapping"]
    models = data_set["models"]
    data_types = data_set["calc_types"]

    internal_caches, qfn_to_watch, ordered_ts_set = _build_cache(
        currency_dataset_mapping=currency_dataset_mapping,
        models=models,
        data_types=data_types,
        scheduled_version=scheduled_version,
    )
    return WaitForSetCalculationCache(
        name=f"CompositeCalcStream_{'.'.join(currency_dataset_mapping.keys())}_{'.'.join(models)}",  # When we scale it'll be by currency due to the composite/supercomposite dependency
        caches=internal_caches,
        calc_types=get_calc_types(currency_dataset_mapping),
        currency_dataset_mapping=currency_dataset_mapping,
        models=models,
        periods=1,
        interval="tick",
        generate_dynamic_params=_get_start_end_time,
        set_to_wait_for=qfn_to_watch,
        timestamp_to_qfn_map=ordered_ts_set,
        exclusion_config=exclusion_config,
    )


if __name__ == "__main__":
    exclusion_config = SSMConfigSettings.load_from_ssm()
    flush_after_secs: int = int(os.environ.get("FLUSH_AFTER_SECS", 40))
    data_sets: list[DataSet] = orjson.loads(os.environ.get("DATA_SETS", "[]"))
    scheduled_version = os.environ.get("SCHEDULED_VERSION", "")

    target_jobs: list[str] = []
    for ds in data_sets:
        target_jobs.append(utils_general.json_dumps(ds))
    try:
        scaling_cache = AutoScalingCacheManager(
            target_jobs=target_jobs,
            create_cache_fn=functools.partial(
                _get_calc_cache,
                scheduled_version=scheduled_version,
                exclusion_config=exclusion_config,
            ),
            data_set_freq_key="output_frequency_ns",
        )

        consumer = Consumer(
            consumer_name="CompositeCalcStream",
            input_data_stream=tick_data_stream,
            output_data_stream=tick_data_stream,
            mode="wait-for-set",
            caches=scaling_cache,
            prep_data=prep_data,
            process_chunk_helper=process_chunk_helper,
            frequency_ns=int(flush_after_secs * 1e9),  # Used to flush through
            clean_up_interval_secs=RUN_CLEANUP_FNS_AFTER_SECS,
            metric_seconds=METRIC_S,
            put_output=_put_output,
            copy_data_on_calc=True,
        )
        consumer.run()

    except Exception as e:
        logging.exception(f"Run failed: {e}")
