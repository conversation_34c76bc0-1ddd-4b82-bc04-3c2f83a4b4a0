import re
import logging
from datetime import datetime, timezone
import pandas as pd
from pyarrow.dataset import dataset as ds
import pyarrow.fs as fs
from pyarrow.dataset import field
import os
import boto3
import time

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)


def construct_output_filename(
    bucket: str, prefix: str, profile: str, filters: dict[str, str] | None
):
    bucket_name = bucket.replace("/", "_")
    prefix_part = os.path.basename(prefix.strip("/")) if prefix else "no_prefix"
    profile_name = profile if profile else "staging"
    filters_str = (
        "_".join(f"{k}-{v}" for k, v in filters.items())
        if filters
        else "no_filters"
    )
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")

    filename = f"{timestamp}_Pyarrow_{bucket_name}_{prefix_part}_{profile_name}_{filters_str}.csv"
    filename = re.sub(r"[^\w\-_.]", "_", filename)

    return filename


def query_s3_parquet(
    bucket_name: str,
    prefix: str,
    profile_name: str = "staging",
    filter_conditions: dict[str, str] | None = None,
    selected_columns: list[str] | None = None,
    start_timestamp: str | None = None,
    end_timestamp: str | None = None,
):
    """
    Query S3 data stored in Parquet format using pyarrow.dataset with efficient filtering.

    Parameters:
        bucket_name (str): The S3 bucket name.
        prefix (str): The S3 prefix (folder or file path) to locate the Parquet files.
        filter_conditions (dict, optional): A dictionary where keys are column names and values are the desired values for filtering.
        selected_columns (list, optional): List of column names to include in the final DataFrame.
        start_timestamp (str, optional): Start timestamp for filtering in ISO format (e.g., '2024-12-17T00:00:00').
        end_timestamp (str, optional): End timestamp for filtering in ISO format (e.g., '2024-12-17T23:59:59').

    Returns:
        pd.DataFrame: A Pandas DataFrame containing the queried data.
    """
    try:
        start_time = time.time()
        logging.info("Initializing AWS session and credentials.")
        session = boto3.Session(profile_name=profile_name)
        credentials = session.get_credentials()
        if credentials is None:
            raise ValueError("AWS credentials could not be loaded.")
        aws_access_key = credentials.access_key
        aws_secret_key = credentials.secret_key
        aws_session_token = credentials.token

        s3_filesystem = fs.S3FileSystem(
            access_key=aws_access_key,
            secret_key=aws_secret_key,
            session_token=aws_session_token,
            connect_timeout=60,
            request_timeout=300,
        )
        logging.info("AWS session initialized.")
        logging.info(f"Time taken: {time.time() - start_time:.2f} seconds")

        start_time = time.time()
        logging.info("Fetching Parquet files from S3.")
        s3_path = f"{bucket_name}/{prefix}"

        file_selector = fs.FileSelector(s3_path, recursive=True)
        files = [
            file.path
            for file in s3_filesystem.get_file_info(file_selector)
            if file.is_file and file.path.endswith(".parquet")
        ]

        if not files:
            raise ValueError(
                "No Parquet files found under the specified prefix."
            )

        logging.info("Parquet files fetched successfully.")
        logging.info(f"Time taken: {time.time() - start_time:.2f} seconds")

        start_time = time.time()
        logging.info("Initializing filter expressions.")
        filter_expr = None
        if filter_conditions:
            for column, value in filter_conditions.items():
                condition = field(column) == value
                filter_expr = (
                    condition
                    if filter_expr is None
                    else (filter_expr & condition)
                )

        if start_timestamp and end_timestamp:
            start_timestamp_dt = pd.Timestamp(start_timestamp).value
            end_timestamp_dt = pd.Timestamp(end_timestamp).value

            timestamp_condition = (field("timestamp") >= start_timestamp_dt) & (
                field("timestamp") <= end_timestamp_dt
            )
            filter_expr = (
                timestamp_condition
                if filter_expr is None
                else (filter_expr & timestamp_condition)
            )

        logging.info("Filter expressions initialized.")
        logging.info(f"Time taken: {time.time() - start_time:.2f} seconds")

        start_time = time.time()
        logging.info("Creating dataset and filtering data.")
        dataset = ds(files, format="parquet", filesystem=s3_filesystem)
        table = dataset.to_table(columns=selected_columns, filter=filter_expr)
        data_frame = table.to_pandas()
        data_frame.sort_values(by="timestamp", inplace=True)

        if selected_columns:
            data_frame = data_frame.dropna(subset=selected_columns)

        logging.info("Dataset created and data filtered.")
        logging.info(f"Time taken: {time.time() - start_time:.2f} seconds")

        return data_frame

    except Exception as e:
        raise ValueError("Error querying data") from e


def construct_s3_prefix(
    year: str,
    month: str,
    day: str,
    exchange: str,
    asset_class: str,
    currency: str,
) -> str:
    """
    Construct an S3 prefix from individual components.

    Parameters:
        year (str): Year component.
        month (str): Month component.
        day (str): Day component.
        exchange (str): Exchange component.
        asset_class (str): Asset class component.
        currency (str): Currency component.

    Returns:
        str: The constructed S3 prefix.
    """
    return f"year={year}/month={month}/day={day}/exchange={exchange}/asset_class={asset_class}/currency={currency}/"


if __name__ == "__main__":
    bucket = "s3-tick-data-partitioned"
    prefix = construct_s3_prefix(
        year="2024",
        month="12",
        day="25",
        exchange="deribit",
        asset_class="option",
        currency="BTC",
    )
    profile = os.environ.get("AWS_PROFILE")
    if profile is None:
        logging.error("No Profile supplied / present in environment")
        exit(1)

    filters: dict[str, str] | None = {
        "qualified_name": "deribit.option.BTC-27DEC24-110000-C.tick.bid.px"
    }
    selected_columns = [
        "qualified_name",
        "timestamp",
        "bid_px",
    ]
    start_timestamp = "2024-12-25T15:00:00"
    end_timestamp = "2024-12-25T16:00:00"

    try:
        start_time = time.time()
        result_df = query_s3_parquet(
            bucket,
            prefix,
            profile,
            filters,
            selected_columns,
            start_timestamp,
            end_timestamp,
        )
        logging.info(
            f"Total time for querying: {time.time() - start_time:.2f} seconds"
        )

        output_dir = "output/"
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(
            output_dir,
            construct_output_filename(bucket, prefix, profile, filters),
        )
        result_df.to_csv(output_file, index=False)
        logging.info(f"Result saved to {output_file}")
        logging.info(f"First few rows:\n{result_df.head()}")
    except ValueError as e:
        logging.error(e)
