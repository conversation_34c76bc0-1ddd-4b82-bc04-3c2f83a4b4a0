import asyncio
import logging
import random
import time
from string import ascii_lowercase
from typing import cast

import requests
from aiobotocore.session import AioSession
from cachetools import TTL<PERSON>ache
from types_aiobotocore_ecs.type_defs import ServiceTypeDef, TaskTypeDef
from utils_general import log_bsdebug

from block_stream.config import (
    ECS_INDEX_OVERRIDE,
    IS_ECS,
    TASK_METADATA_URL,
)
from block_stream.typings import ECSTaskDef, EcsTaskInfo

_MAX_SERVICE_INFO_QUERY_RETRIES = 20


class EcsClusterManager:
    _cluster_name: str | None
    _family_name: str | None
    _task_arn: str | None
    _service_name: str | None

    def __init__(self, task_info_cache_sec: int = 0) -> None:
        # Relies on task family name (as opposed to Service) as only this is available
        # via the ECS metadata API while still allowing to filter all running
        # tasks retrieved via BOTO

        self._cache_ttl: TTLCache[str, EcsTaskInfo] = TTLCache(
            maxsize=1, ttl=task_info_cache_sec
        )
        self._session = AioSession()

        if current_task_metadata := self._get_current_task_metadata():
            # Metadata contains Cluster ARN (not name)
            self._cluster_name = current_task_metadata["Cluster"].split("/")[-1]
            self._family_name = current_task_metadata["Family"]
            self._task_arn = current_task_metadata["TaskARN"]
            self._service_name = current_task_metadata.get("ServiceName")

        else:
            self._cluster_name = None
            self._family_name = None
            self._task_arn = None
            self._service_name = None

    @staticmethod
    def arn_to_id(arn: str) -> str:
        return arn.split("/")[-1]

    def get_current_task_id(self) -> str:
        return self.arn_to_id(self._task_arn if self._task_arn else "")

    async def get_task_info(self) -> EcsTaskInfo:
        # For testing special scaling configurations
        if ECS_INDEX_OVERRIDE:
            index_override = ECS_INDEX_OVERRIDE.split("/")
            logging.warning(f"Using ECS_INDEX_OVERRIDE: {ECS_INDEX_OVERRIDE}")

            return {
                "desired_task_count": int(index_override[1]),
                "pending_task_count": 0,
                "running_task_count": int(index_override[1]),
                "current_task_id": f"local_{index_override[0]}",
                "current_task_index": int(index_override[0]),
                "family_name": "local",
                "running_task_ids": [
                    f"local_{x}" for x in range(int(index_override[1]))
                ],
            }

        # To enable local runs without additional override config
        elif not self._task_arn:
            return {
                "desired_task_count": 1,
                "pending_task_count": 0,
                "running_task_count": 1,
                "current_task_index": 0,
            }

        task_info = self._cache_ttl.get("task_info")
        if not task_info:
            (
                service_info,
                running_tasks,
            ) = await self.get_service_info_and_running_tasks()

            _iterations = 1
            while (
                len(running_tasks) != service_info.get("runningCount")
            ) and _iterations < _MAX_SERVICE_INFO_QUERY_RETRIES:
                # There exists an edge case where the number of tasks returned by
                # Aiosession.create_client("ecs").describe_services(), differs from the result task list returned by
                # Aiosession.create_client("ecs").list_tasks(). After a maximum of 3 tries, we return what we've
                # currently queried as we shouldn't spent too long in this function
                (
                    service_info,
                    running_tasks,
                ) = await self.get_service_info_and_running_tasks()

                _iterations += 1
                await asyncio.sleep(0.33)

            running_ids = [self.arn_to_id(task["taskArn"]) for task in running_tasks]
            current_task_id = self.get_current_task_id()
            if current_task_id not in running_ids:
                logging.error(
                    f"Current ECS Task ID {current_task_id} not found in running IDs: {running_ids}"
                )

            task_info = {
                "desired_task_count": service_info.get(
                    "desiredCount", len(running_tasks)
                ),
                "pending_task_count": service_info.get("pendingCount", 0),
                "running_task_count": service_info.get(
                    "runningCount", len(running_tasks)
                ),
                "current_task_index": self._get_current_task_index(running_tasks),
                "current_task_id": current_task_id,
                "running_task_ids": running_ids,
                "family_name": self._family_name,
            }
            self._cache_ttl["task_info"] = task_info

        return task_info

    async def get_service_info_and_running_tasks(
        self,
    ) -> tuple[ServiceTypeDef, list[TaskTypeDef]]:
        service_info, running_task = await asyncio.gather(
            self._get_service_info(), self._get_running_ecs_tasks()
        )
        return service_info, running_task

    def _get_current_task_index(self, tasks: list[TaskTypeDef]) -> int:
        if tasks:
            return next(
                (
                    index
                    for index, task in enumerate(tasks)
                    if task["taskArn"] == self._task_arn
                ),
                -1,  # For tasks whose desiredStatus is not RUNNING
            )
        return 0

    async def _get_running_ecs_tasks(self) -> list[TaskTypeDef]:
        """
        Returns a sorted list of task ARNs for a given task family (service)

        Note: Relying on family_name instead of service as the latter is not available via the AWS metadata SVC.
        """

        if not self._cluster_name or not self._family_name:
            raise ValueError(
                f"Cluster name and service must be defined ({self._family_name=} {self._cluster_name=})."
            )

        t = time.time()
        async with self._session.create_client("ecs") as client:
            response = await client.list_tasks(
                cluster=self._cluster_name,
                family=self._family_name,
                maxResults=100,
                desiredStatus="RUNNING",
            )

            all_tasks_arns = response.get("taskArns", [])
            next_token = response.get("nextToken")

            while next_token:
                response = await client.list_tasks(
                    cluster=self._cluster_name,
                    family=self._family_name,
                    maxResults=100,
                    desiredStatus="RUNNING",
                    nextToken=next_token,
                )
                all_tasks_arns.extend(response.get("taskArns", []))
                next_token = response.get("nextToken")

            if not all_tasks_arns:
                logging.error("No running ECS tasks found")
                return []

            tasks_detail = await client.describe_tasks(
                cluster=self._cluster_name, tasks=all_tasks_arns
            )

            # NOTE: there's a "PROVISIONING" status as well but I think it's preferable to exclude that
            running_arns = [
                x
                for x in sorted(tasks_detail["tasks"], key=lambda x: x["createdAt"])
                if x["lastStatus"] in {"RUNNING", "PENDING", "ACTIVATING"}
            ]

            t = time.time() - t
            log_bsdebug(
                f"Running task fetching done in: {t:.3f}s ({len(running_arns)} tasks)"
            )

            return running_arns

    async def _get_service_info(self) -> ServiceTypeDef:
        if not self._cluster_name or not self._task_arn:
            raise ValueError(
                f"Cluster name and task arn must be defined ({self._task_arn=} {self._cluster_name=})."
            )

        async with self._session.create_client("ecs") as client:
            if not self._service_name:
                tasks_detail = await client.describe_tasks(
                    cluster=self._cluster_name, tasks=[self._task_arn]
                )

                assert len(tasks_detail["tasks"]) == 1

                task_group = tasks_detail["tasks"][0].get("group", "")

                # The 'group' attribute follows the pattern "service:serviceName"
                if task_group.startswith("service:"):
                    self._service_name = task_group.split(":")[1]
                    logging.info(
                        f"Task ARN: {self._task_arn} is part of Service: {self._service_name}"
                    )
                else:
                    logging.error(
                        f"Task ARN: {self._task_arn} is not part of a service, task details: {tasks_detail}"
                    )
                    return {}

            services_rsp = await client.describe_services(
                cluster=self._cluster_name, services=[self._service_name]
            )
            assert len(services_rsp["services"]) == 1

            return services_rsp["services"][0]

    @staticmethod
    def _get_current_task_metadata() -> ECSTaskDef | None:
        """
        This allows for retrieval of cluster name, task family and ARN without
        the need for manual config
        """

        if not IS_ECS:
            log_bsdebug("Running outside ECS; scaling disabled")
            return None

        # This creates a mock instance simplifying local testing
        if TASK_METADATA_URL is None:
            if ECS_INDEX_OVERRIDE is None:
                logging.error("ECS_CONTAINER_METADATA_URI_V4 not set in environment. ")
                return None

            random_id = "".join(random.choice(ascii_lowercase) for i in range(10))
            return {
                "Cluster": "Local/Local",
                "Family": "Local",
                "TaskARN": f"Local/{random_id}",
                "ServiceName": "Local",
            }

        metadata_resp = requests.get(TASK_METADATA_URL + "/task")

        task_details = cast(ECSTaskDef, metadata_resp.json())

        return task_details


if __name__ == "__main__":

    async def calls() -> None:
        ecm = EcsClusterManager()
        ecm._cluster_name = "Staging"
        ecm._family_name = "volSmileFlexStream-task-definition"

        res = await ecm.get_task_info()
        print(f"{res=}")

        res = await ecm.get_task_info()
        print(f"{res=}")

    asyncio.get_event_loop().run_until_complete(calls())
