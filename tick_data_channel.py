from multiprocessing import Queue

from block_stream.agent import Agent

from base import Tick<PERSON><PERSON><PERSON><PERSON>
from config import (
    AGENT_NAME,
    RESULT_CHANNEL,
)
from dpm_to_blockstream import TickDataStream
from dpm_to_s3 import TickDataS3
from typings import (
    StreamType,
)


def get_blockstream_agent(channel_type: StreamType) -> Agent:
    if channel_type == "stream":
        return Agent(AGENT_NAME, consumer_mode="fanout")
    elif channel_type == "s3":
        return Agent(AGENT_NAME, consumer_mode="unregistered")
    else:
        raise ValueError(f"Unknown channel type: {channel_type}")


def initialize_tick_data_channel(s3_saver_queue: Queue) -> TickDataBase:
    if RESULT_CHANNEL in ["tickData", "stream"]:
        return TickDataStream()
    elif RESULT_CHANNEL == "S3":
        return TickDataS3(s3_saver_queue=s3_saver_queue)
    else:
        raise ValueError(f"Unknown RESULT_CHANNEL: {RESULT_CHANNEL}")
