[project]
name = "syntheticpricecalc"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "boto3-stubs>=1.35.54",
    "boto3>=1.35.36",
    "numpy==1.26.4",
    "pandas-stubs==1.5.3.230321",
    "utils-aws>=1.3.1",
    "utils-calc>=3.0.0",
    "utils-general>=1.11.0",
    "aws-lambda-typing==2.17.0",
    "psycopg-binary==3.2.2",
    "block-stream",
    "datagrabber",
    "pydantic-settings>=2.7.1",
    "pydantic>=2.7.4",
    "scikit-learn>=1.6.1",
    "scipy>=1.15.2",
    "orjson>=3.10.15",
]


[tool.pytest.ini_options]
pythonpath = ["."]

[tool.black]

line-length = 80
target-version = ['py312']
include = '\.pyi?$'
exclude = '(venv/*|env/*)'

[tool.ruff]
line-length = 80
extend-exclude = ['venv', 'env']


[tool.ruff.lint]
select = [
    'F',   # pyflakes
    'E',   # pycodestyle
    'W',   # pycodestyle
    'I',   # isort
    'UP',  # pyupgrade
    'B',   # flake8-bugbear
    'C',   # flake8-comprehensions
    'DTZ', # flake8-datetimez
    'RUF', # ruff
]

ignore = [
    'E501', # line too long, handled by black
    'C901', # complex structure, not needed
    'E712', # Doesn't work correctly with dataframes
]

[tool.ruff.lint.per-file-ignores]
'__init__.py' = [
    'F401', # unused import
    'E402', # module import not at top of file
]

[tool.mypy]

warn_return_any = true
python_version = '3.12'
warn_unused_configs = true
allow_redefinition = false
disallow_untyped_defs = true
no_implicit_optional = true
check_untyped_defs = false
strict = true
plugins = 'pydantic.mypy'

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true

[tool.uv]
dev-dependencies = [
    "black>=24.10.0",
    "mypy>=1.13.0",
    "pytest-snapshot>=0.9.0",
    "pytest>=8.3.3",
    "ruff>=0.7.2",
]

[tool.uv.sources]
utils-general = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_general", rev = "v4.1.17" }
utils-aws = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_aws", rev = "v4.1.17" }
utils-calc = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_calc", rev = "v4.1.17" }
block-stream = { git = "https://github.com/blockscholes/blockStream", rev = "v3.8.14" }
datagrabber = { git = "https://github.com/blockscholes/datagrabber", rev = "v1.12.0" }

[tool.setuptools.packages.find]
where = "synthetic_price_calc"
