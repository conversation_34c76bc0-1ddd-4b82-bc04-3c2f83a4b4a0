[project]
name = "oraclefeedupdater"
version = "0.1.0"
description = "Feed updater service for Block Scholes Oracle project"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aioboto3==12.4.0",
    "aiobotocore==2.12.3",
    "aiohttp==3.9.3",
    "boto3-stubs==1.34.72",
    "boto3==1.34.51",
    "datagrabber",
    "block_stream",
    "utils-api",
    "utils-aws",
    "utils-general",
    "utils-oracle",
    "pydantic==2.6.4",
    "pydantic-core==2.16.3",
    "urllib3==2.0.7",
    "requests==2.31.0",
    "websockets==12.0",
    "typing-extensions==4.10.0",
    "types-aioboto3==12.0.0",
    "annotated-types==0.6.0",
    "types-aiobotocore==2.12.1",
    "types-aiobotocore-cloudwatch==2.12.1",
    "types-aiobotocore-ecs==2.12.1",
    "types-aiobotocore-kinesis==2.12.1",
    "types-aiobotocore-sts==2.12.1",
    "eth-account==0.9.0",
    "eth-typing==4.4.0",
    "eth-abi==4.2.1",
    "hexbytes==0.3.1",
    "web3==6.11.0",
    "backoff~=2.2.1",
    "sqlalchemy==2.0.36",
    "asyncpg==0.30.0",
    "setuptools==75.6.0",
]

[tool.black]
line-length = 80
target-version = ['py312']
include = '\.pyi?$'
exclude = '(venv/*|env/*)'

[tool.ruff]
line-length = 80
extend-exclude = ['venv', 'env']

[tool.ruff.lint]
select = [
    'F',    # pyflakes
    'E',    # pycodestyle
    'W',    # pycodestyle
    'I',    # isort
    'UP',   # pyupgrade
    'B',    # flake8-bugbear
    'C',    # flake8-comprehensions
    'DTZ',  # flake8-datetimez
    'RUF',  # ruff
]

ignore = [
    'E501', # line too long, handled by black
    'C901', # complex structure, not needed
]

[tool.ruff.lint.per-file-ignores]
'__init__.py' = [
    'F401', # unused import
    'E402', # module import not at top of file
]

[tool.mypy]
python_version = '3.13'
warn_return_any = true
warn_unused_configs = true
allow_redefinition = false
disallow_untyped_defs = true
no_implicit_optional = true
check_untyped_defs = false
strict = true
plugins = 'pydantic.mypy'

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true

[tool.pytest.ini_options]
pythonpath = ["."]

[tool.uv]
dev-dependencies = [
    "black>=24.10.0",
    "mypy>=1.11.2",
    "pre-commit>=4.0.1",
    "pytest-asyncio>=0.24.0",
    "pytest>=8.3.3",
    "python-dotenv>=1.0.1",
    "ruff>=0.6.9",
]

[tool.uv.sources]
datagrabber = { git = "https://github.com/blockscholes/datagrabber", rev = "v1.7.2" }
block_stream = { git = "https://github.com/blockscholes/blockStream", rev = "v3.8.8" }
utils-general = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_general", rev = "constant_matirity_str_reconstructuon" }
utils-aws = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_aws", rev = "constant_matirity_str_reconstructuon" }
utils-api = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_api", rev = "constant_matirity_str_reconstructuon" }
utils-oracle = { git = "https://github.com/blockscholes/bs-python-utils", subdirectory = "utils_oracle", rev = "constant_matirity_str_reconstructuon" }
