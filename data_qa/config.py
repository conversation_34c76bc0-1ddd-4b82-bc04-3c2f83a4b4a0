import os
from pathlib import Path
from typing import Any, Final, Literal, TypedDict, TypeVar, cast

import json5

from .typings import (
    ParamsIv,
    ParamsOptionAnalyticsParams,
    ParamsPx,
    TargetConfig,
    TargetConfigAnalyticsPx,
    TargetConfigIv,
    TargetConfigPx,
)

QUERY_START = os.getenv("QUERY_START", "2025-06-19T00:00:00Z")
QUERY_END = os.getenv("QUERY_END", "2025-06-19T17:00:00Z")

ENV_PARAMS: Final[dict[str, "EnvParams"]] = {
    "staging": {"versions": [""]},
    "prod": {"versions": ["v-00004"]},
}


AWS_ENV = os.getenv("AWS_ENV", "staging")
EXCLUDE_ZERO = (
    False
    if os.environ.get("OUTPUT_TARGET") == "dump_raw"
    else bool(os.getenv("EXCLUDE_ZERO", 1))
)
DUMP_PATH = os.getenv("DUMP_PATH", "data_dumps")
OUTPUT_SEPARATOR = os.getenv("OUTPUT_SEPARATOR", "")


SECONDS_PER_DAY = 86400
OKX_OPTION_QUOTE_LISTING_CHANGE_DATE = (
    "2024-02-26T00:00:00.000Z"  # changed to quoted in underlying
)
LEVEL_TYPE_TO_QN_SUFFIX = {
    "money": "moneyness",
    "delta": "smile",
    "strike": "strike",
    "params": "params",
    "index": "vol",
}
COLORS: Final[list[str]] = [
    "#1f77b4",  # muted blue
    "#ff7f0e",  # safety orange
    "#2ca02c",  # cooked asparagus green
    "#9467bd",  # muted purple
    "#8c564b",  # chestnut brown
    "#e377c2",  # raspberry yogurt pink
    "#d62728",  # brick red
    "#7f7f7f",  # middle gray
    "#bcbd22",  # curry yellow-green
    "#17becf",  # blue-teal
]
DECIMALS: Final[int] = 9
T = TypeVar("T", ParamsIv, ParamsPx, ParamsOptionAnalyticsParams)


class EnvParams(TypedDict, total=True):
    versions: list[str]


def _load_json_config(filename: str) -> dict[str, Any]:
    queries_dir = Path(__file__).parent / "query_target_configs"
    with open(queries_dir / f"{filename}.jsonc", "r") as f:
        config = json5.load(f)
    return config


def _parse_exchange_list(
    exchanges_dict: dict[str, list[str]],
) -> dict[tuple[str, ...], list[str]]:
    """Convert exchange strings with commas into tuples"""
    return {tuple(k.split(",")): v for k, v in exchanges_dict.items()}


def load_config(target: str, stage: str = "prod") -> TargetConfig:
    """Load and parse a query config file into a TargetConfig object"""

    config = _load_json_config(target)
    config.update(**ENV_PARAMS[stage])

    match target:
        case "iv":
            return cast(
                TargetConfigIv,
                {
                    "target": "iv",
                    "params": cast(ParamsIv, config),
                },
            )

        case target if target.startswith("px_"):
            if "exchanges" in config:
                config["exchanges"] = _parse_exchange_list(config["exchanges"])
            return cast(
                TargetConfigPx,
                {
                    "target": "px",
                    "params": cast(ParamsPx, config),
                },
            )

        case "option_analytics":
            return cast(
                TargetConfigAnalyticsPx,
                {
                    "target": "analytics_px_option",
                    "params": cast(ParamsOptionAnalyticsParams, config),
                },
            )

        case _:
            raise ValueError(f"Invalid target config: {target}")


TARGET_CONFIGS: dict[
    Literal[
        "px_option", "px_future", "px_spot", "px_perp", "iv", "analytics_px_option"
    ],
    TargetConfig,
] = {
    "iv": load_config("iv", AWS_ENV),
    "px_option": load_config("px_option", AWS_ENV),
    "px_future": load_config("px_future", AWS_ENV),
    "px_spot": load_config("px_spot", AWS_ENV),
    "px_perp": load_config("px_perp", AWS_ENV),
    "analytics_px_option": load_config("option_analytics", AWS_ENV),
}
