{
  "exchanges": ["v2lyra"],
  "base_assets": ["ETH", "BTC"],
  "freq": "1h",
  "models": ["SVI"],
  "tenors": [
//    { "expiry": { "days_gte": 0 } },
    // "60m"
    // "480m"
    // "1d",
    // "2d",
     "3d",
     "7d",
     "14d",
     "30d"

//     "90d",
    // "180d",
    // "365d"
    // "19MAR25"
    // "22MAR25",
    // "30MAY25"
    // "27JUN25",
    // "26DEC25"
  ],
  //   "filter": {
  //     "time": {
  //       "hour": 0,
  //       "tz": "UTC"
  //     }
  //   },
   // ---- MONEYNESS ------
//     "level": {
//       "type": "money",
//       "values": [
////         "10money",
////         "20money",
////         "30money",
////         "40money",
////         "50money",
////         "60money",
////         "70money",
////         "80money",
//         "90money",
////         "91money",
////         "92money",
////         "93money",
////         "94money",
////         "95money",
////         "96money",
////         "97money",
////         "98money",
////         "99money",
//         "100money",
////         "101money",
////         "102money",
////         "103money",
////         "104money",
////         "105money",
////         "106money",
////         "107money",
////         "108money",
////         "109money",
////         "110money",
//         "120money",
////         "130money",
////         "140money",
////         "150money",
////         "160money",
////         "170money",
////         "180money",
////         "190money",
////         "200money",
////         "210money",
////         "220money",
////         "230money",
////         "240money",
////         "250money",
////         "260money",
////         "270money",
////         "280money",
////         "290money",
////         "300money"
//       ]
//     }
  //   // ---- DELTA ------
  "level": {
    "type": "delta",
    "values": [
         "atm",
      //     "-1delta",
      //     "-2delta",
      //     "-3delta",
      //     "-4delta",
      //     "-5delta",
      //   "-10delta",
      //     "-15delta",
      //   "-20delta",
      //   "-25delta",
      //   "-30delta",
      //     "-35delta",
      //     "-40delta",
      //     "-45delta",
      //     "-50delta",
//      "50delta",
      //     "45delta",
      //     "40delta",
      //     "35delta",
      //     "30delta",
      //   "25delta",
//      "20delta",
      //     "15delta",
//      "10delta"
      //     "5delta",
      //     "4delta",
      //     "3delta",
      //     "2delta",
      //     "1delta"
    ]
  }
  //   // ---- STRIKE ------

  //   "level": {
  //     "type": "strike",
  //     "values": ["100000strike"]
  //   }

  //   // ---- PARAMS ------

  //   "level": {
  //     "type": "params",
  //     "values": ["forward", "spot"]
  //     //   "values": ["svi_a"]
  //   }

  //   // ---- INDEX (BSIV) ------

  //   "level": {
  //     "type": "index",
  //     "values": []
  //   }
}
