import asyncio
import logging
import os
from collections import defaultdict
from datetime import datetime, timedelta
from functools import partial
from typing import Callable, Literal, Optional, Sequence, cast

import pandas as pd
import plotly.graph_objects as go
import utils_general

from .config import (
    AWS_ENV,
    COLORS,
    DECIMALS,
    DUMP_PATH,
    OUTPUT_SEPARATOR,
    QUERY_END,
    QUERY_START,
    TARGET_CONFIGS,
)
from .prep import prep_data
from .retrieve import retrieve_data
from .typings import (
    AssetOptionParams,
    ExchangePxTypeMap,
    ParamsIv,
    ParamsOptionAnalyticsParams,
    ParamsPx,
    PreppedData,
    PxItem,
    Scalar,
    Snap,
    TargetConfig,
)
from .util import (
    get_decimal_places,
    to_iso,
)

utils_general.setup_python_logger(os.getenv("LOG_LEVEL", logging.INFO))


def _get_separator_value(key: str, separator: Literal["tenors", "base_assets"]) -> str:
    """Extract separator value from data key.

    Args:
        key: The data key to extract from
        separator: Type of separator to extract ('tenors' or 'base_assets')

    Returns:
        The extracted separator value

    Raises:
        ValueError: If separator type is not supported or key format is invalid
    """
    if separator == "tenors":
        return _datakey_to_expiry(key)
    elif separator == "base_assets":
        return _datakey_to_base_asset(key)
    raise ValueError(f"Unsupported separator: {separator}")


def _title_key_name(config: TargetConfig) -> str:
    params_title_tokens: list[str] = []
    for k, v in config["params"].items():
        if k == "exchanges":
            # Handle ExchangePxTypeMap type
            if isinstance(v, dict):
                exch_to_pxtype = cast(ExchangePxTypeMap, v)
                # Flatten all exchange tuples into a single sorted list
                exchanges = sorted(
                    set(
                        exch
                        for tuple_key in exch_to_pxtype.keys()
                        for exch in tuple_key
                    )
                )
                if exchanges:
                    params_title_tokens.append("+".join(exchanges))
            else:
                # Handle simple list of exchanges
                exchange_list = cast(list[str], v)
                params_title_tokens.append("+".join(sorted(exchange_list)))
        elif k == "asset_params":
            v = cast(AssetOptionParams, v)
            if v["asset_type"] == "option":
                params_title_tokens.append("+".join(v["instrument_types"]))
            params_title_tokens.append(v["asset_type"])
        elif not (isinstance(v, dict) or k in ["tenors", "level", "filter"]):
            if v:
                params_title_tokens.append(
                    f"{'+'.join(cast(list[str], v)) if isinstance(v, list) else str(v)}"
                )

    return f"{config['target']} | {'_'.join(params_title_tokens)} - [{AWS_ENV.upper()}]"


def _plot_series_key_name(key: str, config: TargetConfig) -> str:
    """
    Generates a concise series key name by modifying the input `key` based on
    the provided `config`. Removes single-value parameters from the key for
    succinctness, as they would be included in the title key anyway.

    Parameters:
        key: The original series key to be modified.
        config: Configuration containing parameters and target information.

    Returns:
        The modified, more succinct key as a string.

    Key Formats:
        - Removes single-value parameters ("versions", "exchanges", "models",
            "base_assets") from the key.
    """

    params = config["params"]

    # Handle non-exchange single value params first
    for param in ["versions", "models", "base_assets"]:
        # remove single value params from key for conciseness
        # as it would be included in title key anyway
        if (
            params.get(param)
            and len(cast(Sequence[Scalar], params[param])) == 1
            and (params[param][0] != "")
        ):
            key = key.replace(f"{params[param][0]}.", "")

    if config["target"] == "px":
        exch_to_pxtype = cast(ExchangePxTypeMap, params.get("exchanges", {}))
        if (
            len(exch_to_pxtype) == 1  # Only one exchange tuple
            and len(next(iter(exch_to_pxtype.values()))) == 1  # Only one price type
        ):
            exchange_tuple = next(iter(exch_to_pxtype.keys()))
            price_type = next(iter(exch_to_pxtype.values()))[0]
            # Remove both the exchange path and price type if found
            exchange_str = ".".join(exchange_tuple)
            key = key.replace(f"{exchange_str}.", "")
            key = key.replace(f"{price_type}.", "")

    elif config["target"] == "iv":
        params = cast(ParamsIv, params)
        if len(params["exchanges"]) == 1:
            key = key.replace(f"{params['exchanges'][0]}.", "")
        # remove field suffix for conciseness
        key = ".".join(key.split(".")[:-1])

    else:
        raise ValueError(f"Unsupported target: {config['target']}")

    return key


def _dump_series_key_name(key: str, config: TargetConfig) -> str:
    """
    Removes some single-value parameters ("versions", "models") from the key.

    Parameters:
        key: The original series key to be modified.
        config: Configuration containing parameters and target information.

    Returns:
        The modified key name.
    """

    params = config["params"]
    for param in ["versions", "models"]:
        if (
            params.get(param)
            and len(params.get(param, [])) == 1
            and (params[param][0] != "")
        ):
            key = key.replace(f"{params[param][0]}.", "")

    return key


def _datakey_to_expiry(key: str) -> str:
    """Extract expiry date from data key and convert to ISO format if needed."""
    if key.split(".")[-2] in {"theoretical", "mid", "bid", "ask", "trade", "index"}:
        key_tokens = key.split(".")[-3].split("-")
        return to_iso(key_tokens[0])

    elif key.split(".")[-2:] == ["vol", "iv"]:
        # iv index
        return key.split(".")[-4]

    else:
        # params & vols (other than index)
        expiry = key.split(".")[-3]
        if expiry.endswith("d") or expiry.endswith("m"):
            return expiry
        else:
            return to_iso(expiry)


def _datakey_to_base_asset(key: str) -> str:
    """Extract base asset from data key."""
    # For px data, base asset is always the first part of dot-split token before underscore
    # e.g., "deribit.BTC_USD.31Dec24.theoretical.px" -> "BTC"
    if "px" in key:
        return key.split(".")[1].split("_")[0]
    # For non-px data like iv, base asset is the third token
    # e.g., "v-00004.deribit.SOL.spline.31Dec24.5delta.iv" -> "SOL"
    return key.split(".")[2].split("_")[0]


def _multi_output(
    data: PreppedData, output_fn: Callable[[PreppedData, Optional[str]], None]
) -> None:
    if not OUTPUT_SEPARATOR:
        output_fn(data, None)
        return

    separated_data: defaultdict[str, PreppedData] = utils_general.nested_dict()

    for k, v in data.items():
        try:
            separator_value = _get_separator_value(
                k, cast(Literal["tenors", "base_assets"], OUTPUT_SEPARATOR)
            )
            separated_data[separator_value][k] = v
        except Exception:
            logging.exception(f"Failed to parse {OUTPUT_SEPARATOR} from key={k}")

    for sep_value in sorted(separated_data):
        try:
            output_fn(separated_data[sep_value], sep_value)
        except Exception:
            logging.exception(
                f"Output callback failed for {OUTPUT_SEPARATOR}={sep_value}"
            )


def _plot(
    data: PreppedData,
    config: TargetConfig,
) -> None:
    def _single_plot(
        filtered_data: PreppedData, separator_value: Optional[str]
    ) -> None:
        fig = go.Figure()
        all_values: list[float] = []

        for index, (key, series) in enumerate(
            sorted(filtered_data.items(), key=lambda x: x[0])
        ):
            fig.add_trace(  # type: ignore partially unknown
                go.Scatter(
                    x=list(series.keys()),
                    y=list(series.values()),
                    mode="lines",
                    name=_plot_series_key_name(key, config),
                    line=dict(color=COLORS[index % len(COLORS)]),
                )
            )
            all_values.extend(series.values())  # type:ignore partially unknown

        # Determine the maximum number of decimal places needed
        max_decimal_places = max(get_decimal_places(val) for val in all_values)

        # Set tick format based on maximum decimal places
        if max_decimal_places == 0:
            tick_format = ".0f"
        else:
            tick_format = f".{max_decimal_places}f"

        fig.update_layout(  # type: ignore partially unknown
            title={
                "text": _title_key_name(config),
                "y": 0.9,
                "x": 0.95,  # Anchoring the title to the right
                "xanchor": "right",
                "yanchor": "top",
            },
            xaxis_title="Timestamp",
            yaxis_title="Value",
            xaxis=dict(rangeslider=dict(visible=False), type="date"),
            yaxis=dict(
                tickformat=tick_format,  # Adjust tick format dynamically
                exponentformat="none",  # Prevent exponential notation
            ),
            template="plotly_dark",
            showlegend=True,
            legend=dict(
                orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1
            ),
            hovermode="x",
            hoverlabel=dict(namelength=-1),
        )

        fig.show()  # type: ignore partially unknown

    _multi_output(data, _single_plot)


def _key_token_idx(
    token_name: Literal[
        "exchange", "base_asset", "model", "maturity", "iv_level", "suffix"
    ],
    version_offset: int = 0,
    model_offset: int = 0,
) -> int:
    return {
        "exchange": 0 + version_offset,
        "base_asset": 1 + version_offset,
        "model": 2 + version_offset,
        "maturity": 2 + version_offset + model_offset,
        "iv_level": 3 + version_offset + model_offset,
        "suffix": 4 + version_offset + model_offset,
    }[token_name]


def _prep_df_dump_iv(df: pd.DataFrame, level_type: str) -> pd.DataFrame:
    # key format: {?version}.{exchange}.{base_asset}.{?model}.{expiry}.{level_v}.iv

    # NOTE: does not support both versioned and unversioned as part of same output
    tokens_df = df["key"].str.split(  # type:ignore partially unknown
        ".", expand=True
    )
    first_key = str(tokens_df[0].iloc[0])  # type:ignore partially unknown
    version = utils_general.get_qfn_and_version(first_key)[0]
    version_offset = 1 if version else 0
    includes_model = tokens_df[
        _key_token_idx("model", version_offset)
    ].iloc[  # type:ignore partially unknown
        0
    ] in [
        "SVI",
        "spline",
        "SABR",
    ]
    model_offset = 1 if includes_model else 0

    maturity_token = cast(
        str,
        tokens_df[
            _key_token_idx("maturity", version_offset, model_offset)
        ].iloc[  # type:ignore partially unknown
            0
        ],
    )
    maturity_name = (
        "tenor"
        if (maturity_token.endswith("d") or maturity_token.endswith("m"))
        else "expiry"
    )
    df["exchange"], df["base_asset"], df[maturity_name], df["iv_level"] = (
        tokens_df[_key_token_idx("exchange", version_offset, model_offset)],
        tokens_df[_key_token_idx("base_asset", version_offset, model_offset)],
        tokens_df[_key_token_idx("maturity", version_offset, model_offset)],
        tokens_df[_key_token_idx("iv_level", version_offset, model_offset)],
    )
    # Reconstruct key excluding suffix
    df["key"] = tokens_df[tokens_df.columns[:-2]].agg(  # type:ignore partially unknown
        ".".join, axis=1
    )

    df_pivot = df.pivot(
        index=["key", "exchange", "base_asset", "isodate", maturity_name],
        columns="iv_level",
        values="value",
    )

    df_pivot.reset_index(inplace=True)
    df_pivot["maturity_sort_key"] = df_pivot[
        maturity_name
    ].apply(  # type:ignore partially unknown
        _maturity_sort_key
    )

    # Sort columns removing iv_level & converting to numerical values
    front_cols = [
        "key",
        "isodate",
    ]
    columns_ordered = front_cols + sorted(
        [
            cast(str, col)
            for col in df_pivot.columns
            if col
            not in [
                *front_cols,
                "maturity_sort_key",
                maturity_name,
                "key",
                "expiry",
                "base_asset",
                "exchange",
            ]
        ],
        key=partial(_iv_level_sort_key, level_type=level_type),
    )
    df_pivot = df_pivot.sort_values(  # type:ignore partially unknown
        by=["exchange", "base_asset", "isodate", "maturity_sort_key"], ascending=True
    )[columns_ordered]

    return df_pivot


def _prep_df_dump_px(df: pd.DataFrame) -> pd.DataFrame:
    tokens_df = df["key"].str.split(  # type:ignore partially unknown
        ".", expand=True
    )
    df["suffix"] = tokens_df[
        tokens_df.columns[-2:]
    ].agg(  # type:ignore partially unknown
        ".".join, axis=1
    )
    df["key"] = tokens_df[tokens_df.columns[:-2]].agg(  # type:ignore partially unknown
        ".".join, axis=1
    )

    df_pivot = df.pivot(
        index=["key", "isodate"],
        columns="suffix",
        values="value",
    )
    df_pivot.reset_index(inplace=True)

    return df_pivot


def _dump(data: PreppedData, config: TargetConfig) -> None:
    logging.info("Preparing data for dump...")

    def _single_dump(
        filtered_data: PreppedData, separator_value: Optional[str]
    ) -> None:
        start, end = "", ""
        rows: list[dict[str, Scalar]] = []

        for key, datetime_value_dict in filtered_data.items():
            for dt, value in datetime_value_dict.items():
                if pd.isna(value):  # type:ignore partially unknown
                    continue
                isodate = utils_general.to_iso(dt)
                start = f"{(isodate if not start else min(start, isodate))[:19]}Z"
                end = f"{(isodate if not end else max(end, isodate))[:19]}Z"
                rows.append(
                    {
                        "key": _dump_series_key_name(key, config),
                        "isodate": isodate,
                        "value": value,
                    }
                )

        df = pd.DataFrame(rows)
        float_format = f"{{:.{DECIMALS}f}}"
        df = cast(
            pd.DataFrame,
            df.apply(  # type:ignore
                lambda x: (  # type:ignore
                    float_format.format(x) if isinstance(x, float) else x
                )
            ),
        )

        sep_str = "_" + separator_value if separator_value else ""
        fpath = f"{DUMP_PATH}/{_title_key_name(config)}{sep_str}"

        if config["target"] == "iv":
            level_type = config["params"]["level"]["type"]
            df = _prep_df_dump_iv(df, level_type)
            fpath = f"{fpath}_{level_type}"

        elif config["target"] == "px":
            df = _prep_df_dump_px(df)

        else:
            raise ValueError(f"Invalid target: {config['target']}")

        fpath = f"{fpath}_{start}_{end}.csv"
        df.to_csv(fpath, index=False, na_rep="")
        logging.info(f"Data successfully written to {fpath}")

    _multi_output(data, _single_dump)


def _iv_level_sort_key(
    col: str, level_type: str
) -> tuple[Literal[0, 1], int] | int | tuple[Literal[2], float]:
    if col.endswith(level_type):
        num_value = int(col.replace(level_type, ""))
        if level_type == "delta":
            # negative from -1 to -inf, positive from inf to 1 (smile)
            return (0, -num_value) if num_value < 0 else (1, -num_value)
        else:
            return num_value
    return (2, float("inf"))  # Put non-matching columns at the end


def _maturity_sort_key(value: str) -> int | str | float:
    try:
        if value.endswith("d"):
            return int(value[:-1])
        else:
            return to_iso(value)
    except (IndexError, ValueError):
        return float("inf")  # Handle unexpected format gracefully


def _analytics_to_px_params(
    analytics_params: ParamsOptionAnalyticsParams,
) -> ParamsPx:
    """Convert analytics parameters to PX parameters for data retrieval."""
    return cast(
        ParamsPx,
        {
            "exchanges": {
                tuple(analytics_params["exchanges"]): ["bid", "ask", "theoretical"],
            },
            "base_assets": analytics_params["base_assets"],
            "asset_params": {
                "asset_type": "option",
                "instrument_types": ["C", "P"],
                "strikes": [2400],
            },
            "freq": analytics_params["freq"],
            "tenors": [{"expiry": {"days_lte": 365}}],
            "versions": analytics_params["versions"],
        },
    )


def _prep_cfg(cfg: TargetConfig) -> TargetConfig:
    new_cfg = cfg.copy()
    if new_cfg["target"] == "analytics_px_option":
        # Convert analytics params to px params for retrieval
        px_params = _analytics_to_px_params(new_cfg["params"])
        new_cfg = cast(
            TargetConfig,
            {
                "target": "px",
                "params": px_params,
            },
        )

    if new_cfg["target"] == "px":
        asset_params = new_cfg["params"]["asset_params"]
        if asset_params["asset_type"] != "option" or not asset_params["strikes"]:
            return new_cfg
        # Determine the maximum number of decimal places needed
        max_decimal_places = max(
            get_decimal_places(val) if isinstance(val, float) else 0
            for val in asset_params["strikes"]
        )
        # Format each strike with the max_decimal_places and convert to string
        new_cfg["params"]["asset_params"]["strikes"] = cast(  # type:ignore
            list[int | str],
            [f"{strike:.{max_decimal_places}f}" for strike in asset_params["strikes"]],
        )

    return new_cfg


def _dump_raw(data: Sequence[Snap], config: TargetConfig) -> None:
    fpath = f"{DUMP_PATH}/RAW_{_title_key_name(config)}"

    # Prepare ticks data
    ticks_data = cast(
        list[PxItem],
        [
            {
                "qualified_name": item["qualified_name"],
                "timestamp": item["timestamp"],
                "px": item.get("px", ""),
            }
            for item in data
        ],
    )

    # Prepare catalog data
    catalog_data = {
        (
            f"{item['exchange']}.{item['asset_type']}.contracts",
            item.get("expiry"),
            item.get("listing"),
            item.get("type"),
            item.get("strike"),
            item.get("base_asset"),
            item.get("quote_asset"),
            item.get("instrument"),
        )
        for item in data
    }

    # Save to CSV files
    pd.DataFrame(ticks_data).sort_values(  # type:ignore partially unknown
        by="timestamp"
    ).to_csv(f"{fpath}_ticks.csv", index=False, na_rep="")
    pd.DataFrame(
        catalog_data,
        columns=[
            "qualified_name",
            "expiry",
            "listing",
            "type",
            "strike",
            "baseAsset",
            "quoteAsset",
            "instrument",
        ],
    ).to_csv(f"{fpath}_catalog.csv", index=False, na_rep="")

    logging.info(
        f"Raw data successfully written to {fpath}_ticks.csv and {fpath}_catalog.csv"
    )


def group_missing_timestamps(
    missing_ts: list[datetime], gap_threshold: timedelta
) -> tuple[list[str], list[dict[str, str]]]:

    missing_ts.sort()
    if not missing_ts:
        return [], []

    single_points: list[str] = []
    ranges: list[dict[str, str]] = []
    curr_start = curr_end = missing_ts[0]

    for dt in sorted(missing_ts[1:]):
        if dt - curr_end > gap_threshold:
            if curr_start == curr_end:
                single_points.append(utils_general.to_iso(curr_start))
            else:
                ranges.append(
                    {
                        "start": utils_general.to_iso(curr_start),
                        "end": utils_general.to_iso(curr_end),
                    }
                )
            curr_start = curr_end = dt
        else:
            curr_end = dt

    # Handle final range
    if curr_start == curr_end:
        single_points.append(utils_general.to_iso(curr_start))
    else:
        ranges.append(
            {
                "start": utils_general.to_iso(curr_start),
                "end": utils_general.to_iso(curr_end),
            }
        )

    return single_points, ranges


def analyze_missing_data(
    data: PreppedData,
    config: TargetConfig,
) -> None:
    freq = config["params"]["freq"]
    if freq == "tick":
        return

    periods = int(freq[:-1])
    match freq[-1]:
        case "m":
            gap_threshold = timedelta(minutes=periods)
            interval = "minute"
        case "h":
            gap_threshold = timedelta(hours=periods)
            interval = "hour"
        case _:
            raise ValueError(f"Unsupported interval: {freq[-1]}")

    logging.info(f"Validating missing history for: {list(data.keys())}")

    for key, history in data.items():
        total_ts = 0
        missing_dt = []
        for ts in utils_general.generate_timestamps(
            QUERY_START, QUERY_END, interval, periods, {}
        ):
            total_ts += 1
            dt = utils_general.to_datetime(ts)
            if dt not in history:
                missing_dt.append(dt)

        if missing_dt:
            single_points, ranges = group_missing_timestamps(missing_dt, gap_threshold)

            logging.warning(
                f"{key} is missing {len(missing_dt)} datapoints out of {total_ts}."
                f"\nSingle datapoints: {len(single_points)}: {single_points}."
                if single_points
                else "" f"\nDate ranges: {len(ranges)}: {ranges}" if ranges else ""
            )


async def run() -> None:
    target_cfg = TARGET_CONFIGS[os.environ["TARGET_CONFIG"]]

    prepped_cfg = _prep_cfg(target_cfg)
    d = await retrieve_data(prepped_cfg)

    if len(d["raw_data"]) == 0:
        logging.error("No data retrieved")
        return

    output_target = os.environ.get("OUTPUT_TARGET", "plot")
    if output_target == "dump_raw":
        _dump_raw(d["raw_data"], config=prepped_cfg)
        return

    prepped_d = prep_data(d, prepped_cfg)
    if len(prepped_d) == 0:
        logging.error("Prep data produced empty results")
        return

    analyze_missing_data(prepped_d, prepped_cfg)

    if target_cfg["target"] == "analytics_px_option":
        raise ValueError("Invalid target: analytics_px_option")

    if output_target == "plot":
        _plot(prepped_d, prepped_cfg)
    elif output_target == "dump":
        _dump(prepped_d, prepped_cfg)
    else:
        raise ValueError(f"Invalid OUTPUT_TARGET: {output_target}")


if __name__ == "__main__":
    asyncio.run(run())
