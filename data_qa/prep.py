import logging
import math
from collections import defaultdict
from collections.abc import Mapping
from datetime import datetime
from typing import Any, Callable, Literal, Optional, cast
from uuid import uuid4

import utils_general

from datagrabber import (
    CatalogAssetType,
)

from .config import (
    DECIMALS,
    EXCLUDE_ZERO,
    LEVEL_TYPE_TO_QN_SUFFIX,
)
from .typings import (
    AssetParams,
    AssetTransformation,
    ExchangePxItem,
    IvItem,
    ListedSmile,
    ParamsIv,
    ParamsPx,
    PreppedData,
    PxItem,
    PxType,
    QnTokens,
    RetrieveData,
    Scalar,
    TargetConfig,
    TenorItem,
    TOptionPxItem,
)
from .util import (
    get_days_to_expiry,
    get_field_value,
    parse_qualified_name,
)

OUTPUT_KEY_TO_UUID: defaultdict[str, str] = defaultdict(lambda: str(uuid4()))


def _remove_matching_keys(
    d: Mapping[str, Any], substrings: list[str], check_fn: Callable[..., bool]
) -> dict[str, Any]:
    return {k: v for k, v in d.items() if not check_fn(s in k for s in substrings)}


def _update_px_output(
    pxtype: PxType,
    asset_type_param: CatalogAssetType,
    exchange: str,
    freq: str,
    version: str,
    dt: datetime,
    output: PreppedData,
    quote_asset: str | None,
    base_asset: str,
    field_name: Literal["px", "annualised_rate", "rate"],
    field_value: float,
    exp: str | None = None,
    it: Literal["C", "P"] | None = None,
    strike: Scalar | None = None,
    is_conversion: bool = False,
    output_quote_asset: str | None = None,
    transformations: AssetTransformation | None = None,
) -> None:
    """
    Updates the `output` dictionary with pricing data based on provided parameters.

    Constructs a specific key depending on the type of price (`pxtype`) and
    asset type (`asset_type_param`), and updates the `output` dictionary with
    the given `field_value` for the datetime `dt`.

    Parameters:
        pxtype: Type of price
        asset_type_param: Target data asset type. May be different from the
            asset type that the field name / value correspond to (ie. the
            latter may be spot used for conversions while former is option)
        asset_type: Asset type of the data point which field name/value correspond to
        exchange: Name of the exchange.
        freq: Data frequency.
        version: Version identifier for the data.
        dt: The date and time of the data point.
        output: The dictionary to be updated with the new data.
        exp: Expiry date for options, if applicable.
        quote_asset: The quote asset in the currency pair.
        base_asset: The base asset in the currency pair.
        field_name: The name of the field to update.
        field_value: The value to update in the field.
        tenor: The tenor of the instrument, if applicable.
        it: Instrument type, if applicable.
        strike: Strike price for options, if applicable.
        is_conversion: Specified if is a conversion price or not.
        output_quote_asset: The expected output quote, used for conversion, if applicable.
        transformations: Transformation rules to apply, if applicable.

    Returns:
        None

    Raises:
        ValueError: If `pxtype` is not one of the expected types.

    """
    if EXCLUDE_ZERO and field_value == 0:
        return

    if is_conversion:
        # may be used for conversions
        conv_key = f"convspot.{base_asset}_{quote_asset}.index.{field_name}"
        output.setdefault(conv_key, {})[dt] = round(field_value, DECIMALS)
        return

    def _handle_key_exists(key: str, dt: datetime) -> str:
        if key in output and dt in output[key]:
            logging.warning(
                f"Key already exists in output dict: {key}_{dt}. Investigate to ensure it is unique. Appending key uuid to avoid overwrite {exchange=} {base_asset=} {quote_asset=} {asset_type_param=} {pxtype=} {field_name=}"
            )
            return key + f".{OUTPUT_KEY_TO_UUID[key][:5]}"
        return key

    if transformations and "output_base_asset_symbol" in transformations:
        base_asset = transformations["output_base_asset_symbol"]

    if pxtype == "index":
        key = f"{exchange}.{base_asset}_{output_quote_asset if output_quote_asset else quote_asset}.index.{field_name}"
        output.setdefault(key, {})[dt] = _normalize_px(
            field_value,
            key,
            base_asset,
            quote_asset,
            output_quote_asset,
            freq,
            dt,
            output,
            transformations,
        )

    elif pxtype in ["mid", "bid", "ask", "trade"]:
        quotestr = "_" + (
            output_quote_asset if output_quote_asset else quote_asset or ""
        )
        if asset_type_param == "option":
            assert exp
            assert strike
            dmy_exp = utils_general.normalise_expiry(exp)
            strike_tokens = f"{float(strike):.9f}".rstrip("0").split(".")
            strike_str = (
                "d".join(strike_tokens)
                if (len(strike_tokens) > 1 and strike_tokens[1])
                else strike_tokens[0]
            )
            key = f"{exchange}.{base_asset}{quotestr}.{dmy_exp}-{strike_str}-{it}.{pxtype}.{field_name}"
        elif asset_type_param == "future":
            assert exp
            dmy_exp = utils_general.normalise_expiry(exp)
            key = f"{exchange}.{base_asset}{quotestr}.{dmy_exp}.{pxtype}.{field_name}"
        elif asset_type_param in ["spot", "perpetual"]:
            key = f"{exchange}.{base_asset}{quotestr}.{pxtype}.{field_name}"
        else:
            raise ValueError(f"Invalid asset type: {asset_type_param}")
        key = _handle_key_exists(key, dt)

        output.setdefault(key, {})[dt] = _normalize_px(
            field_value,
            key,
            base_asset,
            quote_asset,
            output_quote_asset,
            freq,
            dt,
            output,
            transformations,
        )

    elif pxtype == "theoretical":
        vstr = f"{version}." if version else ""
        assert exp
        dmy_exp = utils_general.normalise_expiry(exp)
        if asset_type_param == "option":
            assert strike
            strike_tokens = f"{float(strike):.9f}".rstrip("0").split(".")
            strike_str = (
                "d".join(strike_tokens)
                if (len(strike_tokens) > 1 and strike_tokens[1])
                else strike_tokens[0]
            )
            key = f"{vstr}{exchange}.{base_asset}.{dmy_exp}-{strike_str}-{it}.theoretical.{field_name}"
        else:
            key = f"{vstr}{exchange}.{base_asset}.{dmy_exp}.theoretical.{field_name}"
        key = _handle_key_exists(key, dt)
        output.setdefault(key, {})[dt] = round(field_value, DECIMALS)

    elif pxtype == "funding":
        key = f"{exchange}.{base_asset}_{quote_asset}.funding.{field_name}"
        key = _handle_key_exists(key, dt)
        output.setdefault(key, {})[dt] = round(field_value, DECIMALS)
    else:
        raise ValueError(f"Invalid pxtype: {pxtype}")


def _normalize_px(
    value: float,
    key: str,
    base: str,
    quote: str | None,
    output_quote_asset: str | None,
    freq: str,
    dt: datetime,
    output: dict[str, Any],
    transformations: AssetTransformation | None,
) -> float:
    # Special cases
    if transformations:
        for transformation, v in transformations.items():
            match transformation:
                case "scale_factor":
                    assert isinstance(v, (int, float))
                    value = value * v
                case _:
                    raise ValueError(
                        f"Unsupported transformation for {base}: {transformation}"
                    )

    should_conv: bool = (
        output_quote_asset is not None
        and freq != "tick"
        and quote != output_quote_asset
    )

    if not should_conv:
        return round(value, DECIMALS)

    if quote is None:
        if output_quote_asset is not None:
            logging.warning(f"cannot normalize price with unknown quote: {key}")
        return round(value, DECIMALS)

    if output_quote_asset is None:
        return round(value, DECIMALS)

    quote = quote[1:] if quote.startswith("Z") else quote

    conv_rate = _get_conversion_rate(key, quote, output_quote_asset, dt, output)

    return round(conv_rate * value, DECIMALS)


def _get_conversion_rate(
    key: str, base: str, quote: str, dt: datetime, output: dict[str, Any]
) -> float:
    """
    This function computes the conversion rate between two assets (`base` and `quote`) by using conversion prices
    available in the provided `output` dictionary, which is expected to contain conversion spot rates for different
    assets to USD (e.g., `base` and `quote`).

    The conversion is based on the following approach:
    - If the base and quote are the same, the conversion rate is 1 (no conversion needed).
    - If the base or quote is not USD, the function looks for the conversion rate from that asset (base or quote) to USD as all our index.px are USD quoted.
    - If the asset conversion price (e.g., from `base` to USD or from `quote` to USD) is not available in the output dictionary,
      a `ValueError` is raised.
    - If the conversion prices do not exist for the specified datetime (`dt`), another `ValueError` is raised.
    - The function uses the conversion prices for `base` and `quote` (if needed) to compute the final conversion rate.

    Args:
        key (str): The identifier for the specific conversion, likely used for logging or error handling.
        base (str): The asset to convert from.
        quote (str): The asset to convert to.
        dt (datetime): The specific date and time at which the conversion price should be looked up.
        output (dict): A dictionary containing the conversion spot prices indexed by asset and datetime. The expected keys are
                       of the form `"convspot.<asset>_USD.index.px"` (e.g., `"convspot.BTC_USD.index.px"`).

    Returns:
        float: The computed conversion rate between `base` and `quote`.

    Raises:
        ValueError: If the conversion rate for the base or quote asset is missing in the output dictionary, or if the
                    conversion rate for the specified datetime is not found.

    Example:
        If the function is called as `_get_conversion_rate("BTC-USD-to-USDT", "BTC", "USDT", datetime(2022, 1, 1), output_dict)`,
        the function will:
        - Find the conversion rate for BTC to USD and USDT to USD.
        - Calculate the rate `BTC/USD * USD/USDT` to return the conversion rate from BTC to USDT.
    """
    if base == quote:
        return 1.0

    base_conv_key = None
    quote_conv_key = None

    if base != "USD":
        base_conv_key = f"convspot.{base}_USD.index.px"
    if quote != "USD":
        quote_conv_key = f"convspot.{quote}_USD.index.px"

    if (base_conv_key and base_conv_key not in output) or (
        quote_conv_key and quote_conv_key not in output
    ):
        raise ValueError(
            f"Conversion key not found in output dict: {base_conv_key=}, {quote_conv_key=}, {key=}"
        )

    # Validate conversion price exists at dt
    if (base_conv_key and dt not in output[base_conv_key]) or (
        quote_conv_key and dt not in output[quote_conv_key]
    ):
        raise ValueError(
            f"Conversion price not found at '{utils_general.to_iso(dt)}' for {base_conv_key=}, {quote_conv_key=}, {key=}"
        )

    conv_rate = 1.0
    if quote_conv_key:
        conv_rate = cast(
            float,
            (
                output[quote_conv_key][dt]
                if quote == "USD"
                else 1 / output[quote_conv_key][dt]
            ),
        )

    if base_conv_key:
        conv_rate = cast(float, output[base_conv_key][dt]) * conv_rate

    return conv_rate


def _prep_px_row(
    row: PxItem,
    params: ParamsPx,
    output: PreppedData,
    is_conversion: bool = False,
) -> None:
    asset_params: AssetParams = params["asset_params"]
    freq = params["freq"]
    asset_type_param = asset_params["asset_type"]
    qn_tokens: QnTokens = parse_qualified_name(row["qualified_name"])
    version: str = qn_tokens["version"]
    suf: Literal["pxs", "px"] = qn_tokens["suf"]
    pxtype = cast(PxType, qn_tokens["pxtype"])
    exchange: str = row["exchange"]
    asset_type = cast(CatalogAssetType, row["asset_type"])

    dt: datetime = utils_general.to_datetime(row["timestamp"])
    base_asset: str = row["base_asset"]
    quote_asset: Optional[str] = row.get("quote_asset")
    field_name: Literal["annualised_rate", "px", "rate"] = asset_params.get(
        "field", "rate" if pxtype == "funding" else "px"
    )
    transformation_rules = params.get("transformation_rules", {})

    # Validate conversion quote
    output_quote_asset = transformation_rules.get(
        "output_quote_asset", quote_asset or "USD"
    )

    field_value = get_field_value(
        pxtype=pxtype,
        row=row,
        suf=suf,
        asset_type=asset_type,
        field=field_name,
    )

    _update_px_output(
        pxtype=pxtype,
        asset_type_param=asset_type_param,
        field_name=field_name,
        field_value=field_value,
        strike=row.get("strike"),
        it=row.get("type"),
        exchange=exchange,
        version=version,
        dt=dt,
        output=output,
        exp=row.get("expiry"),
        quote_asset=quote_asset,
        base_asset=base_asset,
        freq=freq,
        output_quote_asset=output_quote_asset,
        is_conversion=is_conversion,
        transformations=transformation_rules.get("transformations", {}).get(base_asset),
    )


def _check_tenor_condition(exp: str, tenor: str | TenorItem, timestamp: int) -> bool:
    if isinstance(tenor, str):
        return exp == tenor
    days = get_days_to_expiry(exp, timestamp)
    return (
        days >= 0
        and days <= tenor["expiry"].get("days_lte", math.inf)
        and days >= tenor["expiry"].get("days_gte", -math.inf)
    )


def _get_smile_expiry(params: ParamsIv, s: ListedSmile, row: Mapping[str, Any]) -> str:
    if exp_iso := s.get("expiry_iso", s.get("expiry_str")):
        assert isinstance(exp_iso, str)
        exp = utils_general.to_datetime(exp_iso).strftime("%d%b%y").upper()
        if any(
            _check_tenor_condition(exp, t, row["timestamp"]) for t in params["tenors"]
        ):
            return exp
    return ""


def _update_iv_output(
    version: str,
    exchange: str,
    base_asset: str,
    model: str,
    expiry: str,
    row: dict[str, float | str],
    params: ParamsIv,
    snap_dt: datetime,
    output: PreppedData,
) -> None:
    level_values = (
        params["level"]["values"]
        or [v for v in row.keys() if v.endswith(params["level"]["type"])]
        or [LEVEL_TYPE_TO_QN_SUFFIX[params["level"]["type"]]]
    )
    for level_v in level_values:
        key = f"{version}{exchange}.{base_asset}.{model}.{expiry}.{level_v}.iv"
        iv = row.get(level_v, math.nan)
        output[key][snap_dt] = round(float(iv), DECIMALS)


def _prep_iv_row(row: IvItem, params: ParamsIv, output: PreppedData) -> None:
    v, qn_tokens = utils_general.get_qfn_and_version(row["qualified_name"])
    v_str = f"{v}." if v else ""
    e = qn_tokens[0]
    base = qn_tokens[2]
    m = qn_tokens[3]
    suf = LEVEL_TYPE_TO_QN_SUFFIX[params["level"]["type"]]
    dt = utils_general.to_datetime(row["timestamp"])
    tenor = qn_tokens[-3]

    if tenor == "listed":
        # listed exp bundle
        smiles: list[ListedSmile] = []
        if isinstance(row[suf], str):
            smiles = utils_general.json_loads(row[suf].replace("'", '"'))
        elif isinstance(row[suf], list):
            smiles = row[suf]
        else:
            raise ValueError(f"Invalid smile type: {row}")

        for s in smiles:
            if exp := _get_smile_expiry(params, s, row):
                _update_iv_output(v_str, e, base, m, exp, s, params, dt, output)
    else:
        # constant tenor
        _update_iv_output(v_str, e, base, m, tenor, row, params, dt, output)


def prep_data(retrieve_data: RetrieveData, config: TargetConfig) -> PreppedData:
    output: PreppedData = defaultdict(dict)

    # process conversions prices first
    if retrieve_data["conversion_data"]:
        assert config["target"] == "px"
        for row in retrieve_data["conversion_data"]:
            _prep_px_row(
                cast(ExchangePxItem | TOptionPxItem, row),
                config["params"],
                output,
                True,
            )

    for row in retrieve_data["raw_data"]:
        try:
            if config["target"] == "iv":
                _prep_iv_row(cast(IvItem, row), config["params"], output)
            elif config["target"] == "px":
                _prep_px_row(
                    cast(ExchangePxItem | TOptionPxItem, row), config["params"], output
                )
            else:
                raise ValueError(f"Invalid target: {config['target']}")
        except Exception:
            logging.exception(
                f"Error prepping row at {utils_general.to_iso(row['timestamp'])} {row=}"
            )

    if config["target"] == "px":
        if config["params"]["asset_params"]["asset_type"] != "spot":
            # remove spot prices used for conversions
            output = cast(PreppedData, _remove_matching_keys(output, ["spot"], any))
        else:
            # For spot assets, only keep keys for specified base assets
            transformations = config["params"].get("transformation_rules", {})
            base_assets = set(
                [
                    (
                        base
                        if base not in transformations
                        else transformations[base].get("output_base_asset_symbol", base)
                    )
                    for base in config["params"]["base_assets"]
                ]
            )
            keys_to_remove = [
                f".{base}_"
                for base in {key.split(".")[1].split("_")[0] for key in output}
                if base not in base_assets
            ]
            keys_to_remove.append("convspot")
            output = cast(
                PreppedData, _remove_matching_keys(output, keys_to_remove, any)
            )

    return output
