import logging
import math
from decimal import ROUND_HALF_UP, Decimal

import utils_aws
import utils_calc
import utils_general
from utils_calc import Model, SingleParams

from synthetic_price_calc.config import S3, S3_PREFIX
from synthetic_price_calc.smoothing.config import SMOOTHING_CURRENCIES
from synthetic_price_calc.typings import (
    DataSetModel,
    S3Details,
)


def get_data_set_name(data_set: DataSetModel) -> str:
    target_asset = data_set.target_asset
    reference_asset = data_set.reference_asset
    reference_exchange = data_set.reference_exchange
    target_exchange = data_set.target_exchange
    models = data_set.models

    return f"{target_exchange}_{target_asset}_TO_{reference_asset}_{reference_exchange}_{'+'.join(models)}"


def put_s3_object(
    details: S3Details, data: str, sub_prefix: str = "", name_prefix: str = ""
) -> str:

    key = f"{S3_PREFIX}/"
    if sub_prefix:
        key += f"{sub_prefix}/"
    key += (
        f"{name_prefix}{details.obj_prefix}_{details.data_sets}_{details.frequency}_"
        + f"{details.start}_{details.end}_{details.obj_suffix}.csv.gz"
    )

    utils_aws.put_s3_object_sync(
        S3,
        details.bucket,
        key,
        data,
    )
    logging.info(
        f"Stored at https://s3.console.aws.amazon.com/s3/object/{details.bucket}/{key}"
    )

    return key


def extract_future_details(qualified_name: str) -> tuple[str, str]:
    """
    Extracts the frequency, expiry string, and underlying index from a qualified name.

    Args:
        qualified_name (str): The qualified name string to process.

    Returns:
        tuple[str, str, str]: A tuple containing (qn_freq, expiry_str, underlying_index).
    """

    # todo: this will not work for futures that are actually traded. Update function when we accomodea
    qn_tokens = utils_general.get_qfn_and_version(qualified_name)[1]
    qn_freq = qn_tokens[-2]
    expiry_str = qn_tokens[2].split("_")[-1].replace("-SYN", "")
    return qn_freq, expiry_str


def validate_data_sets(data_sets: list[DataSetModel]) -> None:
    """
    Validates that there are no duplicate target_asset and target_exchange pairs in the data_sets.
    A duplicate will cause clashes in the output qualified_name.
    """
    seen = set()
    for ds in data_sets:
        key = (ds.target_asset, ds.target_exchange)
        if key in seen:
            raise ValueError(
                f"Duplicate data_set found for target_asset='{ds.target_asset}' and target_exchange='{ds.target_exchange}'. "
                "This will cause clashes in the output qualified_name."
            )
        seen.add(key)


####################################
####################################
####################################

# todo: move below to utils

####################################
####################################
####################################
####################################


def round_to_nearest_value(
    value: float, nearest: float, strategy: str
) -> float:
    """
    Rounds the value to the nearest multiple of `nearest` based on the rounding strategy.
    """
    if strategy == "floor":
        return math.floor(value / nearest) * nearest
    elif strategy == "ceil":
        return math.ceil(value / nearest) * nearest
    else:
        raise NotImplementedError("Invalid rounding strategy specified.")


def round_with_precision(value: float, precision: int, strategy: str) -> float:
    """
    Rounds the value to a specified number of decimal places using the rounding strategy.
    """
    if strategy == "default":
        return float(
            Decimal(value).quantize(
                Decimal(f'1.{"0" * precision}'), rounding=ROUND_HALF_UP
            )
        )
    else:
        raise NotImplementedError(
            "Invalid rounding strategy for precision specified."
        )


def round_value(
    value: float | int,
    precision: int | None = None,
    round_to_nearest: float | None = None,
    rounding_strategy: str = "default",
) -> float:
    """
    Rounds a value based on the given parameters.

    If both `round_to_nearest` and `precision` are provided, an error is raised.
    If `round_to_nearest` is provided, the function will round to the nearest multiple of that value.
    If `precision` is provided, the function will round to the specified number of decimal places.
    """

    # Assert that both `round_to_nearest` and `precision` are not specified
    if round_to_nearest is not None and precision is not None:
        raise ValueError(
            "Cannot specify both 'round_to_nearest' and 'precision'. Please choose one."
        )

    if round_to_nearest is not None:
        return round_to_nearest_value(
            value, round_to_nearest, rounding_strategy
        )

    elif precision is not None:
        return round_with_precision(value, precision, rounding_strategy)

    # Default rounding strategy
    return round(value)


def is_currency_enabled_for_smoothing(currency: str) -> bool:
    return SMOOTHING_CURRENCIES.get(currency, False)


def get_atm_vol(
    future_px: float, expiry: float, model: Model, params: SingleParams
) -> float:
    return float(
        utils_calc.utils_calc_helpers.model_vol(
            strike=future_px,
            forward=future_px,
            exp=expiry,
            model=model,
            model_params=utils_calc.extract_model_params(params, model),
        )
    )
