import pandas as pd

from synthetic_price_calc.config import LOOKBACK_COLUMN_NAME
from synthetic_price_calc.typings import (
    ParamsSurfaces,
)
from synthetic_price_calc.utils import is_currency_enabled_for_smoothing


def _prepare_expiry_slices_for_smoothing(
    results_for_smoothing: list[ParamsSurfaces],
    lookback_data: pd.DataFrame,
) -> list[pd.DataFrame]:
    """
    Combines new results with historical data, then sorts and groups
    the data into chronologically ordered slices for each qualified_name for smoothing.

    These slices are prepared for a downstream EMA smoothing operation.

    Args:
        results_for_smoothing: A list of nested ParamsSurfaces snapshots that are eligible for smoothing
        lookback_data: A DataFrame with all historical data.

    Returns:
        A list of DataFrames, where each DataFrame contains all data
        for a single 'qualified_name', sorted by timestamp.
    """

    all_snapshot_results = []

    for result in results_for_smoothing:
        target_asset = result["target_asset"]
        assert is_currency_enabled_for_smoothing(target_asset)
        for param in result["params"]:
            all_snapshot_results.append(param)

    new_results_df = pd.DataFrame(all_snapshot_results)
    new_results_df[LOOKBACK_COLUMN_NAME] = False
    new_results_df["smoothed"] = False

    combined_df = pd.concat([lookback_data, new_results_df], ignore_index=True)
    combined_df.sort_values(by=["qualified_name", "timestamp"], inplace=True)
    output_slices = [
        group.reset_index(drop=True)
        for _, group in combined_df.groupby("qualified_name", sort=False)
    ]

    return output_slices
