from synthetic_price_calc.typings import SmoothParamsDict

SPAN_SMOOTH_DEFAULT = {  # TODO: correct comments
    "1h": 20,  # impact: 7h: ~50% / 14h: ~75% / 24h: ~90%
    "1m": 20,  # impact: 7m: ~50% / 14m: ~75% / 24m: ~90%
    "20s": 20,  # impact: 7m: ~50% / 14m: ~75% / 24m: ~90%
}
SPAN_ZSCORE_DEFAULT = {
    "1h": 20,  # impact: 7h: ~50% / 14h: ~75% / 24h: ~90%
    "1m": 20,  # impact: 7m: ~50% / 14m: ~75% / 24m: ~90%
    "20s": 20,  # impact: 7m: ~50% / 14m: ~75% / 24m: ~90%
}

SMOOTH_PARAMS: SmoothParamsDict = {
    "1h": {
        "window": 48,
        "span_smooth": {},
        "span_zscore": {},
        "adjust": True,
    },
    "1m": {
        "window": 60,
        "span_smooth": {},
        "span_zscore": {},
        "adjust": True,
    },
    "20s": {
        "round_to_seconds": 60,
        "window": 60,
        "span_smooth": {},
        "span_zscore": {},
        "adjust": True,
    },
}
