import functools
import logging
import time
from collections import defaultdict
from datetime import UTC, datetime
from itertools import chain
from multiprocessing.connection import Connection
from typing import Any, cast

import orjson
import pandas as pd
import utils_calc
import utils_general
from utils_calc import Model, ModelParamsBase

from synthetic_price_calc.arbitrage import _apply_svi_refitting_to_smoothed_data
from synthetic_price_calc.config import NUM_WORKERS
from synthetic_price_calc.smoothing.smoothing import (
    prepare_and_perform_smoothing,
)
from synthetic_price_calc.synthetic_calc import (
    calc_synthetic_params,
)
from synthetic_price_calc.typings import (
    CalcOutput,
    DataSetSnap,
    FailedTenorObject,
    ParamsSurfaces,
    ProcessDataResult,
    ProcessDataSnapshot,
    S3Details,
    SingleTenor,
    SingleTenorResult,
    Snapshot,
    SpotFromManager,
)
from synthetic_price_calc.utils import (
    extract_future_details,
    is_currency_enabled_for_smoothing,
    put_s3_object,
)

# default='warn'
pd.options.mode.chained_assignment = None  # type: ignore


def process_chunk_helper(
    chunk: list[list[Snapshot]],
    freq: str,
    version: str,
    s3_details: S3Details,
    debug: bool = False,
    **kwargs: Any,  # Added for compatibility with blockstream utils
) -> list[ParamsSurfaces]:

    version_prefix = ""

    if version:
        version_prefix = f"{version}."

    flattened_chunk: list[Snapshot] = list(chain.from_iterable(chunk))
    if not flattened_chunk:
        raise ValueError("Chunk is empty!")

    results = []
    failed_snapshots = []
    for snap in flattened_chunk:
        model_results: dict[str, list[SingleTenorResult]] = defaultdict(list)
        failed_model_results: dict[str, list[FailedTenorObject]] = defaultdict(
            list
        )

        try:
            for data in snap.data_snap:
                tenor_result, failed_tenor_info = _process_single_tenor(
                    data=data,
                    target_asset_spot_data=snap.target_asset_spot_data,
                    reference_asset_spot_data=snap.reference_asset_spot_data,
                    target_asset=snap.target_asset,
                    target_exchange=snap.target_exchange,
                    version_prefix=version_prefix,
                    timestamp=snap.timestamp,
                    debug=debug,
                )

                if failed_tenor_info is not None:
                    failed_model_results.setdefault(data.model, []).append(
                        failed_tenor_info
                    )

                if tenor_result is not None:
                    model_results.setdefault(data.model, []).append(
                        tenor_result
                    )

            for model, failed_tenors in failed_model_results.items():
                failed_snapshot_record = {
                    "model": model,
                    "failed_params": orjson.dumps(
                        [res["failed_tenor"] for res in failed_tenors],
                        option=orjson.OPT_SERIALIZE_NUMPY,
                    ).decode("utf-8"),
                    "reference_params": orjson.dumps(
                        [res["reference_tenor"] for res in failed_tenors],
                        option=orjson.OPT_SERIALIZE_NUMPY,
                    ).decode("utf-8"),
                    "timestamp": snap.timestamp,
                    "target_asset": snap.target_asset,
                    "reference_asset": snap.reference_asset,
                    "ref_asset_spot": snap.reference_asset_spot_data,
                    "target_asset_spot": snap.target_asset_spot_data,
                }
                failed_snapshots.append(failed_snapshot_record)

            for model, model_params in model_results.items():
                if model_params:
                    results.append(
                        ParamsSurfaces(
                            params=model_params,
                            model=model,
                            timestamp=snap.timestamp,
                            target_asset=snap.target_asset,
                            target_exchange=snap.target_exchange,
                            # qualified_name=f"{version_prefix}{snap.target_exchange}.option.{snap.target_asset}.{model}.{freq}.params",
                            runtime=utils_general.to_iso(datetime.now(tz=UTC)),
                        )
                    )
                else:
                    logging.error(
                        f"Empty results for {model=}, iso_stamp={utils_general.to_iso(snap.timestamp)}, {snap.timestamp=}, {snap.target_asset=}, {snap.reference_asset=}, {snap.reference_exchange=} "
                    )

        except Exception:
            logging.exception(
                f"Error while processing snapshot iso_stamp={utils_general.to_iso(snap.timestamp)}, {snap.timestamp=}, {snap.target_asset=}, {snap.reference_asset=}, {snap.reference_exchange=}"
            )

    if failed_snapshots:
        try:
            put_s3_object(
                details=s3_details,
                data=pd.DataFrame(failed_snapshots).to_csv(index=False),
                sub_prefix="failed_snapshots",
            )
            logging.info("Stored failed snapshots to S3")
        except Exception:
            logging.exception("Failed to upload invalid snapshots to S3")

    return results


def _process_chunk(
    chunk: list[list[Snapshot]],
    conn: Connection,
    freq: str,
    version: str,
    s3_details: S3Details,
    debug: bool = False,
) -> None:
    try:
        results = process_chunk_helper(
            chunk=chunk,
            freq=freq,
            version=version,
            debug=debug,
            s3_details=s3_details,
        )
        conn.send(results)
    except Exception as e:
        conn.send({"error": str(e), "details": ""})
        logging.exception("Error processing data chunk")
    conn.close()


def process_data(
    chunk: ProcessDataSnapshot,
    freq: str,
    version: str,
    s3_details: S3Details,
    smooth: bool = False,
    debug: bool = False,
    **kwargs: Any,  # Added for compatibility with blockstream utils
) -> ProcessDataResult:

    t = time.time()

    data_slices = chunk.params

    processed_results: list[ParamsSurfaces] = utils_general.parallel_process(
        data_slices=data_slices,
        num_workers=NUM_WORKERS,
        process_chunk_fn=functools.partial(
            _process_chunk,
            freq=freq,
            version=version,
            s3_details=s3_details,
            debug=debug,
        ),
        chunk_data=True,
    )

    if not processed_results:
        raise ValueError("Failed to calculate synthetic params")

    final_results = processed_results

    logging.info(f"Params calc took {len(processed_results)} results")

    if smooth:
        t = time.time()

        lookback_df = chunk.previous_results["df"]

        if lookback_df is None or lookback_df.empty:
            raise ValueError("Lookback data is empty")

        final_results = _apply_smoothing_if_needed(
            results=final_results,
            lookback_df=lookback_df,
            freq=freq,
        )

        # todo: slice_lookback

        logging.info(f"Smoothing took {round(time.time() - t, 2)}s")

    smoothed_results = _apply_svi_refitting_to_smoothed_data(smoothed_results)

    final_results = _convert_intermediate_to_process_data_results(
        smoothed_results
    )

    try:
        if final_results:
            final_results = sorted(final_results, key=lambda x: x["timestamp"])
    except Exception:
        logging.exception("Error processing final results")

    logging.info(f"SyntheticPrice calc took {round(time.time() - t)}s")

    return {
        "calc_output": final_results,
        "lookback_result": None,  # todo: doesnt need to be a df
    }


def _apply_smoothing_if_needed(
    results: list[ParamsSurfaces],
    lookback_df: pd.DataFrame,
    freq: str,
) -> list[ParamsSurfaces]:
    """
    Mutates `results` in-place: for every item whose target asset is enabled
    for smoothing, compute the smoothed params and overwrite r["params"].
    """

    to_smooth = [
        r
        for r in results
        if is_currency_enabled_for_smoothing(r["target_asset"])
    ]
    if not to_smooth:  # Fast-path: nothing to smooth
        return results

    aggregator = prepare_and_perform_smoothing(
        results_for_smoothing=to_smooth,
        lookback_df=lookback_df,
        freq=freq,
    )

    for r in to_smooth:
        r["params"] = aggregator[r["target_exchange"]][r["target_asset"]][
            r["model"]
        ][r["timestamp"]]

    logging.info("Smoothing took %.2fs", time.time() - t)
    return results


def _convert_intermediate_to_process_data_results(
    intermediate_results: list[ParamsSurfaces],
) -> list[CalcOutput]:
    """
    Convert ParamsSurfaces objects to CalcOutput format.
    This is the ONLY function that performs JSON serialization.

    Args:
        intermediate_results: List of ParamsSurfaces objects

    Returns:
        List of CalcOutput objects with JSON-serialized params
    """
    final_results = []

    for result in intermediate_results:
        final_results.append(
            CalcOutput(
                timestamp=result["timestamp"],
                params=orjson.dumps(
                    result["params"], option=orjson.OPT_SERIALIZE_NUMPY
                ).decode("utf-8"),
                qualified_name=result["qualified_name"],
                runtime=result["runtime"],
            )
        )

    return final_results


def _process_single_tenor(
    data: DataSetSnap,
    target_asset: str,
    target_exchange: str,
    target_asset_spot_data: list[SpotFromManager],
    reference_asset_spot_data: list[SpotFromManager],
    version_prefix: str,
    timestamp: int,
    debug: bool = False,
) -> tuple[SingleTenorResult | None, FailedTenorObject | None]:

    params = data.params
    future = data.future

    qn_freq, expiry_str = extract_future_details(future["qualified_name"])
    try:
        tenor_result, is_successful = calc_synthetic_params(
            ref_asset_daily_spot_pxs=reference_asset_spot_data,
            target_asset_daily_spot_pxs=target_asset_spot_data,
            tenor_days=params["expiry"] * 365,
            ref_params=params,
            model=data.model,
        )
        if not is_successful:
            if debug:
                return None, {
                    "failed_tenor": tenor_result,
                    "reference_tenor": params,
                }
            return None, None

        return (
            cast(
                SingleTenorResult,
                {
                    **tenor_result,
                    "qualified_name": f"{version_prefix}{target_exchange}.option.{target_asset}.{data.model}.{expiry_str}.{qn_freq}.params",
                    "underlying_index": utils_general.get_qfn_and_version(
                        future["qualified_name"]
                    )[1][-3],
                    "spot": data.spot,
                    "forward": future["px"],
                    "atm_vol": _get_atm_vol(
                        future_px=future["px"],
                        expiry=params["expiry"],
                        model=cast(Model, data.model),
                        params=tenor_result,
                    ),
                },
            ),
            None,
        )

    except Exception:
        logging.exception(
            f"Error while creating synthetic params from {target_asset=} from {params['base']}. iso_stamp={utils_general.to_iso(timestamp)}, {timestamp=}, {expiry_str=}"
        )
        return None, None


def _get_atm_vol(
    future_px: float, expiry: float, model: Model, params: SingleTenor
) -> float:
    return float(
        utils_calc.utils_calc_helpers.model_vol(
            strike=(future_px),
            forward=(future_px),
            exp=expiry,
            model=model,
            model_params=cast(
                ModelParamsBase,
                utils_calc.extract_model_params(params, model),  # type: ignore
            ),
        ),
    )
