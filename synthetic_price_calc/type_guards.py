from typing import Any, TypeGuard

import utils_general

from synthetic_price_calc.typings import (
    BSBaseIndexQueryResult,
    FutureQueryResult,
    ListedParamsBundle,
    ListedTenorParams,
    SabrParamsBundle,
    SviParamsBundle,
)


def is_listed_params_bundle(data: Any) -> TypeGuard[ListedParamsBundle]:
    _, qn_token = utils_general.get_qfn_and_version(data["qualified_name"])
    return (
        "option" in data["qualified_name"]
        and len(qn_token) == 6
        and "params" in data
    )


def is_sabr_listed_params_bundle(data: Any) -> TypeGuard[ListedParamsBundle]:
    _, qn_token = utils_general.get_qfn_and_version(data["qualified_name"])
    return (
        "option" in data["qualified_name"]
        and len(qn_token) == 6
        and "params" in data
        and "SABR" in data["qualified_name"]
    )


def is_svi_listed_params_bundle(data: Any) -> TypeGuard[ListedParamsBundle]:
    _, qn_token = utils_general.get_qfn_and_version(data["qualified_name"])
    return (
        "option" in data["qualified_name"]
        and len(qn_token) == 6
        and "params" in data
        and "SVI" in data["qualified_name"]
    )


def is_listed_tenor_params(data: Any) -> TypeGuard[ListedTenorParams]:
    return all(
        key in data
        for key in ["atm_vol", "underlying_index", "expiry", "timestamp"]
    ) and isinstance(data, dict)


def is_raw_spot(data: Any) -> TypeGuard[BSBaseIndexQueryResult]:
    return "spot" in data["qualified_name"]


def is_raw_future(data: Any) -> TypeGuard[FutureQueryResult]:
    return "future" in data["qualified_name"]


def is_sabr_model_params(data: Any, model: str) -> TypeGuard[SabrParamsBundle]:
    return (
        any(model.lower() in key.lower() for key in data.keys())
        and "sabr" in model.lower()
    )


def is_svi_model_params(data: Any, model: str) -> TypeGuard[SviParamsBundle]:
    return (
        any(model.lower() in key.lower() for key in data.keys())
        and "svi" in model.lower()
    )


def _is_model_present_in_keys(data: Any, model: str) -> bool:
    model_lower = model.lower()
    return any(model_lower in key.lower() for key in data.keys())


def is_matching_model_params_bundle(
    data: Any, model: str
) -> TypeGuard[ListedTenorParams]:
    if not isinstance(data, dict):
        return False
    return _is_model_present_in_keys(data, model)
