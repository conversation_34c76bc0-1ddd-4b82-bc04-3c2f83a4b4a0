import logging
from datetime import timed<PERSON><PERSON>
from typing import Literal, cast

import pandas as pd
import utils_general
from block_stream.typings import InstrumentsDetailsMap
from datagrabber import (
    CatalogItem,
    GrabParams,
    LookupOptions,
    construct_timeseries_queries,
    get_instruments,
    grab,
)
from utils_general import AssetPairData, SpotUSDQuotedConfig

from synthetic_price_calc.config import (
    HISTORIC_SPOT_DATA_CONFIG,
    SPOT_DATA_HISTORICAL_TIMEDELTA,
)
from synthetic_price_calc.lookback import generate_lookback_timestamps
from synthetic_price_calc.smoothing.config import SMOOTH_PARAMS
from synthetic_price_calc.typings import (
    DataQueryResult,
    DataSetModel,
    LookbackCalcOutput,
    RetrievedData,
    SingleTenorResult,
)
from synthetic_price_calc.utils import is_currency_enabled_for_smoothing


def _get_spot_queries(
    start: str,
    end: str,
    frequency: str,
    data_sets: list[DataSetModel],
    consistent_read: bool = False,
) -> tuple[list[GrabParams], list[CatalogItem]]:

    base_assets = [
        asset
        for data_set in data_sets
        for asset in (data_set.reference_asset, data_set.target_asset)
    ]

    spot_queries = []
    start_datetime = utils_general.to_datetime(start)
    end_datetime = utils_general.to_datetime(end)
    start_end_diff_days = (end_datetime - start_datetime).days

    daily_query_end_day = utils_general.to_iso(
        end_datetime.replace(hour=0, minute=0, second=0, microsecond=0)
    )
    daily_query_start = utils_general.to_iso(
        start_datetime.replace(hour=0, minute=0, second=0, microsecond=0)
        - (SPOT_DATA_HISTORICAL_TIMEDELTA + timedelta(days=start_end_diff_days))
    )
    historical_timestamps = utils_general.generate_timestamps(
        start=daily_query_start,
        end=daily_query_end_day,
        interval=str(HISTORIC_SPOT_DATA_CONFIG["interval"]),
        periods=int(HISTORIC_SPOT_DATA_CONFIG["periods"]),
        time_condition={},
    )
    spot_data: list[AssetPairData] = []
    for base_asset in base_assets:
        spot_data.extend(_get_spot_data_details(base_asset, "USD", frequency))

    historical_qns = [
        spot.qualified_name.replace("1m", "1h") for spot in spot_data
    ]

    # spot queries for historical data
    spot_queries.extend(
        construct_timeseries_queries(
            qualified_names=list(set(historical_qns)),
            timestamp_list=list(historical_timestamps),
            consistent_read=consistent_read,
            lookup_options={},
        )
    )

    # The start and end dates passed in the live system have a diff of LIVE_SYSTEM_START_END_DIFF. This is to ensure
    # that we have macthing spot data for the preloaded modelparams.
    # get latest spot queries
    spot_queries.extend(
        construct_timeseries_queries(
            qualified_names=list({spot.qualified_name for spot in spot_data}),
            start=int(utils_general.to_datetime(start).timestamp() * 1e9),
            end=int(utils_general.to_datetime(end).timestamp() * 1e9),
            consistent_read=consistent_read,
            lookup_options={},
        )
    )

    exchanges = [spot.qualified_name.split(".")[0] for spot in spot_data]
    base_instruments = base_assets.copy()
    quote_assets = ["USD"]
    # handle kraken
    if "kraken" in exchanges:
        quote_assets.append("ZUSD")

    spot_instruments = get_instruments(
        fields=[],
        start=start,
        end=end,
        exchanges=list(set(exchanges)),
        asset_types=["spot"],
        base_assets=base_instruments,
        # ZUSD for Kraken
        quote_assets=quote_assets,
    )

    return spot_queries, spot_instruments


def _get_forward_qualified_names(
    freq: str,
    target_exchange: str,
    target_asset: str,
    unique_expiries: set[str],
) -> list[str]:
    """
    We use the options catalog for the reference asset to get the expiries and construct
    the synthetic futures qualified names for the target asset
    """

    futures_qualified_names: list[str] = []
    for iso_exp in unique_expiries:
        instrument = utils_general.generate_bs_instrument_name(
            asset_type=utils_general.AssetTypes.FUTURE,
            base=target_asset,
            quote="USD",
            expiry=iso_exp,
        )
        futures_qualified_names.append(
            f"{target_exchange}.future.{instrument}-SYN.{freq}.px"
        )

    return futures_qualified_names


def _get_unique_expiries_for_dataset(
    reference_exchange: str,
    reference_asset: str,
    start: str,
    end: str,
) -> set[str]:
    option_instruments = get_instruments(
        fields=[],
        start=start,
        end=end,
        exchanges=[
            (
                reference_exchange
                if reference_exchange != "v2composite"
                else "blockscholes"
            )
        ],
        asset_types=["option"],
        base_assets=[reference_asset],
    )
    unique_expiries: set[str] = {item["expiry"] for item in option_instruments}
    return unique_expiries


def retrieve_data(
    data_sets: list[DataSetModel],
    frequency: str,
    interval: str,
    start: str,
    end: str,
    version: str = "",
    fetch_lookback_data: bool = False,
    lookup_options: LookupOptions | None = None,
    consistent_read: bool = False,
) -> RetrievedData:

    if not data_sets:
        raise ValueError("data_sets cannot be empty")

    version_prefix = f"{version}." if version else ""
    lookup_options = lookup_options or {}

    all_queries: list[GrabParams] = []

    spot_queries, spot_instruments = _get_spot_queries(
        data_sets=data_sets,
        start=start,
        end=end,
        frequency=frequency,
        consistent_read=consistent_read,
    )
    instruments_map = _construct_instruments_map(instruments=spot_instruments)
    all_queries.extend(spot_queries)

    # construct params and futures qualified names
    all_params_qualified_names: set[str] = set()
    futures_qualified_names: set[str] = set()
    for ds in data_sets:
        params_qns, forward_qns = _get_params_and_forward_qualified_names(
            version_prefix=version_prefix,
            dataset=ds,
            start=start,
            end=end,
            frequency=frequency,
        )
        all_params_qualified_names.update(params_qns)
        futures_qualified_names.update(forward_qns)

    futures_and_params_queries = construct_timeseries_queries(
        qualified_names=list(
            all_params_qualified_names | futures_qualified_names
        ),
        start=int(utils_general.from_iso(start).timestamp() * 1e9),
        end=int(utils_general.from_iso(end).timestamp() * 1e9),
        consistent_read=consistent_read,
        lookup_options=lookup_options,
    )
    all_queries.extend(futures_and_params_queries)

    raw_data = cast(list[DataQueryResult], grab(all_queries))

    lookback_data = None
    if fetch_lookback_data:
        lookback_params_qns = _get_lookback_qualified_names(
            version_prefix=version_prefix,
            data_sets=data_sets,
            frequency=frequency,
        )
        if not lookback_params_qns:
            raise ValueError("Lookback params qualified_names cannot be empty")

        # The end of the lookback should be the start of our chunk
        lookback_data = _retrieve_lookback_data(
            lookback_qns=lookback_params_qns,
            lookback_end=start,
            freq=frequency,
            interval=interval,
            consistent_read=consistent_read,
            lookup_options=lookup_options,
        )

    return RetrievedData(
        raw_data=raw_data,
        instruments_details=instruments_map,
        lookback_data=lookback_data,
    )


def _get_lookback_qualified_names(
    version_prefix: str, data_sets: list[DataSetModel], frequency: str
) -> set[str]:
    lookback_params_qns = set()
    for dataset in data_sets:
        if is_currency_enabled_for_smoothing(dataset.target_asset):
            lookback_params_qns.update(
                {
                    f"{version_prefix}{dataset.target_exchange}.option.{dataset.target_asset}.{m}.{frequency}.params"
                    for m in dataset.models
                }
            )

    return lookback_params_qns


def _retrieve_lookback_data(
    lookback_qns: set[str],
    lookback_end: str,
    freq: str,
    interval: str,
    consistent_read: bool = False,
    lookup_options: LookupOptions | None = None,
) -> pd.DataFrame:

    if lookup_options is None:
        lookup_options = {}

    smooth_freq = "20s" if freq == "live" else freq
    lookback_window_size = SMOOTH_PARAMS[smooth_freq]["window"]

    if lookback_window_size == 0:
        raise ValueError("Lookback window size cannot be 0")

    snapshot_timestamps = list(
        generate_lookback_timestamps(
            lookback_end, lookback_window_size, interval
        )
    )

    qn_freq = "1m" if freq == "live" else freq

    lookback_queries = construct_timeseries_queries(
        qualified_names=list(lookback_qns),
        start=min(snapshot_timestamps),
        end=max(snapshot_timestamps),
        consistent_read=consistent_read,
        lookup_options=lookup_options,
    )

    grab_results = cast(list[LookbackCalcOutput], grab(lookback_queries))
    logging.info("--Retrieved lookback data--")

    return pd.DataFrame(
         _unpack_expiries_from_params_bundle(
            grab_results, qn_freq=qn_freq, is_live_version=(freq == "live")
        )
    )


def _unpack_expiries_from_params_bundle(
    results: list[LookbackCalcOutput], qn_freq: str, is_live_version: bool
) -> list[SingleTenorResult]:
    """
    Extract listed result if needed
    """
    transform: list[SingleTenorResult] = []
    try:
        for result in results:
            for r in result["params"]:
                if is_live_version:
                    r["qualified_name"] = r["qualified_name"].replace(
                        f".{qn_freq}.", ".live."
                    )
                r["timestamp"] = result["timestamp"]
                transform.append(r)

    except Exception as e:
        logging.exception(
            f"Error trying to extract params inside retrieved listed params. error: {e}"
        )

    return transform


def _get_spot_data_details(
    base: str,
    quote: Literal["USD"],
    frequency: str,
) -> list[AssetPairData]:
    assert quote == "USD"

    spot_data = utils_general.get_spot_with_backup_data(
        base=base,
        frequency=frequency,
        quote=quote,
        config=SpotUSDQuotedConfig(
            use_mids=True,
            number_per_ccy=5,
            use_exchange_indices=False,
            run_type="scheduled",  # retrieve_data is only used to preload minutely data in live system
            # .live qfns do not exist in db
        ),
    )

    return spot_data


def _construct_instruments_map(
    instruments: list[CatalogItem],
) -> InstrumentsDetailsMap:

    instruments_map = {}
    for instrument in instruments:
        exchange = str(instrument["qualified_name"]).split(".")[0]
        if exchange == "kraken":
            if instrument["baseAsset"] == "XETH":
                instrument["baseAsset"] = "ETH"
            if instrument["quoteAsset"] == "ZUSD":
                instrument["quoteAsset"] = "USD"

        qn_split = instrument["qualified_name"].split(".")
        instruments_map[
            f"{qn_split[0]}.{qn_split[1]}.{instrument['instrument']}"
        ] = instrument

    return cast(InstrumentsDetailsMap, instruments_map)


def _get_params_and_forward_qualified_names(
    version_prefix: str,
    dataset: DataSetModel,
    start: str,
    end: str,
    frequency: str,
) -> tuple[list[str], list[str]]:

    params_qns = [
        f"{version_prefix}{dataset.reference_exchange}.option.{dataset.reference_asset}.{m}.{frequency}.params"
        for m in dataset.models
    ]

    unique_expiries = _get_unique_expiries_for_dataset(
        reference_exchange=dataset.reference_exchange,
        reference_asset=dataset.reference_asset,
        start=start,
        end=end,
    )

    futures_qns = _get_forward_qualified_names(
        target_asset=dataset.target_asset,
        target_exchange=dataset.target_exchange,
        freq=frequency,
        unique_expiries=unique_expiries,
    )

    return params_qns, futures_qns
