import functools
import logging
from itertools import chain
from multiprocessing.connection import Connection
from typing import cast

import numpy as np
import numpy.typing as npt
import pandas as pd
import utils_general
from sklearn.isotonic import IsotonicRegression  # type: ignore
from sklearn.metrics import r2_score  # type: ignore
from utils_aws import load_ssm_params
from utils_calc import (
    DEFAULT_SURFACE_MONEYNESS,
    SABR_RHO_BOUND,
    CalibrationQuery,
    Model,
    get_domestic_rate,
    perform_svi_calibration,
    svi_var,
)

from synthetic_price_calc.config import NUM_WORKERS, R2_ACCEPTABLE
from synthetic_price_calc.typings import (
    ParamsSurfaces,
    SingleTenorResult,
    SveRefittingAggregator,
    SviTenorResult,
)
from synthetic_price_calc.utils import get_atm_vol


def _refit_svi_to_reduce_calendar_arb(
    svi_params_list: list[SingleTenorResult],
) -> list[SingleTenorResult]:
    """
    Recalibrates SVI parameters to reduce calendar arbitrage across expiries.

    This function takes in a set of SVI parameters across different expiries,
    constructs total variance curves by strike, applies isotonic regression to
    enforce monotonicity of total variance with respect to time (expiry), and
    then refits SVI parameters per expiry to the smoothed variance surface.

    :param svi_params_list: A list containing the initial SVI fit results.
    :return: A list containing the recalibrated SVI fit results.
    """
    df = (
        pd.DataFrame(svi_params_list)
        .sort_values(by="expiry")
        .reset_index(drop=True)
    )
    domestic_rates = load_ssm_params(
        {
            "DOMESTIC_RATES": "/data/interest-rates/domestic",
        }
    )["DOMESTIC_RATES"]

    # Step 1: Compute total variance for each expiry and strike
    moneyness_standard = DEFAULT_SURFACE_MONEYNESS
    moneyness_grid = np.tile(moneyness_standard, (len(df), 1))
    f = np.asarray(df["forward"].values, dtype=np.float64)
    forward = f.reshape(-1, 1)
    strike_matrix = forward * moneyness_grid

    # Extract and convert SVI parameters to proper NumPy arrays
    a = np.asarray(df["svi_a"].values, dtype=np.float64)
    b = np.asarray(df["svi_b"].values, dtype=np.float64)
    rho = np.asarray(df["svi_rho"].values, dtype=np.float64)
    m = np.asarray(df["svi_m"].values, dtype=np.float64)
    sigma = np.asarray(df["svi_sigma"].values, dtype=np.float64)

    # Compute vectorized total variance matrix
    tv_matrix = np.asarray(
        svi_var(a=a, b=b, rho=rho, m=m, sigma=sigma, f=f, k=strike_matrix)
    ).astype(np.float64)

    # Step 2: Apply isotonic regression for each strike
    T = np.asarray(df["expiry"].values, dtype=np.float64)
    tv_monotonic = apply_isotonic_regression_by_strike(
        expiries=T,
        total_variance_matrix=tv_matrix,
    )

    # Step 3: Refit SVI per expiry
    def recalibration_condition(sol: dict[str, float]) -> bool:
        return (
            np.isinf(np.asarray(list(sol.values()))).any()
            or np.isnan(np.asarray(list(sol.values()))).any()
            or abs(sol["svi_rho"]) > SABR_RHO_BOUND
        )

    refitted_svi_params_list: list[SingleTenorResult] = []
    for i, expiry in enumerate(T):
        w_target = tv_monotonic[i]
        row = df.loc[i]
        strike_list = row["forward"] * np.asarray(moneyness_standard)
        vol_target = np.sqrt(w_target / expiry)

        strikes_vol_zip_full_range = list(
            zip(strike_list, vol_target, strict=False)
        )
        r_d = get_domestic_rate(domestic_rates, row["expiry"] * 365)

        query: CalibrationQuery = {
            "expiry": row["expiry"],
            "forward": row["forward"],
            "spot": row["spot"],
            "domestic_rate": r_d,
            "model": "SVI",
            "test_type": "strikes",
            "vol_test_type": "vol_lognormal",
            "LNvols": vol_target,
            "strikes": strike_list,
            "biv": vol_target,
            "aiv": vol_target,
        }

        new_result = perform_svi_calibration(
            query=query,
            method="SLSQP",
            tol=1e-12,
            recalibration_condition=recalibration_condition,
            init_guess_var_by_strike_to_use=strikes_vol_zip_full_range,
            optimizer_options={"maxiter": 1000},
        )

        recalibrated_params = new_result["svi_parameters"]
        a_new = recalibrated_params["svi_a"]
        b_new = recalibrated_params["svi_b"]
        rho_new = recalibrated_params["svi_rho"]
        m_new = recalibrated_params["svi_m"]
        sigma_new = recalibrated_params["svi_sigma"]

        recalibrated_tv = svi_var(
            a=a_new,
            b=b_new,
            rho=rho_new,
            m=m_new,
            sigma=sigma_new,
            f=row["forward"],
            k=strike_list,
        )
        recalibrated_vol = np.sqrt(np.array(recalibrated_tv) / expiry)

        calib_r2 = r2_score(vol_target, recalibrated_vol)
        atm_vol = get_atm_vol(
            future_px=row["forward"],
            expiry=row["expiry"],
            model=cast(Model, "SVI"),
            params=recalibrated_params,
        )

        # Fallback to original params if calibration fails
        if calib_r2 < R2_ACCEPTABLE:
            qualified_name = str(row["qualified_name"])
            timestamp = int(row["timestamp"])
            logging.error(
                f"Low R2 score for {qualified_name=}, {timestamp=}, {expiry=}, {calib_r2=}. Reverting to original params."
            )
            a_new = row["svi_a"]
            b_new = row["svi_b"]
            rho_new = row["svi_rho"]
            m_new = row["svi_m"]
            sigma_new = row["svi_sigma"]

            atm_vol = get_atm_vol(row["forward"], row["expiry"], "SVI", row)

        refitted_result = SviTenorResult(
            svi_a=float(a_new),
            svi_b=float(b_new),
            svi_rho=float(rho_new),
            svi_m=float(m_new),
            svi_sigma=float(sigma_new),
            expiry=float(row["expiry"]),
            timestamp=int(row["timestamp"]),
            qualified_name=str(row["qualified_name"]),
            underlying_index=str(row["underlying_index"]),
            spot=float(row["spot"]),
            forward=float(row["forward"]),
            atm_vol=atm_vol,
            calib_r2=float(calib_r2),
            smoothed=bool(row.get("smoothed", False)),
            repaired=True,
        )

        refitted_svi_params_list.append(refitted_result)

    return refitted_svi_params_list


def apply_isotonic_regression_by_strike(
    expiries: npt.NDArray[np.float64],
    total_variance_matrix: npt.NDArray[np.float64],
) -> npt.NDArray[np.float64]:
    """
    Applies isotonic regression to each strike slice across expiries to ensure
    total variance is non-decreasing in time.

    :param expiries: 1D array of expiry times.
    :param total_variance_matrix: 2D array of total variances, shape (n_expiries, n_strikes).
    :return: 2D array of monotonic total variances, same shape as input.
    """
    n_strikes = total_variance_matrix.shape[1]
    tv_monotonic = np.vstack(
        [
            IsotonicRegression(
                increasing=True, out_of_bounds="clip"
            ).fit_transform(expiries, total_variance_matrix[:, j])
            for j in range(n_strikes)
        ]
    ).T
    return tv_monotonic


def apply_svi_refitting(
    results: list[ParamsSurfaces],
) -> list[ParamsSurfaces]:
    """
    Apply SVI refitting to smoothed data, preserving the ParamsSurfaces format.

    Args:
        smoothed_results: List of smoothed ParamsSurfaces objects

    Returns:
        List of ParamsSurfaces objects with SVI refitting applied
    """

    to_repair = [r for r in results if r["model"] == "SVI"]
    if not to_repair:
        return results

    repair_slices = [[ts_params] for ts_params in to_repair]

    refitted_results: list[ParamsSurfaces] = utils_general.parallel_process(
        data_slices=repair_slices,
        num_workers=NUM_WORKERS,
        process_chunk_fn=functools.partial(
            _apply_calendar_arbitrage_refitting,
        ),
        chunk_data=True,
    )

    refitting_aggregator: SveRefittingAggregator = utils_general.nested_dict()
    for item in refitted_results:
        refitting_aggregator[item["target_exchange"]][item["target_asset"]][
            item["model"]
        ][item["timestamp"]] = item

    for og_item in to_repair:
        refitted_item = refitting_aggregator[og_item["target_exchange"]][
            og_item["target_asset"]
        ][og_item["model"]][og_item["timestamp"]]

        og_item.update(refitted_item)

    return results


def _apply_calendar_arbitrage_refitting(
    chunk: list[list[ParamsSurfaces]],
    conn: Connection,
) -> None:
    try:
        results = _calendar_arbitrage_repair_helper(
            chunk=chunk,
        )
        conn.send(results)
    except Exception as e:
        conn.send({"error": str(e), "details": ""})
        logging.exception("Error processing data chunk")
    conn.close()


def _calendar_arbitrage_repair_helper(
    chunk: list[list[ParamsSurfaces]],
) -> list[ParamsSurfaces]:

    results = []
    flattened_chunk: list[ParamsSurfaces] = list(chain.from_iterable(chunk))
    if not flattened_chunk:
        raise ValueError("Chunk is empty!")

    for snap in flattened_chunk:
        snap["params"] = _refit_svi_to_reduce_calendar_arb(snap["params"])
        results.append(snap)
    return results
