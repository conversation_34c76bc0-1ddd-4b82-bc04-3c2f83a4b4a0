AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Block Scholes Model Parameters Calculation SVI/SABR

Resources:
  midCalcFunction:
    Type: AWS::Serverless::Function # More info about Function Resource: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction
    Properties:
      PackageType: Image
      CodeUri: ./api
      Timeout: 900
      MemorySize: 10240
      Policies:
        - arn:aws:iam::685767522279:policy/allServicesDynamoDBPolicy
        - arn:aws:iam::685767522279:policy/LambdaFunctionsS3Policy
        - arn:aws:iam::685767522279:policy/CalcSSMReadPolicy
      Environment:
        Variables:
          LOG_LEVEL: INFO
          SMOOTHING: 0
      Architectures:
        - arm64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./api
      DockerTag: latest

Outputs:
  midCalcFunction:
    Description: "midCalc Lambda Function ARN"
    Value: !GetAtt midCalcFunction.Arn
  midCalcFunctionIamRole:
    Description: "Implicit IAM Role created for midCalc function"
    Value: !GetAtt midCalcFunctionRole.Arn
  midCalcFunctionLogGroupName:
    Description: "midCalc Lambda Function name"
    Value: !Sub "/aws/lambda/${midCalcFunction}"
    Export:
      Name: midCalcFunctionLogGroupName
