import asyncio
import functools
import logging
import os
import time
from collections.abc import Iterable
from datetime import UTC, datetime
from typing import Any

import orjson
import utils_aws
import utils_general
from block_stream.agent import Agent
from block_stream.channel import Channel
from block_stream.consumer import (
    Consumer,
    DerivedDataCache,
    HistoricalTickCache,
    WaitForSetCalculationCache,
    cancel_all_tasks,
)
from block_stream.consumer.auto_scaling_cache_manager import (
    AutoScalingCacheManager,
)

from synthetic_price_calc.config import (
    BLOCKSTREAM_ENDPOINT,
    LATEST_TICK_TIME_WINDOW,
    LIVE_SYSTEM_START_END_DIFF,
    METRIC_S,
    RUN_CLEANUP_FNS_AFTER_SECS,
    SPOT_DATA_HISTORICAL_TIMEDELTA,
    SSM_DATA_SET_CONFIG_PATH,
)
from synthetic_price_calc.prep_data import prep_data
from synthetic_price_calc.process_data import process_data
from synthetic_price_calc.retrieve_data import retrieve_data
from synthetic_price_calc.type_guards import (
    is_listed_params_bundle,
    is_raw_future,
    is_raw_spot,
)
from synthetic_price_calc.typings import (
    CalcOutput,
    DataSetModel,
    FutureQueryResult,
    ListedParamsBundle,
    ProcessDataResult,
    RetrievedData,
)
from synthetic_price_calc.utils import get_data_set_name

utils_general.setup_python_logger(os.environ.get("LOG_LEVEL", logging.INFO))


def _spot_predicate_fn(
    tick: dict[str, Any],
    instrument: dict[str, Any],
    exchanges: list[str],
    base_assets: list[str],
    check_timestamp: bool,
) -> bool:

    # accept spot_data + timestamp == daily stamp only for target and reference_asset
    if "t" not in tick:
        return False

    if check_timestamp:
        date_time = utils_general.to_datetime(tick["t"])
        if (
            date_time.hour != 0
            or date_time.minute != 0
            or date_time.second != 0
        ):
            return False

    _, qn_tokens = utils_general.get_qfn_and_version(tick["q"])
    is_valid = bool(
        len(qn_tokens) == 6
        and qn_tokens[0] in exchanges
        and (qn_tokens[1] == "spot")
        and qn_tokens[2].startswith(tuple(base_assets))
        and "USD." in tick["q"]  # protects against USDC and USDT
        and tick["q"].endswith(("mid.px", "index.px"))
    )

    def _round_to_nearest_second(tstamp: int) -> int:
        return int(round(tstamp / 1e9) * 1e9)

    if is_valid:
        # round the index price of the valid tick to the nearest second
        tick["t"] = _round_to_nearest_second(tick["t"])

    return is_valid


def _params_predicate_fn(
    tick: dict[str, Any],
    models: list[str],
    reference_asset: str,
    reference_exchange: str,
) -> bool:
    if "t" not in tick:
        return False
    _, qn_tokens = utils_general.get_qfn_and_version(tick["q"])
    # v2compsoite.option.BTC.SVI.1m.params
    is_valid = bool(
        len(qn_tokens) == 6
        and qn_tokens[0] == reference_exchange
        and (qn_tokens[1] == "option")
        and (reference_asset == qn_tokens[2])
        and qn_tokens[3] in models
        and qn_tokens[-1] == "params"
    )

    return is_valid


def _futures_predicate_fn(
    tick: dict[str, Any],
    target_asset: str,
    target_exchange: str,
) -> bool:
    if "t" not in tick:
        return False

    _, qn_tokens = utils_general.get_qfn_and_version(tick["q"])
    # blockscholes-syn.future.CCY_USD_2024-05-22T08:00:00Z-SYN.1m.px
    is_valid = bool(
        len(qn_tokens) == 5
        and qn_tokens[0] == target_exchange
        and (qn_tokens[1] == "future")
        and (target_asset in qn_tokens[2])
        and ("_USD_" in qn_tokens[2])
        and ("d-" not in qn_tokens[2])
        and qn_tokens[-1] == "px"
    )

    return is_valid


async def _put_output(
    output_data_stream: Channel,
    output: ProcessDataResult,
) -> None:

    result = output["calc_output"]

    if not isinstance(result, list):
        raise ValueError(f"Expected list of calc outputs, got {type(result)}")

    if not result:
        logging.warning(
            "Empty output, while trying to flush data to stream. Skipping"
        )
        return None

    results = _transform_results_and_log(result)

    await output_data_stream.put_batch(results)
    await output_data_stream.flush()


def _transform_results_and_log(
    results: list[CalcOutput],
) -> Iterable[tuple[dict[str, Any], str]]:
    for item in results:
        # output is unversioned
        qn_split = item["qualified_name"].split(".")
        key = f"{qn_split[0]}.{qn_split[2]}.{qn_split[3]}"

        params = item["params"]
        result = {
            "q": item["qualified_name"],
            "t": item["timestamp"],
            "p": (
                orjson.loads(params) if isinstance(params, str) else params
            ),  # p for VSF
        }

        utils_general.log_bsdebug("put: %s onto %s", result, key)
        logging.info(f"Putting {key=} output onto stream")

        yield result, key


def _preload_historic_data(
    data_set: DataSetModel, scheduled_version: str
) -> RetrievedData:
    # to fetch latest params
    start = utils_general.to_iso(
        datetime.now(tz=UTC) - LIVE_SYSTEM_START_END_DIFF
    )
    end = utils_general.to_iso(datetime.now(tz=UTC))

    retrieved_data = retrieve_data(
        data_sets=[data_set],
        frequency="1m",  # grab historical 1m data
        version=scheduled_version,
        start=start,
        end=end,
        lookup_options={
            "order_by": {"pkey": "asc", "skey": "desc"},
            "limit": 1,
        },
        fetch_lookback_data=False,
        interval="minute",  # used to generate lookback timestamps when smoothing is enabled
    )
    logging.info("Preloaded Historic Data")
    return retrieved_data


def _update_with_prefetched_items(
    item_list: list[ListedParamsBundle] | list[FutureQueryResult],
    prefetched_points: dict[int, dict[str, Any]],
    ts_set: dict[int, set[str]],
) -> None:
    for item in item_list:
        t = item["timestamp"]
        qn = item["qualified_name"]
        if t not in prefetched_points:
            prefetched_points[t] = {}
        if t not in ts_set:
            ts_set[t] = set()
        prefetched_points[t][qn] = item
        ts_set[t].add(qn)

    return


def _build_caches(
    target: str, scheduled_version: str
) -> WaitForSetCalculationCache:
    start_time = time.time()

    target_data = orjson.loads(target)
    data_set = DataSetModel(**target_data)
    set_smoothing_currencies_cache

    historic_data = _preload_historic_data(data_set, scheduled_version)

    def _clean_qn(qn: str) -> str:
        # Live system does not use versioning. Note that not all data will be versioned
        if scheduled_version:
            qn = qn.replace(f"{scheduled_version}.", "")
        return qn.replace(".1m.", ".live.")

    spot_list = []
    params_list = []
    param_qns = []
    futures_list = []

    spot_instruments = {
        key: val
        for key, val in historic_data.instruments_details.items()
        if "spot" in key
    }
    spot_exchanges = [key.split(".")[0] for key in spot_instruments.keys()]

    for item in historic_data.raw_data:
        qn = item["qualified_name"]
        qn = _clean_qn(qn)
        item["qualified_name"] = qn
        if is_raw_spot(item):
            spot_list.append(item)

        elif is_raw_future(item):
            futures_list.append(item)

        elif is_listed_params_bundle(item):
            params_list.append(item)
            param_qns.append(qn)

    params_pf: dict[int, dict[str, Any]] = {}
    futures_pf: dict[int, dict[str, Any]] = {}
    ts_to_qn_sets: dict[int, set[str]] = {}

    _update_with_prefetched_items(params_list, params_pf, ts_to_qn_sets)
    _update_with_prefetched_items(futures_list, futures_pf, ts_to_qn_sets)

    retrieve_time = time.time() - start_time
    logging.info(f"Retrieval time took {round(retrieve_time)}s")

    # todo: will need to migrate to using a tick cache when we start smoothing in live mode as it allows for
    #  persisting of calc outputs and inputs
    # Two spot data caches are used:
    # 1. Historical cache: Stores validated daily timestamps for both target and reference assets
    # 2. Latest tick cache: Stores recent ticks for the target asset only, without timestamp validation
    # This allows for both historical analysis and real-time price updates
    spot_data_caches = [
        # one cache for the history
        HistoricalTickCache(
            time_window=SPOT_DATA_HISTORICAL_TIMEDELTA,
            prefetched_ticks=spot_list,
            prefetched_instruments=spot_instruments,
            predicate_fn=(
                functools.partial(
                    _spot_predicate_fn,
                    exchanges=spot_exchanges,
                    base_assets=[
                        data_set.target_asset,
                        data_set.reference_asset,
                    ],
                    check_timestamp=True,
                )
            ),
            copy_new_data=True,
        ),
        # one cache for the latest ticks
        HistoricalTickCache(
            time_window=LATEST_TICK_TIME_WINDOW,
            prefetched_ticks=spot_list,
            prefetched_instruments=spot_instruments,
            predicate_fn=functools.partial(
                _spot_predicate_fn,
                exchanges=spot_exchanges,
                base_assets=[data_set.target_asset],
                check_timestamp=False,
            ),
            copy_new_data=True,
        ),
    ]
    for cache in spot_data_caches:
        cache.on_clean_up()

    params_cache = DerivedDataCache(
        static_qualified_names=set(param_qns),
        prefetched_datapoints=params_pf,
        predicate_fn=functools.partial(
            _params_predicate_fn,
            models=data_set.models,
            reference_asset=data_set.reference_asset,
            reference_exchange=data_set.reference_exchange,
        ),
        store_by_timestamp=True,
        copy_new_data=True,
        cleanup_secs=RUN_CLEANUP_FNS_AFTER_SECS,
    )
    params_cache.on_clean_up()

    futures_cache = DerivedDataCache(
        prefetched_datapoints=futures_pf,
        predicate_fn=functools.partial(
            _futures_predicate_fn,
            target_asset=data_set.target_asset,
            target_exchange=data_set.target_exchange,
        ),
        store_by_timestamp=True,
        copy_new_data=True,
        cleanup_secs=RUN_CLEANUP_FNS_AFTER_SECS,
    )
    futures_cache.on_clean_up()

    # todo: dont forget to Add calculated_data reference to shared dict to allow updating lookback_df inside
    #  _put_output when smoothing is enabled in the live system

    return WaitForSetCalculationCache(
        name=f"SyntheticPriceCalcStream_{get_data_set_name(data_set)}",
        caches=[
            params_cache,
            futures_cache,
            *spot_data_caches,
        ],
        set_to_wait_for={*param_qns},
        timestamp_to_qfn_map=ts_to_qn_sets,
        data_sets=[data_set],
        freq="live",
        debug=False,
        s3_details=None,  # safe as debug if off in live
        # live data is unversioned. versioned listed + constant tenor params have already been pre-loaded
        version="",
    )


def _load_data_sets_from_ssm() -> list[str]:
    def _list_merge(params: list[list[dict[str, Any]]]) -> list[dict[str, Any]]:
        output = []
        for param in params:
            output.extend(param)
        return output

    params = utils_aws.load_multi_ssm_params_by_path(
        SSM_DATA_SET_CONFIG_PATH, _list_merge
    )
    return [orjson.dumps(p).decode("utf-8") for p in params]


if __name__ == "__main__":
    frequency_seconds = int(os.environ.get("FREQUENCY_SECONDS", 20))
    agent = Agent("SyntheticPriceCalc", endpoint=BLOCKSTREAM_ENDPOINT)
    scheduled_version: str = os.environ.get("SCHEDULED_VERSION", "")
    tick_data_stream = agent.channel(
        "tickData",
        auto_flush_s=None,
        seconds_per_checkpoint=None,
        output_mode="multiple",
    )
    data_sets = _load_data_sets_from_ssm()
    while True:
        try:
            scaling_cache = AutoScalingCacheManager(
                target_jobs=data_sets,
                create_cache_fn=functools.partial(
                    _build_caches, scheduled_version=scheduled_version
                ),
            )

            consumer = Consumer(
                consumer_name="SyntheticPriceCalcStream",
                input_data_stream=tick_data_stream,
                output_data_stream=tick_data_stream,
                mode="wait-for-set",
                frequency_ns=int(frequency_seconds * 1e9),
                caches=scaling_cache,
                prep_data=prep_data,
                clean_up_interval_secs=300,
                process_chunk_helper=process_data,  # type: ignore
                # todo: pass in _put_output as a partial function with shared lookback
                #  cache. Clean lookback from within function as well.
                put_output=_put_output,  # type: ignore
                metric_seconds=METRIC_S,
                allowed_calc_lag_metric_stages=[
                    "start_lag",
                    "start_calc_lag",
                    "prep_data_lag",
                    "process_data_lag",
                    "put_output_lag",
                    "total_lag",
                ],
                copy_data_on_calc=True,
            )
            loop = asyncio.get_event_loop()
            loop.run_until_complete(asyncio.gather(consumer.async_run()))

        except Exception:
            logging.exception("Run failed")
            cancel_all_tasks()
            time.sleep(10)  # Time to recover, reduce log spew
