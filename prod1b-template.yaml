AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Block Scholes Model Parameters Calculation SVI/SABR

Resources:
  modelParametersCalcFunction:
    Type: AWS::Serverless::Function # More info about Function Resource: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction
    Properties:
      PackageType: Image
      CodeUri: ./api
      Timeout: 180
      MemorySize: 1769
      Policies:
        - arn:aws:iam::685767522279:policy/LambdaFunctionsDynamoDbPolicy-2
        - arn:aws:iam::685767522279:policy/LambdaFunctionsS3Policy-2
        - arn:aws:iam::685767522279:policy/CalcSSMReadPolicy
      Environment:
        Variables:
          LOG_LEVEL: INFO
          BID_ASK_FALLBACK_ENABLED: "False"
          DELTA_TRIM: "False"
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./api
      DockerTag: latest

Outputs:
  modelParametersCalcFunction:
    Description: "modelParametersCalc Lambda Function ARN"
    Value: !GetAtt modelParametersCalcFunction.Arn
  modelParametersCalcFunctionIamRole:
    Description: "Implicit IAM Role created for modelParametersCalc function"
    Value: !GetAtt modelParametersCalcFunctionRole.Arn
  modelParametersCalcFunctionLogGroupName:
    Description: "modelParametersCalc Lambda Function name"
    Value: !Sub "/aws/lambda/${modelParametersCalcFunction}"
    Export:
      Name: modelParametersCalcFunctionLogGroupName
