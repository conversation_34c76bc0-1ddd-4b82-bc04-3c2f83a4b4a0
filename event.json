{"body": {"version": "1.0.0", "type": "syntheticPriceCalc", "calc": {"args": {"data_sets": [{"target_asset": "ARB", "reference_asset": "ETH", "reference_exchange": "v2composite", "target_exchange": "blockscholes-syn", "models": ["SVI"], "smooth": true}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"absolute": {"start": "2025-06-14T14:54:00Z", "end": "2025-06-14T14:55:00Z"}}, "debug": true, "consistent_read": false}, "output_options": {"type": "csv", "format": "timeseries", "version": "", "do_store": {"s3_bucket": "blockscholes-test-staging", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}