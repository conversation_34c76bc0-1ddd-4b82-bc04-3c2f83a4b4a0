from datetime import datetime, timezone
from typing import Any, Callable, Optional

import pytest
from datagrabber import constants
from utils_general import to_datetime

from catalog import (
    _get_artificial_future,
    _handle_catalog_futures_record,
    _handle_catalog_options_record,
    _handle_catalog_spot_record,
    _handle_catalog_super_set_record,
)
from typings import CatalogCacheSavedInfo, CatalogItemStream


def mock_get_ssm_params() -> dict[str, Any]:
    # Return a value structured as the real function would
    return {
        "BLOCKSCHOLES_INDICES_MAP": {
            "option": {
                "asset_class": "option",
                "index_currency": "USD",
                "components": [
                    {"exchange": "deribit", "base": "BTC", "quote": "BTC"},
                    {"exchange": "bybit", "base": "BTC", "quote": "USD"},
                ],
            },
            "spot": {
                "asset_class": "spot",
                "index_currency": "USD",
                "components": [
                    {"exchange": "kraken", "base": "BTC", "quote": "USD"},
                    {"exchange": "bitstamp", "base": "BTC", "quote": "USD"},
                ],
            },
            "future": {
                "asset_class": "future",
                "index_currency": "USD",
                "components": [
                    {"exchange": "kraken", "base": "BTC", "quote": "USD"},
                    {"exchange": "bybit", "base": "BTC", "quote": "USD"},
                    {"exchange": "okx", "base": "BTC", "quote": "USD"},
                ],
            },
        },
        "V2_COMPOSITE": {"option": ["deribit", "okx"]},
    }


@pytest.fixture(autouse=True)
def auto_mock_get_blockscholes_indices_map(
    monkeypatch: pytest.MonkeyPatch,
) -> None:
    monkeypatch.setattr(
        constants,
        "get_ssm_params",
        mock_get_ssm_params,
    )


@pytest.fixture
def get_test_record() -> Callable[..., dict[str, Any]]:
    def make_record(
        exchange: str,
        base: str,
        quote: str,
        settlement: str,
        name: str,
        expiry: str,
        strike: Optional[float],
        type: Optional[str],
    ):
        record: dict[str, Any] = {
            "exchange": exchange,
            "ticker": base,
            "quoteAsset": quote,
            "name": name,
            "settlementAsset": settlement,
            "expiry": expiry,
            "style": "european",
        }
        if strike:
            record["strike"] = strike
            record["type"] = type
            record["style"] = "european"
        return record

    return make_record


def test__get_artificial_future_empty_cache() -> None:
    cached_val, key = _get_artificial_future(
        {
            "instrument": "BTC_USDC-01JAN24-30000-P",
            "q": "exchange.option.contracts",
        },
        {},
    )

    assert cached_val is None
    assert key == "exchange.BTC_USDC.01JAN24"


def test__get_artificial_future_full_cache() -> None:
    date = datetime.now(tz=timezone.utc)
    cached_val, key = _get_artificial_future(
        {
            "instrument": "BTC_USDC-01JAN24-30000-P",
            "q": "exchange.option.contracts",
        },
        {
            "exchange.BTC_USDC.01JAN24": {
                "expiry": "expiry",
                "added": date,
            }
        },
    )

    assert cached_val == {
        "expiry": "expiry",
        "added": date,
    }
    assert key == "exchange.BTC_USDC.01JAN24"


@pytest.mark.parametrize(
    "exchange, base, quote, name",
    [
        ("exchange1", "BTC", "USD", "BTCUSD"),
        ("exchange2", "BTC", "USDC", "BTC_USDC"),
        ("exchange3", "ETH", "WETH", "ETH-GWEI"),
    ],
)
def test__handle_catalog_spot_record(
    exchange: str, base: str, quote: str, name: str
) -> None:
    assert _handle_catalog_spot_record(
        exchange, {"baseAsset": base, "quoteAsset": quote, "name": name}
    ) == {
        "q": f"{exchange}.spot.contracts",
        "instrument": name,
        "baseAsset": base,
        "quoteAsset": quote,
    }


@pytest.mark.parametrize(
    "exchange, base, quote, settlement, name, expiry",
    [
        (
            "exchange1",
            "BTC",
            "USD",
            "USD",
            "BTCUSD-01JAN24",
            "2024-01-01T08:00:00Z",
        ),
        (
            "exchange2",
            "BTC",
            "USDC",
            "USDC",
            "BTC_USDC-1JAN24",
            "2024-01-01T08:00:00Z",
        ),
        (
            "exchange3",
            "ETH",
            "WETH",
            "USD",
            "ETH-010124",
            "2024-01-01T08:00:00Z",
        ),
        ("exchange3", "ETH", "WETH", "USD", "ETH-USD", "PERPETUAL"),
        (
            "exchange3",
            "ETH",
            "WETH",
            "USD",
            "ETH-USD-PERP",
            "3000-01-01T08:00:00.000Z",
        ),
    ],
)
def test_handle_catalog_future_record(
    exchange: str,
    base: str,
    quote: str,
    settlement: str,
    name: str,
    expiry: str,
) -> None:
    active = False
    try:
        if to_datetime(expiry) > datetime.fromisoformat(
            "2999-01-01T08:00:00.000Z"
        ):
            qn = f"{exchange}.perpetual.contracts"
            active = True

        else:
            qn = f"{exchange}.future.contracts"

    except Exception:
        qn = f"{exchange}.perpetual.contracts"
        active = True

    assert _handle_catalog_futures_record(
        exchange,
        {
            "baseAsset": base,
            "quoteAsset": quote,
            "name": name,
            "settlementAsset": settlement,
            "expiry": expiry,
        },
    ) == {
        "q": qn,
        "instrument": name,
        "baseAsset": base,
        "settlementAsset": settlement,
        "quoteAsset": quote,
        "active": active,
        "expiry": expiry,
    }


def test_handle_catalog_option_record(
    get_test_record: Callable[..., dict[str, Any]]
) -> None:
    record = get_test_record(
        "exchange1",
        "BTC",
        "USD",
        "USD",
        "BTCUSD-01JAN24-strike-p",
        "2024-01-01T08:00:00Z",
        3000,
        "P",
    )
    catalog_item, syn_item, pcp_item = _handle_catalog_options_record(
        record["exchange"],
        record,
        {},
        {
            "bybit_expiry_to_listing": {},
            "blockscholes_output": {},
            "v2composite_output": {},
        },
    )

    assert catalog_item == {
        "q": f"{record['exchange']}.option.contracts",
        "instrument": record["name"],
        "baseAsset": record["ticker"],
        "settlementAsset": record["settlementAsset"],
        "quoteAsset": record["quoteAsset"],
        "expiry": record["expiry"],
        "strike": record["strike"],
        "style": "european",
        "type": record["type"],
    }
    assert pcp_item == {}
    assert syn_item == {}


def test_handle_catalog_option_record_bybit(
    get_test_record: Callable[..., dict[str, Any]]
) -> None:
    record = get_test_record(
        "bybit",
        "BTC",
        "USDC",
        "USD",
        "BTCUSD-01JAN24-strike-p",
        "2024-01-01T08:00:00Z",
        3000,
        "P",
    )
    catalog_item, syn_item, pcp_item = _handle_catalog_options_record(
        record["exchange"],
        record,
        {},
        {
            "bybit_expiry_to_listing": {},
            "v2composite_output": {},
            "blockscholes_output": {},
        },
    )

    expected_catalog_item = {
        "q": f"{record['exchange']}.option.contracts",
        "instrument": record["name"],
        "baseAsset": record["ticker"],
        "settlementAsset": record["settlementAsset"],
        "quoteAsset": record["quoteAsset"],
        "expiry": record["expiry"],
        "strike": record["strike"],
        "style": "european",
        "type": record["type"],
    }
    assert catalog_item == expected_catalog_item
    assert pcp_item == {
        "baseAsset": "BTC",
        "expiry": "2024-01-01T08:00:00Z",
        "instrument": "BTC_USDC_2024-01-01T08:00:00Z-PCP",
        "q": "bybit.future.contracts",
        "quoteAsset": "USDC",
        "settlementAsset": "USD",
    }
    assert syn_item == {
        "baseAsset": "BTC",
        "expiry": "2024-01-01T08:00:00Z",
        "instrument": "BTC_USDC_2024-01-01T08:00:00Z-SYN",
        "q": "bybit.future.contracts",
        "quoteAsset": "USDC",
        "settlementAsset": "USD",
    }


def test_handle_catalog_option_record_bybit_with_contract(
    get_test_record: Callable[..., dict[str, Any]]
) -> None:
    record = get_test_record(
        "bybit",
        "BTC",
        "USDC",
        "USD",
        "BTCUSD-01JAN24-strike-p",
        "2024-01-01T08:00:00Z",
        3000,
        "P",
    )
    record["contractSize"] = 5.0
    catalog_item, syn_item, pcp_item = _handle_catalog_options_record(
        record["exchange"],
        record,
        {},
        {
            "bybit_expiry_to_listing": {},
            "v2composite_output": {},
            "blockscholes_output": {},
        },
    )

    expected_catalog_item = {
        "q": f"{record['exchange']}.option.contracts",
        "instrument": record["name"],
        "baseAsset": record["ticker"],
        "settlementAsset": record["settlementAsset"],
        "quoteAsset": record["quoteAsset"],
        "expiry": record["expiry"],
        "strike": record["strike"],
        "style": "european",
        "type": record["type"],
    }
    if record.get("contractSize") is not None and record["contractSize"] > 0:
        expected_catalog_item["contractSize"] = float(record["contractSize"])
    assert catalog_item == expected_catalog_item
    assert pcp_item == {
        "baseAsset": "BTC",
        "expiry": "2024-01-01T08:00:00Z",
        "instrument": "BTC_USDC_2024-01-01T08:00:00Z-PCP",
        "q": "bybit.future.contracts",
        "quoteAsset": "USDC",
        "settlementAsset": "USD",
    }
    assert syn_item == {
        "baseAsset": "BTC",
        "expiry": "2024-01-01T08:00:00Z",
        "instrument": "BTC_USDC_2024-01-01T08:00:00Z-SYN",
        "q": "bybit.future.contracts",
        "quoteAsset": "USDC",
        "settlementAsset": "USD",
    }


def test_handle_catalog_option_record_v2lyra(
    get_test_record: Callable[..., dict[str, Any]]
) -> None:
    record = get_test_record(
        "v2lyra",
        "BTC",
        "USDC",
        "USDC",
        "BTCUSD-01JAN24-strike-p",
        "2024-01-01T08:00:00Z",
        3000,
        "P",
    )
    catalog_item, syn_item, pcp_item = _handle_catalog_options_record(
        record["exchange"],
        record,
        {},
        {
            "bybit_expiry_to_listing": {},
            "blockscholes_output": {},
            "v2composite_output": {},
        },
    )

    expected_catalog_item = {
        "q": f"{record['exchange']}.option.contracts",
        "instrument": record["name"],
        "baseAsset": record["ticker"],
        "settlementAsset": record["settlementAsset"],
        "quoteAsset": record["quoteAsset"],
        "expiry": record["expiry"],
        "strike": record["strike"],
        "style": "european",
        "type": record["type"],
    }
    assert catalog_item == expected_catalog_item
    assert pcp_item == {
        "baseAsset": "BTC",
        "expiry": "2024-01-01T08:00:00Z",
        "instrument": "BTC_USDC_2024-01-01T08:00:00Z-PCP",
        "q": "v2lyra.future.contracts",
        "quoteAsset": "USDC",
        "settlementAsset": "USDC",
    }
    assert syn_item == {}


@pytest.mark.parametrize(
    "item, blockscholes_cached_outputs, v2composite_cached_outputs, result",
    [
        ({"q": "exch1.future.contracts"}, {}, {}, []),
        (
            {
                "q": "okx.future.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "USDC",
                "settlementAsset": "USDC",
            },
            {},
            {},
            [],
        ),
        (
            {
                "q": "okx.future.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "USD",
                "settlementAsset": "USD",
                "expiry": "2024-01-01T08:00:00Z",
            },
            {},
            {},
            [
                {
                    "baseAsset": "BTC",
                    "expiry": "2024-01-01T08:00:00Z",
                    "instrument": "BTC_USD_2024-01-01T08:00:00Z",
                    "q": "blockscholes.future.contracts",
                    "quoteAsset": "USD",
                    "settlementAsset": "USD",
                }
            ],
        ),
        (
            {
                "q": "deribit.option.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "BTC",
                "settlementAsset": "USD",
                "expiry": "2024-01-01T08:00:00Z",
                "strike": 4000,
                "type": "P",
            },
            {},
            {},
            [
                {
                    "baseAsset": "BTC",
                    "expiry": "2024-01-01T08:00:00Z",
                    "instrument": "BTC_USD_2024-01-01T08:00:00Z_4000_P",
                    "q": "blockscholes.option.contracts",
                    "quoteAsset": "USD",
                    "settlementAsset": "USD",
                    "strike": 4000,
                    "type": "P",
                },
                {
                    "baseAsset": "BTC",
                    "expiry": "2024-01-01T08:00:00Z",
                    "instrument": "BTC_BTC_2024-01-01T08:00:00Z_4000_P",
                    "q": "v2composite.option.contracts",
                    "quoteAsset": "BTC",
                    "settlementAsset": "USD",
                    "strike": 4000,
                    "type": "P",
                },
            ],
        ),
        (
            {
                "q": "okx.option.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "BTC",
                "settlementAsset": "USD",
                "expiry": "2024-01-01T08:00:00Z",
                "strike": 4000,
                "type": "P",
            },
            {},
            {},
            [
                {
                    "baseAsset": "BTC",
                    "expiry": "2024-01-01T08:00:00Z",
                    "instrument": "BTC_BTC_2024-01-01T08:00:00Z_4000_P",
                    "q": "v2composite.option.contracts",
                    "quoteAsset": "BTC",
                    "settlementAsset": "USD",
                    "strike": 4000,
                    "type": "P",
                },
            ],
        ),
        (
            {
                "q": "bybit.option.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "USD",
                "settlementAsset": "USD",
                "expiry": "2024-01-01T08:00:00Z",
                "strike": 4000,
                "type": "P",
            },
            {},
            {},
            [
                {
                    "baseAsset": "BTC",
                    "expiry": "2024-01-01T08:00:00Z",
                    "instrument": "BTC_USD_2024-01-01T08:00:00Z_4000_P",
                    "q": "blockscholes.option.contracts",
                    "quoteAsset": "USD",
                    "settlementAsset": "USD",
                    "strike": 4000,
                    "type": "P",
                },
            ],
        ),
        (
            {
                "q": "bybit.option.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "USD",
                "settlementAsset": "USD",
                "expiry": "2024-01-01T08:00:00Z",
                "strike": 4000,
                "type": "P",
            },
            {"BTC_USD_2024-01-01T08:00:00Z_4000_P": {}},
            {},
            [],  # Already published once so its empty
        ),
        (
            {
                "q": "deribit.option.contracts",
                "baseAsset": "BTC",
                "quoteAsset": "BTC",
                "settlementAsset": "USD",
                "expiry": "2024-01-01T08:00:00Z",
                "strike": 4000,
                "type": "P",
            },
            {"BTC_USD_2024-01-01T08:00:00Z_4000_P": {}},
            {"BTC_BTC_2024-01-01T08:00:00Z_4000_P": {}},
            [],
        ),
    ],
)
def test_create_superset_record(
    item: CatalogItemStream,
    blockscholes_cached_outputs: dict[str, CatalogCacheSavedInfo],
    v2composite_cached_outputs: dict[str, CatalogCacheSavedInfo],
    result,
) -> None:
    catalog_item = _handle_catalog_super_set_record(
        item,
        {
            "blockscholes_output": blockscholes_cached_outputs,
            "v2composite_output": v2composite_cached_outputs,
            "bybit_expiry_to_listing": {},
        },
    )
    assert catalog_item == result
