from collections.abc import Callable
from pathlib import Path
from typing import Any, cast

import pandas as pd
import pytest

from synthetic_price_calc.config import LOOKBACK_COLUMN_NAME
from synthetic_price_calc.smoothing.config import SMOOTH_COLUMN_NAME
from synthetic_price_calc.typings import (
    DataSetSnap,
    SmoothConfig,
    Snapshot,
    SpotFromManager,
)

from .helpers import load_json, set_snapshot_path_to_parent


@pytest.fixture
def snapshot(snapshot: Any) -> Any:
    return set_snapshot_path_to_parent(snapshot)


@pytest.fixture
def assemble_process_chunk_helper_snapshot() -> Callable[[str], Snapshot]:
    def _assemble_snapshot(test_case: str) -> Snapshot:
        data_dir = Path(__file__).parent / "fixtures" / test_case

        other_fields_path = data_dir / "base_snapshot.json"
        other_fields = load_json(other_fields_path)

        target_spot_path = data_dir / "spot" / "target_asset.json"

        target_spot_data = cast(
            list[SpotFromManager], load_json(target_spot_path)
        )

        reference_spot_path = data_dir / "spot" / "reference_asset.json"
        reference_spot_data = cast(
            list[SpotFromManager], load_json(reference_spot_path)
        )

        # Load data_snap
        data_snap_raw: list[dict[str, Any]] = load_json(
            data_dir / "data_snap.json"
        )
        data_snap = [DataSetSnap(**data) for data in data_snap_raw]

        snapshot_data = Snapshot(
            timestamp=other_fields["timestamp"],
            target_asset=other_fields["target_asset"],
            target_exchange=other_fields["target_exchange"],
            reference_asset=other_fields["reference_asset"],
            reference_exchange=other_fields["reference_exchange"],
            target_asset_spot_data=target_spot_data,
            reference_asset_spot_data=reference_spot_data,
            data_snap=data_snap,
        )

        return snapshot_data

    return _assemble_snapshot


@pytest.fixture(params=["process_chunk_helper"])
def process_chunk_helper_snapshot(
    request: pytest.FixtureRequest,
    assemble_process_chunk_helper_snapshot: Callable[[str], Snapshot],
) -> Snapshot:
    return assemble_process_chunk_helper_snapshot(request.param)


@pytest.fixture(params=["apply_smoothing_if_needed"])
def apply_smoothing_if_needed_snapshot(
    request: pytest.FixtureRequest,
) -> list[dict[str, Any]]:
    # fmt: off
    # ARB_USD_2025-06-27T08:00:00Z-SYN
    return [
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342200000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.699921749358027, 'expiry': 0.021213850837138507, 'forward': 0.3024174267280267, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3018376009828846, 'svi_a': 0.0028815335614643077, 'svi_b': 0.05705435816300979, 'svi_m': -0.011550680858094995, 'svi_rho': -0.1486444612481191, 'svi_sigma': 0.132860941664789, 'timestamp': 1750342200000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342260000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.6984806470204621, 'expiry': 0.021211948249619483, 'forward': 0.302516027330082, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3018401215360561, 'svi_a': 0.0027788454087312123, 'svi_b': 0.057238114502147865, 'svi_m': -0.010427477983078607, 'svi_rho': -0.1468418620117035, 'svi_sigma': 0.13337765539909366, 'timestamp': 1750342260000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342320000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.6986787346896352, 'expiry': 0.021210045662100458, 'forward': 0.3026889327605195, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3022258056643644, 'svi_a': 0.002729487445167328, 'svi_b': 0.05729624407339627, 'svi_m': -0.009868077354057148, 'svi_rho': -0.14700016982140712, 'svi_sigma': 0.1341551455371237, 'timestamp': 1750342320000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342380000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.699918989167562, 'expiry': 0.02120814307458143, 'forward': 0.3026363364199777, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3021121497812615, 'svi_a': 0.0027597629651031435, 'svi_b': 0.05722549875949397, 'svi_m': -0.010211863051867357, 'svi_rho': -0.1475261043983774, 'svi_sigma': 0.13444831906565324, 'timestamp': 1750342380000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342440000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.7001786085707163, 'expiry': 0.021206240487062405, 'forward': 0.3025817723502151, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3019194122389673, 'svi_a': 0.002784179925746253, 'svi_b': 0.057191533461559106, 'svi_m': -0.010483548937026969, 'svi_rho': -0.1476617559368793, 'svi_sigma': 0.13423907211920752, 'timestamp': 1750342440000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342500000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.7001199566533041, 'expiry': 0.021204337899543377, 'forward': 0.3024654675864478, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3017427867386103, 'svi_a': 0.002812598683344516, 'svi_b': 0.05712576067834366, 'svi_m': -0.010861898694339489, 'svi_rho': -0.14692077554872757, 'svi_sigma': 0.13386463834112752, 'timestamp': 1750342500000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342560000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.700144754579428, 'expiry': 0.021202435312024353, 'forward': 0.3026909598801064, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3022270307948776, 'svi_a': 0.0027921070127292806, 'svi_b': 0.05705851747952285, 'svi_m': -0.010626462411377106, 'svi_rho': -0.1487157670884621, 'svi_sigma': 0.13438166286362713, 'timestamp': 1750342560000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342620000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.6997098278871073, 'expiry': 0.02120053272450533, 'forward': 0.302509921689175, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.30166716528239, 'svi_a': 0.0028173706097778055, 'svi_b': 0.05714583302433325, 'svi_m': -0.01082974258231638, 'svi_rho': -0.14836225500434153, 'svi_sigma': 0.1335011887046291, 'timestamp': 1750342620000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342680000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.6971377933011746, 'expiry': 0.0211986301369863, 'forward': 0.3024177933563569, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3014895367302908, 'svi_a': 0.0026256760509641834, 'svi_b': 0.05796252944389065, 'svi_m': -0.008919088357149956, 'svi_rho': -0.1323723321317941, 'svi_sigma': 0.13332824196544876, 'timestamp': 1750342680000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "ARB", "target_exchange": "blockscholes-syn", 'timestamp': 1750342740000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.6963957834618513, 'expiry': 0.021196727549467276, 'forward': 0.3023219696761612, 'qualified_name': 'blockscholes-syn.option.ARB.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 0.3010585500418422, 'svi_a': 0.0026003859000126627, 'svi_b': 0.05804974101427004, 'svi_m': -0.008648538422148697, 'svi_rho': -0.13107792848041924, 'svi_sigma': 0.1331418134248097, 'timestamp': 1750342740000000000, 'underlying_index': 'ARB_USD_2025-06-27T08:00:00Z-SYN'}]},

        # sui should remain unsmothed
        {"target_asset": "SUI", "target_exchange": "blockscholes-syn", 'timestamp': 1750342680000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.8, 'expiry': 0.0211986301369863, 'forward': 6.0, 'qualified_name': 'blockscholes-syn.option.SUI.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 3.5, 'svi_a': 0.0026256760509641834, 'svi_b': 0.05796252944389065, 'svi_m': -0.008919088357149956, 'svi_rho': -0.1323723321317941, 'svi_sigma': 0.13332824196544876, 'timestamp': 1750342680000000000, 'underlying_index': 'SUI_USD_2025-06-27T08:00:00Z-SYN'}]},
        {"target_asset": "SUI", "target_exchange": "blockscholes-syn", 'timestamp': 1750342740000000000, 'model': 'SVI', 'params': [{'atm_vol': 0.81, 'expiry': 0.021196727549467276, 'forward': 6.0, 'qualified_name': 'blockscholes-syn.option.SUI.SVI.2025-06-27T08:00:00Z.1m.params', 'spot': 3.5, 'svi_a': 0.0026003859000126627, 'svi_b': 0.05804974101427004, 'svi_m': -0.008648538422148697, 'svi_rho': -0.13107792848041924, 'svi_sigma': 0.1331418134248097, 'timestamp': 1750342740000000000, 'underlying_index': 'SUI_USD_2025-06-27T08:00:00Z-SYN'}]}
    ]
    # fmt: on


@pytest.fixture
def test_dataframe() -> pd.DataFrame:
    return pd.DataFrame(
        {
            "qualified_name": ["test_qn"] * 6,
            "field_a": [1.0, 2.0, 3.0, 4.0, 5.0, 6.0],
            "field_b": [10.0, 20.0, 30.0, 40.0, 50.0, 60.0],
            LOOKBACK_COLUMN_NAME: [True, True, False, False, True, False],
            SMOOTH_COLUMN_NAME: [False, False, False, False, False, None],
        }
    )


@pytest.fixture
def smooth_config() -> SmoothConfig:
    return {
        "span_smooth": 2,
        "adjust": True,
        "fields_to_smooth": ["field_a", "field_b"],
    }
