from collections.abc import Generator
from enum import Enum, IntEnum
from typing import Literal, cast
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import eth_pydantic_types
import pytest
import utils_general
from block_stream.typings import CatalogData
from pytest_mock import MockFixture
from utils_oracle import (
    ConfigLoader,
    ConfigModel,
    EnumDict,
    Feed,
    FeedConfig,
    FeedIndex,
    FeedParameters,
    FeedState,
    Network,
    OtherParamType,
    get_feed_key,
)

from feed_indexer.catalog_filter_manager import CatalogFilterManager
from feed_indexer.database_handler import DatabaseHandler
from feed_indexer.feed_cleanup_manager import FeedCleanupManager
from feed_indexer.feed_indexer import FeedIndexer
from feed_indexer.sqs import FeedIndexerSQSSender
from feed_indexer.typings import (
    AssetTypeToCurrencyConstantTenors,
    AuxDetails,
    ChainCatalogFilters,
    ChainToAuxDetails,
    FeedIndexerCatalogFilter,
    FeedIndexerInternalFeeds,
    TargetConfig,
    TargetFeed,
    Targets,
)

_BASE_ASSETS = ["BTC", "ETH"]


@pytest.fixture(autouse=True)
def mock_chain_id_to_network_name() -> Generator[None, None, None]:
    class MockNetwork(IntEnum):
        MOCK_CHAIN_1 = 1
        MOCK_CHAIN_2 = 2
        MOCK_CHAIN_3 = 3

    with patch(
        "utils_oracle.Network",
        new=MockNetwork,
    ):
        yield


@pytest.fixture
def mock_catalog_data() -> CatalogData:
    return CatalogData(
        q="deribit.option.contracts",
        instrument="BTC-27JUN25-100000-C",
        baseAsset="BTC",
        quoteAsset="BTC",
        expiry="2025-06-27T08:00:00Z",
        type="C",
        strike=100_000,
    )


@pytest.fixture
def mock_constant_tenors() -> list[str]:
    return [
        utils_general.convert_tenor_days_to_constant_maturity(t)
        for t in [4 / 24, 8 / 24, 12 / 24]
    ]


@pytest.fixture
def mock_asset_type_to_constant_tenor(
    mock_constant_tenors: list[str],
) -> AssetTypeToCurrencyConstantTenors:

    asset_type_to_constant_tenors: AssetTypeToCurrencyConstantTenors = {}
    for b_a in _BASE_ASSETS:
        asset_type_to_constant_tenors["option"] = {b_a: mock_constant_tenors}
        asset_type_to_constant_tenors["future"] = {b_a: mock_constant_tenors}
    return asset_type_to_constant_tenors


@pytest.fixture
def mock_target_config() -> Targets:

    from utils_oracle import Network  # will use mocked network

    exchanges = ["deribit", "bybit", "blockscholes"]

    target_config: Targets = {}
    for n in Network:
        targets: list[TargetFeed] = []
        for a_t in ["future", "option"]:
            targets.append(
                TargetFeed(
                    asset_class=cast(Literal["future", "option"], a_t),
                    exchange=exchanges,
                    base_asset=_BASE_ASSETS,
                    constant_tenors=["7d", "14d", "30d"],
                )
            )

        # used model_construct to skip validation of Network used in TargetConfig
        target_config[n.name] = TargetConfig.model_construct(
            targets=targets,
            id=n,
            decimals=9,
            version=1,
            enable=True,
        )
    return target_config


@pytest.fixture
def mock_chain_asset_type_to_constant_tenors() -> (
    dict[Network, AssetTypeToCurrencyConstantTenors]
):
    from utils_oracle import Network  # will use mocked Network

    asset_type_to_constant_tenors: AssetTypeToCurrencyConstantTenors = {}
    for b_a in _BASE_ASSETS:
        asset_type_to_constant_tenors["option"] = {b_a: ["7d", "14d", "30d"]}
        asset_type_to_constant_tenors["future"] = {b_a: ["7d", "14d", "30d"]}

    return {
        Network(1): asset_type_to_constant_tenors,
        Network(2): asset_type_to_constant_tenors,
        Network(3): asset_type_to_constant_tenors,
    }


@pytest.fixture
def mock_all_chain_catalog_filter(
    mock_constant_tenors: list[str],
) -> ChainCatalogFilters:
    filter = FeedIndexerCatalogFilter(
        exchanges=["deribit", "bybit", "blockscholes"],
        asset_class="option",
        base_assets=_BASE_ASSETS,
        constant_tenors=mock_constant_tenors,
    )
    from utils_oracle import Network  # will use mocked Network

    return {Network(1): [filter], Network(2): [filter], Network(3): [filter]}


@pytest.fixture
def chain_id_to_aux_details(
    mock_constant_tenors: list[str],
    mock_asset_type_to_constant_tenor: AssetTypeToCurrencyConstantTenors,
) -> ChainToAuxDetails:
    from utils_oracle import Network  # will use mocked Network

    return {
        chain: AuxDetails(
            max_feed_index=2_000,
            decimals=9,
            version=1,
        )
        for chain in [Network(1), Network(2), Network(3)]
    }


@pytest.fixture
def mock_time() -> Generator[Mock, None, None]:
    with patch("time.time", return_value=1234567890) as mock:
        yield mock


FEED_AND_CATALOG_CONFIG_MOCK = ConfigModel.model_construct(
    feed_type_to_config={
        "future": FeedConfig.model_construct(
            id=1,
            is_derived=False,
            wsapi_feed="mark.px",
            ordered_enum_params=[
                "ExpiryTypeEnum",
                "ExchangeEnum",
                "BaseAssetEnum",
            ],
            ordered_other_params=[
                OtherParamType.model_construct(name="expiry", type="int64"),
            ],
        ),
        "params": FeedConfig.model_construct(
            id=2,
            is_derived=False,
            wsapi_feed="model.params",
            ordered_enum_params=[
                "SVIParamEnum",
                "ExpiryTypeEnum",
                "ExchangeEnum",
                "BaseAssetEnum",
            ],
            ordered_other_params=[
                OtherParamType.model_construct(name="expiry", type="int64"),
            ],
        ),
        "spot": FeedConfig.model_construct(
            id=3,
            is_derived=False,
            wsapi_feed="index.px",
            ordered_enum_params=["ExchangeEnum", "BaseAssetEnum"],
            ordered_other_params=[],
        ),
        "interest_rate": FeedConfig.model_construct(
            id=4,
            is_derived=False,
            wsapi_feed="interest.rate",
            ordered_enum_params=[
                "ExpiryTypeEnum",
                "ExchangeEnum",
                "BaseAssetEnum",
            ],
            ordered_other_params=[
                OtherParamType.model_construct(name="expiry", type="int64"),
            ],
        ),
        "settlement_price": FeedConfig.model_construct(
            id=5,
            is_derived=False,
            wsapi_feed="settlement.px",
            ordered_enum_params=[
                "ExpiryTypeEnum",
                "ExchangeEnum",
                "BaseAssetEnum",
            ],
            ordered_other_params=[
                OtherParamType.model_construct(name="expiry", type="int64"),
            ],
        ),
        "domestic_rate": FeedConfig.model_construct(
            id=6,
            is_derived=False,
            wsapi_feed="interest.rate",
            ordered_enum_params=[],
            ordered_other_params=[],
        ),
        "implied_volatility": FeedConfig.model_construct(
            id=7,
            is_derived=True,
            wsapi_feed=None,
            ordered_enum_params=[
                "IVLevelTypeEnum",
                "ExpiryTypeEnum",
                "ExchangeEnum",
                "BaseAssetEnum",
            ],
            ordered_other_params=[
                OtherParamType.model_construct(name="expiry", type="int64"),
                OtherParamType.model_construct(
                    name="ivLevelValue", type="int64"
                ),
            ],
            input_feeds=[
                "future",
                "params",
                "params",
                "params",
                "params",
                "params",
            ],
        ),
        "option_mark_price": FeedConfig.model_construct(
            id=8,
            is_derived=False,
            wsapi_feed="mark.px",
            ordered_enum_params=[
                "OptionTypeEnum",
                "IVLevelTypeEnum",
                "ExpiryTypeEnum",
                "BaseAssetEnum",
            ],
            ordered_other_params=[
                OtherParamType.model_construct(name="expiry", type="int64"),
                OtherParamType.model_construct(
                    name="ivLevelValue", type="int64"
                ),
            ],
            input_feeds=[
                "spot",
                "domestic_rate",
                "future",
                "params",
                "params",
                "params",
                "params",
                "params",
            ],
        ),
    },
    related_catalog_asset_lookup={
        "qualified_name": {
            "option": [
                "spot",
                "future",
                "params",
                "interest_rate",
                "domestic_rate",
                "settlement_price",
            ],
            "future": ["future", "interest_rate"],
            "spot": ["spot"],
        },
    },
)


FEED_ID_TO_TYPE_MOCK: dict[int, str] = {
    config.id: feed_type
    for feed_type, config in FEED_AND_CATALOG_CONFIG_MOCK.feed_type_to_config.items()
}


@pytest.fixture
def enum_fixture() -> dict[str, Enum]:
    def create_enums(enum_dict: EnumDict) -> dict[str, Enum]:
        if enum_dict is None:
            raise (NotImplementedError("Please Sepcify Enums"))
        enums = {}
        for name, values in enum_dict.items():
            enums[name] = Enum(name, values)
        return enums

    return create_enums(
        {
            "ExpiryTypeEnum": {"TIMESTAMP": 0, "TENOR": 1},
            "ExchangeEnum": {
                "BLOCKSCHOLES": 0,
                "DERIBIT": 1,
                "BYBIT": 2,
                "OKX": 3,
            },
            "BaseAssetEnum": {"BTC": 1, "ETH": 2},
            "OptionTypeEnum": {"CALL": 0, "PUT": 1},
            "IVLevelTypeEnum": {"STRIKE": 0, "MONEYNESS": 1},
            "SVIParamEnum": {
                "SVI_A": 0,
                "SVI_B": 1,
                "SVI_RHO": 2,
                "SVI_M": 3,
                "SVI_SIGMA": 4,
            },
        }
    )


@pytest.fixture(autouse=True)
def mock_config_loader(
    enum_fixture: dict[str, Enum],
) -> Generator[None, None, None]:
    with patch.object(ConfigLoader, "_feed_parameter_enums", enum_fixture):
        with patch.object(
            ConfigLoader,
            "_feed_definitions",
            FEED_AND_CATALOG_CONFIG_MOCK,
        ):
            with patch.object(
                ConfigLoader, "_feed_id_to_type", FEED_ID_TO_TYPE_MOCK
            ):
                yield


@pytest.fixture
def feeds() -> list[Feed]:
    return [
        Feed(
            id=2,
            parameters=FeedParameters(
                enumerable=[0, 1, 1, 1],
                other=eth_pydantic_types.HexBytes(
                    b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00"
                ),
            ),
        ),
        Feed(
            id=2,
            parameters=FeedParameters(
                enumerable=[1, 1, 1, 1],
                other=eth_pydantic_types.HexBytes(
                    b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00"
                ),
            ),
        ),
        Feed(
            id=2,
            parameters=FeedParameters(
                enumerable=[0, 1, 0, 1],
                other=eth_pydantic_types.HexBytes(
                    b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00"
                ),
            ),
        ),
        Feed(
            id=2,
            parameters=FeedParameters(
                enumerable=[1, 1, 1, 2],
                other=eth_pydantic_types.HexBytes(
                    b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00"
                ),
            ),
        ),
        Feed(
            id=2,
            parameters=FeedParameters(
                enumerable=[0, 1, 1, 2],
                other=eth_pydantic_types.HexBytes(
                    b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00"
                ),
            ),
        ),
    ]


@pytest.fixture
def internal_state(
    feeds: list[Feed], chain_id_to_aux_details: ChainToAuxDetails
) -> FeedIndexerInternalFeeds:
    """Fixture that creates a state-aware internal state from feed instances with unique indices per state."""

    from utils_oracle import FeedState, Network  # will use mocked Network

    internal_state: FeedIndexerInternalFeeds = utils_general.nested_dict()

    # Set up empty dictionaries for each chain and each FeedState
    for chain_id in range(1, 4):
        network = Network(chain_id)
        for state in FeedState:
            internal_state[network][state] = {}

    all_states = list(FeedState.__members__.values())

    # Assign each feed to a distinct state for each chain with unique indices
    for chain_id in range(1, 4):
        network = Network(chain_id)
        current_index = 1  # Ensure unique indices within each chain
        for i, feed_instance in enumerate(feeds):
            feed_key = get_feed_key(feed_instance)
            # Assign states sequentially; if more feeds than states, cycle through
            chosen_state = all_states[i % len(all_states)]

            feed_index = FeedIndex(
                index=current_index,
                chain_id=chain_id,
                feed=feed_instance,
                version=chain_id_to_aux_details[network].version,
            )
            internal_state[network][chosen_state][feed_key] = feed_index
            current_index += 1

    return internal_state


@pytest.fixture
def feed_indexer_with_internal_state(
    internal_state: FeedIndexerInternalFeeds,
    chain_id_to_aux_details: ChainToAuxDetails,
    catalog_filter_manager: CatalogFilterManager,
    mock_all_chain_catalog_filter: ChainCatalogFilters,
    mock_chain_asset_type_to_constant_tenors: dict[
        Network, AssetTypeToCurrencyConstantTenors
    ],
) -> FeedIndexer:
    return FeedIndexer(
        preloaded_internal_state=internal_state,
        chain_aux_details=chain_id_to_aux_details,
        catalog_filter_manager=catalog_filter_manager,
    )


@pytest.fixture
def sqs_sender_mock(mocker: MockFixture) -> FeedIndexerSQSSender:
    sender = FeedIndexerSQSSender(
        queue_name="mock_queue",
        deadletter_queue_name="mock_queue_dlq",
    )
    mocker.patch.object(sender, "send_message", mocker.AsyncMock())
    return sender


@pytest.fixture
def database_handler_mock(mocker: MockFixture, feeds: list[Feed]) -> MagicMock:
    mock = mocker.Mock(spec=DatabaseHandler)

    # Mock asynchronous methods
    mock.get_feed_indices_by_state_for_chain_id = mocker.AsyncMock()
    mock.write_feed_indices = mocker.AsyncMock()

    # when called with a chain_id, the FeedIndices returned will have the same chain_id
    # version and states are not used, but are required to match the signature of the
    # get_feed_indices_by_state_for_chain_id method
    async def mock_get_feed_indices_by_state_for_chain_id(
        chain_id: Network, version: int, states: list[FeedState]
    ) -> list[FeedIndex]:
        return [
            FeedIndex(chain_id=chain_id, feed=feed, index=1, version=1)
            for feed in feeds
        ]

    mock.get_feed_indices_by_state_for_chain_id.side_effect = (
        mock_get_feed_indices_by_state_for_chain_id
    )

    return cast(MagicMock, mock)


@pytest.fixture
def catalog_filter_manager(
    mock_target_config: Targets,
) -> CatalogFilterManager:
    """Create a FeedCleanupManager instance for testing."""
    return CatalogFilterManager(config=mock_target_config)


@pytest.fixture
def feed_cleanup_manager(
    catalog_filter_manager: CatalogFilterManager,
) -> FeedCleanupManager:
    """Create a FeedCleanupManager instance for testing."""
    return FeedCleanupManager(catalog_filter_manager=catalog_filter_manager)
