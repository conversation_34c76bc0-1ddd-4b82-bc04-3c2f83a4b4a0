from datetime import UTC, datetime, timedelta
from typing import Any

import pytest
from datagrabber import CatalogAssetType
from utils_general import to_iso

from feed_indexer.typings import ExpiryRangeConfig, FeedIndexerCatalogFilter


class TestExpiryRangeConfig:
    @pytest.mark.parametrize(
        "gte, lte, should_raise",
        [
            (None, None, False),
            (10, None, False),
            (None, 5, False),
            (10, 5, True),  # gte > lte, should raise
            (5, 10, False),
        ],
    )
    def test_validate_days_comparison(
        self, gte: float | None, lte: float | None, should_raise: bool
    ) -> None:
        """Test the validation of gte and lte comparison."""
        if should_raise:
            with pytest.raises(ValueError, match="must be less than"):
                ExpiryRangeConfig(gte=gte, lte=lte)
        else:
            config = ExpiryRangeConfig(gte=gte, lte=lte)
            assert config.gte == gte
            assert config.lte == lte

    @pytest.mark.parametrize(
        "gte, lte, tenor_days, expected",
        [
            (None, None, 10, True),  # No constraints
            (5, None, 10, True),  # Above minimum
            (5, None, 3, False),  # Below minimum
            (None, 15, 10, True),  # Below maximum
            (None, 15, 20, False),  # Above maximum
            (5, 15, 10, True),  # Within range
            (5, 15, 3, False),  # Below range
            (5, 15, 20, False),  # Above range
        ],
    )
    def test_is_tenor_within_range_with_tenor(
        self,
        gte: float | None,
        lte: float | None,
        tenor_days: int,
        expected: bool,
    ) -> None:
        """Test is_tenor_within_range with direct tenor value."""
        config = ExpiryRangeConfig(gte=gte, lte=lte)
        result = config.is_tenor_within_range(tenor_days=tenor_days)
        assert result == expected

    @pytest.mark.parametrize(
        "gte, lte, days_diff, expected",
        [
            (None, None, 10, True),  # No constraints
            (5, None, 10, True),  # Above minimum
            (5, None, 3, False),  # Below minimum
            (None, 15, 10, True),  # Below maximum
            (None, 15, 20, False),  # Above maximum
            (5, 15, 10, True),  # Within range
            (5, 15, 3, False),  # Below range
            (5, 15, 20, False),  # Above range
        ],
    )
    def test_is_tenor_within_range_with_dates(
        self,
        gte: float | None,
        lte: float | None,
        days_diff: int,
        expected: bool,
    ) -> None:
        """Test is_tenor_within_range with listing and expiry dates."""
        config = ExpiryRangeConfig(gte=gte, lte=lte)
        listing_date = datetime(2023, 1, 1, tzinfo=UTC)
        expiry_date = listing_date + timedelta(days=days_diff)
        result = config.is_tenor_within_range(
            listing_date=listing_date, expiry_date=expiry_date
        )
        assert result == expected

    def test_is_tenor_within_range_missing_args(self) -> None:
        """Test is_tenor_within_range raises error when required args are missing."""
        config = ExpiryRangeConfig()

        # No arguments provided
        with pytest.raises(
            ValueError,
            match="Both listing_date and expiry_date must be provided",
        ):
            config.is_tenor_within_range()

        # Only listing_date provided
        with pytest.raises(
            ValueError,
            match="Both listing_date and expiry_date must be provided",
        ):
            config.is_tenor_within_range(listing_date=datetime.now(UTC))

        # Only expiry_date provided
        with pytest.raises(
            ValueError,
            match="Both listing_date and expiry_date must be provided",
        ):
            config.is_tenor_within_range(expiry_date=datetime.now(UTC))


class TestFeedIndexerCatalogFilter:
    @pytest.mark.parametrize(
        "asset_class, listing_filter, constant_tenors, should_raise",
        [
            ("option", ExpiryRangeConfig(gte=5, lte=10), ["7d", "14d"], False),
            ("future", ExpiryRangeConfig(gte=5, lte=10), ["7d", "14d"], False),
            (
                "option-equity",
                ExpiryRangeConfig(gte=5, lte=10),
                ["7d", "14d"],
                False,
            ),
            (
                "future-equity",
                ExpiryRangeConfig(gte=5, lte=10),
                ["7d", "14d"],
                False,
            ),
            (
                "spot",
                ExpiryRangeConfig(gte=5, lte=10),
                None,
                True,
            ),  # Should raise for listing_filter
            (
                "spot",
                None,
                ["7d", "14d"],
                True,
            ),  # Should raise for constant_tenors
            ("spot", None, None, False),  # Valid for spot
        ],
    )
    def test_validate_option_future_only_fields(
        self,
        asset_class: CatalogAssetType,
        listing_filter: ExpiryRangeConfig,
        constant_tenors: list[str] | None,
        should_raise: bool,
    ) -> None:
        """Test validation of option/future-only fields."""
        if should_raise:
            with pytest.raises(ValueError):
                FeedIndexerCatalogFilter(
                    exchanges=["deribit"],
                    base_assets=["BTC"],
                    asset_class=asset_class,
                    listing_filter=listing_filter,
                    constant_tenors=constant_tenors,
                )
        else:
            filter_obj = FeedIndexerCatalogFilter(
                exchanges=["deribit"],
                base_assets=["BTC"],
                asset_class=asset_class,
                listing_filter=listing_filter,
                constant_tenors=constant_tenors,
            )
            assert filter_obj.asset_class == asset_class
            assert filter_obj.listing_filter == listing_filter
            assert filter_obj.constant_tenors == constant_tenors

    @pytest.mark.parametrize(
        "catalog_data, expected",
        [
            # Valid option with matching exchange, asset class, base asset
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "7d",
                },
                True,
            ),
            # Exchange mismatch
            (
                {
                    "q": "bybit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "7d",
                },
                False,
            ),
            # Asset class mismatch
            (
                {
                    "q": "deribit.future.contracts",
                    "baseAsset": "BTC",
                    "expiry": "7d",
                },
                False,
            ),
            # Base asset mismatch
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "ETH",
                    "expiry": "7d",
                },
                False,
            ),
            # Tenor not in constant_tenors
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "30d",
                },
                False,
            ),
            # Invalid qualified name format
            (
                {
                    "q": "deribit",
                    "baseAsset": "BTC",
                    "expiry": "7d",
                },
                False,
            ),
        ],
    )
    def test_accept_catalog_item_basic(
        self, catalog_data: Any, expected: bool
    ) -> None:
        """Test accept_catalog_item method with basic scenarios."""
        filter_obj = FeedIndexerCatalogFilter(
            exchanges=["deribit"],
            base_assets=["BTC"],
            asset_class="option",
            constant_tenors=["7d", "14d"],
        )
        assert filter_obj.accept_catalog_item(catalog_data) == expected

    @pytest.mark.parametrize(
        "catalog_data, quote_assets, expected",
        [
            # Quote asset matches
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "USD",
                    "expiry": "7d",
                },
                ["USD"],
                True,
            ),
            # Quote asset mismatch
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "EUR",
                    "expiry": "7d",
                },
                ["USD"],
                False,
            ),
            # Quote asset not specified in filter
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "USD",
                    "expiry": "7d",
                },
                [],
                True,
            ),
        ],
    )
    def test_accept_catalog_item_quote_assets(
        self, catalog_data: Any, quote_assets: list[str], expected: bool
    ) -> None:
        """Test accept_catalog_item method with quote assets."""
        filter_obj = FeedIndexerCatalogFilter(
            exchanges=["deribit"],
            base_assets=["BTC"],
            quote_assets=quote_assets,
            asset_class="option",
            constant_tenors=["7d", "14d"],
        )
        assert filter_obj.accept_catalog_item(catalog_data) == expected

    @pytest.mark.parametrize(
        "days_diff, expected",
        [(7, True), (2, False), (15, False)],
    )
    def test_accept_catalog_item_with_listing_filter(
        self, days_diff: int, expected: bool
    ) -> None:
        """Test accept_catalog_item method with listing filter."""
        now = datetime.now(UTC)
        listing_date = now

        # Create expiry date based on parameter
        expiry_date = listing_date + timedelta(days=days_diff)

        listing_filter = ExpiryRangeConfig(gte=5, lte=10)
        filter_obj = FeedIndexerCatalogFilter(
            exchanges=["deribit"],
            base_assets=["BTC"],
            asset_class="option",
            listing_filter=listing_filter,
        )

        catalog_data = {
            "q": "deribit.option.contracts",
            "baseAsset": "BTC",
            "expiry": to_iso(expiry_date),
            "listing": to_iso(listing_date),
        }
        assert filter_obj.accept_catalog_item(catalog_data) is expected

    @pytest.mark.parametrize(
        "data, qn_tokens, exchanges, asset_class, base_assets, expected",
        [
            # All match - should return True
            (
                {"baseAsset": "BTC"},
                ["deribit", "option", "contracts"],
                ["deribit"],
                "option",
                ["BTC"],
                True,
            ),
            # Exchange mismatch - should return False
            (
                {"baseAsset": "BTC"},
                ["bybit", "option", "contracts"],
                ["deribit"],
                "option",
                ["BTC"],
                False,
            ),
            # Asset class mismatch - should return False
            (
                {"baseAsset": "BTC"},
                ["deribit", "future", "contracts"],
                ["deribit"],
                "option",
                ["BTC"],
                False,
            ),
            # Base asset mismatch - should return False
            (
                {"baseAsset": "ETH"},
                ["deribit", "option", "contracts"],
                ["deribit"],
                "option",
                ["BTC"],
                False,
            ),
        ],
    )
    def test_check_basic_info(
        self,
        data: dict[str, Any],
        qn_tokens: list[str],
        exchanges: list[str],
        asset_class: str,
        base_assets: list[str],
        expected: bool,
    ) -> None:
        """Test the _check_basic_info method."""
        filter_obj = FeedIndexerCatalogFilter(
            exchanges=exchanges,
            asset_class=asset_class,
            base_assets=base_assets,
        )
        result = filter_obj._check_basic_info(data, qn_tokens)
        assert result == expected

    @pytest.mark.parametrize(
        "data, quote_assets, expected",
        [
            # No quote assets in filter - should return True
            ({"quoteAsset": "USD"}, [], True),
            # Quote asset matches - should return True
            ({"quoteAsset": "USD"}, ["USD", "EUR"], True),
            # Quote asset mismatch - should return False
            ({"quoteAsset": "GBP"}, ["USD", "EUR"], False),
            # No quote asset in data but filter has quote assets - should return False
            ({}, ["USD", "EUR"], False),
        ],
    )
    def test_check_quote_asset(
        self, data: dict[str, Any], quote_assets: list[str], expected: bool
    ) -> None:
        """Test the _check_quote_asset method."""
        filter_obj = FeedIndexerCatalogFilter(
            exchanges=["deribit"],
            asset_class="option",
            base_assets=["BTC"],
            quote_assets=quote_assets,
        )
        result = filter_obj._check_quote_asset(data)
        assert result == expected

    @pytest.mark.parametrize(
        "data, constant_tenors, expected",
        [
            # No expiry in data - should return True
            ({}, ["7d", "14d"], True),
            # ISO date expiry - should return True
            ({"expiry": "2023-01-01T00:00:00Z"}, ["7d", "14d"], True),
            # Tenor matches constant_tenors - should return True
            ({"expiry": "7d"}, ["7d", "14d"], True),
            # Tenor doesn't match constant_tenors - should return False
            ({"expiry": "30d"}, ["7d", "14d"], False),
            # No constant_tenors specified - should return True
            ({"expiry": "30d"}, None, True),
        ],
    )
    def test_check_constant_tenor(
        self,
        data: dict[str, Any],
        constant_tenors: list[str] | None,
        expected: bool,
    ) -> None:
        """Test the _check_constant_tenor method."""
        filter_obj = FeedIndexerCatalogFilter(
            exchanges=["deribit"],
            asset_class="option",
            base_assets=["BTC"],
            constant_tenors=constant_tenors,
        )
        result = filter_obj._check_constant_tenor(data)
        assert result == expected

    @pytest.mark.parametrize(
        "data, listing_filter, expected",
        [
            # No listing_filter - should return True
            (
                {
                    "expiry": "2023-01-10T00:00:00Z",
                    "listing": "2023-01-01T00:00:00Z",
                },
                None,
                True,
            ),
            # Non-ISO expiry - should return True (not checked by this method)
            ({"expiry": "7d"}, ExpiryRangeConfig(gte=5, lte=15), True),
            # Tenor within range - should return True
            (
                {
                    "expiry": "2023-01-10T00:00:00Z",
                    "listing": "2023-01-01T00:00:00Z",
                },
                ExpiryRangeConfig(gte=5, lte=15),
                True,
            ),
            # Tenor below range - should return False
            (
                {
                    "expiry": "2023-01-03T00:00:00Z",
                    "listing": "2023-01-01T00:00:00Z",
                },
                ExpiryRangeConfig(gte=5, lte=15),
                False,
            ),
            # Tenor above range - should return False
            (
                {
                    "expiry": "2023-01-20T00:00:00Z",
                    "listing": "2023-01-01T00:00:00Z",
                },
                ExpiryRangeConfig(gte=5, lte=15),
                False,
            ),
        ],
    )
    def test_check_listing_filter(
        self,
        data: dict[str, Any],
        listing_filter: ExpiryRangeConfig | None,
        expected: bool,
    ) -> None:
        """Test the _check_listing_filter method."""
        filter_obj = FeedIndexerCatalogFilter(
            exchanges=["deribit"],
            asset_class="option",
            base_assets=["BTC"],
            listing_filter=listing_filter,
        )
        result = filter_obj._check_listing_filter(data)
        assert result == expected

    def test_check_listing_filter_missing_listing(self) -> None:
        """Test _check_listing_filter raises error when listing date is missing."""
        filter_obj = FeedIndexerCatalogFilter(
            exchanges=["deribit"],
            asset_class="option",
            base_assets=["BTC"],
            listing_filter=ExpiryRangeConfig(gte=5, lte=15),
        )

        with pytest.raises(ValueError, match="must have a listing date"):
            filter_obj._check_listing_filter({"expiry": "2023-01-10T00:00:00Z"})

    @pytest.mark.parametrize(
        "data, filter_params, expected",
        [
            # All criteria match - should return True
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "USD",
                    "expiry": "7d",
                },
                {
                    "exchanges": ["deribit"],
                    "asset_class": "option",
                    "base_assets": ["BTC"],
                    "quote_assets": ["USD"],
                    "constant_tenors": ["7d", "14d"],
                },
                True,
            ),
            # Invalid qualified name format - should return False
            (
                {"q": "deribit", "baseAsset": "BTC"},
                {
                    "exchanges": ["deribit"],
                    "asset_class": "option",
                    "base_assets": ["BTC"],
                },
                False,
            ),
            # Exchange mismatch - should return False
            (
                {"q": "bybit.option.contracts", "baseAsset": "BTC"},
                {
                    "exchanges": ["deribit"],
                    "asset_class": "option",
                    "base_assets": ["BTC"],
                },
                False,
            ),
            # Asset class mismatch - should return False
            (
                {"q": "deribit.future.contracts", "baseAsset": "BTC"},
                {
                    "exchanges": ["deribit"],
                    "asset_class": "option",
                    "base_assets": ["BTC"],
                },
                False,
            ),
            # Base asset mismatch - should return False
            (
                {"q": "deribit.option.contracts", "baseAsset": "ETH"},
                {
                    "exchanges": ["deribit"],
                    "asset_class": "option",
                    "base_assets": ["BTC"],
                },
                False,
            ),
            # Quote asset mismatch - should return False
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "quoteAsset": "EUR",
                },
                {
                    "exchanges": ["deribit"],
                    "asset_class": "option",
                    "base_assets": ["BTC"],
                    "quote_assets": ["USD"],
                },
                False,
            ),
            # Tenor mismatch - should return False
            (
                {
                    "q": "deribit.option.contracts",
                    "baseAsset": "BTC",
                    "expiry": "30d",
                },
                {
                    "exchanges": ["deribit"],
                    "asset_class": "option",
                    "base_assets": ["BTC"],
                    "constant_tenors": ["7d", "14d"],
                },
                False,
            ),
        ],
    )
    def test_accept_catalog_item_comprehensive(
        self,
        data: dict[str, Any],
        filter_params: dict[str, Any],
        expected: bool,
    ) -> None:
        """Test the accept_catalog_item method with various scenarios."""
        filter_obj = FeedIndexerCatalogFilter(**filter_params)
        result = filter_obj.accept_catalog_item(data)
        assert result == expected
