from datetime import datetime, timed<PERSON><PERSON>
from unittest.mock import patch

import eth_pydantic_types
import pytest
import utils_general
from hexbytes import HexBytes
from utils_oracle import (
    Feed,
    FeedIndex,
    FeedParameters,
    Network,
)
from utils_oracle.db.table_models import FeedState

from feed_indexer.feed_cleanup_manager import FeedCleanupManager
from feed_indexer.typings import (
    AssetTypeToCurrencyConstantTenors,
    ChainToAuxDetails,
    ExpiryRangeConfig,
    FeedIndexerCatalogFilter,
    FeedIndexerInternalFeeds,
    FeedKeyToIndexMap,
)


@pytest.mark.asyncio
class TestFeedCleanupManager:
    async def test_get_expired_feeds_for_chain(
        self,
        chain_id_to_aux_details: ChainToAuxDetails,
        feed_cleanup_manager: FeedCleanupManager,
    ) -> None:
        def create_test_data(chain_id: int) -> dict[HexBytes, FeedIndex]:
            # created from "deribit.option.contracts_BTC_2024-06-14T08:00:00.000Z"
            return {
                # fmt: off
                # interest_rate
                HexBytes("0x982fca9baf83e1d41e642e492d4a21550f25cef155edcea79d8b4c6318ba941f"): FeedIndex(chain_id=chain_id, index=1, version=1, feed=Feed(id=4,parameters=FeedParameters(enumerable=[0, 1, 1],other=eth_pydantic_types.HexBytes(b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00")))),
                # option params
                HexBytes("0x5a389f00f86f02f49877cf9cc501d7af760cdce5d3b04511f2c3787e82bea004"): FeedIndex(chain_id=chain_id, index=2, version=1, feed=Feed(id=2,parameters=FeedParameters(enumerable=[0, 0, 1, 1],other=eth_pydantic_types.HexBytes(b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00")))),
                HexBytes("0x0bd1cb04f411a01b6601d8af9f74e0375ef4966347c21a22dd3d2e9aa6321416"): FeedIndex(chain_id=chain_id, index=3, version=1, feed=Feed(id=2,parameters=FeedParameters(enumerable=[1, 0, 1, 1],other=eth_pydantic_types.HexBytes(b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00")))),
                HexBytes("0xbc3c95b6c796a81e4ec8a149d0534db4231b7eccffe58d0d47f9b9cd3c8df257"): FeedIndex(chain_id=chain_id, index=4, version=1, feed=Feed(id=2,parameters=FeedParameters(enumerable=[2, 0, 1, 1],other=eth_pydantic_types.HexBytes(b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00")))),
                HexBytes("0xaf846e2f9a4d9a7d9b6a859327e410ec5419e623da810eb89979ade69a81a416"): FeedIndex(chain_id=chain_id, index=5, version=1, feed=Feed(id=2,parameters=FeedParameters(enumerable=[3, 0, 1, 1],other=eth_pydantic_types.HexBytes(b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00")))),
                HexBytes("0xc968f29d599180d42d792cfa22d7e15d88f36fa43984a4bd5a03ac164e22ab83"): FeedIndex(chain_id=chain_id, index=6, version=1, feed=Feed(id=2,parameters=FeedParameters(enumerable=[4, 0, 1, 1],other=eth_pydantic_types.HexBytes(b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00")))),
                # settlement_price
                HexBytes("0xc071e032832c5bda26fda1893d46e4242191e8b7dd29192e9bb209654674d50b"): FeedIndex(chain_id=chain_id, index=7, version=1, feed=Feed(id=5,parameters=FeedParameters(enumerable=[0, 1, 1],other=eth_pydantic_types.HexBytes(b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00")))),
                # spot
                HexBytes("0x5d80301d7b33b2be4c4535d0d8c19b202fd03c5a3182ca31a96e8ed41aba7376"): FeedIndex(chain_id=chain_id, index=8, version=1, feed=Feed(id=3,parameters=FeedParameters(enumerable=[1, 1], other=eth_pydantic_types.HexBytes(b"")),)),
                # future
                HexBytes("0x9fdbc7bb0ba0dcbb497efa9db5e93ef0e45354061865f7dd186467b4fdf0b8af"): FeedIndex(chain_id=chain_id, index=9, version=1, feed=Feed(id=1,parameters=FeedParameters(enumerable=[0, 1, 1],other=eth_pydantic_types.HexBytes(b"\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x17\xd8\xd0\n/\x15\x00\x00")))),
                # fmt: on
            }

        def _create_other_keys(chain_id: int) -> FeedKeyToIndexMap:
            return {
                HexBytes(
                    "0x9fdbc7bb0ba0dcbb47efa9db5e9000000000000000000000000fffffffffff1"
                ): FeedIndex(
                    chain_id=chain_id,
                    index=99,
                    version=9,
                    feed=Feed(
                        id=1,
                        parameters=FeedParameters(
                            enumerable=[0, 1, 1],
                            other=eth_pydantic_types.HexBytes(b""),
                        ),
                    ),
                )
            }

        preloaded_internal_state: FeedIndexerInternalFeeds = (
            utils_general.nested_dict()
        )

        non_confirmed_states = [
            FeedState.INITIALISED_ADD,
            FeedState.INITIALISED_REMOVE,
            FeedState.PENDING_ADD,
            FeedState.PENDING_REMOVE,
        ]

        for chain_id in chain_id_to_aux_details.keys():
            preloaded_internal_state[chain_id][FeedState.CONFIRMED_ADD] = (
                create_test_data(chain_id)
            )
            for _state in non_confirmed_states:
                preloaded_internal_state[chain_id][_state] = _create_other_keys(
                    chain_id.value
                )

        for chain_id in chain_id_to_aux_details.keys():
            confirmed_feeds = preloaded_internal_state[chain_id][
                FeedState.CONFIRMED_ADD
            ]
            expired_chain_feeds = (
                await feed_cleanup_manager.get_feeds_to_remove_for_chain(
                    chain_id=chain_id,
                    chain_decimals=chain_id_to_aux_details[chain_id].decimals,
                    confirmed_feeds=confirmed_feeds,
                )
            )
            # 5 SVI params, 1 future feed, 1 settlement feed, 1 interest rate - spot is excluded
            assert len(expired_chain_feeds) == 8

    async def test_get_expired_feeds_for_chain_no_expired_feeds(
        self,
        chain_id_to_aux_details: ChainToAuxDetails,
        feed_cleanup_manager: FeedCleanupManager,
    ) -> None:
        for chain_id, details in chain_id_to_aux_details.items():
            assert not await feed_cleanup_manager.get_feeds_to_remove_for_chain(
                chain_id, details.decimals, {}
            )

    @pytest.mark.parametrize(
        "tenor_info, expected_result",
        [
            # option contains 720m feed
            (
                {"future": {"BTC": ["720m"]}, "option": {"BTC": ["720m"]}},
                True,
            ),
            # futures doesn't contain 720m, but options does
            (
                {"future": {"BTC": []}, "option": {"BTC": ["720m", "1080m"]}},
                True,
            ),
            # option does not contain 720m feed
            (
                {
                    "future": {"BTC": ["1d", "2d"]},
                    "option": {"BTC": ["1d", "2d"]},
                },
                False,
            ),
        ],
    )
    def test_is_config_defined_constant_tenor_feed_option(
        self,
        chain_id_to_aux_details: ChainToAuxDetails,
        feed_cleanup_manager: FeedCleanupManager,
        tenor_info: AssetTypeToCurrencyConstantTenors,
        expected_result: bool,
    ) -> None:

        # 720 min expiry
        param_feed = Feed(
            id=4,
            parameters=FeedParameters(
                enumerable=[1, 1, 1],
                other=eth_pydantic_types.HexBytes(
                    "0x000000000000000000000000000000000000000000000000000000000014e707"
                ),
            ),
        )
        chain = next(iter(chain_id_to_aux_details.keys()))
        decoded_param_feed = param_feed.get_decoded_feed()

        with patch.object(
            feed_cleanup_manager._catalog_filter_manager,
            "get_constant_tenors_for_chain",
            return_value=tenor_info,
        ):
            result = feed_cleanup_manager._is_valid_constant_tenor(
                decoded_feed=decoded_param_feed,
                chain_decimals=chain_id_to_aux_details[chain].decimals,
                chain_id=chain,
            )

            assert result == expected_result

    @pytest.mark.parametrize(
        "tenor_info, expected_result",
        [
            # option and future contains 720m feed
            (
                {"future": {"BTC": ["720m"]}, "option": {"BTC": ["720m"]}},
                True,
            ),
            # futures doesn't contain 720m, but options does
            (
                {"future": {"BTC": []}, "option": {"BTC": ["720m", "1080m"]}},
                True,
            ),
            # option doesn't contain 720m, but future does
            (
                {"option": {"BTC": []}, "future": {"BTC": ["720m", "1080m"]}},
                True,
            ),
            # neither future nor option contains 720m feed
            (
                {
                    "future": {"BTC": ["1d", "2d"]},
                    "option": {"BTC": ["1d", "2d"]},
                },
                False,
            ),
        ],
    )
    def test_is_config_defined_constant_tenor_feed_interest_rate(
        self,
        chain_id_to_aux_details: ChainToAuxDetails,
        feed_cleanup_manager: FeedCleanupManager,
        tenor_info: AssetTypeToCurrencyConstantTenors,
        expected_result: bool,
    ) -> None:

        # 720 min expiry
        interest_rate = Feed(
            id=4,
            parameters=FeedParameters(
                enumerable=[1, 1, 1],
                other=eth_pydantic_types.HexBytes(
                    "0x000000000000000000000000000000000000000000000000000000000014e707"
                ),
            ),
        )
        chain = next(iter(chain_id_to_aux_details.keys()))
        decoded_interest_rate = interest_rate.get_decoded_feed()

        with patch.object(
            feed_cleanup_manager._catalog_filter_manager,
            "get_constant_tenors_for_chain",
            return_value=tenor_info,
        ):
            result = feed_cleanup_manager._is_valid_constant_tenor(
                decoded_feed=decoded_interest_rate,
                chain_decimals=chain_id_to_aux_details[chain].decimals,
                chain_id=chain,
            )

            assert result == expected_result

    @pytest.mark.parametrize(
        "feed_string, expected",
        [
            # "params_SVIParamEnum:SVI_SIGMA+ExpiryTypeEnum:TIMESTAMP+ExchangeEnum:BYBIT+BaseAssetEnum:ETH_expiry:2120-05-22T08:00:00"
            (
                '{"id": 2, "parameters": {"other": "0x00000000000000000000000000000000000000000000000041dc7bf51b7c6000", "enumerable": [4, 0, 2, 2]}}',
                False,
            ),
            # # "params_SVIParamEnum:SVI_SIGMA+ExpiryTypeEnum:TIMESTAMP+ExchangeEnum:BYBIT+BaseAssetEnum:ETH_expiry:1970-01-01T08:00:00"
            (
                '{"id": 2, "parameters": {"other": "0x000000000000000000000000000000000000000000000000000016eb550c6000", "enumerable": [4, 0, 2, 2]}}',
                True,
            ),
            # # spot_ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_
            (
                '{"id": 3, "parameters": {"other": "0x", "enumerable": [1, 1]}}',
                False,
            ),
            # future_ExpiryTypeEnum:TENOR+ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_expiry:720m
            (
                '{"id": 1, "parameters": {"other": "0x000000000000000000000000000000000000000000000000000000000014e707", "enumerable": [1, 1, 1]}}',
                False,
            ),
            # "params_SVIParamEnum:SVI_RHO+ExpiryTypeEnum:TENOR+ExchangeEnum:BYBIT+BaseAssetEnum:BTC_expiry:480m"
            (
                '{"id": 2, "parameters": {"other": "0x00000000000000000000000000000000000000000000000000000000000def5a", "enumerable": [2, 1, 2, 1]}}',
                True,
            ),
            # "future_ExpiryTypeEnum:TENOR+ExchangeEnum:DERIBIT+BaseAssetEnum:BTC_expiry:30d" - contains constant tenor rounding which the FI should
            # consistently identify as 30d
            (
                '{"id":1,"parameters":{"other":"0x0000000000000000000000000000000000000000000000000000000004e625a4","enumerable":[1,1,1]}}',
                False,
            ),
        ],
    )
    # fmt: on
    async def test__should_remove_feed(
        self,
        feed_string: str,
        expected: bool,
        chain_id_to_aux_details: ChainToAuxDetails,
        feed_cleanup_manager: FeedCleanupManager,
    ) -> None:
        feed = Feed.model_validate_json(feed_string)
        decoded_feed = feed.get_decoded_feed()

        chain_asset_type_to_constant_tenors: dict[
            Network, AssetTypeToCurrencyConstantTenors
        ] = {}
        for chain_id in chain_id_to_aux_details.keys():
            chain_asset_type_to_constant_tenors[chain_id] = {
                "future": {"BTC": ["720m", "1d"]},
                "option": {"BTC": ["2d", "30d"]},
            }

        with patch.object(
            feed_cleanup_manager._catalog_filter_manager,
            "get_constant_tenors_for_chain",
            return_value={
                "future": {"BTC": ["720m", "1d"]},
                "option": {"BTC": ["2d", "30d"]},
            },
        ):
            for chain_id in chain_id_to_aux_details.keys():
                assert (
                    await feed_cleanup_manager._should_remove_feed(
                        decoded_feed=decoded_feed,
                        chain_id=chain_id,
                        chain_decimals=9,
                    )
                    == expected
                )

    @pytest.mark.parametrize(
        "gte, lte, listing_ts, expected",
        # all tested with "params_SVIParamEnum:SVI_SIGMA+ExpiryTypeEnum:TIMESTAMP+ExchangeEnum:BYBIT+BaseAssetEnum:ETH_expiry:2120-05-22T08:00:00"
        [
            (
                10,
                100,
                # set time.time_ns() to 50 days before the feeds expiry
                utils_general.to_datetime("2120-05-22T08:00:00Z")
                - timedelta(days=50),
                False,  # should not remove
            ),
            (
                10,
                100,
                utils_general.to_datetime("2120-05-22T08:00:00Z")
                - timedelta(days=110),  # 110 days is larger than our lte filter
                True,
            ),
            (
                10,
                100,
                utils_general.to_datetime("2120-05-22T08:00:00Z")
                - timedelta(days=9),  # 9 days is smaller than the gte filter
                True,
            ),
        ],
    )
    # fmt: on
    async def test_should_remove_feed_with_expiry_range_filter(
        self,
        gte: float | None,
        lte: float | None,
        listing_ts: datetime,
        expected: bool,
        chain_id_to_aux_details: ChainToAuxDetails,
        feed_cleanup_manager: FeedCleanupManager,
        monkeypatch: pytest.MonkeyPatch,
    ) -> None:

        from utils_oracle import Network

        # "params_SVIParamEnum:SVI_SIGMA+ExpiryTypeEnum:TIMESTAMP+ExchangeEnum:BYBIT+BaseAssetEnum:ETH_expiry:2120-05-22T08:00:00"
        feed_string = '{"id": 2, "parameters": {"other": "0x00000000000000000000000000000000000000000000000041dc7bf51b7c6000", "enumerable": [4, 0, 2, 2]}}'

        feed = Feed.model_validate_json(feed_string)
        decoded_feed = feed.get_decoded_feed()

        chain_asset_type_to_constant_tenors: dict[
            Network, AssetTypeToCurrencyConstantTenors
        ] = {}
        for chain_id in chain_id_to_aux_details.keys():
            chain_asset_type_to_constant_tenors[chain_id] = {
                "future": {"BTC": ["720m", "1d"]},
                "option": {"BTC": ["2d", "30d"]},
            }

        mock_filters = {
            chain_id: [
                FeedIndexerCatalogFilter(
                    exchanges=["deribit", "bybit", "blockscholes"],
                    base_assets=["BTC", "ETH"],
                    quote_assets=[],
                    constant_tenors=["7d", "14d", "30d"],
                    asset_class="future",
                    listing_filter=ExpiryRangeConfig(gte=gte, lte=lte),
                ),
                FeedIndexerCatalogFilter(
                    exchanges=["deribit", "bybit", "blockscholes"],
                    base_assets=["BTC", "ETH"],
                    quote_assets=[],
                    constant_tenors=["7d", "14d", "30d"],
                    asset_class="option",
                    listing_filter=ExpiryRangeConfig(gte=gte, lte=lte),
                ),
            ]
            for chain_id in chain_id_to_aux_details.keys()
        }

        # add some listing filters
        feed_cleanup_manager._catalog_filter_manager._catalog_filters = {
            chain_id: [
                FeedIndexerCatalogFilter(
                    exchanges=["deribit", "bybit", "blockscholes"],
                    base_assets=["BTC", "ETH"],
                    quote_assets=[],
                    constant_tenors=["7d", "14d", "30d"],
                    asset_class="future",
                    listing_filter=ExpiryRangeConfig(gte=gte, lte=lte),
                ),
                FeedIndexerCatalogFilter(
                    exchanges=["deribit", "bybit", "blockscholes"],
                    base_assets=["BTC", "ETH"],
                    quote_assets=[],
                    constant_tenors=["7d", "14d", "30d"],
                    asset_class="option",
                    listing_filter=ExpiryRangeConfig(gte=gte, lte=lte),
                ),
            ]
            for chain_id in chain_id_to_aux_details.keys()
        }

        # add items to the catalog_map
        feed_cleanup_manager._catalog_filter_manager._accepted_catalog_items = {
            chain_id: {
                "bybit_option_ETH_USD_2120-05-22T07:00:00Z": {
                    "qualified_name": "deribit.option.contracts_BTC_2024-06-14T08:00:00.000Z",
                    "baseAsset": "ETH",
                    "listing": utils_general.to_iso(listing_ts),
                    "expiry": "2120-05-22T08:00:00Z",
                },
            }
            for chain_id in chain_id_to_aux_details.keys()
        }
        for chain_id in chain_id_to_aux_details.keys():
            should_remove = await feed_cleanup_manager._should_remove_feed(
                decoded_feed=decoded_feed,
                chain_id=chain_id,
                chain_decimals=9,
            )
            assert should_remove == expected
