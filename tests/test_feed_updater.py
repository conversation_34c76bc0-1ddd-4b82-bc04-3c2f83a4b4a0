import copy as cp
from unittest.mock import AsyncMock, MagicMock, patch

import eth_pydantic_types
import pytest
from utils_oracle import Feed, FeedIndex, FeedParameters, FeedState, Network

from oracle_feed_updater.feed_updater import FeedUpdater
from oracle_feed_updater.utils import get_feeds_by_chain


@pytest.fixture
def feed_updater() -> FeedUpdater:
    return FeedUpdater(
        data_storage=MagicMock(),
        ws_manager=MagicMock(),
        sqs_producer=AsyncMock(),
        data_updaters=[MagicMock(), MagicMock()],
    )


@pytest.fixture
def sample_feed_indices() -> list[FeedIndex]:
    return [
        FeedIndex.construct(
            version=1,
            chain_id=1,
            feed=Feed.construct(
                id=3,
                parameters=FeedParameters(
                    enumerable=[0, 2], other=eth_pydantic_types.HexBytes(b"")
                ),
            ),
            index=0,
        ),
        FeedIndex.construct(
            version=1,
            chain_id=1,
            feed=Feed.construct(
                id=3,
                parameters=FeedParameters(
                    enumerable=[0, 1], other=eth_pydantic_types.HexBytes(b"")
                ),
            ),
            index=0,
        ),
    ]


@pytest.mark.asyncio
async def test_update_data_on_chain(
    feed_updater: FeedUpdater, sample_feed_indices: list[FeedIndex]
) -> None:
    feeds = sample_feed_indices
    new_feed = cp.deepcopy(sample_feed_indices[0])
    new_feed.chain_id = Network.ARBITRUM_SEPOLIA
    # testing partial updates for chain ARBITRUM_SEPOLIA
    feeds.append(new_feed)

    full_chain_values: tuple[list[int], list[int]] = ([1, 2], [3, 4])
    default_feed_data = (100, 99999)
    feed_updater._data_storage.get_chain_data.return_value = full_chain_values  # type: ignore
    feed_updater._data_storage.get_feed_data.return_value = default_feed_data  # type: ignore

    with patch(
        "oracle_feed_updater.feed_updater.feeds_new_values",
        new_callable=MagicMock,
    ) as mock_metric:

        await feed_updater._update_data_on_chain(feeds)

        feed_updater._data_storage.update_metrics.assert_called_once()  # type: ignore

        feed_updater._sqs_producer.batch_update_data.assert_called_once_with(  # type: ignore
            {Network.MAINNET: full_chain_values}, {}
        )
        feed_updater._sqs_producer.batch_update_data.update_data_for_feeds(  # type: ignore
            {Network.ARBITRUM_SEPOLIA: [(new_feed, default_feed_data)]}
        )

        mock_metric.add.assert_called()


@pytest.mark.asyncio
async def test_add_new_feeds(
    feed_updater: FeedUpdater, sample_feed_indices: list[FeedIndex]
) -> None:

    feed_updater._data_storage.add_new_feeds.return_value = (  # type: ignore
        sample_feed_indices,
        sample_feed_indices,
    )

    with patch(
        "oracle_feed_updater.feed_updater.update_feeds_state",
        new_callable=AsyncMock,
    ) as mock_db_update:

        await feed_updater.add_new_feeds(sample_feed_indices)

        feed_updater._sqs_producer.add_feeds.assert_called_once()  # type: ignore
        feed_updater._ws_manager.add_new_feeds.assert_called_once()  # type: ignore
        for su in feed_updater._schedule_updaters:
            su.add_feeds.assert_called_once()  # type: ignore

        mock_db_update.assert_called_once_with(
            sample_feed_indices, FeedState.PENDING_ADD
        )


@pytest.mark.asyncio
async def test_remove_feeds(
    feed_updater: FeedUpdater, sample_feed_indices: list[FeedIndex]
) -> None:

    feed_updater._data_storage.remove_feeds.return_value = (  # type: ignore
        sample_feed_indices,
        sample_feed_indices[:1],
    )

    with patch(
        "oracle_feed_updater.feed_updater.update_feeds_state",
        new_callable=AsyncMock,
    ) as mock_db_update:

        await feed_updater.remove_feeds(sample_feed_indices)

        feed_to_remove_by_chain = get_feeds_by_chain(sample_feed_indices)
        feed_updater._sqs_producer.remove_feeds.assert_called_once_with(  # type: ignore
            feed_to_remove_by_chain
        )
        feed_updater._ws_manager.remove_feeds.assert_called_once_with(  # type: ignore
            sample_feed_indices[:1]
        )
        for su in feed_updater._schedule_updaters:
            su.remove_feeds.assert_called_once()  # type: ignore

        mock_db_update.assert_called_once_with(
            sample_feed_indices, FeedState.PENDING_REMOVE
        )
