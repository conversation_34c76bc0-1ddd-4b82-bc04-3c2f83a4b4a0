from datetime import UTC, datetime, timedelta
from typing import ClassVar

import pytest
from pydantic import ValidationError

from telegram_bot.configs.bot_config import (
    Botfig,
)
from telegram_bot.constants import (
    CURRENCY_TO_MAX_PRICING_CUTOFF,
    DEFAULT_MAX_PRICING_CUTOFF,
)
from telegram_bot.handlers.utils import is_beyond_max_tenor_cutoff
from telegram_bot.typings import (
    ExchangeToCurrencies,
    WhitelistedUser,
)


class TestBotfig:
    AllowedCurrencies: ClassVar[list[str]] = ["BTC", "ETH", "SOL", "XRP"]

    @pytest.fixture
    def valid_botfig(
        self,
        funding_rate_config: ExchangeToCurrencies,
        spot_config: ExchangeToCurrencies,
    ) -> Botfig:
        return Botfig(
            funding_rate_config=funding_rate_config,
            spot_config=spot_config,
            whitelisted_users=[
                WhitelistedUser(id=123, name="", username="test_user1"),
                WhitelistedUser(id=456, name="", username="test_user2"),
            ],
            currency_to_exchange_mapping={
                "XRP": "deribit",
                "SUI": "coincall",
                "BNB": "coincall",
            },
            default_exchange="v2composite",
            supported_vol_chart_currencies=["BTC", "ETH", "XRP", "SUI", "BNB"],
            supported_pricing_currencies=["BTC", "ETH", "XRP", "SUI", "BNB"],
            is_premium_bot=False,
            internal_api_key="test_key",
            timeseries_version="v1.0",
        )

    @pytest.fixture
    def premium_botfig(
        self,
        funding_rate_config: ExchangeToCurrencies,
        spot_config: ExchangeToCurrencies,
    ) -> Botfig:
        return Botfig(
            funding_rate_config=funding_rate_config,
            spot_config=spot_config,
            whitelisted_users=[],
            currency_to_exchange_mapping={
                "XRP": "deribit",
                "SUI": "coincall",
                "BNB": "coincall",
            },
            default_exchange="okx",
            supported_vol_chart_currencies=["SOL", "XRP", "SUI", "BNB"],
            supported_pricing_currencies=["SOL", "XRP", "SUI", "BNB"],
            is_premium_bot=True,
            internal_api_key="",
            timeseries_version="",
        )

    def test_botfig_initialization(self, valid_botfig: Botfig) -> None:
        assert valid_botfig.default_exchange == "v2composite"
        assert valid_botfig.currency_to_exchange_mapping == {
            "XRP": "deribit",
            "SUI": "coincall",
            "BNB": "coincall",
        }
        assert valid_botfig.is_premium_bot is False
        assert valid_botfig.internal_api_key == "test_key"
        assert valid_botfig.timeseries_version == "v1.0"
        assert len(valid_botfig.whitelisted_users) == 2

    def test_get_exchange_for_currency(self, valid_botfig: Botfig) -> None:
        # Test currencies in the mapping
        assert valid_botfig.get_exchange_for_currency("XRP") == "deribit"
        assert valid_botfig.get_exchange_for_currency("SUI") == "coincall"
        assert valid_botfig.get_exchange_for_currency("BNB") == "coincall"

        # Test currencies not in the mapping (should use default exchange)
        assert valid_botfig.get_exchange_for_currency("BTC") == "v2composite"
        assert valid_botfig.get_exchange_for_currency("ETH") == "v2composite"
        assert valid_botfig.get_exchange_for_currency("SOL") == "v2composite"

    def test_premium_botfig_with_whitelisted_users(
        self,
        funding_rate_config: ExchangeToCurrencies,
        spot_config: ExchangeToCurrencies,
    ) -> None:
        with pytest.raises(ValueError):
            Botfig(
                funding_rate_config=funding_rate_config,
                spot_config=spot_config,
                whitelisted_users=[WhitelistedUser(id=789, name="test_user3")],
                currency_to_exchange_mapping={
                    "XRP": "deribit",
                    "SUI": "coincall",
                    "BNB": "coincall",
                },
                default_exchange="okx",
                supported_vol_chart_currencies=["SOL", "XRP", "SUI", "BNB"],
                supported_pricing_currencies=["SOL", "XRP", "SUI", "BNB"],
                is_premium_bot=True,
                internal_api_key="",
                timeseries_version="",
            )

    def test_display_allowed_constant_tenors(
        self, valid_botfig: Botfig
    ) -> None:
        expected_lines = []
        for currency in valid_botfig.supported_vol_chart_currencies:
            tenors = valid_botfig.get_currency_tenor_target(currency)
            if tenors:
                expected_lines.append(f"{currency}: {', '.join(tenors)}")
        expected_output = "\n".join(expected_lines)
        assert valid_botfig.display_allowed_constant_tenors == expected_output

    def test_display_allowed_constant_tenors_empty(
        self,
        funding_rate_config: ExchangeToCurrencies,
        spot_config: ExchangeToCurrencies,
    ) -> None:
        botfig = Botfig(
            funding_rate_config=funding_rate_config,
            spot_config=spot_config,
            whitelisted_users=[],
            supported_vol_chart_currencies=[],
            supported_pricing_currencies=["SOL"],
            is_premium_bot=False,
            internal_api_key="",
            timeseries_version="",
        )
        assert botfig.display_allowed_constant_tenors == ""

    def test_get_whitelist(self, valid_botfig: Botfig) -> None:
        expected_whitelist = [123, 456, "test_user1", "test_user2"]
        assert valid_botfig.get_whitelist == expected_whitelist

    def test_get_whitelist_empty(self, premium_botfig: Botfig) -> None:
        assert premium_botfig.get_whitelist == []

    def test_botfig_invalid_initialization(
        self,
        funding_rate_config: ExchangeToCurrencies,
        spot_config: ExchangeToCurrencies,
    ) -> None:
        with pytest.raises(ValidationError):
            Botfig(
                funding_rate_config=funding_rate_config,
                spot_config=spot_config,
                whitelisted_users="not_a_list",  # type: ignore
                currency_to_exchange_mapping={
                    "XRP": "deribit",
                    "SUI": "coincall",
                    "BNB": "coincall",
                },
                default_exchange="v2composite",
                supported_vol_chart_currencies=["BTC", "XRP", "SUI", "BNB"],
                supported_pricing_currencies=["BTC", "XRP", "SUI", "BNB"],
                is_premium_bot=False,
                internal_api_key="test_key",
                timeseries_version="v1.0",
            )


class TestMaxTenorCutoff:
    def test_get_max_tenor_cutoff_for_defined_currency(
        self, botfig: Botfig
    ) -> None:
        """Test that get_max_tenor_cutoff returns the correct value for a defined currency."""
        # Test a currency that is defined in CURRENCY_TO_MAX_PRICING_CUTOFF
        for currency, expected_cutoff in CURRENCY_TO_MAX_PRICING_CUTOFF.items():
            assert botfig.get_max_tenor_cutoff(currency) == expected_cutoff

    def test_get_max_tenor_cutoff_for_undefined_currency(
        self, botfig: Botfig
    ) -> None:
        """Test that get_max_tenor_cutoff returns the default value for an undefined currency."""
        # Test a currency that is not defined in CURRENCY_TO_MAX_PRICING_CUTOFF
        assert (
            botfig.get_max_tenor_cutoff("UNDEFINED_CURRENCY")
            == DEFAULT_MAX_PRICING_CUTOFF
        )

    def test_is_beyond_max_tenor_cutoff_with_future_date(self) -> None:
        """Test that is_beyond_max_tenor_cutoff returns True for a date beyond the cutoff."""
        # Create a date that is beyond the cutoff
        max_cutoff_days = 365
        future_date = (
            (datetime.now(UTC) + timedelta(days=max_cutoff_days + 30))
            .strftime("%d%b%y")
            .upper()
        )

        assert is_beyond_max_tenor_cutoff(future_date, max_cutoff_days) is True

    def test_is_beyond_max_tenor_cutoff_with_valid_date(self) -> None:
        """Test that is_beyond_max_tenor_cutoff returns False for a date within the cutoff."""
        # Create a date that is within the cutoff
        max_cutoff_days = 365
        valid_date = (
            (datetime.now(UTC) + timedelta(days=max_cutoff_days - 30))
            .strftime("%d%b%y")
            .upper()
        )

        assert is_beyond_max_tenor_cutoff(valid_date, max_cutoff_days) is False
