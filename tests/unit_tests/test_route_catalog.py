from collections.abc import Callable, Iterator
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from app.typings import SourceRedirectMappings


@pytest.fixture(scope="session", autouse=True)
def mock_ssm_redirect_mappings() -> Iterator[dict[str, SourceRedirectMappings]]:
    mock_redirect_mappings = {
        "mappings": {
            "composite": {
                "BTC": "v2composite",
                "": "blockscholes",
                "BNB": "bybit",
            },
            "deribit": {
                "ETH": "coincall",
                "SOL": "blockscholes",
                "BNB": "bybit",
            },
        }
    }

    with patch(
        "utils_aws.load_ssm_params",
        return_value=mock_redirect_mappings,
    ):
        yield mock_redirect_mappings


@pytest.mark.asyncio
async def test_fetch_catalog_success(
    client: TestClient,
    mocker: MagicMock,
    auth_override_factory: Callable[[dict[str, Any]], None],
) -> None:
    # Set specific permissions needed for this test
    auth_override_factory(
        {
            "CLIENT_LAYER": "Grvt",  # Required for get_client_layer
            "catalog_exchanges": ["blockscholes"],
            "catalog_asset_types": ["option"],
            "catalog_base_assets": ["BTC"],
        }
    )

    instruments = [
        {
            "availableSince": "2023-12-28T08:00:09.000Z",
            "baseAsset": "BTC",
            "strike": 10000,
            "listing": "2023-12-28T08:00:09.000Z",
            "quoteAsset": "BTC",
            "instrument": "BTC_BTC_2024-12-27T08:00:00Z_10000_C",
            "qualified_name": "blockscholes.option.BTC_BTC_2024-12-27T08:00:00Z_10000_C",
            "expiry": "2024-12-27T08:00:00.000Z",
            "settlementAsset": "BTC",
            "style": "european",
            "type": "C",
        },
        {
            "availableSince": "2023-12-28T08:00:09.000Z",
            "baseAsset": "BTC",
            "strike": 10000,
            "listing": "2023-12-28T08:00:09.000Z",
            "quoteAsset": "BTC",
            "instrument": "BTC_BTC_2024-12-27T08:00:00Z_10000_C",
            "qualified_name": "blockscholes.option.BTC_BTC_2024-12-27T08:00:00Z_10000_P",
            "expiry": "2024-12-27T08:00:00.000Z",
            "settlementAsset": "BTC",
            "style": "european",
            "type": "P",
        },
    ]
    mocker.patch(
        "database.database_catalog.get_instruments_async",
        return_value=instruments,
    )

    params = {
        "fields": ["instrument"],
        "start": "2023-12-01T00:00:00Z",
        "end": "2023-12-02T00:00:00Z",
        "exchanges": ["blockscholes"],
        "asset_types": ["option"],
        "base_assets": ["BTC"],
        "quote_assets": ["USD"],
        "aws_stage": "test",
    }

    response = client.post(
        "/api/v1/catalog/",
        headers={"X-API-Key": "ABC"},
        json=params,
    )

    assert response.status_code == 200
    res = response.json()
    assert res == {
        "data": [{"instrument": i["instrument"]} for i in instruments]
    }
    assert len(res["data"]) == len(instruments)
