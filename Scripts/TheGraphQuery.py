import logging
import os
from datetime import datetime
from itertools import islice

# pretty print is used to print the output in the console in an easy to read format
from typing import Dict

import pandas as pd
import requests
from pandas import json_normalize

THEGRAPH_API_KEY = os.environ.get("THEGRAPH_API_KEY")
path = (
    "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/LiquidationData/"
)

# COMP_POSITIONS_URL = f"https://gateway.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/6tGbL7WBx287EZwGUvvcQdL6m67JGMJrma3JSTtt5SV7"
# AAVE_POSITIONS_URL = f"https://gateway.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/84CvqQHYhydZzr2KSth8s1AFYpBRzUbVJXq6PWuZm9U9"
# COMP_LIQUIDATIONS_URL = f"https://gateway.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/6tGbL7WBx287EZwGUvvcQdL6m67JGMJrma3JSTtt5SV7"
# AAVE_LIQUIDATIONS_URL = f"https://gateway.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/84CvqQHYhydZzr2KSth8s1AFYpBRzUbVJXq6PWuZm9U9"

COMP_POSITIONS_URL = f"https://gateway.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/4TbqVA8p2DoBd5qDbPMwmDZv3CsJjWtxo8nVSqF2tA9a"
AAVE_POSITIONS_URL = f"https://gateway.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/C2zniPn45RnLDGzVeGZCx2Sw3GXrbc9gL4ZfL8B8Em2j"
COMP_LIQUIDATIONS_URL = f"https://gateway.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/4TbqVA8p2DoBd5qDbPMwmDZv3CsJjWtxo8nVSqF2tA9a"
AAVE_LIQUIDATIONS_URL = f"https://gateway.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/C2zniPn45RnLDGzVeGZCx2Sw3GXrbc9gL4ZfL8B8Em2j"


# UNISWAP_V3_URL = "https://api.thegraph.com/subgraphs/name/uniswap/uniswap-v3"
UNISWAP_V3_URL = f"https://gateway-arbitrum.network.thegraph.com/api/{THEGRAPH_API_KEY}/subgraphs/id/5zvR82QoaXYFyDEKLZ9t6v9adgnptxYpKpSbxtgVENFV"
UNISWAP_V2_URL = "https://api.thegraph.com/subgraphs/name/uniswap/uniswap-v2"
# The Graph query - Query Uniswap for a list of the top 10 pairs where the reserve is > 1000000 USD and the volume is >50000 USD
query = """

{
 liquidationCalls(
  	where:{timestamp_gte: 1667260800, timestamp_lte: 1667779200}
){
  txHash
  timestamp
  user{
   id
  }
  collateralReserve{
    symbol
  }
  collateralAmount
  collateralAssetPriceUSD
  principalReserve{
    symbol
  }
  principalAmount
  borrowAssetPriceUSD
 }
}
"""


# function to use requests.post to make an API call to the subgraph url
def run_query(query, URL):
    # endpoint where you are making the request
    response = requests.post(URL, json={"query": query})
    if response.status_code == 200:
        return response.json()
    else:
        # Include the response content in the exception message
        error_message = f"Query failed. Return code is {response.status_code}. Response content: {response.content.decode('utf-8')}"
        raise Exception(error_message)


def liquidations_query(start_time, end_time, protocol):
    # URL's are from messari subgreaph
    if (protocol != "Aave") and (protocol != "Compound"):
        raise ValueError
    elif protocol == "Compound":
        URL = COMP_POSITIONS_URL
    elif protocol == "Aave":
        URL = AAVE_LIQUIDATIONS_URL

    df_list = []
    skip = 0

    query = f"""
    {{
      liquidates(
        first: 1000
        skip: {skip}
        where: {{timestamp_gte: "{end_time}", timestamp_lte: "{start_time}"}}
        orderBy: timestamp
        orderDirection: desc){{
        hash
        timestamp
        amount
        asset {{
          decimals
          symbol
        }}
        amountUSD
        liquidator {{
          id
        }}
        profitUSD
      }}
    }}
    """
    result = run_query(query, URL)
    df = json_normalize(result["data"]["liquidates"])
    df_list.append(df)

    while len(df) == 1000:
        skip += 1000
        query = f"""
        {{
          liquidates(
            first: 1000
            skip: {skip}
            where: {{timestamp_gte: "{end_time}", timestamp_lte: "{start_time}"}}
            orderBy: timestamp
            orderDirection: desc){{
            hash
            timestamp
            amount
            asset {{
              decimals
              symbol
            }}
            amountUSD
            liquidator {{
              id
            }}
            profitUSD
          }}
        }}
        """
        result = run_query(query, URL)
        df = json_normalize(result["data"])
        df_list.append(df)
    bigDf = pd.concat(df_list, ignore_index=True)
    return bigDf


def positions_query(start_time, end_time, protocol):
    query_type = ["borrows", "deposits", "withdraws", "repays"]

    # URL's are from messari subgreaph
    if (protocol != "Aave") and (protocol != "Compound"):
        raise ValueError
    elif protocol == "Compound":
        URL = COMP_POSITIONS_URL
    elif protocol == "Aave":
        URL = AAVE_POSITIONS_URL

    df_dict = {}
    for type in query_type:
        skip = 0
        df_list = []
        query = f"""
            {{
              {type}(
                first: 1000
                skip:{skip}
                where: {{timestamp_gte: "{end_time}", timestamp_lte: "{start_time}"}}
                orderBy: timestamp
                orderDirection: desc
              ) {{
                asset {{
                  symbol
                  decimals
                }}
                blockNumber
                timestamp
                amount
                amountUSD
                hash
              }}
            }}
            """
        result = run_query(query, URL)
        # print(result)
        df = json_normalize(result["data"][type])
        df_list.append(df)

        while len(df) == 1000:
            skip += 1000
            query = f"""
            {{
              {type}(
                first: 1000
                skip:{skip}
                where: {{timestamp_gte: "{end_time}", timestamp_lte: "{start_time}"}}
                orderBy: timestamp
                orderDirection: desc
              ) {{
                asset {{
                  symbol
                  decimals
                }}
                blockNumber
                timestamp
                amount
                amountUSD
                hash
              }}
            }}
            """
            result = run_query(query, URL)
            df = json_normalize(result["data"][type])
            df_list.append(df)
        bigDf = pd.concat(df_list, ignore_index=True)
        df_dict[type] = bigDf
    return df_dict


def make_dict(df, row1, row2):
    timestamps = df[row1].tolist()
    block = df[row2].tolist()
    return dict(zip(timestamps, block))


def timetravel_query(
    time_and_blockNo_df: pd.DataFrame, protocol: str
) -> Dict[str, pd.DataFrame]:
    timestamps_block_dict = make_dict(time_and_blockNo_df, "unix", "blockNumber")
    # print(timestamps_block_dict)

    if (protocol != "Aave") and (protocol != "Compound"):
        raise ValueError
    elif protocol == "Compound":
        URL = COMP_POSITIONS_URL
    elif protocol == "Aave":
        URL = AAVE_POSITIONS_URL

    skip = 0
    # coin_list = ["USDT", "USDC", "BUSD", "DAI"]
    if protocol == "Aave":
        coin_list = ["USDT", "USDC", "BUSD", "DAI", "TUSD", "WETH", "WBTC"]
    else:
        coin_list = ["Dai", "USD Coin", "USDT", "TrueUSD", "Ether", "Wrapped BTC"]
        # here to achieve uniform otputs
        comp_coin_dict = {
            "Dai": "DAI",
            "USD Coin": "USDC",
            "USDT": "USDT",
            "Ether": "WETH",
            "Wrapped BTC": "WBTC",
            "TrueUSD": "TUSD",
        }
    df_dict = {}
    # Define the batch size

    for coin in coin_list:
        batch_size = 336

        # Get an iterator for the timestamps_block_dict items
        iter_items = iter(timestamps_block_dict.items())
        results_data = {}

        # Iterate over the items in batches
        while True:
            # Get the next batch of items
            batch_items = dict(islice(iter_items, batch_size))

            # Check if the batch is empty
            if not batch_items:
                break
            query = "{"
            # Build the query for the batch
            for unix, blockNumber in batch_items.items():
                query += f"""  
                    t{unix}:markets(
                        block: {{number: {blockNumber}}},
                        where: {{name_contains: "{coin}"}}) {{
                        totalBorrows:totalBorrowBalanceUSD
                        totalDeposit:totalDepositBalanceUSD
                    }}
                """

            query += "}"

            # Make the query
            # print(query)
            # print(coin)
            result = run_query(query, URL)
            # print(result)
            try:
                results_data.update(result["data"])
            except Exception:
                print()

        # for coin in coin_list:
        #     query = "{"
        #     for unix, blockNumber in timestamps_block_dict.items():
        #         query += f"""
        #             t{unix}:markets(
        #                 block: {{number: {blockNumber}}},
        #                 where: {{name_contains: "{coin}"}}) {{
        #                 totalBorrows:totalBorrowBalanceUSD
        #                 totalDeposit:totalDepositBalanceUSD
        #             }}
        #         """
        #     query += "}"
        #     result = run_query(query, URL)

        df = json_normalize(result["data"])
        # print(df)
        unix_list = []
        timestamp_list = []
        totalBorrows_list = []
        totalDeposit_list = []

        for unix, data in results_data.items():
            if coin == "Wrapped BTC":
                totalBorrows = data[1]["totalBorrows"]
                totalDeposit = data[1]["totalDeposit"]
            else:
                totalBorrows = data[0]["totalBorrows"]
                totalDeposit = data[0]["totalDeposit"]
            unix = int(unix[1:])
            unix_list.append(unix)
            timestamp_list.append(datetime.fromtimestamp(unix))
            totalBorrows_list.append(totalBorrows)
            totalDeposit_list.append(totalDeposit)

        df = pd.DataFrame(
            {
                "timestamp": timestamp_list,
                "unix": unix_list,
                "totalBorrows": totalBorrows_list,
                "totalDeposit": totalDeposit_list,
            }
        )

        df.set_index("timestamp", inplace=True)
        # print(df)
        if protocol == "Aave":
            df_dict[coin] = df
        else:
            df_dict[comp_coin_dict[coin]] = df
    # process data and save file based on coin

    return df_dict


def process_positions_data(df_dict):
    for key, df_list in df_dict.items():
        template_list = []
    return


def uniswap_volume_query(time_and_blockNo_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    timestamps_block_dict = make_dict(time_and_blockNo_df, "unix", "blockNumber")
    # print(timestamps_block_dict)

    freq = time_and_blockNo_df["unix"][0] - time_and_blockNo_df["unix"][1]
    n_hours = freq / 3600

    batch_size = 336

    iter_items = iter(timestamps_block_dict.items())
    v3_result_data = {}

    """Uniswap_V3"""
    while True:
        # Get the next batch of items
        batch_items = dict(islice(iter_items, batch_size))

        # Check if the batch is empty
        if not batch_items:
            break

        query = "{"
        for unix, blockNumber in batch_items.items():
            query += f"""  
                t{unix}:factories(
                    block: {{number: {blockNumber}}} ){{
                    poolCount
                    txCount
                    totalVolumeUSD
                }}
            """
        query += "}"
        # print(query)
        # make query
        logging.info("here")
        v3_result = run_query(query, UNISWAP_V3_URL)
        v3_result_data.update(v3_result["data"])

    # print(result["data"])
    # print(v3_result)
    # print(len(v3_result['data']))
    # df = json_normalize(v3_result['data'])
    # print(df)

    unix_list = []
    timestamp_list = []
    poolCount_list = []
    txCount_list = []
    totalVolumeUSD_list = []

    for unix, data in v3_result_data.items():
        poolCount = data[0]["poolCount"]
        txCount = data[0]["txCount"]
        totalVolumeUSD = data[0]["totalVolumeUSD"]

        unix = int(unix[1:])
        unix_list.append(unix)
        timestamp_list.append(datetime.fromtimestamp(unix))
        poolCount_list.append(poolCount)
        txCount_list.append(txCount)
        totalVolumeUSD_list.append(totalVolumeUSD)

    v3_df = pd.DataFrame(
        {
            "timestamp": timestamp_list,
            "unix": unix_list,
            "v3_poolCount": poolCount_list,
            "v3_txCount": txCount_list,
            "v3_totalVolumeUSD": totalVolumeUSD_list,
        }
    )
    v3_df["timestamp"] = pd.to_datetime(v3_df["timestamp"])
    v3_df.set_index("timestamp", inplace=True)
    v3_df.sort_index(inplace=True)
    v3_df[f"{n_hours}H_volumeUSD"] = (
        v3_df["v3_totalVolumeUSD"].astype(float).diff().shift(-1)
    )
    print(v3_df)

    # "UNISWAP V2"
    #
    #
    # query = "{"
    # for unix, blockNumber in timestamps_block_dict.items():
    #     query += f"""
    #         t{unix}:uniswapFactories(
    #             block: {{number: {blockNumber}}} ){{
    #             pairCount
    #             txCount
    #             totalVolumeUSD
    #         }}
    #     """
    # query += "}"
    #
    # v2_result = run_query(query, UNISWAP_V2_URL)
    # print(v2_result)
    # print(len(v2_result['data']))
    #
    # unix_list = []
    # timestamp_list = []
    # pairCount_list = []
    # totalVolumeUSD_list = []
    #
    # for unix, data in v2_result["data"].items():
    #     pairCount = data[0]["pairCount"]
    #     txCount = data[0]["txCount"]
    #     totalVolumeUSD = data[0]["totalVolumeUSD"]
    #
    #     unix = int(unix[1:])
    #     unix_list.append(unix)
    #     timestamp_list.append(datetime.fromtimestamp(unix))
    #     pairCount_list.append(pairCount)
    #     txCount_list.append(txCount)
    #     totalVolumeUSD_list.append(totalVolumeUSD)
    #
    # v2_df = pd.DataFrame(
    #     {
    #         "timestamp": timestamp_list,
    #         "unix": unix_list,
    #         "v2_poolCount": poolCount_list,
    #         "v2_txCount": txCount_list,
    #         "v2_totalVolumeUSD": totalVolumeUSD_list,
    #     }
    # )
    # v2_df.set_index("timestamp", inplace=True)
    #
    # unisawp_total_df = pd.merge(v3_df, v2_df, on='timestamp')
    # print(unisawp_total_df)

    return v3_df


def get_24H_volume(df):
    return


if __name__ == "__main__":
    import plotly.graph_objects as go

    start_time, end_time = 1686214800, 1685610000

    # eth_blocks, timestamp_block_dict = asyncio.run(
    #     async_create_blocklist_from_timestamp(
    #         start_time, end_time, "hour", async_get_block_by_timestamp
    #     )
    # )
    t_b = pd.read_csv(
        "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/One-offs/ethblocks_1686225600_1685620800.csv"
    )

    # time_and_blockno_df = pd.DataFrame(list(timestamp_block_dict.items()), columns=['unix', 'blockNumber'])
    v3_df = uniswap_volume_query(t_b)
    # saveData(time_and_blockno_df, '/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/One-offs', '/uniswap_blocks.csv')
    # saveData(v3_df, '/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/One-offs', '/uniswap_v3.csv')

    # v3_df = pd.read_csv('/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/One-offs/uniswap_v3.csv')
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=v3_df.index, y=v3_df["1.0H_volumeUSD"], mode="lines"))
    title = "Uniswap V3 1H Volumes"
    fig.update_layout(
        xaxis_title="Date",
        title="Uniswap V3 1H Volumes",
        yaxis_title="Volume (USD)",
        margin=dict(b=10, t=60, l=10, r=10),
        # legend_x=0.75,
        # legend_y=1,
        # legend_orientation="h",
        height=600,
        width=1000,
        font=dict(size=24),
        template="plotly",
    )
    fig.show()
