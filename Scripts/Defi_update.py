import asyncio
import itertools
import logging
import time
from datetime import datetime, timedelta, timezone
from typing import Callable, Dict, List, Tuple

import backoff
import datagrabber
import eth_abi
import numpy as np
import pandas as pd
import polars as pl
from eth_abi import abi
from requests.exceptions import HTTPError

from constants import *
from Library import (
    encodeFunction,
    get_block_by_timestamp,
    get_block_by_timestamp_arbitrum,
    get_block_by_timestamp_optimism,
    load_json_file,
    makeContract,
    save_json,
    saveData,
)
from Scripts.beacon_chain_def import grab_beacon_chain_state
from Scripts.SuperBlockQuery import (
    api_key_fetcher,
    get_block_params_fast,
    process_block_results_withdrawals,
)
from Scripts.TheGraphQuery import (
    liquidations_query,
    positions_query,
    uniswap_volume_query,
)


async def dopex_batch_query_callss(
    starting_block: int, max_size_copy: int, params: str
) -> Tuple[str, List[int], int]:
    # create the API handle
    global counter

    option = params["option_object"]
    function = params["function"]
    block_interval = params["block_interval"]

    async with aio_eth.EthAioAPI(PROVIDER_URL, max_tasks=100000) as api:
        # express queries - example: get all transactions from 100 blocks
        # starting from 10553978
        for strike in option["strikes"]:
            start = starting_block
            counter = 0
            blocks = []
            for i in range(max_size_copy):
                # submit tasks to the task list, if `current tasks > max_tasks`
                # this method throws an exception.
                api.push_task(
                    {
                        "method": "eth_call",
                        "params": [
                            {
                                "to": f'{option["address"]}',
                                "data": f"{encodeFunction(function, False, strike)}",
                            },
                            hex(start),
                        ],
                    }
                )
                counter += 1
                blocks.append(start)

                if PAST is True:
                    start -= block_interval
                else:
                    start += block_interval

        st = time.time()
        print("im here")
        # execute the tasks together concurrently, outputs are returned in the same
        # order in which their corresponding queries where submitted.
        result = await api.exec_tasks_batch()

        et = time.time()
        print("time taken: ", et - st, " seconds")
        return result, blocks, start


def dopex_weekly_implied_volatilities():
    """function will change the sart and end blocks"""

    import math

    global start_block, end_block, last_block, interval, sample_frequency_per_day, blocks_per_day, block_interval

    abi = json.loads(
        '[{"inputs":[],"name":"currentEpoch","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},'
        '{"inputs":[{"internalType":"uint256","name":"epoch","type":"uint256"}],"name":"getEpochTimes","outputs":[{"internalType":"uint256","name":"start","type":"uint256"},{"internalType":"uint256","name":"end","type":"uint256"}],"stateMutability":"view","type":"function"},'
        '{"inputs":[{"internalType":"uint256","name":"_strike","type":"uint256"}],"name":"getVolatility","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}]'
    )

    weekly_options = []
    ETH_weekly_call_str = [1200, 1300, 1400, 1600]
    ETH_weekly_puts_str = [1200, 1100, 1000, 800]

    ETH_weekly_calls = {
        "address": "******************************************",
        "epoch": 12,
        "strikes": [int(i * 1e8) for i in ETH_weekly_call_str],
        "name": "ETH_weekly_calls",
    }
    ETH_weekly_puts = {
        "address": "******************************************",
        "epoch": 12,
        "strikes": [int(i * 1e8) for i in ETH_weekly_puts_str],
        "name": "ETH_weekly_puts",
    }
    weekly_options.append(ETH_weekly_calls)
    weekly_options.append(ETH_weekly_puts)

    ETH_weekly_object_contract = web3.eth.contract(
        address=ETH_weekly_calls["address"], abi=abi
    )
    currentEpoch = ETH_weekly_object_contract.functions.currentEpoch().call(
        block_identifier="latest"
    )
    end, start = ETH_weekly_object_contract.functions.getEpochTimes(currentEpoch).call(
        block_identifier="latest"
    )
    print("start", start)
    print("end", end)

    """ARBITRUM"""
    """
    7days = 1895567
    1day = 270795.286
    1hour = 11283.137

    test = 37896122

    """

    # cwe want to start querying in the next hour after the epoch start
    interval = 1  # in hours (24 = daily interval)
    sample_frequency_per_day = int(24 / interval)
    blocks_per_day = 270795
    block_interval = round(blocks_per_day / sample_frequency_per_day)
    start_time = (math.floor(start / 3600) + 1) * 3600
    end_time = math.floor(end / 3600) * 3600
    current_time = time.time()
    if current_time < start_time:
        start_time = math.floor(current_time / 3600) * 3600

    start_block = get_block_by_timestamp_arbitrum(1668988800)
    end_block = get_block_by_timestamp_arbitrum(1668384000)
    last_block = start_block

    print(start_block)
    print(end_block)
    # print('test',
    #       ETH_weekly_object_contract.functions.getVolatility(120000000000).call(block_identifier=start_block-(77 * block_interval)),
    #       'at block',start_block-(77 * block_interval))

    #
    # follow querying procedures for every strike price inside

    for option in weekly_options:
        market_name = option["name"]
        dopex_query_params = {
            "option_object": option,
            "function": "getVolatility(uint256)",
            "start_block": start_block,
            "end_block": end_block,
            "block_interval": block_interval,
        }
        option_call_query, _ = query_maker(
            dopex_batch_query_callss, last_block, dopex_query_params
        )
        option_call_query_dict = process_query_data(
            option_call_query, len(option["strikes"])
        )

        # replace keys of dictionary
        for idx, strike in enumerate(option["strikes"]):
            option_call_query_dict[strike] = option_call_query_dict.pop(idx)

        option_iv_df = pd.DataFrame.from_dict(option_call_query_dict)
        print(option_iv_df)

        for column in option_iv_df:
            strike = column / 100000000
            option_iv_df[strike] = [
                (
                    bytes.fromhex(response["result"][2:])
                    if "result" in response
                    else bytes.fromhex("0" * 64)
                )
                for response in option_iv_df[column]
            ]
            option_iv_df[strike] = option_iv_df[strike].apply(decode_dopex_volatility)
            del option_iv_df[column]
            print(option_iv_df)
            saveData(
                option_iv_df,
                f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/ImpliedVolatility/Dopex",
                f"/Dopex_{market_name}_IV_{start_time}_{end_time}.csv",
            )

    return


"""######################################################################################################## NEW ############################################################################################################################################################"""


async def batch_query_blockNo_new(
    query_index: int, max_size_copy: int, params: str
) -> List[str]:
    block_list = params["block_list"]
    block_list = block_list[query_index : query_index + max_size_copy]
    # create the API handle
    async with aio_eth.EthAioAPI(PROVIDER_URL, max_tasks=100000) as api:
        # express queries - example: get all transactions from 100 blocks
        # starting from 10553978
        for block in block_list:
            # submit tasks to the task list, if `current tasks > max_tasks`
            # this method throws an exception.
            api.push_task(
                {"method": "eth_getBlockByNumber", "params": [hex(block), True]}
            )

        st = time.time()
        # execute the tasks together concurrently, outputs are returned in the same
        # order in which their corresponding queries where submitted.
        result = await api.exec_tasks_async()

        et = time.time()
        print("time taken: ", et - st, " seconds")
        return result


async def total_supply_batch_query_call_new(
    query_index: int, max_size_copy: int, params: str
) -> List[str]:
    # create the API handle
    global counter
    address_list = params["address_list"]
    function = params["function"]
    # block_interval = params['block_interval']
    block_list = params["block_list"]

    block_list = block_list[query_index : query_index + max_size_copy]
    # print('block_window', block_list)

    async with aio_eth.EthAioAPI(PROVIDER_URL, max_tasks=100000) as api:
        # express queries - example: get all transactions from 100 blocks
        # starting from 10553978
        for package in address_list:
            address = package[1]
            for block in block_list:
                # submit tasks to the task list, if `current tasks > max_tasks`
                # this method throws an exception.
                api.push_task(
                    {
                        "method": "eth_call",
                        "params": [
                            {
                                "to": f"{address}",
                                "data": f"{encodeFunction(function, False)}",
                            },
                            hex(block),
                        ],
                    }
                )

        st = time.time()
        # execute the tasks together concurrently, outputs are returned in the same
        # order in which their corresponding queries where submitted.
        result = await api.exec_tasks_async()

        et = time.time()
        print("time taken: ", et - st, " seconds")
        return result


async def aave_batch_query_callss_new(
    query_index: int, max_size_copy: int, params: str
) -> List[str]:
    # create the API handle
    global counter
    to_address = params["data_provider_address"]
    aave_address_list = params["address_list"]
    function = params["function"]
    # block_interval = params['block_interval']
    block_list = params["block_list"]

    block_list = block_list[query_index : query_index + max_size_copy]
    # print('block_window', block_list)

    async with aio_eth.EthAioAPI(PROVIDER_URL, max_tasks=100000) as api:
        for idx, pack in enumerate(aave_address_list):
            addr = pack[1]
            for block in block_list:
                # submit tasks to the task list, if `current tasks > max_tasks`
                # this method throws an exception.
                api.push_task(
                    {
                        "method": "eth_call",
                        "params": [
                            {
                                "to": f"{to_address}",
                                "data": f"{encodeFunction(function, True, addr)}",
                            },
                            hex(int(block)),
                        ],
                    }
                )

        st = time.time()
        # execute the tasks together concurrently, outputs are returned in the same
        # order in which their corresponding queries where submitted.
        result = await api.exec_tasks_async()

        et = time.time()
        print("time taken: ", et - st, " seconds")
        return result


async def comp_batch_query_callss_new(
    query_index: int, max_size_copy: int, params: str
) -> List[str]:
    # create the API handle
    global counter

    function = params["function"]
    # block_interval = params['block_interval']
    comp_address_list = params["address_list"]
    block_list = params["block_list"]

    block_list = block_list[query_index : query_index + max_size_copy]

    async with aio_eth.EthAioAPI(PROVIDER_URL, max_tasks=100000) as api:
        # express queries - example: get all transactions from 100 blocks
        # starting from 10553978
        for address in comp_address_list:
            for block in block_list:
                # submit tasks to the task list, if `current tasks > max_tasks`
                # this method throws an exception.
                api.push_task(
                    {
                        "method": "eth_call",
                        "params": [
                            {
                                "to": f"{address}",
                                "data": f"{encodeFunction(function, False)}",
                            },
                            hex(block),
                        ],
                    }
                )

        st = time.time()
        # execute the tasks together concurrently, outputs are returned in the same
        # order in which their corresponding queries where submitted.
        result = await api.exec_tasks_async()

        et = time.time()
        print("time taken: ", et - st, " seconds")
        return result


def query_maker_new(
    query_type: Callable[[int, int, str], List[str]], params: str
) -> str:
    """
    :param:  max_size & max_size_copy will dictate the size od the batch queries we make
                batch RPC requests will fail when >10K requesta are made. calibrate such that requets are less

        :query_type: The querying function, ie, aave_batch_query...
        :last_block_c: This input should be the starting block or index
        :params: This is a dict of params that are involved
    """
    import math

    if "address_list" in params.keys():
        max_size_copy = math.floor(2000 / len(params["address_list"]))
        print(params)
    else:
        max_size_copy = MAX_SIZE
    block_list = params["block_list"]
    query_index = 0
    end_index = len(block_list)

    big_call_query = []
    i = 1
    print("max size", max_size_copy)

    # sanity checks i.e if block_list[0] = 1669766400 & block_list[1] = 1669680000
    print("block_list[query_index] ", block_list[query_index])
    print("block_list[end_index-1]", block_list[end_index - 1])
    assert block_list[query_index] > block_list[end_index - 1]
    assert query_index < end_index

    print("starting last block", block_list[0])
    print("starting query index", query_index)
    while query_index < end_index:
        if (end_index - query_index) < max_size_copy:
            max_size_copy = end_index - query_index
            print(
                "Weve reached the edge, Query window is now:", end_index - query_index
            )
            print(max_size_copy, "edge size")

        print("current query index", query_index)
        time.sleep(70)
        query_result = loop.run_until_complete(
            query_type(query_index, max_size_copy, params)
        )

        print("call query length", len(query_result))

        big_call_query.append(query_result)
        print(len(query_result))

        i += 1
        copy = query_index
        # next query window
        query_index += max_size_copy
        print(f"index range queried {copy}-{query_index}")
        # print("last block", block_list[query_index])
        print("end_block", end_block, "\n")

    """set end_block as last item in the list or the last index in the list
        set last_block as the last block that we queried in the blocklist, or the index in the blocklist
        max_size condition is if the index of index of last_block-end_block is less than max_size
            set max size =  difference

        """

    """queries are retunrned in the order they were processed
    e.g [eth,eth,eth,aave,aave,aave,uni,uni,uni]

    following list will contain a list of these lists
    i.e. [[eth1,eth2,eth3,aave1,aave2,aave3,uni1,uni2,uni3], [eth4,eth5,aave4,aave5,uni4,uni5], [eth6,aave6,uni6]]
    [['eth1','eth2','eth3','aave1','aave2','aave3','uni1','uni2','uni3', 'z1','z2','z3'], ['eth4','eth5','aave4','aave5','uni4','uni5','z4','z5'], ['eth6','aave6','uni6','z6']]
    needs strategic processing"""

    # big_call_query = [item for sublist in big_call_query for item in sublist]
    # big_blocks = [item for sublist in big_blocks for item in sublist]

    return big_call_query


def aave_yields(time_and_blockno_df: pd.DataFrame, block_list: List[int]) -> None:
    blockNumber_list = list(time_and_blockno_df["blockNumber"])
    assert blockNumber_list == block_list, "Mismatch between Df and supplied block_list"

    logging.info("Querying Aave Yields...")
    aave_address_provider = makeContract(
        "******************************************", "ethereum"
    )
    aave_address_provider_poly = makeContract(
        "******************************************", "polygon"
    )
    aave_address_provider_avax = makeContract(
        "******************************************", "avalanche"
    )
    # aave_address_provider_contract = web3.eth.contract(address=aave_address_provider['address'], abi=aave_address_provider['abi'])
    # aave_address_list = aave_address_provider_contract.functions.getAllReservesTokens().call(block_identifier='latest')

    """
    ('WBTC','******************************************'),
    ,('WETH','******************************************')
    """

    aave_address_list = [
        ("USDC", "******************************************"),
        ("USDT", "******************************************"),
        # ("BUSD", "******************************************"),
        ("DAI", "******************************************"),
        ("WETH", "******************************************"),
        ("WBTC", "******************************************"),
        ("TUSD", "******************************************"),
    ]

    aave_address_list_poly = [
        ("USDC", "******************************************"),
        ("USDT", "******************************************"),
        ("DAI", "******************************************"),
        ("WETH", "******************************************"),
        ("WBTC", "******************************************"),
    ]

    aave_address_list_avax = [
        ("USDC", "******************************************"),
        ("USDT", "******************************************"),
        ("DAI", "******************************************"),
        ("WETH", "******************************************"),
        ("WBTC", "******************************************"),
    ]

    aave_query_params = {
        "data_provider_address": aave_address_provider["address"],
        "abi": aave_address_provider["abi"],
        "address_list": aave_address_list,
        "function": "getReserveData(address)",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval': block_interval,
        "block_list": block_list,
    }

    aave_query_params_poly = {
        "data_provider_address": aave_address_provider_poly["address"],
        "address_list": aave_address_list_poly,
        "function": "getReserveData(address)",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval': block_interval,
        "block_list": block_list,
    }
    aave_query_params_avax = {
        "data_provider_address": aave_address_provider_avax["address"],
        "address_list": aave_address_list_avax,
        "function": "getReserveData(address)",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval': block_interval,
        "block_list": block_list,
    }
    # print("sleeping for 30 seconds")
    # time.sleep(30)
    # print("sleeping for 30 seconds")

    # aave_call_query, _ = query_maker(aave_batch_query_callss, last_block, aave_query_params)
    aave_call_query = query_maker_new(aave_batch_query_callss_new, aave_query_params)
    # print(aave_call_query)
    logging.info(f"{len(aave_call_query)=}")
    # process query results
    aave_call_query_dict = process_query_data(aave_call_query, len(aave_address_list))
    # print(aave_call_query_dict)
    # df = pd.json_normalize(aave_call_query_dict)
    # # saveData(df,
    # #          f'/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Yield data/NEWPCA/AAVE/AVALANCHE',
    # #          f'/Aave_{start_time}_{end_time}_raw.csv')

    # print(aave_call_query_dict)
    logging.info(f"{len(aave_call_query[0])=}")
    logging.info(
        f"{len(aave_address_list) * len(time_and_blockno_df)=}",
    )
    assert len(list(itertools.chain.from_iterable(aave_call_query))) == len(
        aave_address_list
    ) * len(time_and_blockno_df)

    """
    currently call query is a list of ALL queries of ALL AAVE markets
    we chunk them up into a list of list with each sublist containing the query result of
    """

    # i use blocksDF because i know its equal to the length of one aave address should be
    # call dict has keys corresponding to the indexes of the addresses in the address list provided
    # recall that our query function arranges queries in the order they are supplied, so aav1,aav2,aav3...
    # we can trust the key value paris we are iterating though are in the same index positions as our address_list
    # address_list[idx] should correspond to aave_call_query_dict[idx]
    # print(len(blockNumber_list))
    # print(len(aave_call_query_dict[0]))
    for key, value in aave_call_query_dict.items():
        market_name = aave_address_list[key][0]

        aave_yields_df = pd.DataFrame(
            {
                "blockNumber": blockNumber_list,
                "result": [
                    (
                        bytes.fromhex("0" * 640)
                        if "result" not in response or response["result"] == "0x"
                        else bytes.fromhex(response["result"][2:])
                    )
                    for response in value
                ],
                # [bytes.fromhex(response['result'][2:]) if 'result' in response or response['result'] == "0x" else bytes.fromhex("0"*64) for response in value]
            }
        )

        # [_, _, _, liquidity_rate, _, _, _, _, _, _] = decoded_output
        # remember that the item on the 4th position in the string is the liquidity rate
        aave_yields_df["liquidityRate"] = aave_yields_df.apply(
            decode_aave_yields, axis=1
        )
        aave_yields_df["depositAPR"] = aave_yields_df["liquidityRate"] / RAY
        aave_yields_df["APY"] = (
            (
                (1 + (aave_yields_df["depositAPR"] / SECONDS_PER_YEAR))
                ** SECONDS_PER_YEAR
            )
            - 1
        ) * 100
        aave_yields_df = time_and_blockno_df.merge(
            aave_yields_df, left_on="blockNumber", right_on="blockNumber"
        )
        aave_yields_df.drop(["result"], axis=1, inplace=True)

        saveData(
            aave_yields_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/StableCoinYields/AAVE",
            f'/Aave_{market_name}_yields_{aave_yields_df.iloc[0]["blockNumber"]}_{aave_yields_df.iloc[-1]["blockNumber"]}.csv',
        )

    return


def aave_yields_new(time_and_blockno_df: pd.DataFrame, block_list: List[int]) -> None:
    blockNumber_list = list(time_and_blockno_df["blockNumber"])
    assert blockNumber_list == block_list, "Mismatch between Df and supplied block_list"

    logging.info("Querying Aave Yields...")
    aave_address_provider = makeContract(
        "******************************************", "ethereum"
    )
    aave_address_provider_poly = makeContract(
        "******************************************", "polygon"
    )
    aave_address_provider_avax = makeContract(
        "******************************************", "avalanche"
    )
    # aave_address_provider_contract = web3.eth.contract(address=aave_address_provider['address'], abi=aave_address_provider['abi'])
    # aave_address_list = aave_address_provider_contract.functions.getAllReservesTokens().call(block_identifier='latest')

    """
    ('WBTC','******************************************'),
    ,('WETH','******************************************')
    """

    aave_address_list = [
        ("USDC", "******************************************"),
        ("USDT", "******************************************"),
        # ("BUSD", "******************************************"),
        ("DAI", "******************************************"),
        ("WETH", "******************************************"),
        ("WBTC", "******************************************"),
        ("TUSD", "******************************************"),
    ]

    aave_address_list_poly = [
        ("USDC", "******************************************"),
        ("USDT", "******************************************"),
        ("DAI", "******************************************"),
        ("WETH", "******************************************"),
        ("WBTC", "******************************************"),
    ]

    aave_address_list_avax = [
        ("USDC", "******************************************"),
        ("USDT", "******************************************"),
        ("DAI", "******************************************"),
        ("WETH", "******************************************"),
        ("WBTC", "******************************************"),
    ]

    aave_query_params = {
        "data_provider_address": aave_address_provider["address"],
        "abi": aave_address_provider["abi"],
        "address_list": aave_address_list,
        "function": "getReserveData(address)",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval': block_interval,
        "block_list": block_list,
    }

    aave_query_params_poly = {
        "data_provider_address": aave_address_provider_poly["address"],
        "address_list": aave_address_list_poly,
        "function": "getReserveData(address)",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval': block_interval,
        "block_list": block_list,
    }
    aave_query_params_avax = {
        "data_provider_address": aave_address_provider_avax["address"],
        "address_list": aave_address_list_avax,
        "function": "getReserveData(address)",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval': block_interval,
        "block_list": block_list,
    }
    underylying_token_decimals = {
        "USDC": 6,
        "USDT": 6,
        "DAI": 18,
        "WETH": 18,
        "WBTC": 8,
        "TUSD": 18,
    }
    contract_details_list = []
    for name_address in aave_address_list:
        addr = name_address[1]
        name = name_address[0]
        contract_details_list.append(
            {
                "contract_address": aave_address_provider["address"],
                "abi": aave_address_provider["abi"],  # Contract ABI
                "attribute_name": "functions",
                "function_name": "getReserveData",
                "method_name": "call",
                "args": [addr],  # Additional arguments for the function
                "address_name": name,
            }
        )

    dataframe_dict = asyncio.run(
        api_key_fetcher(
            contract_details_list=contract_details_list,
            block_list=blockNumber_list,
        )
    )

    for market_name, aave_yields_df in dataframe_dict.items():
        decimal = underylying_token_decimals[market_name]

        aave_yields_df["totalBorrows"] = (
            aave_yields_df["totalStableDebt"] + aave_yields_df["totalVariableDebt"]
        )
        aave_yields_df["totalSupply"] = (
            aave_yields_df["totalBorrows"] + aave_yields_df["availableLiquidity"]
        )

        aave_yields_df["totalSupply"] = aave_yields_df["totalSupply"] / 10**decimal
        aave_yields_df["totalBorrows"] = aave_yields_df["totalBorrows"] / 10**decimal

        aave_yields_df["depositAPR"] = aave_yields_df["liquidityRate"] / RAY
        aave_yields_df["APY"] = (
            (
                (1 + (aave_yields_df["depositAPR"] / SECONDS_PER_YEAR))
                ** SECONDS_PER_YEAR
            )
            - 1
        ) * 100
        aave_yields_df = time_and_blockno_df.merge(
            aave_yields_df, left_on="blockNumber", right_on="blockNumber"
        )

        saveData(
            aave_yields_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/StableCoinYields/AAVE",
            f'/Aave_{market_name}_yields_{aave_yields_df.iloc[0]["blockNumber"]}_{aave_yields_df.iloc[-1]["blockNumber"]}.csv',
        )

    return


def comp_yields(time_and_blockno_df: pd.DataFrame, block_list: List[int]) -> None:
    blockNumber_list = list(time_and_blockno_df["blockNumber"])
    assert blockNumber_list == block_list, "Mismatch between Df and supplied block_list"

    logging.info("Querying Compound Yields...")
    # proxy = makeContract('******************************************', 'ethereum')
    comp_address_provider = makeContract(
        "******************************************", "ethereum"
    )
    # comp_address_provider_contract = web3.eth.contract(address=comp_address_provider['address'], abi=proxy['abi'])
    # cComp_addresses = comp_address_provider_contract.functions.getAllMarkets().call(block_identifier='latest')
    # print(cComp_addresses)
    # print("number of comp markets", len(cComp_addresses))

    comp_market_names = [
        "cBAT",
        "cDAI",
        "cETH",
        "cREP",
        "cUSDC",
        "cUSDT",
        "cWBTC",
        "cZRX",
        "cSAI",
        "cUNI",
        "cCOMP",
        "cWBTC2",
        "cTUSD",
        "cLINK",
        "cMKR",
        "cSUSHI",
        "cAAVE",
        "cYFI",
        "cUSDP",
        "cFEI",
    ]

    cComp_addresses = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
    ]
    comp_market_names = ["cUSDC", "cUSDT", "cDAI", "cETH", "cWBTC", "cTUSD"]

    comp_query_params = {
        "data_provider_address": comp_address_provider["address"],
        "address_list": cComp_addresses,
        "function": "supplyRatePerBlock()",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval':block_interval,
        "block_list": block_list,
    }

    # comp_call_query, _ = query_maker(comp_batch_query_callss, last_block, comp_query_params)
    comp_call_query = query_maker_new(comp_batch_query_callss_new, comp_query_params)
    # process query results
    comp_call_query_dict = process_query_data(comp_call_query, len(cComp_addresses))
    # print(comp_call_query)

    # df = pd.json_normalize(comp_call_query_dict)
    # saveData(df,
    #          f'/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Yield data/NEWPCA/timestampAligned/COMP',
    #          f'/COMP_{start_time}_{end_time}_raw.csv')

    assert len(list(itertools.chain.from_iterable(comp_call_query))) == len(
        cComp_addresses
    ) * len(time_and_blockno_df)

    # i use blocksDF because i know its equal to the length of one aave address should be

    for key, values in comp_call_query_dict.items():
        market_name = comp_market_names[key]
        comp_yields_df = pd.DataFrame(
            {
                "blockNumber": blockNumber_list,
                "result": [
                    (
                        bytes.fromhex("0" * 64)
                        if "result" not in response or response["result"] == "0x"
                        else bytes.fromhex(response["result"][2:])
                    )
                    for response in values
                ],
                # [bytes.fromhex(response['result'][2:]) if 'result' in response or response['result'] == '0x' else bytes.fromhex("0"*64) for response in values]
            }
        )
        comp_yields_df["supplRatePerBlock"] = comp_yields_df.apply(
            decode_comp_yields, axis=1
        )
        comp_yields_df["APY"] = (
            (
                pow(
                    (comp_yields_df["supplRatePerBlock"] / ETHMANTISSA * BLOCKSPERDAY)
                    + 1,
                    DAYSPERYEAR,
                )
            )
            - 1
        ) * 100
        comp_yields_df = time_and_blockno_df.merge(
            comp_yields_df, left_on="blockNumber", right_on="blockNumber"
        )

        comp_yields_df.drop(["result"], axis=1, inplace=True)

        saveData(
            comp_yields_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/StableCoinYields/COMP",
            f'/Compound_{market_name}_yields_{comp_yields_df.iloc[0]["blockNumber"]}_{comp_yields_df.iloc[-1]["blockNumber"]}.csv',
        )
    return


def comp_yields_new(time_and_blockno_df: pd.DataFrame, block_list: List[int]) -> None:
    blockNumber_list = list(time_and_blockno_df["blockNumber"])
    assert blockNumber_list == block_list, "Mismatch between Df and supplied block_list"

    print("Querying Compound Yields...")
    # proxy = makeContract('******************************************', 'ethereum')
    comp_address_provider = makeContract(
        "******************************************", "ethereum"
    )

    comp_market_names = [
        "cBAT",
        "cDAI",
        "cETH",
        "cREP",
        "cREP",
        "cUSDC",
        "cUSDT",
        "cWBTC",
        "cZRX",
        "cSAI",
        "cUNI",
        "cCOMP",
        "cWBTC2",
        "cTUSD",
        "cLINK",
        "cMKR",
        "cSUSHI",
        "cAAVE",
        "cYFI",
        "cUSDP",
        "cFEI",
    ]

    cComp_addresses = [
        ("cUSDC", "******************************************"),
        ("cUSDT", "******************************************"),
        ("cDAI", "******************************************"),
        ("cETH", "******************************************"),
        ("cWBTC", "******************************************"),
        ("cTUSD", "******************************************"),
    ]
    underylying_token_decimals = {
        "cUSDC": 6,
        "cUSDT": 6,
        "cDAI": 18,
        "cETH": 18,
        "cWBTC": 8,
        "cTUSD": 18,
    }
    comp_market_names = ["cUSDC", "cUSDT", "cDAI", "cETH", "cWBTC", "cTUSD"]

    # comp_query_params = {
    #     "data_provider_address": comp_address_provider["address"],
    #     "function_name": "supplyRatePerBlock",
    #     "block_list": block_list,
    #     "contract_address": aave_address_provider["address"],
    #     "abi": aave_address_provider["abi"],  # Contract ABI
    #     "attribute_name": "functions",
    #     "method_name": "call",
    #     "args": addr,  # Additional arguments for the function
    #     "address_name": name,
    # }

    contract_details_list = []
    for name_address in cComp_addresses:
        addr = name_address[1]
        name = name_address[0]
        contract_details_list.extend(
            # {
            #     "contract_address": addr,
            #     # "abi": comp_address_provider["abi"],  # Contract ABI
            #     "abi": makeContract(addr, "ethereum")["abi"],
            #     "function_name": "supplyRatePerBlock",
            #     "attribute_name": "functions",
            #     "method_name": "call",
            #     # "args": addr,  # Additional arguments for the function
            #     "address_name": name,
            # }
            [
                {
                    "contract_address": addr,
                    "abi": makeContract(addr, "ethereum")["abi"],
                    "function_name": "supplyRatePerBlock",
                    "attribute_name": "functions",
                    "method_name": "call",
                    # "args": addr,  # Additional arguments for the function
                    "address_name": name,
                },
                {
                    "contract_address": addr,
                    "abi": makeContract(addr, "ethereum")["abi"],
                    "function_name": "totalSupply",
                    "attribute_name": "functions",
                    "method_name": "call",
                    # "args": addr,  # Additional arguments for the function
                    "address_name": name,
                },
                # {
                #     "contract_address": addr,
                #     "abi": makeContract(addr, "ethereum")["abi"],
                #     "function_name": "totalReserves",
                #     "attribute_name": "functions",
                #     "method_name": "call",
                #     # "args": addr,  # Additional arguments for the function
                #     "address_name": name,
                # },
                {
                    "contract_address": addr,
                    "abi": makeContract(addr, "ethereum")["abi"],
                    "function_name": "totalBorrows",
                    "attribute_name": "functions",
                    "method_name": "call",
                    # "args": addr,  # Additional arguments for the function
                    "address_name": name,
                },
                {
                    "contract_address": addr,
                    "abi": makeContract(addr, "ethereum")["abi"],
                    "function_name": "exchangeRateCurrent",
                    "attribute_name": "functions",
                    "method_name": "call",
                    # "args": addr,  # Additional arguments for the function
                    "address_name": name,
                },
            ]
        )

    dataframe_dict = asyncio.run(
        api_key_fetcher(
            contract_details_list=contract_details_list,
            block_list=blockNumber_list,
        )
    )
    # todo: calculate total supply for each block. backtest historicallt against TheGraph data
    #  find out what needs to be discounted from total supply.

    # compute totalSupply
    for market_name, comp_yields_df in dataframe_dict.items():
        decimals = underylying_token_decimals[market_name]

        comp_yields_df["totalcTokenSupply"] = comp_yields_df["totalSupply"]
        comp_yields_df["totalSupply"] = comp_yields_df["totalcTokenSupply"] * (
            comp_yields_df["exchangeRateCurrent"] / 10 ** (10 + decimals)
        )
        comp_yields_df["APY"] = (
            (
                pow(
                    (comp_yields_df["supplyRatePerBlock"] / ETHMANTISSA * BLOCKSPERDAY)
                    + 1,
                    DAYSPERYEAR,
                )
            )
            - 1
        ) * 100
        comp_yields_df = time_and_blockno_df.merge(
            comp_yields_df, left_on="blockNumber", right_on="blockNumber"
        )

        saveData(
            comp_yields_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/StableCoinYields/COMP",
            f'/Compound_{market_name}_yields_{comp_yields_df.iloc[0]["blockNumber"]}_{comp_yields_df.iloc[-1]["blockNumber"]}.csv',
        )
    return


# async def query_comp_yields(token_contract):
#     return


@backoff.on_exception(backoff.expo, (asyncio.exceptions.TimeoutError), max_tries=5)
async def query_comp_yields(contract_object, block_number: int):
    """
    :param contract_object: a web3.contract instance of the smart contract we want to query data from
    :param address: market address we want to query data from i.e Lyra optimism ETH market address
    :param block_number: block number we want to query data from
    :return: return LyraLiveBoards object
    """
    try:
        comp_result = await contract_object.functions.supplyRatePerBlock().call(
            block_identifier=block_number
        )
    # error we get when we query this function at a block in which data didnt exist
    except web3.exceptions.ContractLogicError:
        return
    except web3.exceptions.BadFunctionCallOutput:
        return
    except eth_abi.exceptions.InsufficientDataBytes:
        return
    return comp_result


async def get_comp_yields(time_and_blockno_df: pd.DataFrame, block_list: List[int]):
    blockNumber_list = list(time_and_blockno_df["blockNumber"])
    assert blockNumber_list == block_list, "Mismatch between Df and supplied block_list"

    print("Querying Compound Yields...")

    web3 = Web3(
        Web3.AsyncHTTPProvider(
            PROVIDER_URL, request_kwargs={"timeout": HTTP_PROVIDER_TIMEOUT}
        ),
        modules={"eth": (AsyncEth,)},
        middlewares=[],
    )

    comp_address_provider = makeContract(
        "******************************************", "ethereum"
    )

    cComp_addresses = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
    ]

    comp_market_names = ["cUSDC", "cUSDT", "cDAI", "cETH", "cWBTC", "cTUSD"]

    comp_query_params = {
        "data_provider_address": comp_address_provider["address"],
        "address_list": cComp_addresses,
        "function": "supplyRatePerBlock()",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval':block_interval,
        "block_list": block_list,
    }

    # comp_call_query, _ = query_maker(comp_batch_query_callss, last_block, comp_query_params)
    comp_call_query = query_maker_new(comp_batch_query_callss_new, comp_query_params)
    # process query results
    comp_call_query_dict = process_query_data(comp_call_query, len(cComp_addresses))
    # print(comp_call_query)

    # df = pd.json_normalize(comp_call_query_dict)
    # saveData(df,
    #          f'/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Yield data/NEWPCA/timestampAligned/COMP',
    #          f'/COMP_{start_time}_{end_time}_raw.csv')

    assert len(list(itertools.chain.from_iterable(comp_call_query))) == len(
        cComp_addresses
    ) * len(time_and_blockno_df)

    # i use blocksDF because i know its equal to the length of one aave address should be

    for key, values in comp_call_query_dict.items():
        market_name = comp_market_names[key]
        comp_yields_df = pd.DataFrame(
            {
                "blockNumber": blockNumber_list,
                "result": [
                    (
                        bytes.fromhex("0" * 64)
                        if "result" not in response or response["result"] == "0x"
                        else bytes.fromhex(response["result"][2:])
                    )
                    for response in values
                ],
                # [bytes.fromhex(response['result'][2:]) if 'result' in response or response['result'] == '0x' else bytes.fromhex("0"*64) for response in values]
            }
        )
        comp_yields_df["supplRatePerBlock"] = comp_yields_df.apply(
            decode_comp_yields, axis=1
        )
        comp_yields_df["APY"] = (
            (
                pow(
                    (comp_yields_df["supplRatePerBlock"] / ETHMANTISSA * BLOCKSPERDAY)
                    + 1,
                    DAYSPERYEAR,
                )
            )
            - 1
        ) * 100
        comp_yields_df = time_and_blockno_df.merge(
            comp_yields_df, left_on="blockNumber", right_on="blockNumber"
        )
        comp_yields_df.drop(["result"], axis=1, inplace=True)

        saveData(
            comp_yields_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/StableCoinYields/COMP",
            f'/Compound_{market_name}_yields_{comp_yields_df.iloc[0]["blockNumber"]}_{comp_yields_df.iloc[-1]["blockNumber"]}.csv',
        )

    return


def aave_stablecoin_tvl(time_and_blockno_df: pd.DataFrame, block_list: List[int]):
    blockNumber_list = list(time_and_blockno_df["blockNumber"])
    assert blockNumber_list == block_list, "Mismatch between Df and supplied block_list"

    print("Querying Aave Yields...")
    aave_address_provider = makeContract(
        "******************************************", "ethereum"
    )

    aave_address_list = [
        ("aUSDC", "******************************************"),
        ("aUSDT", "******************************************"),
        ("aBUSD", "******************************************"),
        ("aDAI", "******************************************"),
    ]

    aave_query_params = {
        "data_provider_address": aave_address_provider["address"],
        "address_list": aave_address_list,
        "function": "totalSupply()",
        "start_block": start_block,
        "end_block": end_block,
        "block_list": block_list,
    }

    # aave_call_query, _ = query_maker(aave_batch_query_callss, last_block, aave_query_params)
    aave_call_query = query_maker_new(
        total_supply_batch_query_call_new, aave_query_params
    )

    aave_call_query_dict = process_query_data(aave_call_query, len(aave_address_list))

    # df = pd.json_normalize(aave_call_query_dict)
    # # saveData(df,
    # #          f'/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Yield data/NEWPCA/AAVE/AVALANCHE',
    # #          f'/Aave_{start_time}_{end_time}_raw.csv')

    # print(aave_call_query_dict)
    print("len(aave_call_query)", len(aave_call_query[0]))
    print(
        "len(aave_address_list)*len(time_and_blockno_df)",
        len(aave_address_list) * len(time_and_blockno_df),
    )
    assert len(aave_call_query[0]) == len(aave_address_list) * len(time_and_blockno_df)

    """currently call query is a list of ALL queries of ALL AAVE markets
    we cunk them up into a list of list with each sublist containing the query result of
    """

    # i use blocksDF because i know its equal to the length of one aave address should be
    # call dict has keys corresponding to the indexes of the addresses in the address list provided
    # recall that our query function arranges queries in the order they are supplied, so aav1,aav2,aav3...
    # we can trust the key value paris we are iterating though are in the same index positions as our address_list
    # address_list[idx] should correspond to aave_call_query_dict[idx]
    print(len(blockNumber_list))
    print(len(aave_call_query_dict[0]))
    for key, value in aave_call_query_dict.items():
        market_name = aave_address_list[key][0]

        divider_dict = {"aUSDC": 6, "aUSDT": 6, "aDAI": 18, "aBUSD": 18}

        aave_yields_df = pd.DataFrame(
            {
                "blockNumber": blockNumber_list,
                "result": [
                    (
                        bytes.fromhex("0" * 640)
                        if "result" not in response or response["result"] == "0x"
                        else bytes.fromhex(response["result"][2:])
                    )
                    for response in value
                ],
                # [bytes.fromhex(response['result'][2:]) if 'result' in response or response['result'] == "0x" else bytes.fromhex("0"*64) for response in value]
            }
        )

        # [_, _, _, liquidity_rate, _, _, _, _, _, _] = decoded_output
        # remember that the item on the 4th position in the string is the liquidity rate
        aave_yields_df["totalSupply"] = aave_yields_df.apply(decode_totalSupply, axis=1)
        aave_yields_df["totalSupply"] = (
            aave_yields_df["totalSupply"] / 10 ** divider_dict[market_name]
        )
        aave_yields_df = time_and_blockno_df.merge(
            aave_yields_df, left_on="blockNumber", right_on="blockNumber"
        )
        aave_yields_df.drop(["result"], axis=1, inplace=True)

        saveData(
            aave_yields_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/TVL/AAVE",
            f'/Aave_{market_name}_TVL_{aave_yields_df.iloc[0]["blockNumber"]}_{aave_yields_df.iloc[-1]["blockNumber"]}.csv',
        )

    return


def comp_stablecoin_tvl(
    time_and_blockno_df: pd.DataFrame, block_list: List[int]
) -> None:
    """double the amount of queries"""

    blockNumber_list = list(time_and_blockno_df["blockNumber"])
    assert blockNumber_list == block_list, "Mismatch between Df and supplied block_list"

    print("Querying Compound Yields...")
    # proxy = makeContract('******************************************', 'ethereum')
    comp_address_provider = makeContract(
        "******************************************", "ethereum"
    )
    cComp_addresses = [
        "******************************************",
        "******************************************",
        "******************************************",
    ]
    comp_market_names = ["cUSDC", "cUSDT", "cDAI"]

    comp_query_params = {
        "data_provider_address": comp_address_provider["address"],
        "address_list": cComp_addresses,
        "function": "totalSupply()",  # 8 decimals
        "start_block": start_block,
        "end_block": end_block,
        "block_list": block_list,
    }

    comp_exchangeRate_query_params = {
        "data_provider_address": comp_address_provider["address"],
        "address_list": cComp_addresses,
        "function": "exchangeRateStored()",  # 8 decimals
        "start_block": start_block,
        "end_block": end_block,
        "block_list": block_list,
    }

    comp_call_query = query_maker_new(comp_batch_query_callss_new, comp_query_params)
    comp_exchangeRate_query = query_maker_new(
        comp_batch_query_callss_new, comp_exchangeRate_query_params
    )

    # process query results
    comp_call_query_dict = process_query_data(comp_call_query, len(cComp_addresses))
    comp_exchangeRate_query_dict = process_query_data(
        comp_exchangeRate_query, len(cComp_addresses)
    )

    assert len(list(itertools.chain.from_iterable(comp_exchangeRate_query))) == len(
        cComp_addresses
    ) * len(time_and_blockno_df)
    assert len(list(itertools.chain.from_iterable(comp_call_query))) == len(
        cComp_addresses
    ) * len(time_and_blockno_df)

    # i use blocksDF because i know its equal to the length of one aave address should be
    for key, values in comp_call_query_dict.items():
        result_list = []
        market_name = comp_market_names[key]
        exchangeRate_values = comp_exchangeRate_query_dict[key]
        divider_dict = {
            "cUSDC": 6,
            "cUSDT": 6,
            "cDAI": 18,
        }

        comp_yields_df = pd.DataFrame(
            {
                "blockNumber": blockNumber_list,
                "result": [
                    (
                        bytes.fromhex("0" * 64)
                        if "result" not in response or response["result"] == "0x"
                        else bytes.fromhex(response["result"][2:])
                    )
                    for response in values
                ],
                "exchangeRate": [
                    (
                        bytes.fromhex("0" * 64)
                        if "result" not in response or response["result"] == "0x"
                        else bytes.fromhex(response["result"][2:])
                    )
                    for response in exchangeRate_values
                ],
                # [bytes.fromhex(response['result'][2:]) if 'result' in response or response['result'] == '0x' else bytes.fromhex("0"*64) for response in values]
            }
        )

        comp_yields_df["result"] = comp_yields_df.apply(decode_comp_tvl, axis=1)
        comp_yields_df["exchangeRate"] = comp_yields_df.apply(decode_comp_eRate, axis=1)
        comp_yields_df["totalSupply"] = (comp_yields_df.result / 1e8) * (
            comp_yields_df.exchangeRate / 10 ** (18 - 8 + divider_dict[market_name])
        )
        comp_yields_df = time_and_blockno_df.merge(
            comp_yields_df, left_on="blockNumber", right_on="blockNumber"
        )
        # comp_yields_df.drop(["result"], axis=1, inplace=True)

        saveData(
            comp_yields_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/TVL/COMP",
            f'/Compound_{market_name}_TVL_{comp_yields_df.iloc[0]["blockNumber"]}_{comp_yields_df.iloc[-1]["blockNumber"]}.csv',
        )
        # saveData(comp_yields_df, f'/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Yield data/NEWPCA/timestampAligned/COMP', f'/Compound_{market_name}_{start_time}_{end_time}_yields.csv')
    return


def query_total_supply(time_and_blockno_df: pd.DataFrame, block_list: List[int]):
    blockNumber_list = list(time_and_blockno_df["blockNumber"])
    assert blockNumber_list == block_list, "Mismatch between Df and supplied block_list"

    print("Querying Tokens Total Supply...")

    address_list = [
        ("USDC", "******************************************"),
        ("USDT", "******************************************"),
        ("BUSD", "******************************************"),
        ("DAI", "******************************************"),
    ]
    # address_list = [('BUSD','******************************************')]

    query_params = {
        "address_list": address_list,
        "function": "totalSupply()",
        "start_block": start_block,
        "end_block": end_block,
        # 'block_interval':block_interval,
        "block_list": block_list,
    }

    call_query = query_maker_new(total_supply_batch_query_call_new, query_params)
    # print(call_query)
    # process query results
    call_query_dict = process_query_data(call_query, len(address_list))
    # print(comp_call_query)

    # df = pd.json_normalize(comp_call_query_dict)
    # saveData(df,
    #          f'/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Yield data/NEWPCA/timestampAligned/COMP',
    #          f'/COMP_{start_time}_{end_time}_raw.csv')

    assert len(list(itertools.chain.from_iterable(call_query))) == len(
        address_list
    ) * len(time_and_blockno_df)

    # i use blocksDF because i know its equal to the length of one aave address should be

    # print(len(blockNumber_list))
    # print(len(call_query_dict[0]))
    for key, value in call_query_dict.items():
        market_name = address_list[key][0]
        divider_dict = {"USDC": 6, "USDT": 6, "DAI": 18, "BUSD": 18}

        total_supply_df = pd.DataFrame(
            {
                "blockNumber": blockNumber_list,
                "result": [
                    (
                        bytes.fromhex("0" * 64)
                        if "result" not in response or response["result"] == "0x"
                        else bytes.fromhex(response["result"][2:])
                    )
                    for response in value
                ],
                # [bytes.fromhex(response['result'][2:]) if 'result' in response or response['result'] == "0x" else bytes.fromhex("0"*64) for response in value]
            }
        )

        # [_, _, _, liquidity_rate, _, _, _, _, _, _] = decoded_output
        # remember that the item on the 4th position in the string is the liquidity rate
        total_supply_df["totalSupply"] = (
            total_supply_df.apply(decode_totalSupply, axis=1)
            / 10 ** divider_dict[market_name]
        )
        total_supply_df = time_and_blockno_df.merge(
            total_supply_df, left_on="blockNumber", right_on="blockNumber"
        )
        total_supply_df.drop(["result"], axis=1, inplace=True)

        saveData(
            total_supply_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/totalSupply/",
            f'/{market_name}_totalSupply_{total_supply_df.iloc[0]["blockNumber"]}_{total_supply_df.iloc[-1]["blockNumber"]}.csv',
        )

    return


def blocks_query_new(block_list):
    print("Starting Block and Timestamp Query..")
    block_query_params = {"block_list": block_list}
    blocks_call_query = query_maker_new(batch_query_blockNo_new, block_query_params)

    # process the query data
    blocks_call_query_dict = process_query_data(blocks_call_query, 1)

    blockNumber_list = []
    timestamp_list = []
    unix_list = []

    # blocks query only contains one "entity" so the block will be in key "0"
    for block in blocks_call_query_dict[0]:
        timestamp = int(block["result"]["timestamp"], base=16)
        unix_list.append(timestamp)
        timee = datetime.fromtimestamp(timestamp)
        timestamp_list.append(timee)
        block_number = int(block["result"]["number"], base=16)
        blockNumber_list.append(block_number)

    time_and_blockno_df = pd.DataFrame(
        {
            "timestamp": timestamp_list,
            "unix": unix_list,
            "blockNumber": blockNumber_list,
        }
    )
    print(time_and_blockno_df)
    print("Number of Blocks:", len(blockNumber_list))
    print("Finished Block Query")

    return time_and_blockno_df


def process_query_data(
    query_list: str, number_of_entities: int
) -> Dict[int, List[str]]:
    """funtion takes in a list of list of queries i.e [[eth1,eth2,eth3,aave1,aave2,aave3,uni1,uni2,uni3], [eth4,eth5,aave4,aave5,uni4,uni5], [eth6,aave6,uni6]]
    and returns them in a sorted orderly fashion [[eth1,eth2,eth3,eth4,eth5,eth6], [aave1,aave2,aave3,aave4,aave5,aave6], [uni,uni,uni,uni,uni,uni]]
    """
    dict_keys = [i for i in range(number_of_entities)]
    dict_sort = {key: [] for key in dict_keys}
    import itertools

    for sublist in query_list:
        split = np.array_split(sublist, number_of_entities)
        for idx, splitted in enumerate(split):
            dict_sort[idx].append(splitted)

    for key, value in dict_sort.items():
        dict_sort[key] = list(itertools.chain.from_iterable(value))
        # dict_sort[key] = np.concatenate(value).flat

    return dict_sort


def plotYields(project):
    import plotly.graph_objects as go

    directory_in_str = f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{project}"
    pathlist = Path(directory_in_str).glob("**/*.csv")
    fig = go.Figure()

    # print(pathlist)
    for path in pathlist:
        path_in_str = str(path)
        filename = path_in_str.split("/")[11].split(".")[0]
        print(filename)

        project, token, _, startblock, endblock = filename.split("_")
        print(token)
        df = pd.read_csv(path_in_str)

        fig.add_trace(go.Scatter(x=df["timestamp"], y=df["APY"], name=filename))
    fig.update_layout(title=project, xaxis_title="Timestamp", yaxis_title="APY (%)")
    fig.show()
    return


def decode_aave_yields(row):
    # [_, _, _, liquidity_rate, _, _, _, _, _, _] = decoded_output
    # remember that the item on the 4th position in the string is the liquidity rate
    decoded = abi.decode(
        [
            "uint256",
            "uint256",
            "uint256",
            "uint256",
            "uint256",
            "uint256",
            "uint256",
            "uint256",
            "uint256",
            "uint40",
        ],
        row["result"],
    )[3]
    return decoded


def decode_comp_tvl(row):
    decoded = abi.decode(["uint256"], row["result"])[0]
    return decoded


def decode_comp_eRate(row):
    decoded = abi.decode(["uint256"], row["exchangeRate"])[0]
    return decoded


def decode_aave_tvl(row):
    decoded = abi.decode(["uint256"], row["result"])[0]
    return decoded


def decode_totalSupply(row):
    decoded = abi.decode(["uint256"], row["result"])[0]
    return decoded


def decode_comp_yields(row):
    decoded = abi.decode(["uint256"], row["result"])[0]
    return decoded


def decode_dopex_volatility(row):
    decoded = abi.decode(["uint256"], row)[0]
    return decoded


def days_to_block(days: int):
    # 7200
    return blocks_per_day * days


def split_liquidation():
    return


def get_lyra_options(timestamp):
    INFURA_KEY = "********************************"
    chain_list = ["arbitrum", "optimism"]
    protocol_version = "newport"
    for chain in chain_list:
        print(chain)
        # optimism_infura = f"https://optimism-mainnet.infura.io/v3/{INFURA_KEY}"
        # PROVIDER_URL = optimism_infura

        web3 = SYNC_WEB3_DICT[chain]
        # web3.middleware_onion.inject(geth_poa_middleware, layer=0)

        snapshot_timestamp = timestamp
        unix_day = 86400
        unix_year = 365 * 86400

        query_block = get_block_by_timestamp(timestamp, chain)
        print(f"{chain} block_number", query_block)

        market_viewer = LYRA_MARKET_VIEWER_ADDRESSES[chain][protocol_version]["address"]
        eth_address = MARKET_ADDRESSES["lyra"][chain][protocol_version]["ETH"][
            "address"
        ]
        btc_address = MARKET_ADDRESSES["lyra"][chain][protocol_version]["BTC"][
            "address"
        ]

        # market_viewer_object = makeContract(market_viewer, chain)

        market_viewer_contract = web3.eth.contract(
            web3.to_checksum_address(market_viewer),
            abi=LYRA_MARKET_VIEWER_ADDRESSES[chain]["newport"]["abi"],
        )

        structure = [
            {"Token": "ETH", "Address": web3.to_checksum_address(eth_address)},
            {"Token": "BTC", "Address": web3.to_checksum_address(btc_address)},
        ]

        for market_dict in structure:
            token = market_dict["Token"]

            web3 = SYNC_WEB3_DICT[chain]
            address = market_dict["Address"]
            BOARDS = market_viewer_contract.functions.getLiveBoards(address).call(
                block_identifier=int(query_block)
            )
            # print(BOARDS)
            price = get_chainlink_price(token, snapshot_timestamp)
            options_dict = {
                "snapshot_timestamp": snapshot_timestamp,
                "token": token,
                "price": price,
                "boards": [],
            }
            # print(BOARDS[0])
            for boardObject in BOARDS:
                if protocol_version == "newport":
                    (
                        market,
                        boardID,
                        expiry,
                        baseIV,
                        priceAtExpiry,
                        isPaused,
                        varianceGwavIv,
                        forceCloseGwavIV,
                        longScaleFactor,
                        netGreeks,
                        strikeView,
                    ) = boardObject
                elif protocol_version == "avalon":
                    (
                        market,
                        boardID,
                        expiry,
                        baseIV,
                        priceAtExpiry,
                        isPaused,
                        forceCloseGwavIV,
                        netGreeks,
                        strikeView,
                    ) = boardObject

                else:
                    raise ValueError(
                        f"Lyra {chain} does not have a {protocol_version} version"
                    )

                (
                    netDelta,
                    netStdVeg,
                    netOptionValue,
                ) = netGreeks

                date = datetime.fromtimestamp(expiry)
                date = date.strftime("%d%b%y").upper()
                time_to_expiry = expiry - snapshot_timestamp
                if date[0] == "0":
                    date = date[1:]
                temp_dict = {
                    "underlying_index": token + "-" + date,
                    "unix_expiry": expiry,
                    "expiry_date": f"{datetime.fromtimestamp(expiry)}",
                    "unix_time_to_expiry": time_to_expiry,
                    "day_time_to_expiry": time_to_expiry / unix_day,
                    "year_time_to_expiry": time_to_expiry / unix_year,
                    "boardID": boardID,
                    "baseIV": baseIV / ETHMANTISSA,
                    "strikes": {},
                }
                for strikeObject in strikeView:
                    (
                        strikeID,
                        boardID,
                        strikePrice,
                        skew,
                        forceCloseSkew,
                        strikeGreeks,
                        baseReturnedRatio,
                        longCallOpenInterest,
                        longPutOpenInterest,
                        shortCallBaseOpenInterest,
                        shortCallQuoteOpenInterest,
                        shortPutOpenInterest,
                    ) = strikeObject
                    callDelta, putDelta, stdVega, callPrice, putPrice = strikeGreeks
                    # strikePrice = strikePrice/ETHMANTISSA

                    # change qualified name
                    qualified_name = (
                        f"lyraOptimism.option.{temp_dict['underlying_index']}"
                    )
                    temp_temp_dict = {
                        f"{strikePrice / ETHMANTISSA}": {
                            "strikeId": strikeID,
                            "callPrice": callPrice / ETHMANTISSA,
                            "putPrice": putPrice / ETHMANTISSA,
                            "skew": skew / ETHMANTISSA,
                            "queried_iv": (baseIV / ETHMANTISSA) * (skew / ETHMANTISSA),
                            "longCallOpenInterest": longCallOpenInterest / ETHMANTISSA,
                            "longPutOpenInterest": longPutOpenInterest / ETHMANTISSA,
                            "shortCallBaseOpenInterest": shortCallBaseOpenInterest
                            / ETHMANTISSA,
                            "shortCallQuoteOpenInterest": shortCallQuoteOpenInterest
                            / ETHMANTISSA,
                            "shortPutOpenInterest": shortPutOpenInterest / ETHMANTISSA,
                        }
                    }
                    temp_dict["strikes"].update(temp_temp_dict)
                options_dict["boards"].append(temp_dict)
            save_json(
                options_dict,
                "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/hello_hoj/tests",
                f"/{chain}-{token}_{snapshot_timestamp}_snapshot.json",
            )
            save_json(
                options_dict,
                f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/Lyra",
                f"/{chain}-{token}_{snapshot_timestamp}_snapshot.json",
            )

    return


def get_chainlink_price(token, timestamp):
    INFURA_KEY = "********************************"
    optimism_infura = f"https://optimism-mainnet.infura.io/v3/{INFURA_KEY}"
    PROVIDER_URL = optimism_infura
    web3 = Web3(Web3.HTTPProvider(PROVIDER_URL))
    web3.middleware_onion.inject(geth_poa_middleware, layer=0)

    # Price Feed address
    if token == "ETH":
        address = "******************************************"
    elif token == "BTC":
        address = "******************************************"

    # Change this to use your own RPC URL
    # AggregatorV3Interface ABI
    abi = '[{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"description","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint80","name":"_roundId","type":"uint80"}],"name":"getRoundData","outputs":[{"internalType":"uint80","name":"roundId","type":"uint80"},{"internalType":"int256","name":"answer","type":"int256"},{"internalType":"uint256","name":"startedAt","type":"uint256"},{"internalType":"uint256","name":"updatedAt","type":"uint256"},{"internalType":"uint80","name":"answeredInRound","type":"uint80"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"latestRoundData","outputs":[{"internalType":"uint80","name":"roundId","type":"uint80"},{"internalType":"int256","name":"answer","type":"int256"},{"internalType":"uint256","name":"startedAt","type":"uint256"},{"internalType":"uint256","name":"updatedAt","type":"uint256"},{"internalType":"uint80","name":"answeredInRound","type":"uint80"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"}]'

    block = get_block_by_timestamp_optimism(timestamp)

    print(token)
    # Set up contract instance
    contract = web3.eth.contract(address=web3.to_checksum_address(address), abi=abi)
    # Make call to latestRoundData()
    latestData = contract.functions.latestRoundData().call(block_identifier=block)
    print(latestData)

    _, price, _, _, _ = latestData

    return price / 1e8


@backoff.on_exception(backoff.expo, (asyncio.exceptions.TimeoutError), max_tries=5)
async def get_blocks(block_number):
    block = await web3.eth.get_block(block_identifier=block_number)

    return block


# @backoff.on_exception(backoff.expo, (asyncio.exceptions.TimeoutError), max_tries=5)
# async def get_beacon_chain_withdrawals(start_block):
#     # print('hello')
#     state = load_json_file(
#         "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/withdrawals.json"
#     )
#     end_block = state["last_scanned_block"]
#     assert start_block > end_block, "start block must be in the future"
#     print("last_scanned_block", end_block, f"to {start_block}")
#     print(start_block, end_block)
#     blocks_state = state["blocks"]
#
#     tasks = []
#     for block_number in range(end_block + 1, start_block + 1):
#         tasks.append(asyncio.ensure_future(get_blocks(block_number)))
#         await asyncio.sleep(INFURA_AWAIT)
#     st = time.time()
#     print("sending requests")
#     block_results = await asyncio.gather(*tasks)
#     print(f"finished sending requests, {time.time() - st}")
#
#     for block in block_results:
#         block_number = block["number"]
#         timestamp = block["timestamp"]
#         withdrawals = [dict(withdrawal) for withdrawal in block["withdrawals"]]
#         baseFeePerGas_wei = int(block["baseFeePerGas"])
#         gasUsed = int(block["gasUsed"])
#         num_txs = len(block["transactions"])
#
#         blocks_state[block_number] = {
#             "timestamp": timestamp,
#             "withdrawals": withdrawals,
#             "baseFeePerGas_wei": baseFeePerGas_wei,
#             "gasUsed": gasUsed,
#             "num_txs": num_txs,
#         }
#         last_scanned_block = block_number
#
#     state["last_scanned_block"] = last_scanned_block
#     state["blocks"] = blocks_state
#     save_json(
#         state,
#         "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain",
#         f"/withdrawals.json",
#     )
#
#     return state


#
# API_KEY_DICT = {
#     # 0: '********************************',  # ah.madkida
#     # 1: '********************************',  # <EMAIL>
#     # 2: '9f9ce9cbc3a240b4bf3db0a30647f64f',  # <EMAIL> - used in staging
#     # 3: '********************************',  # <EMAIL> - defi report
#     # 4: '89ca8453198d4c5d94416f00827c97b3',  # <EMAIL>
#     # 5: '4b5d28f970774d13b2f05715774a0d1c',  # andrew 1
#     6: "********************************",  # andrew 2
#     7: "89ca8453198d4c5d94416f00827c97b3",
# }


@backoff.on_exception(
    backoff.expo, (asyncio.exceptions.TimeoutError, HTTPError), max_tries=5
)
async def get_beacon_chain_withdrawals_tester(start_block):
    withdrawals_df = (
        pl.scan_parquet(
            "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/withdrawals.parquet"
        )
        .sort(by="blocks")
        .with_columns(
            [
                pl.col("blocks").cast(pl.Int64),
                pl.col("timestamp").cast(pl.Int64),
                pl.col("baseFeePerGas_wei").cast(pl.Int64),
                pl.col("gasUsed").cast(pl.Int64),
                pl.col("num_txs").cast(pl.Int64),
            ]
        )
    ).collect()

    end_block = int(withdrawals_df.select("blocks").max()["blocks"][0])
    print(end_block)
    assert start_block >= end_block, "start block must be in the future"
    print("last_scanned_block", end_block, f"to {start_block}")
    print(start_block, end_block)

    tasks = []
    for block_number in range(end_block + 1, start_block + 1):
        tasks.append(asyncio.ensure_future(get_blocks(block_number)))
        await asyncio.sleep(INFURA_AWAIT)
    print("total block number requests", len(tasks))
    st = time.time()
    print("sending requests")
    block_results = await asyncio.gather(*tasks)
    print(f"finished sending requests, {time.time() - st}")

    rows = []
    for block in block_results:
        block_number = block["number"]
        timestamp = block["timestamp"]
        withdrawals = [dict(withdrawal) for withdrawal in block["withdrawals"]]
        baseFeePerGas_wei = int(block["baseFeePerGas"])
        gasUsed = int(block["gasUsed"])
        num_txs = len(block["transactions"])

        rows.append(
            {
                "blocks": block_number,
                "timestamp": timestamp,
                "withdrawals": withdrawals,
                "baseFeePerGas_wei": baseFeePerGas_wei,
                "gasUsed": gasUsed,
                "num_txs": num_txs,
            }
        )

    ordered_columns = withdrawals_df.columns

    new_withdrawals = pl.DataFrame(rows).select(*ordered_columns)
    full_withdrawals = pl.concat([withdrawals_df, new_withdrawals]).unique(
        subset=["blocks"]
    )

    full_withdrawals.write_parquet(
        "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/withdrawals.parquet",
        use_pyarrow=True,
    )
    # print(f"withdrawal data_processing took {time.time()-dp}]")

    return


def process_withdrawals_to_df():
    state = load_json_file(
        "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/withdrawals.json"
    )
    blocks_state = state["blocks"]

    row_list = []
    partial = 0
    full_withdrwal = 0
    partials_list = []
    full_list = []
    timestamp_list = []

    for block_number, block_data in blocks_state.items():
        block_withdrawals = block_data["withdrawals"]
        timestamp = block_data["timestamp"]
        block_withdrawal_sum = 0

        for withdrawal in block_data["withdrawals"]:
            amount = int(withdrawal["amount"], 0) / 1e9
            block_withdrawal_sum += amount
            if amount > 7:
                full_withdrwal += amount
                full_list.append(amount)
                partials_list.append(0)

            else:
                partial += amount
                partials_list.append(amount)
                full_list.append(0)

            timestamp_list.append(datetime.fromtimestamp(timestamp))

        data = {
            "Date": datetime.fromtimestamp(timestamp),
            "Amount": block_withdrawal_sum,
        }
        row_list.append(data)

    print(len(timestamp_list))
    print(len(full_list))
    print(len(partials_list))

    full_partial_df = pd.DataFrame(
        {
            "Date": timestamp_list,
            "Partial Withdrawals": partials_list,
            "Full withdrawals": full_list,
        }
    )
    return


async def run_ethereum_async_collections(
    start_block: int,
    end_date: datetime,
    start_date: datetime,
    withdrawals_path: str,
    beacon_state_output_filepath: str,
):
    task1 = get_block_params_fast(
        start_block=None,
        end_block=int(start_block),
        filepath=withdrawals_path,
        process_data_func=process_block_results_withdrawals,
    )
    task2 = grab_beacon_chain_state(
        output_filepath=beacon_state_output_filepath,
        start_date=end_date.strftime("%Y-%m-%d"),
        end_date=start_date.strftime("%Y-%m-%d"),
        slots_per_day=1,
        requests_per_time_period=1,
        time_period=20,
    )
    # Run both tasks concurrently
    await asyncio.gather(task1, task2)
    # await asyncio.gather(task1)
    # await asyncio.gather(task2)


if __name__ == "__main__":
    utils_general.setup_python_logger(level="INFO")
    logging.info("initialising")
    ALCHEMY_KEY = os.environ.get("ALCHEMY_KEY")
    # INFURA_KEY = os.environ.get("INFURA_API_KEY")
    # INFURA_KEY = "********************************"
    # INFURA_KEY = "********************************"

    INFURA_KEY = "********************************"  # ah.madkida

    # INFURA_KEY = '********************************'  # <EMAIL> - used in staging

    THEGRAPH_API_KEY = os.environ.get("THEGRAPH_API_KEY")
    ARBITRUM_API_KEY = os.environ.get("ARBITRUM_API_KEY")

    # increment for every request you send to keep track of requests
    global REQUESTS
    """:toDo
        create list with all the RPC URL's you have
        so you can loop through them.
    """

    """##################################################################### initialisation section ############################################################################################"""

    global HTTP_PROVIDER_TIMEOUT
    HTTP_PROVIDER_TIMEOUT = os.environ.get("HTTP_PROVIDER_TIMEOUT")
    ETHERSCAN_API_KEY = os.environ.get("ETHERSCAN_API_KEY")
    SNOWTRACE_API_KEY = "**********************************"
    POLYSCAN_API_KEY = "AUR2PRBIDKW7WN9W93I2CFCYD6W2IIKZ8F"
    alchemy = f"https://eth-mainnet.g.alchemy.com/v2/{ALCHEMY_KEY}"
    avalanche_infura = f"https://avalanche-mainnet.infura.io/v3/{INFURA_KEY}"
    ethereum_infura = f"https://mainnet.infura.io/v3/{INFURA_KEY}"
    optimism_infura = f"https://optimism-mainnet.infura.io/v3/{INFURA_KEY}"

    start_date = datetime(2025, 6, 13, 12, 0, 0, tzinfo=timezone.utc)
    end_date = start_date - timedelta(days=30)

    start_time = int(start_date.timestamp())
    end_time = int(end_date.timestamp())

    start_block = get_block_by_timestamp(start_time, "ethereum")
    end_block = get_block_by_timestamp(end_time, "ethereum")

    logging.info("starting beacon chain withdrawals")
    withdrawals_path = "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/withdrawals.parquet"
    beacon_state_output_filepath = "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/beacon_chain_data.parquet"
    # asyncio.get_event_loop().run_until_complete(
    #     run_ethereum_async_collections(
    #         start_block=int(start_block),
    #         # start_block=22576211,
    #         end_date=end_date,
    #         start_date=start_date,
    #         withdrawals_path=withdrawals_path,
    #         beacon_state_output_filepath=beacon_state_output_filepath,
    #     )
    # )
    # import sys

    # sys.exit()

    # withdrawals_path = "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/withdrawals.parquet"
    # asyncio.get_event_loop().run_until_complete(
    #     get_block_params_fast(
    #         None,
    #         int(start_block),
    #         # 22224514,
    #         withdrawals_path,
    #         process_block_results_withdrawals,
    #     )
    # )
    # asyncio.get_event_loop().run_until_complete(
    #     grab_beacon_chain_state(
    #         output_filepath="/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/beacon_chain_data.parquet",
    #         start_date=end_date.strftime(
    #             "%Y-%m-%d"
    #         ),
    #         end_date=start_date.strftime("%Y-%m-%d"),
    #         slots_per_day=2,
    #         requests_per_time_period=1,
    #         time_period=20,
    #     )
    # )
    # logging.info("done fetching withdrawals")

    # sys.exit()

    # INFURA_KEY = "********************************"
    # INFURA_KEY = "********************************"
    # INFURA_KEY = "********************************"  # <EMAIL>
    ethereum_infura = f"https://mainnet.infura.io/v3/{INFURA_KEY}"

    PROVIDER_URL = ethereum_infura

    web3 = Web3(Web3.HTTPProvider(PROVIDER_URL))

    # print(list(web3.eth.get_block(block_identifier='latest').keys()))

    days_per_year = 365
    RAY = 10**27
    SECONDS_PER_YEAR = 31536000
    ETHMANTISSA = 1e18
    BLOCKSPERDAY = 7200  # 15 seconds per block
    # BLOCKSPERDAY = 5000
    DAYSPERYEAR = 365
    PAST = True

    # aggregate_positions()

    """:exception
        rem that start_time must always be
        greater than end_time"""

    logging.info(f"Start Block: {start_block}")
    logging.info(f"End Block: {end_block}")

    # print(web3.eth.get_block(int(start_block)))

    MAX_SIZE = 100000
    interval = 24  # in hours (24 = daily interval)
    sample_frequency_per_day = int(24 / interval)
    #
    global loop

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # blocks_per_day = 6750
    # blocks_per_day = 5000
    # block_interval = round(blocks_per_day/sample_frequency_per_day)
    # print('Block Interval', block_interval)
    # last_block = start_block

    # start_time = 1669766400
    # end_time = 1632265200
    # start_block = get_block_by_timestamp_avalanche(start_time)
    # end_block = get_block_by_timestamp_avalanche(end_time)
    #
    # print("Start Block:", start_block)
    # print("End Block:", end_block)
    # max_size = 100000
    # interval = 24 #in hours (24 = daily interval)
    # sample_frequency_per_day = int(24/interval)
    # blocks_per_day = 6750
    # blocks_per_day = 40975
    # block_interval = round(blocks_per_day/sample_frequency_per_day)
    # print('Block Interval', block_interval)
    # last_block = start_block

    # start_block = get_block_by_timestamp_avalanche(1669766400)
    # end_block = get_block_by_timestamp_avalanche(1669161600)
    # print(start_block)
    # print(end_block)
    # print((start_block-end_block)/7)

    """##################################################################### end of initialisation section ############################################################################################"""

    """##################################################################### start of query section ############################################################################################"""

    "lyra"
    #
    # get_lyra_options(start_time)
    # import sys
    # sys.exit()
    """Datagrabber"""
    # eth spot price
    currencies = ["ETH", "BTC"]
    for curr in currencies:
        spot_price_params = [
            {
                "entity_type": "timeseries",
                "aws_stage": "dev",
                "fields": [],
                "limit": 100_000_000,
                "order_by": {"ascending": "timestamp"},
                "filter": {
                    "AND": [
                        {
                            "IN": {
                                "field": "qualified_name",
                                "value": [f"blockscholes.spot.{curr}_USD.1h.index.px"],
                            }
                        },
                        {
                            "BETWEEN": {
                                "field": "timestamp",
                                "start": (end_time - (1209600 + 86400)) * 1e9,
                                "end": start_time * 1e9,
                                # "start": end_time * 1e9,
                                # "end": start_time * 1e9,
                            }
                        },
                    ]
                },
            }
        ]
        eth_spot_price_df = pd.DataFrame.from_dict(datagrabber.grab(spot_price_params))
        saveData(
            eth_spot_price_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/{curr}_spot",
            f"/{curr}_spot_{start_time}_{end_time}.csv",
        )

    logging.info("Finished grabbing spot")
    # sys.exit()

    # currency_list = ["ETH", "BTC"]
    # exchange_list = ["deribit", "aevo"]
    # for exchange in exchange_list:
    #     for currency in currency_list:
    #         option_catalog_params = [
    #             {
    #                 "entity_type": "timeseries",
    #                 "aws_stage": "dev",
    #                 "fields": [],
    #                 "limit": 1000,
    #                 "order_by": {"ascending": "timestamp"},
    #                 "filter": {
    #                     "AND": [
    #                         {
    #                             "IN": {
    #                                 "field": "qualified_name",
    #                                 "value": [
    #                                     f"v-00003.{exchange}.option.{currency}.SABR.1h.params"
    #                                 ],
    #                             }
    #                         },
    #                         {
    #                             "BETWEEN": {
    #                                 "field": "timestamp",
    #                                 "start": start_time * 1e9,
    #                                 "end": start_time * 1e9,
    #                             }
    #                         },
    #                     ]
    #                 },
    #             }
    #         ]
    #         deribit_options = datagrabber.grab(option_catalog_params)
    #         print(deribit_options)
    #         save_json(
    #             deribit_options[0],
    #             f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/{exchange.capitalize()}",
    #             f"{exchange}-{currency}_{start_time}_snapshot.json",
    #         )

    logging.info("finished querying lyra")

    """query timestamp from  block """
    # chain = "ethereum"
    # eth_blocks, timestamp_block_dict = asyncio.run(
    #     async_create_blocklist_from_timestamp(
    #         start_time, end_time, "hour", async_get_block_by_timestamp, chain
    #     )
    # )
    # block_df = pd.DataFrame({"blocks": eth_blocks})
    # saveData(
    #     block_df,
    #     f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/blocks",
    #     f"/ethblocks_{start_time}_{end_time}.csv",
    # )
    #
    # time_and_blockno_df = pd.DataFrame(
    #     list(timestamp_block_dict.items()), columns=["unix", "blockNumber"]
    # )
    # time_and_blockno_df["timestamp"] = pd.to_datetime(
    #     time_and_blockno_df["unix"], unit="s"
    # )
    # time_and_blockno_df.set_index("timestamp", inplace=True)
    #
    # saveData(
    #     time_and_blockno_df,
    #     f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/blocks",
    #     f"/timestamp_block{start_time}_{end_time}.csv",
    # )

    """AAVE"""

    # time_and_blockno_df = pd.read_csv(
    #     f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/blocks/timestamp_block{start_time}_{end_time}.csv"
    # )
    # eth_blocks = list(
    #     pd.read_csv(
    #         f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/blocks/ethblocks_{start_time}_{end_time}.csv"
    #     )["blocks"]
    # )

    # comp_yields_new(time_and_blockno_df, eth_blocks)
    # time.sleep(30)
    # aave_yields_new(time_and_blockno_df, eth_blocks)
    # import sys
    #
    # sys.exit()

    # # #     #aave_stablecoin_tvl(time_and_blockno_df, eth_blocks)
    #
    # # #
    #
    # # #
    # # #     # plotYields('COMP')
    # # #     # plotYields('AAVE')
    # # #
    # """POSITIONS and LIQUIDATION QUERY"""
    logging.info("Fetching Liquidations and Positions data...")
    #
    # timetravel_query(time_and_blockno_df, 'Aave')
    time_and_blockno_df = pd.read_csv(
        f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/blocks/timestamp_block{start_time}_{end_time}.csv"
    )
    eth_blocks = list(
        pd.read_csv(
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/blocks/ethblocks_{start_time}_{end_time}.csv"
        )["blocks"]
    )
    # # #

    protocols = ["Compound", "Aave"]
    for protocol in protocols:
        logging.info(f"{protocol} query")
        position_query_dict = positions_query(start_time, end_time, protocol)
        liquidation_query_df = liquidations_query(start_time, end_time, protocol)
        for key, df in position_query_dict.items():
            saveData(
                df,
                f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/PostitionData/{protocol}",
                f"/{protocol}_{key}_{start_time}_{end_time}.csv",
            )
        saveData(
            liquidation_query_df,
            f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/LiquidationData/{protocol}",
            f"/{protocol}_liquidations_{start_time}_{end_time}.csv",
        )

    # for protocol in protocols:
    #     logging.info(f"{protocol}")
    #     TVL_query_dict = timetravel_query(time_and_blockno_df, protocol)
    #     for key, df in TVL_query_dict.items():
    #         saveData(
    #             df,
    #             f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/TVL/{protocol}",
    #             f"/{protocol}_{key}_{start_time}_{end_time}.csv",
    #         )

    """Query Uniswap"""

    freq = time_and_blockno_df["unix"][0] - time_and_blockno_df["unix"][1]
    n_hours = freq / 3600
    v3_df = uniswap_volume_query(time_and_blockno_df)
    saveData(
        v3_df,
        f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/Volume/Uniswap",
        f"/Uniswap_{n_hours}H_volumeUSD_{start_time}_{end_time}.csv",
    )

    # #     #################################################### COMPOUND ####################################################
    # #
    # #     # print("sleeping for 30 seconds")

    """Compound"""
    # print("querying compound")
    # time_and_blockno_df = pd.read_csv(
    #     f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/blocks/timestamp_block{start_time}_{end_time}.csv"
    # )
    # eth_blocks = list(
    #     pd.read_csv(
    #         f"/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/blocks/ethblocks_{start_time}_{end_time}.csv"
    #     )["blocks"]
    # )

    # comp_stablecoin_tvl(time_and_blockno_df, eth_blocks)
    # time.sleep(240)
    # comp_yields_new(time_and_blockno_df, eth_blocks)

    """ EventScanner"""
    # PROVIDER_URL = ethereum_infura
    # time.sleep(240)
    # run(1, 2)

    """ GET WITHDRAWALS ON A BLOCK BY BLOCK BASIS"""
    # PROVIDER_URL = ethereum_infura
    # web3 = Web3(
    #     Web3.AsyncHTTPProvider(
    #         PROVIDER_URL, request_kwargs={"timeout": HTTP_PROVIDER_TIMEOUT}
    #     ),
    #     modules={"eth": (AsyncEth,)},
    #     middlewares=[],
    # )
    # withdrawal_results = get_beacon_chain_withdrawals(17037254)
    # withdrawals_df = process_beacon_chain_withdrawals()

    # saveData(liquidation_query_df, f'/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Defi_update/{datetime.fromtimestamp(start_time)}_{datetime.fromtimestamp(end_time)}/LiquidationData/{protocol}', f"/{protocol}_liquidations_{start_time}_{end_time}.csv" )

# ################################################### DOPEX ####################################################
#
#     ''' redefine web3 with arbiturm URL
#         redefining start and stop times for blocks'''
#
#     ARBITRUM_URL = 'https://arbitrum-mainnet.infura.io/v3/********************************'
#
#     PROVIDER_URL = ARBITRUM_URL
#     web3 = Web3(Web3.HTTPProvider(PROVIDER_URL))
#     print(web3.isConnected())
#
#     '''
#     redefine web3 with arbiturm URL
#     redefining start and stop times for blocks
#     '''
#
#     dopex_weekly_implied_volatilities()
#
#     block_list, timestamp_list = cr
