"""A stateful event scanner for Ethereum-based blockchains using Web3.py.
With the stateful mechanism, you can do one batch scan or incremental scans,
where events are added wherever the scanner left off.
"""

import datetime
from abc import ABC, abstractmethod
from typing import Iterable

import pandas as pd
from eth_abi.codec import ABICodec
from web3._utils.events import get_event_data

# Currently this method is not exposed over official web3 API,
# but we need it to construct eth_getLogs parameters
from web3._utils.filters import construct_event_filter_params
from web3.contract import Contract
from web3.datastructures import AttributeDict
from web3.exceptions import BlockNotFound
from web3.middleware import geth_poa_middleware

from Library import *

logger = logging.getLogger(__name__)


class EventScannerState(ABC):
    """Application state that remembers what blocks we have scanned in the case of
    →crash.
    """

    @abstractmethod
    def get_last_scanned_block(self) -> int:
        """Number of the last block we have scanned on the previous cycle.
        :return: 0 if no blocks scanned yet
        """

    @abstractmethod
    def start_chunk(self, block_number: int, chunk_size: int):
        """<PERSON><PERSON><PERSON> is about to ask data of multiple blocks over JSON-RPC.
        Start a database session if needed.
        """

    @abstractmethod
    def end_chunk(self, block_number: int):
        """Scanner finished a number of blocks.
        Persistent any data in your state now.
        """

    @abstractmethod
    def process_event(
        self, block_when: datetime.datetime, event: AttributeDict
    ) -> object:
        """Process incoming events.
        This function takes raw events from Web3, transforms them to your application
        ˓→internal
        format, then saves them in a database or some other state.
        :param block_when: When this block was mined
        :param event: Symbolic dictionary of the event data
        :return: Internal state structure that is the result of event tranformation.
        """

    @abstractmethod
    def delete_data(self, since_block: int) -> int:
        """Delete any data since this block was scanned.
        Purges any potential minor reorg data.
        """


class EventScanner:
    """Scan blockchain for events and try not to abuse JSON-RPC API too much.
    Can be used for real-time scans, as it detects minor chain reorganisation and
    ˓→rescans.
    Unlike the easy web3.contract.Contract, this scanner can scan events from
    ˓→multiple contracts at once.
    For example, you can get all transfers from all tokens in the same scan.
    You *should* disable the default `http_retry_request_middleware` on your provider
    ˓→for Web3,
    because it cannot correctly throttle and decrease the `eth_getLogs` block number
    ˓→range.
    """

    def __init__(
        self,
        web3: Web3,
        contract: Contract,
        state: EventScannerState,
        events: List,
        filters: {},
        api_keys: List[str] = None,
        base_provider_url: str = "https://mainnet.infura.io/v3/",
        max_chunk_scan_size: int = 8_000,
        max_request_retries: int = 30,
        request_retry_seconds: float = 3.0,
        blocks_per_api_key: int = 10_000,  # NEW: Maximum blocks per API key
    ):
        """
        :param contract: Contract
        :param events: List of web3 Event we scan
        :param filters: Filters passed to getLogs
        :param api_keys: List of API keys to rotate through
        :param base_provider_url: Base URL for the provider (before API key)
        :param max_chunk_scan_size: JSON-RPC API limit in the number of blocks we
        ˓→query. (Recommendation: 10,000 for mainnet, 500,000 for testnets)
        :param max_request_retries: How many times we try to reattempt a failed JSON-
        ˓→RPC call
        :param request_retry_seconds: Delay between failed requests to let JSON-RPC
        ˓→server to recover
        """

        self.logger = logger
        self.contract = contract
        self.web3 = web3
        self.state = state
        self.events = events
        self.filters = filters

        # API key rotation settings
        self.api_keys = api_keys if api_keys else []
        self.current_key_index = 0
        self.base_provider_url = base_provider_url

        # NEW: Track blocks processed per API key
        self.blocks_per_api_key = blocks_per_api_key
        self.blocks_processed_by_key = (
            {i: 0 for i in range(len(self.api_keys))} if self.api_keys else {}
        )

        # Our JSON-RPC throttling parameters
        self.min_scan_chunk_size = 10_000  # 12 s/block = 120 seconds period
        self.max_scan_chunk_size = max_chunk_scan_size
        self.max_request_retries = max_request_retries
        self.request_retry_seconds = request_retry_seconds
        # Factor how fast we increase the chunk size if results are found
        # # (slow down scan after starting to get hits)
        self.chunk_size_decrease = 1
        # Factor how was we increase chunk size if no results found
        self.chunk_size_increase = 2.0

    def rotate_api_key(self, force_next=False):
        """Rotate to the next API key and update the web3 provider

        :param force_next: If True, force rotation to next key regardless of quota
        """
        if not self.api_keys:
            return False

        # If forcing next key or current key has reached its block limit
        if (
            force_next
            or self.blocks_processed_by_key[self.current_key_index]
            >= self.blocks_per_api_key
        ):
            # Move to the next key in the list
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)

            # Reset block count if we've gone through all keys once
            if self.current_key_index == 0:
                self.blocks_processed_by_key = {i: 0 for i in range(len(self.api_keys))}

            current_key = self.api_keys[self.current_key_index]

            # Create a new provider with the rotated key
            provider_url = f"{self.base_provider_url}{current_key}"
            self.web3 = Web3(Web3.HTTPProvider(provider_url), middlewares=[])

            # Re-apply any middleware that might have been on the original web3 instance
            self.web3.middleware_onion.inject(geth_poa_middleware, layer=0)

            # Update the contract with the new web3 instance
            self.contract = self.web3.eth.contract(
                address=self.contract.address, abi=self.contract.abi
            )

            logger.info(
                f"Rotated to API key {self.current_key_index + 1}/{len(self.api_keys)}: {current_key[:5]}... "
                + f"(Processed: {self.blocks_processed_by_key[self.current_key_index]}/{self.blocks_per_api_key} blocks)"
            )
            return True
        return False

    @property
    def address(self):
        return self.token_address

    def get_block_timestamp(self, block_num) -> datetime.datetime:
        """Get Ethereum block timestamp"""

        try:
            block_info = self.web3.eth.get_block(block_identifier=block_num)
        except BlockNotFound:
            # Block was not mined yet,
            # minor chain reorganisation?
            return None
        last_time = block_info["timestamp"]
        time.sleep(1)
        # return datetime.datetime.utcfromtimestamp(last_time)
        return last_time

    def get_suggested_scan_end_block(self, end_block: int | None = None):
        """Get the last mined block on Ethereum chain we are following."""
        """###################################### this will need to hve the end timestamp that we are considering - recieve from even.json ############################################"""

        # Do not scan all the way to the final block, as this
        # block might not be mined yet
        # return self.web3.eth.get_block(block_identifier="latest")["number"]
        if end_block is not None:
            return end_block
        else:
            return 22632357

    def get_last_scanned_block(self) -> int:
        return self.state.get_last_scanned_block()

    def delete_potentially_forked_block_data(self, after_block: int):
        """Purge old data in the case of blockchain reorganisation."""

        self.state.delete_data(after_block)

    def scan_chunk(self, start_block, end_block) -> Tuple[int, datetime.datetime, list]:
        """Read and process events between to block numbers.
        Dynamically decrease the size of the chunk if the case JSON-RPC server pukes
        ˓→out.
        :return: tuple(actual end block number, when this block was mined, processed
        ˓→events)
        """
        block_timestamps = {}
        get_block_timestamp = self.get_block_timestamp

        # Cache block timestamps to reduce some RPC overhead
        # Real solution might include smarter models around block
        def get_block_when(block_num):
            if block_num not in block_timestamps:
                block_timestamps[block_num] = get_block_timestamp(block_num)
            return block_timestamps[block_num]

        all_processed = []
        for event_type in self.events:
            # Callable that takes care of the underlying web3 call
            def _fetch_events(_start_block, _end_block):
                return _fetch_events_for_all_contracts(
                    self.web3,
                    event_type,
                    self.filters,
                    from_block=_start_block,
                    to_block=_end_block,
                )

            # Do `n` retries on `eth_getLogs`,
            # throttle down block range if needed
            end_block, events = _retry_web3_call(
                _fetch_events,
                start_block=start_block,
                end_block=end_block,
                retries=self.max_request_retries,
                delay=self.request_retry_seconds,
            )
            for evt in events:
                idx = evt[
                    "logIndex"
                ]  # Integer of the log index position in the block, null when its pending
                # We cannot avoid minor chain reorganisations, but
                # at least we must avoid blocks that are not mined yet
                assert idx is not None, "Somehow tried to scan a pending block"

                block_number = evt["blockNumber"]
                # Get UTC time when this event happened (block mined timestamp)
                # from our in-memory cache
                block_when = get_block_when(block_number)

                logger.debug(
                    "Processing event %s, block:%d count:%d",
                    evt["event"],
                    evt["blockNumber"],
                )
                processed = self.state.process_event(block_when, evt)
                all_processed.append(processed)

        end_block_timestamp = get_block_when(end_block)

        # Update the block count for the current API key
        if self.api_keys:
            blocks_scanned = end_block - start_block + 1
            self.blocks_processed_by_key[self.current_key_index] += blocks_scanned

        return end_block, end_block_timestamp, all_processed

    def estimate_next_chunk_size(self, current_chuck_size: int, event_found_count: int):
        """Try to figure out optimal chunk size
        Our scanner might need to scan the whole blockchain for all events
        * We want to minimize API calls over empty blocks
        * We want to make sure that one scan chunk does not try to process too many
        ˓→entries once, as we try to control commit buffer size and potentially asynchronous
        ˓→busy loop
        * Do not overload node serving JSON-RPC API by asking data for too many
        ˓→events at a time
        Currently Ethereum JSON-API does not have an API to tell when a first event occurred in a blockchain
        and our heuristics try to accelerate block fetching (chunk size) until we see
        ˓→the first event.
        These heurestics exponentially increase the scan chunk size depending on if
        ˓→we are seeing events or not.
        When any transfers are encountered, we are back to scanning only a few blocks
        ˓→at a time.
        It does not make sense to do a full chain scan starting from block 1, doing one JSON-RPC call per 20 blocks.
        """
        if event_found_count > 0:
            # When we encounter first events, reset the chunk size window
            # current_chuck_size = self.min_scan_chunk_size
            current_chuck_size *= self.chunk_size_decrease
        else:
            current_chuck_size *= self.chunk_size_increase

        current_chuck_size = max(self.min_scan_chunk_size, current_chuck_size)
        current_chuck_size = min(self.max_scan_chunk_size, current_chuck_size)

        # NEW: Ensure chunk size doesn't exceed remaining quota for current API key
        if self.api_keys:
            remaining_blocks = (
                self.blocks_per_api_key
                - self.blocks_processed_by_key[self.current_key_index]
            )
            if remaining_blocks > 0:
                current_chuck_size = min(current_chuck_size, remaining_blocks)

        return int(current_chuck_size)

    def scan(
        self,
        start_block,
        end_block,
        start_chunk_size=10_000,
        progress_callback=Optional[Callable],
    ) -> Tuple[list, int]:
        """Perform a token balances scan.
        Assumes all balances in the database are valid before start_block (no forks
        ˓→sneaked in).
        :param start_block: The first block included in the scan
        :param end_block: The last block included in the scan
        :param start_chunk_size: How many blocks we try to fetch over JSON-RPC on the
        ˓→first attempt
        :param progress_callback: If this is an UI application, update the progress
        ˓→of the scan
        :return: [All processed events, number of chunks used]
        """
        assert start_block <= end_block

        current_block = start_block

        # Scan in chunks, commit between
        chunk_size = start_chunk_size
        last_scan_duration = last_logs_found = 0
        total_chunks_scanned = 0
        # All processed entries we got on this scan cycle
        all_processed = []

        while current_block <= end_block:
            # Check if current API key has reached its limit and rotate if needed
            if (
                self.api_keys
                and self.blocks_processed_by_key[self.current_key_index]
                >= self.blocks_per_api_key
            ):
                self.rotate_api_key(force_next=True)

            self.state.start_chunk(current_block, chunk_size)
            # Print some diagnostics to logs to try to fiddle with real world JSON-RPC API performance
            estimated_end_block = current_block + chunk_size
            if estimated_end_block >= end_block:
                estimated_end_block = end_block
            logger.debug(
                "Scanning token transfers for blocks: %d - %d, chunk size %d, last chunk scan took % f, last logs found % d",
                current_block,
                estimated_end_block,
                chunk_size,
                last_scan_duration,
                last_logs_found,
            )
            start = time.time()
            actual_end_block, end_block_timestamp, new_entries = self.scan_chunk(
                current_block, estimated_end_block
            )

            # Where does our current chunk scan ends - are we out of chain yet?
            current_end = actual_end_block

            last_scan_duration = time.time() - start
            all_processed += new_entries

            # Print progress bar
            if progress_callback:
                progress_callback(
                    start_block,
                    end_block,
                    current_block,
                    end_block_timestamp,
                    chunk_size,
                    len(new_entries),
                )

            # Log API key usage
            if self.api_keys:
                logger.info(
                    f"API key {self.current_key_index + 1}/{len(self.api_keys)} "
                    + f"has processed {self.blocks_processed_by_key[self.current_key_index]}/{self.blocks_per_api_key} blocks"
                )

            # Try to guess how many blocks to fetch over `eth_getLogs` API next time
            chunk_size = self.estimate_next_chunk_size(chunk_size, len(new_entries))

            # Set where the next chunk starts
            current_block = current_end + 1
            total_chunks_scanned += 1
            self.state.end_chunk(current_end)
        return all_processed, total_chunks_scanned


def _retry_web3_call(func, start_block, end_block, retries, delay) -> Tuple[int, list]:
    """A custom retry loop to throttle down block range.
    If our JSON-RPC server cannot serve all incoming `eth_getLogs` in a single
    ˓→request,
    we retry and throttle down block range for every retry.
    For example, Go Ethereum does not indicate what is an acceptable response size.
    It just fails on the server-side with a "context was cancelled" warning.
    :param func: A callable that triggers Ethereum JSON-RPC, as func(start_block, end_
    ˓→block)
    :param start_block: The initial start block of the block range
    :param end_block: The initial start block of the block range
    :param retries: How many times we retry
    :param delay: Time to sleep between retries
    """
    for i in range(retries):
        try:
            return end_block, func(start_block, end_block)
        except Exception as e:
            # Assume this is HTTPConnectionPool(host='localhost', port=8545): Read timed out.(read timeout = 10)
            # from Go Ethereum. This translates to the error "context was cancelled" on the server side:
            # https://github.com/ethereum/go-ethereum/issues/20426
            if i < retries - 1:
                # Give some more verbose info than the default middleware
                logger.warning(
                    "Retrying events for block range %d - %d (%d) failed with %s, retrying in %s seconds",
                    start_block,
                    end_block,
                    end_block - start_block,
                    e,
                    delay,
                )
                # Decrease the `eth_getBlocks` range
                end_block = start_block + ((end_block - start_block) // 2)
                # Let the JSON-RPC to recover e.g. from restart
                time.sleep(delay)
                continue
            else:
                logger.warning("Out of retries")
                raise


def _fetch_events_for_all_contracts(
    web3, event, argument_filters: dict, from_block: int, to_block: int
) -> Iterable:
    """Get events using eth_getLogs API.
    This method is detached from any contract instance.
    This is a stateless method, as opposed to createFilter.
    It can be safely called against nodes which do not provide `eth_newFilter` API,
    ˓→like Infura.
    """
    if from_block is None:
        raise TypeError("Missing mandatory keyword argument to getLogs: fromBlock")
    # Currently no way to poke this using a public Web3.py API.
    # This will return raw underlying ABI JSON object for the event
    abi = event._get_event_abi()

    # Depending on the Solidity version used to compile
    # the contract that uses the ABI,
    # it might have Solidity ABI encoding v1 or v2.
    # We just assume the default that you set on Web3 object here.
    # More information here https://eth-abi.readthedocs.io/en/latest/index.html
    codec: ABICodec = web3.codec

    # Here we need to poke a bit into Web3 internals, as this
    # functionality is not exposed by default.
    # Construct JSON-RPC raw filter presentation based on human readable Python descriptions
    # Namely, convert event names to their keccak signatures
    # More information here:
    # https://github.com/ethereum/web3.py/blob/e176ce0793dafdd0573acc8d4b76425b6eb604ca / web3 / _utils / filters.py  # L71
    data_filter_set, event_filter_params = construct_event_filter_params(
        abi,
        codec,
        address=argument_filters.get("address"),
        argument_filters=argument_filters,
        fromBlock=from_block,
        toBlock=to_block,
    )
    logger.debug(
        "Querying eth_getLogs with the following parameters: %s", event_filter_params
    )
    # Call JSON-RPC API on your Ethereum node.
    # get_logs() returns raw AttributedDict entries
    logs = web3.eth.get_logs(event_filter_params)
    # Convert raw binary data to Python proxy objects as described by ABI
    all_events = []
    for log in logs:
        # Convert raw JSON-RPC log result to human readable event by using ABI data
        # More information how processLog works here
        # https://github.com/ethereum/web3.py/blob/fbaf1ad11b0c7fac09ba34baff2c256cffe0a148 / web3 / _utils / events.py  # L200
        evt = get_event_data(codec, abi, log)
        # Note: This was originally yield,
        # but deferring the timeout exception caused the throttle logic not to work
        all_events.append(evt)
    return all_events


import json
import os

# We use tqdm library to render a nice progress bar in the console
# https://pypi.org/project/tqdm/
from tqdm import tqdm

# RCC has around 11k Transfer events
# https://etherscan.io/token/******************************************
lyra_eth_optimism = "******************************************"
ADDRESS = "******************************************"
ETHERSCAN_API_KEY = os.environ.get("ETHERSCAN_API_KEY")
print("here i am here")


class JSONifiedState(EventScannerState):
    """Store the state of scanned blocks and all events.
    All state is an in-memory dict.
    Simple load/store massive JSON on start up.
    """

    def __init__(self):
        self.state = None
        self.fname = "/Users/<USER>/Documents/Work/BlockScholes/Defi-addresses/Data/Beacon_chain/Deposit_events_safe.json"
        # How many second ago we saved the JSON file
        self.last_save = 0

    def reset(self):
        """Create initial state of nothing scanned."""
        self.state = {
            "last_scanned_block": 0,
            "blocks": {},
        }

    def restore(self):
        """Restore the last scan state from a file."""
        try:
            self.state = json.load(open(self.fname, "rt"))
            print(
                f"Restored the state, previously {self.state['last_scanned_block']} blocks have been scanned"
            )
        except (IOError, json.decoder.JSONDecodeError):
            print("State starting from scratch")
            self.reset()

    def save(self):
        """Save everything we have scanned so far in a file."""

        with open(self.fname, "wt") as f:
            json.dump(self.state, f)
        self.last_save = time.time()

    #
    # EventScannerState methods implemented below
    #

    def get_last_scanned_block(self):
        """The number of the last block we have stored."""
        # 21955811
        return self.state["last_scanned_block"]
        # return 21955811

    def delete_data(self, since_block):
        """Remove potentially reorganised blocks from the scan data."""
        for block_num in range(since_block, self.get_last_scanned_block()):
            if block_num in self.state["blocks"]:
                del self.state["blocks"][block_num]

    def start_chunk(self, block_number, chunk_size):
        pass

    def end_chunk(self, block_number):
        """Save at the end of each block, so we can resume in the case of a crash
        or CTRL+C"""

        # Next time the scanner is started we will resume from this block
        self.state["last_scanned_block"] = block_number
        # Save the database file for every minute
        if time.time() - self.last_save > 600:
            self.save()

    def process_event(self, block_when: datetime.datetime, event: AttributeDict) -> str:
        """Record a ERC-20 transfer in our database."""
        # Events are keyed by their transaction hash and log index
        # One transaction may contain multiple events
        # and each one of those gets their own log index

        # event_name = event.event # "Transfer"
        # print(event)
        log_index = event["logIndex"]  # Log index within the block
        # transaction_index = event.transactionIndex # Transaction index within the block
        txhash = event["transactionHash"].hex()  # Transaction hash
        block_number = event["blockNumber"]

        # Convert ERC-20 Transfer event to our internal format
        args = dict(event["args"])
        args["timestamp"] = int(str(block_when)) * 1e9
        validator_deposit = {
            "pubkey": args["pubkey"].hex(),
            "transactionHash": txhash,
            "amount": args["amount"].hex(),
            "timestamp": int(block_when),
        }

        # Create empty dict as the block that contains all transactions by txhash
        if block_number not in self.state["blocks"]:
            self.state["blocks"][block_number] = {}

        block = self.state["blocks"][block_number]
        if txhash not in block:
            # We have not yet recorded any transfers in this transaction
            # (One transaction may contain multiple events if executed by a smart contract).
            # Create a tx entry that contains all events by a log index
            self.state["blocks"][block_number][txhash] = {}

        # Record ERC-20 transfer in our database
        self.state["blocks"][block_number][txhash][log_index] = validator_deposit

        # Return a pointer that allows us to look up this event later if needed
        return f"{block_number}-{txhash}-{log_index}"


def process_state_data(state):
    df = pd.DataFrame(columns=["listing", "availableSince", "strikeId"])
    blocks = state["blocks"]
    for block, data in blocks.items():
        for txhash, txhashData in data.items():
            for logIdx, data in txhashData.items():
                new_row = {
                    "strikeId": int(data["strikeId"]),
                    "listing": data["timestamp"],
                    "availableSince": data["timestamp"],
                }
                df = df.append(new_row, ignore_index=True)

    return df


def run(start_time, end_time, end_block: int | None = None):
    # Define all available API keys
    api_keys = [
        "c1506cc2c73e43c1b9b76891d763b8c8",  # ah.madkida
        "ff80c2b9ee0848fc86912ff2c38e2bd6",  # kidapsn@gmailcom
        # "bc7d638f06074767ba80f2d5f3204758",  # <EMAIL> - defi report
        "89ca8453198d4c5d94416f00827c97b3",  # <EMAIL>
        "4b5d28f970774d13b2f05715774a0d1c",  # andrew 1
        "126a70482dac4cefb68f87daef2d9990",  # andrew 2
        "deb9412f7347400c94b4b04aca4400d5",
        "8175be7f6699407096ce3f97e4ed64bb",  # burner
        "44e911e4f5c44cb1bbe8917a6090e5e7",  # burner
        "3f43f1506bba41fc9ca0614bc6a96c90",  # burner
        "2e4deee49f7a4376b1758fc79801db33",  # burner
    ]

    # Base provider URL
    BASE_PROVIDER_URL = "https://mainnet.infura.io/v3/"

    # Use the first API key to initialize
    initial_key = api_keys[0]
    PROVIDER_URL = f"{BASE_PROVIDER_URL}{initial_key}"
    print(f"Starting with API key: {initial_key[:5]}...")

    # Enable logs to the stdout.
    logging.basicConfig(level=logging.INFO)

    # Initialize Web3 instance
    web3 = Web3(Web3.HTTPProvider(PROVIDER_URL), middlewares=[])
    web3.middleware_onion.inject(geth_poa_middleware, layer=0)

    # Restore/create our persistent state
    state = JSONifiedState()
    state.restore()

    BEACON_CHAIN_ADDRESS = "******************************************"

    # Create contract instance
    object = makeContract(BEACON_CHAIN_ADDRESS, "ethereum")
    ABI = object["abi"]
    contract_address = web3.eth.contract(
        address=web3.to_checksum_address(BEACON_CHAIN_ADDRESS), abi=ABI
    )

    # Initialize scanner with API key rotation capabilities and block limits
    scanner = EventScanner(
        web3=web3,
        contract=contract_address,
        state=state,
        events=[contract_address.events.DepositEvent],
        filters={"address": BEACON_CHAIN_ADDRESS},
        api_keys=api_keys,
        base_provider_url=BASE_PROVIDER_URL,
        max_chunk_scan_size=10_000,
        blocks_per_api_key=10_000,  # Set the block limit per API key
    )

    # Handle potential chain reorganization
    chain_reorg_safety_blocks = 10
    scanner.delete_potentially_forked_block_data(
        state.get_last_scanned_block() - chain_reorg_safety_blocks
    )

    # Determine block range to scan
    start_block = max(
        state.get_last_scanned_block() - chain_reorg_safety_blocks, 11052984 - 1
    )
    end_block = scanner.get_suggested_scan_end_block(end_block)
    blocks_to_scan = end_block - start_block

    print(f"Scanning events from blocks {start_block} - {end_block}")
    print(f"Total blocks to scan: {blocks_to_scan}")
    print(f"Using {len(api_keys)} API keys with rotation (max {10000} blocks per key)")

    # Calculate estimated blocks per API key
    blocks_per_key_calculated = blocks_to_scan / len(api_keys)
    print(f"Average blocks per API key: {blocks_per_key_calculated:.2f}")

    # Render a progress bar in the console
    start = time.time()
    with tqdm(total=blocks_to_scan) as progress_bar:

        def _update_progress(
            start, end, current, current_block_timestamp, chunk_size, events_count
        ):
            if current_block_timestamp:
                formatted_time = datetime.datetime.utcfromtimestamp(
                    current_block_timestamp
                ).strftime("%d-%m-%Y")
            else:
                formatted_time = "no block time available"

            # Show current API key information in progress bar
            if scanner.api_keys:
                key_info = f"Key {scanner.current_key_index + 1}/{len(scanner.api_keys)}: {scanner.blocks_processed_by_key[scanner.current_key_index]}/{scanner.blocks_per_api_key} blocks"
            else:
                key_info = "No API key rotation"

            progress_bar.set_description(
                f"Block: {current} ({formatted_time}), chunk: {chunk_size}, events: {events_count}, {key_info}"
            )
            progress_bar.update(chunk_size)

        # Run the scan
        result, total_chunks_scanned = scanner.scan(
            start_block, end_block, progress_callback=_update_progress
        )

    state.save()
    duration = time.time() - start
    logging.info(
        f"Scanned total {len(result)} DepositEvent events, in {duration} seconds"
    )
    logging.info(f"Total chunks scanned: {total_chunks_scanned}")
    logging.info(f"Average time per chunk: {duration/total_chunks_scanned:.2f} seconds")

    # Print API key usage statistics
    if scanner.api_keys:
        print("\nAPI Key Usage Statistics:")
        for idx, blocks in scanner.blocks_processed_by_key.items():
            logging.info(f"API Key {idx + 1}: {blocks} blocks processed")

    # Process the results if needed
    # df = process_state_data(state.state)
    # return df
    return result


if __name__ == "__main__":
    run(1, 2)
