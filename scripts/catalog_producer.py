import asyncio
import logging
import random
import sys
import time
from datetime import UTC, datetime, timedelta

import utils_general
from block_stream.agent import Agent
from block_stream.typings import CatalogData

from feed_indexer.catalog_filter_manager import CatalogFilterManager
from feed_indexer.config import Config, load_indexer_target_config

logging.basicConfig(stream=sys.stdout, level=logging.INFO)
logger = logging.getLogger()
agent = Agent("AsyncConsumer")

# Predefined base assets and exchanges
BASE_ASSETS = ["BTC", "ETH"]
EXCHANGES = ["deribit", "bybit", "blockscholes", "okx"]
EXPIRY_FORMAT = "%d%b%y"


def random_expiry() -> str:
    """Generate a random expiry date in ISO format."""
    today = datetime.now(UTC)
    future_date = today + timedelta(seconds=random.randint(1, 60))
    past_date = today - timedelta(seconds=random.randint(1, 60))
    return utils_general.to_iso(random.choice([past_date, future_date]))


def generate_instrument_name(
    base_asset: str,
    expiry_date: str,
    strike: float | None = None,
    instrument_type: str = "futures",
) -> str:
    """Generate the instrument name based on the type and other parameters."""
    expiry_short = (
        datetime.strptime(expiry_date, "%Y-%m-%dT%H:%M:%S.%fZ")
        .replace(tzinfo=UTC)
        .strftime(EXPIRY_FORMAT)
        .upper()
    )
    if instrument_type == "option":
        option_type = random.choice(["C", "P"])
        strike = strike or random.choice([20000, 30000, 40000])
        assert strike is not None
        return f"{base_asset}-{expiry_short}-{strike}-{option_type}"
    else:
        return f"{base_asset}-{expiry_short}"


def random_catalog_instrument() -> CatalogData:
    """Generate a random catalog instrument (futures or options)."""
    base_asset = random.choice(BASE_ASSETS)
    exchange = random.choice(EXCHANGES)
    expiry = random_expiry()
    instrument_type = random.choice(["futures", "option"])

    if instrument_type == "option":
        instrument_name = generate_instrument_name(
            base_asset, expiry, instrument_type="option"
        )
        return {
            "q": f"{exchange}.option.contracts",
            "instrument": instrument_name,
            "baseAsset": base_asset,
            "quoteAsset": "USDC",
            "availableSince": utils_general.to_iso(datetime.now(tz=UTC)),
            "listing": utils_general.to_iso(datetime.now(tz=UTC)),
            "expiry": expiry,
            "type": instrument_name.split("-")[-1],  # Option type (C or P)
            "strike": float(instrument_name.split("-")[-2]),
        }
    else:
        instrument_name = generate_instrument_name(
            base_asset, expiry, instrument_type="futures"
        )
        return {
            "q": f"{exchange}.future.contracts",
            "instrument": instrument_name,
            "baseAsset": base_asset,
            "quoteAsset": "USD",
            "availableSince": utils_general.to_iso(datetime.now(tz=UTC)),
            "listing": utils_general.to_iso(datetime.now(tz=UTC)),
            "expiry": expiry,
            "settlementAsset": "USDC",
        }


async def random_instrument_producer() -> None:
    tick_data_stream = agent.channel("tickData", seconds_per_checkpoint=None)

    while True:
        catalog_instrument = random_catalog_instrument()
        logger.info(f"Putting instrument: {catalog_instrument['instrument']}")
        # await tick_data_stream.put(catalog_instrument)  # type: ignore
        # await tick_data_stream.flush()
        # await asyncio.sleep(1)


if __name__ == "__main__":

    now = time.time_ns()

    # instruments = get_instruments(
    #     fields=[],
    #     start=utils_general.to_iso(now),
    #     end=utils_general.to_iso(now),
    #     exchanges=["bybit", "deribit", "blockscholes"],
    #     asset_types=["option"],
    #     base_assets=["BTC", "ETH"],
    #     quote_assets=["USD"],  # todo: Make this configurable
    # )
    # items = [item for item in instruments if "2025-06-18T08" in item["expiry"]]

    config = Config()
    utils_general.setup_python_logger(level=config.LOG_LEVEL)

    target_config = load_indexer_target_config(config)

    catalog_filter_manager = CatalogFilterManager(target_config)
    catalog = asyncio.run(catalog_filter_manager._get_catalog_entries_from_db())
    print()

    print()
    # asyncio.run(random_instrument_producer())
