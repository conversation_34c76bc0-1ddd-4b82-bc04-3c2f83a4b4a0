import logging
from dataclasses import dataclass
from datetime import UTC, datetime, timedelta
from typing import (
    Annotated,
    Any,
    Literal,
    NamedTuple,
    NotRequired,
    Self,
    TypedDict,
    TypeGuard,
    cast,
)

import utils_general
from datagrabber import ExchangeSourceMappingConfig
from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
)
from pydantic_core.core_schema import ValidationInfo
from utils_general import (
    convert_constant_maturity_to_datetime,
    convert_tenor_days_to_constant_maturity,
    from_iso,
)
from utils_web3 import ByteArray, ClientFormat, EIP712Domain, Number, eip712_sign_data

from wsapi.client_limits import Limits
from wsapi.utils.catalog_utils import get_catalog_exchange

"""
***** Generic Types *****
"""
CatalogExpiryStrikesMap = dict[datetime, set[float]]


class DynamoPxData(TypedDict):
    px: float
    timestamp: float
    qualified_name: str


"""
***** Types related to Subscription commands *****
"""


class BsTickData(TypedDict, total=False):
    # Tick data format sent to Websocket Clients
    q: str
    t: int
    v: Any
    params: Any
    confidence: float
    intermediate: Any


class BslangExpr(TypedDict, total=False):
    type: Literal["FunctionCall", "NumberLiteral", "Identifier"]
    name: str
    args: list[Any]


class RangePerm(TypedDict):
    type: Literal["range"]
    value: dict[Literal["start", "end"], float | timedelta]


class InPerm(TypedDict):
    type: Literal["in"]
    optional: NotRequired[bool]
    value: list[str] | list[float] | list[int] | list[bool]


class AnyPerm(TypedDict):
    type: Literal["any"]


Permission = RangePerm | InPerm | AnyPerm


class PermissionDict(TypedDict):
    permission_data_key: str
    data: dict[str, Permission]


class SigningConfig(TypedDict):
    type: Literal["NOT_REQUIRED", "REQUIRED_ANY", "REQUIRED_INT"]
    include_sid_in_response: NotRequired[bool]
    excluded_fields_on_sid_check: NotRequired[set[str]]
    assert_sid_equals_contract: NotRequired[bool]
    use_legacy_format: NotRequired[
        bool
    ]  # TODO remove once GRVT have migrated to new API key


Greeks = Literal[
    "delta",
    "gamma",
    "vega",
    "theta",
    "volga",
    "vanna",
]


class ClientBatchPermission(TypedDict):
    batch_frequency_ms: list[int]
    min_timestamp_delta_ms: NotRequired[int]
    signing_config: SigningConfig
    signature_enabled: NotRequired[Permission]

    mark_asset: list[str]
    mark_base_asset: list[str] | Literal["*"]
    mark_quote_asset: list[str]

    allowed_indices: PermissionDict

    settlement_base_asset: Permission

    interest_rate_base_asset: Permission

    perp_px_exchange: list[str]
    perp_px_asset: list[str]
    perp_px_base_asset: list[str] | Literal["*"]
    perp_px_quote_asset: list[str]

    model_parameters_exchange: Permission
    model_parameters_base_asset: Permission
    model_parameters_model: list[str]
    vol_smile_flex_calc_types: list[
        Literal["*", "params", "smile", "skew", "moneyness", "butterfly", "strike"]
    ]
    allowed_iv_feeds: list[Literal["moneyness", "delta", "strike"]]
    iv_listed_only: NotRequired[Permission | bool]

    theoretical_pricer_listed_only: NotRequired[bool]
    greeks: NotRequired[Permission]
    ref_expiry: NotRequired[Permission]
    expiry: NotRequired[Permission]
    # IV feed overrides, standards used otherwise, feed toggled via 'allowed_iv_feeds'
    allowed_moneyness: NotRequired[Permission]
    allowed_delta: NotRequired[Permission]
    allowed_tenors: NotRequired[Permission]

    limits: Limits


OptionType = Literal["C", "P"]


"""
***** Subscription commands *****
"""

TimestampPrecision = Literal["s", "ms", "ns", "us"]
ListStrikeSub = list[float | Literal["atm_spot", "atm_forward"]]
StrikeSub = float | Literal["atm_spot", "atm_forward"] | ListStrikeSub | None


class BatchFormat(BaseModel):
    timestamp: TimestampPrecision
    hexify: bool

    # A scalar (int) or a map for each SID -> int
    decimals: int | dict[str, int]


class EIPDomain(BaseModel):
    name: str | None = None
    version: str | None = None  # number, may be hex string
    chain_id: str | None = None  # number, may be hex string
    verifying_contract: str | None = None  # must be hex string

    """
    While these int->str conversions are a little backwards, they should mirror the
    prior behaviour. Additionally the eth lib's make_domain expects a parsable
    int under chain_id (not version) which would reject hexadecimal numbers
    """

    @field_validator("version", mode="before")
    @classmethod
    def version_int(cls, v: str | None) -> str | None:
        if isinstance(v, str):
            return str(int(v, 0))  # Parse hex string, fail otherwise
        else:
            return v

    @field_validator("chain_id", mode="before")
    @classmethod
    def chain_id_int(cls, v: str | None) -> str | None:
        if isinstance(v, str):
            return str(int(v, 0))  # Parse hex string, fail otherwise
        else:
            return v

    @model_validator(mode="after")
    def check_signature_validity(self, info: ValidationInfo) -> Self:
        _validate_permission(
            perm_key="signature_enabled",
            value=True,
            info=info,
            default_perm={"type": "in", "value": [True]},
        )

        # Check if account has permission to get signed data
        if info.context is not None and not info.context.get("signature_enabled", True):
            raise ValueError(
                "Signatures not enabled on this account. Please contact us."
            )

        # Attempt to sign a dummy data package. It should fail if there's a problem
        # with the signature domain, rejecting the subscription with a sensible
        # message to the user
        signature_domain: EIP712Domain = {
            "name": self.name,
            "version": self.version,
            "chainId": self.chain_id,
            "verifyingContract": self.verifying_contract,
        }

        eip712_sign_data(
            data={},
            domain=signature_domain,
            format_options=cast(ClientFormat, {}),
            root_type="data",
        )
        return self


class BatchSignature(BaseModel):
    domain: EIPDomain


class BatchOptions(BaseModel):
    format: BatchFormat
    signature: BatchSignature | None = None


IvBaseAsset = str


class ExpiryModel(BaseModel):
    expiry: str  # Accepts either a maturity (mins (m)/ days(d)) or an ISO date string

    def _is_constant_maturity(self) -> bool:
        try:
            if self.expiry.endswith(("d", "h")):
                float(self.expiry[:-1])
                return True
            return False

        except Exception as e:
            # Note: this should never be hit due to the validation performed on construction
            #       leaving it here just in case
            logging.error(f"Failed to parse expiry {self.expiry}: {e}")
            return False

    def _maturity_to_float(self, output_unit: Literal["days", "minutes"]) -> float:
        assert self._is_constant_maturity()
        expiry_val = float(self.expiry[:-1])
        input_unit = self.expiry[-1]

        if input_unit == "h":
            if output_unit == "days":
                return expiry_val / 24
            elif output_unit == "minutes":
                return expiry_val * 60
        elif input_unit == "d":
            if output_unit == "days":
                return expiry_val
            elif output_unit == "minutes":
                return expiry_val * 60 * 24

        raise RuntimeError(f"Invalid {input_unit=} or {output_unit=}")

    def to_timestamp(self, start_ref_dt: datetime | None = None) -> int:
        """Converts the expiry data to a Unix timestamp."""
        if self._is_constant_maturity():
            # If the expiry is given in days, convert days to minutes, add to current UTC time
            if start_ref_dt:
                dt = start_ref_dt
            else:
                dt = datetime.now(tz=UTC)
            minutes = self._maturity_to_float("minutes")
            future_date = dt + timedelta(minutes=minutes)
            timestamp = int(future_date.timestamp() * 1e9)
        elif isinstance(self.expiry, str):
            # If the expiry is an ISO format date string, parse it to a datetime and convert to timestamp
            timestamp = int(utils_general.from_iso(self.expiry).timestamp() * 1e9)
        else:
            raise ValueError("Invalid expiry type")

        return timestamp

    def raw(self) -> str:
        return self.expiry

    def is_expiry(self) -> bool:
        return (
            self.expiry.endswith(("Z", "+00:00")) and not self._is_constant_maturity()
        )

    def to_datetime(self) -> datetime:
        return utils_general.to_datetime(self.to_timestamp())

    def to_string(self) -> str:
        if self.is_expiry():
            return f"{self.expiry[:19]}Z"
        elif self._is_constant_maturity():
            return utils_general.convert_tenor_days_to_constant_maturity(
                self._maturity_to_float("days")
            )
        else:
            raise ValueError(f"Unable to convert expiry to string {self.expiry}")

    @field_validator("expiry")
    @classmethod
    def check_expiry_type(cls, v: Any) -> str:
        """Ensure the expiry is either a valid ISO date string or a float representing days."""
        if isinstance(v, str):
            if v.endswith(("d", "h")):
                try:
                    float(v[:-1])
                    return v
                except Exception as e:
                    raise ValueError("Cannot convert maturity to float.") from e
            elif not (v.endswith("Z") or v.endswith("+00:00")):
                raise ValueError("Must be a UTC denoted date string.")
            try:
                utils_general.from_iso(v)
                return v
            except ValueError as ve:
                raise ValueError(f"Invalid ISO date format: {v}") from ve
        raise ValueError(
            "Expiry should be an ISO date string or a string representing constant maturity suffixed in h (hours) or d (days)."
        )


class SubscriptionBaseParams(BaseModel):
    sid: str | None = None
    expiry: ExpiryModel | None = None

    @classmethod
    def _get_relevant_catalog(
        self,
        exchange: str,
        base_asset: IvBaseAsset,
        info: ValidationInfo,
        asset_type: Literal["future", "option"] = "option",
    ) -> CatalogExpiryStrikesMap:
        assert info.context is not None
        catalog: dict[str, Any] = info.context["catalog_instruments"]
        catalog_exchange = get_catalog_exchange(exchange)

        related_catalog_section: CatalogExpiryStrikesMap | None = catalog[
            catalog_exchange
        ][asset_type][base_asset]
        if not related_catalog_section:
            logging.warning(
                f"Unable to find relevant catalog for {catalog_exchange=}, {asset_type=}, {base_asset=}. Loaded {catalog=}"
            )
            raise ValueError(
                "Unable to verify expiry is listed, please try again."
            )  # Can occur when we initially load the catalog and accept a subscription
        return related_catalog_section

    @classmethod
    def _check_is_listed_only(
        self,
        perm_key: str,
        exchange: str,
        base_asset: IvBaseAsset,
        asset_type: Literal["future", "option"],
        expiry: str,
        info: ValidationInfo,
    ) -> None:
        should_check_listed = _check_permission(
            perm_key=perm_key,
            value=base_asset,
            info=info,
        )
        if info.context is not None and should_check_listed:
            related_catalog_section = self._get_relevant_catalog(
                exchange=exchange,
                base_asset=base_asset,
                info=info,
                asset_type=asset_type,
            )
            if utils_general.from_iso(expiry) not in related_catalog_section:
                raise ValueError("Should provide a listed expiry.")

    @model_validator(mode="before")
    @classmethod
    def check_required_fields(cls, data: Any, info: ValidationInfo) -> object:
        # Note: field_validator (above) does not run on default values
        #       i.e.: if the field is not set. The recommended way of
        #       handling this is by using a model_validator instead
        if "expiry" in data:
            exp = ExpiryModel(expiry=data["expiry"])
            data["expiry"] = exp
            _validate_permission(
                perm_key="expiry",
                value=exp.to_datetime(),
                info=info,
                error_msg=f"unsupported expiry: {exp.to_string()}",
                default_perm={"type": "any"},
            )
        if (
            info.context is None
            or info.context["signing_config"]["type"] == "NOT_REQUIRED"
        ):
            return data

        if data.get("sid") is None:
            raise ValueError("'sid' must be set!")

        # Raise if int parsing fails, but keep 'sid' as string for storage
        if info.context["signing_config"]["type"] == "REQUIRED_INT":
            try:
                int(data.get("sid"), 0)
            except ValueError:
                raise ValueError("'sid' must be a number!") from None

        return data


class MarketDataBaseParams(SubscriptionBaseParams):
    asset: Literal["spot", "future", "perpetual", "option"]
    base_asset: str

    @classmethod
    def _convert_expiry_to_dt(cls, expiry: float | int | str) -> datetime:
        if isinstance(expiry, float | int):
            return convert_constant_maturity_to_datetime(
                convert_tenor_days_to_constant_maturity(expiry)
            )
        else:
            return from_iso(expiry)


class MarkPxParams(MarketDataBaseParams):
    feed: Literal["mark.px"]
    asset: Literal["future", "perpetual", "option"]
    quote_asset: str = "USD"
    ref_expiry: ExpiryModel | None = None
    strike: StrikeSub = None
    moneyness: float | list[float] | None = None
    type: OptionType | None = None
    greeks: list[Greeks] | None = None

    @field_validator("strike", mode="before")
    @classmethod
    def strike_parse(cls, v: Any | None) -> object:
        # Parse strike/s if passed as string
        if v is not None:
            if isinstance(v, str) and v not in ("atm_spot", "atm_forward"):
                return float(v)
            elif isinstance(v, list):
                return [
                    (
                        float(s)
                        if isinstance(s, str) and s not in ("atm_spot", "atm_forward")
                        else s
                    )
                    for s in v
                ]
        return v

    @field_validator("moneyness", mode="after")
    @classmethod
    def moneyness_validate(
        cls, v: float | list[float] | None
    ) -> float | list[float] | None:
        if v is not None:
            if isinstance(v, float) and (v <= 0 or v > 4):
                raise ValueError("Moneyness must be greater than 0 and less than 4")
            elif isinstance(v, list):
                if any(m < 0 or m > 4 for m in v):
                    raise ValueError("Moneyness must be between 0 and 4")
        return v

    @field_validator("asset")
    @classmethod
    def asset_validate(cls, v: Any, info: ValidationInfo) -> object:
        if info.context is not None and v not in info.context["mark_asset"]:
            raise ValueError(f"unsupported asset: {v}")
        return v

    @field_validator("base_asset")
    @classmethod
    def base_asset_validate(cls, v: Any, info: ValidationInfo) -> object:
        if info.context is not None:
            mark_base_asset = info.context["mark_base_asset"]
            if mark_base_asset != "*" and v not in mark_base_asset:
                raise ValueError(f"unsupported base asset: {v}")
        return v

    @field_validator("quote_asset")
    @classmethod
    def quote_asset_validate(cls, v: Any, info: ValidationInfo) -> object:
        if info.context is not None and v not in info.context["mark_quote_asset"]:
            raise ValueError(f"unsupported quote asset: {v}")
        return v

    @model_validator(mode="before")
    def exp_validate(cls, data: Any, info: ValidationInfo) -> object:
        if "expiry" not in data:
            if data["asset"] in ["option", "future"]:
                raise ValueError("expiry must be provided for options and futures")
            return data

        if "ref_expiry" in data and "expiry" not in data:
            raise ValueError("Please provide an expiry as well as the ref_expiry.")
        _validate_permission(
            perm_key="ref_expiry",
            value=data.get("ref_expiry"),
            info=info,
            error_msg=f"unsupported reference expiry: {data.get('ref_expiry')}",
            default_perm={
                "type": "in",
                "optional": True,
                "value": [],
            },  # disallow by default
        )
        if "ref_expiry" in data:
            data["ref_expiry"] = ExpiryModel(expiry=data["ref_expiry"])
        elif ExpiryModel(expiry=data["expiry"]).to_datetime() - datetime.now(
            UTC
        ) < timedelta(hours=1):
            # TODO: Expiries shorter than 1h are unstable / prone to getting
            # filtered out due to spikes. Threshold their IV ref to 1h until
            # addressed using spline model and/or adjusting smoothing logic
            data["ref_expiry"] = ExpiryModel(expiry="1h")

        return data

    @model_validator(mode="after")
    def check_strike_moneyness(self) -> Self:
        if self.asset == "option":
            if self.strike is None and self.moneyness is None:
                raise ValueError(
                    "strike or moneyness must be provided for option asset"
                )
            if self.moneyness and self.strike:
                raise ValueError("Cannot provide both moneyness and strike.")
            if self.type is None:
                raise ValueError("type must be provided for option asset")
        return self

    @field_validator("greeks", mode="after")
    def greeks_validate(cls, v: list[str], info: ValidationInfo) -> list[str]:
        _validate_permission(
            perm_key="greeks",
            value=v,
            info=info,
            error_msg=f"Not permissioned to use '{v}'",
        )
        return v


class IndexPxParams(MarketDataBaseParams):
    feed: Literal["index.px"]
    quote_asset: Literal["USD"] = "USD"

    @field_validator("asset")
    @classmethod
    def asset_validate(cls, v: Any, info: ValidationInfo) -> object:
        if (
            info.context is not None
            and v not in info.context["allowed_indices"]["data"]
        ):
            raise ValueError(f"unsupported asset: {v}")
        return v

    @field_validator("base_asset")
    @classmethod
    def base_asset_validate(cls, v: Any, info: ValidationInfo) -> object:
        _validate_permission(
            perm_key="allowed_indices",
            value=v,
            info=info,
            error_msg=f"unsupported base asset: {v}",
        )
        return v

    @model_validator(mode="before")
    def index_validator(cls, data: Any, info: ValidationInfo) -> object:
        asset = data.get("asset")

        if not asset:
            raise ValueError("Asset required.")

        if asset in ["spot", "perpetual"]:
            return data

        # Handle option/future expiries
        related_catalog_section = cls._get_relevant_catalog(
            exchange="blockscholes",
            base_asset=data.get("base_asset"),
            info=info,
            asset_type=data.get("asset"),
        )
        expiry = from_iso(data["expiry"])
        if expiry not in related_catalog_section:
            raise ValueError("Should provide a listed expiry.")

        strikes_for_expiry = related_catalog_section[expiry]
        if data["asset"] == "option" and (
            strikes_for_expiry and data["strike"] not in strikes_for_expiry
        ):
            raise ValueError("Should provide a listed strike.")

        return data

    @model_validator(mode="after")
    def check_asset(self) -> Self:
        if self.asset == "option":
            raise ValueError(
                "Option price indices not currently supported, please reach out for alternatives."
            )
        return self


# The allowed tenor days for index.iv feed is defined here rather than in
# config.py to avoid a circular import.
INDEX_IV_TENOR_DAYS = [7, 14, 30, 90, 180, 365]


class IndexIVParams(SubscriptionBaseParams):

    expiry: ExpiryModel
    feed: Literal["index.iv"]
    exchange: Literal["composite"] = "composite"
    base_asset: Literal["BTC", "ETH"]

    @field_validator("expiry")
    @classmethod
    def _check_valid_expiry(cls, v: ExpiryModel | None) -> ExpiryModel:
        """For index.iv, expiry must be in days and one of the allowed tenor days."""
        if v is None:
            raise ValueError("Expiry is required for index.iv.")

        expiry = v.expiry
        if not expiry.endswith("d"):
            raise ValueError("Expiry must be in days.")

        days = int(expiry[:-1])
        if days not in INDEX_IV_TENOR_DAYS:
            raise ValueError(
                f"Invalid expiry days, must be one of {INDEX_IV_TENOR_DAYS}, received {days}"
            )

        return v


class SettlementPxParams(SubscriptionBaseParams):
    feed: Literal["settlement.px"]
    base_asset: str
    exchange: str = "v2composite"  # TODO update when proper solution
    expiry: ExpiryModel

    @field_validator("base_asset")
    @classmethod
    def base_asset_validate(cls, v: str, info: ValidationInfo) -> str:
        _validate_permission(
            perm_key="settlement_base_asset",
            value=v,
            info=info,
            error_msg=f"Unsupported base asset: '{v}'",
        )
        return v

    def __hash__(self) -> int:
        # Needs to be hashable for the Lambda polling's logic
        return self.base_asset.__hash__() + self.expiry.to_timestamp()

    @model_validator(mode="before")
    def validate_expiry(cls, data: Any, info: ValidationInfo) -> object:
        if isinstance(data["expiry"], float | int):
            raise ValueError("Expected ISO date string.")
        return data


class InterestRateParams(SubscriptionBaseParams):
    feed: Literal["interest.rate"]
    base_asset: str
    expiry: ExpiryModel

    @field_validator("base_asset")
    @classmethod
    def base_asset_validate(cls, v: str, info: ValidationInfo) -> str:
        _validate_permission(
            perm_key="interest_rate_base_asset",
            value=v,
            info=info,
            error_msg=f"Unsupported base asset: '{v}'",
        )
        return v

    @model_validator(mode="before")
    def validator(cls, data: Any, info: ValidationInfo) -> object:
        if not data["base_asset"].startswith("USD"):
            cls._check_is_listed_only(
                perm_key="theoretical_pricer_listed_only",
                exchange="blockscholes",
                base_asset=data["base_asset"],
                asset_type="future",
                expiry=data["expiry"],
                info=info,
            )
        return data


class PerpPxParams(SubscriptionBaseParams):
    # Not specific to perps, should be: should be same as index price ?

    feed: Literal[
        "impact.bid.diff.twap.px", "impact.ask.diff.twap.px", "mid.diff.twap.px"
    ]
    exchange: str
    asset: str
    base_asset: str
    quote_asset: str
    interval: timedelta = timedelta(minutes=30)

    @field_validator("exchange")
    @classmethod
    def exchange_validate(cls, v: str, info: ValidationInfo) -> str:
        if info.context is not None and v not in info.context["perp_px_exchange"]:
            raise ValueError(f"unsupported exchange: {v}")
        return v

    @field_validator("asset")
    @classmethod
    def asset_validate(cls, v: str, info: ValidationInfo) -> str:
        if info.context is not None and v not in info.context["perp_px_asset"]:
            raise ValueError(f"unsupported asset: {v}")
        return v

    @field_validator("base_asset")
    @classmethod
    def base_asset_validate(cls, v: str, info: ValidationInfo) -> str:
        if info.context is not None:
            perp_px_base_asset = info.context["perp_px_base_asset"]
            if perp_px_base_asset != "*" and v not in perp_px_base_asset:
                raise ValueError(f"unsupported base asset: {v}")
        return v

    @field_validator("quote_asset")
    @classmethod
    def quote_asset_validate(cls, v: str, info: ValidationInfo) -> str:
        if info.context is not None and v not in info.context["perp_px_quote_asset"]:
            raise ValueError(f"unsupported quote asset: {v}")
        return v

    @field_validator("interval", mode="before")
    @classmethod
    def interval_validate(cls, v: Any) -> timedelta:
        if not isinstance(v, str) or not v.endswith("m"):
            raise ValueError("Interval must end with 'm', indicating minutes.")

        try:
            minutes = int(v[:-1])
        except Exception as err:
            raise ValueError(
                "Interval must be a positive integer followed by 'm'"
            ) from err

        if minutes <= 0:
            raise ValueError("Interval minutes must be greater than 0")
        return timedelta(minutes=minutes)


class ModelParametersParams(SubscriptionBaseParams):
    feed: Literal["model.params"]
    asset: Literal["option"]
    exchange: str
    base_asset: str
    model: Literal["SVI", "SABR"]
    expiry: ExpiryModel

    @field_validator("exchange")
    @classmethod
    def exchange_validate(cls, v: str, info: ValidationInfo) -> str:
        _validate_permission(
            perm_key="model_parameters_exchange",
            value=v,
            info=info,
            error_msg=f"unsupported exchange: {v}",
        )
        return v

    @field_validator("base_asset")
    @classmethod
    def base_asset_validate(cls, v: str, info: ValidationInfo) -> str:
        _validate_permission(
            perm_key="model_parameters_base_asset",
            value=v,
            info=info,
            error_msg=f"unsupported base asset: {v}",
        )

        return v

    @model_validator(mode="before")
    @classmethod
    def validate_model(cls, data: Any, info: ValidationInfo) -> Any:
        if "model" not in data or not data["model"]:
            # Use default model if not provided
            if info.context is not None:
                client_default: str | None = (
                    info.context["defaults"]
                    .get("client_defaults", {})
                    .get(data["feed"], {})
                    .get(data["base_asset"])
                )
                data["model"] = (
                    client_default
                    if client_default
                    else info.context["defaults"]["global_defaults"]["model"]
                )

        if not data["model"] or data["model"] not in ["SVI", "SABR"]:
            raise ValueError("Model must be one of: SVI or SABR")

        _validate_permission(
            perm_key="model_parameters_model",
            value=data["model"],
            info=info,
            error_msg=f"unsupported model: {data['model']}",
        )
        return data

    @field_validator("feed")
    @classmethod
    def validate_allowed_feed(cls, v: Any, info: ValidationInfo) -> object:
        _validate_permission(
            perm_key="vol_smile_flex_calc_types",
            value="params",
            info=info,
            error_msg=f"unsupported feed: {v}",
        )

        return v


IVFeeds = Literal["moneyness.iv", "delta.iv", "strike.iv"]


class BaseIVParams(SubscriptionBaseParams):
    exchange: str
    base_asset: IvBaseAsset
    model: Literal["SVI", "SABR"]
    feed: IVFeeds
    expiry: ExpiryModel

    @field_validator("feed")
    def validate_allowed_feed(cls, v: str, info: ValidationInfo) -> str:
        if (
            info.context is not None
            and v.split(".")[0] not in info.context["allowed_iv_feeds"]
        ):
            raise ValueError("Unsupported feed.")
        return v

    @field_validator("exchange")
    @classmethod
    def exchange_validate(cls, v: str, info: ValidationInfo) -> str:
        _validate_permission(
            perm_key="model_parameters_exchange",
            value=v,
            info=info,
            error_msg=f"unsupported exchange: {v}",
        )

        return v

    @field_validator("base_asset")
    @classmethod
    def base_asset_validate(cls, v: str, info: ValidationInfo) -> str:
        _validate_permission(
            perm_key="model_parameters_base_asset",
            value=v,
            info=info,
            error_msg=f"unsupported base asset: {v}",
        )
        return v

    @model_validator(mode="before")
    def listed_validator(cls, data: Any, info: ValidationInfo) -> object:
        if "model" not in data or not data["model"]:
            # Use default model if not provided
            if info.context is not None:
                client_default: str | None = (
                    info.context["defaults"]
                    .get("client_defaults", {})
                    .get(data["feed"], {})
                    .get(data["base_asset"])
                )

                data["model"] = (
                    client_default
                    if client_default
                    else info.context["defaults"]["global_defaults"]["model"]
                )

        if not data["model"] or data["model"] not in ["SVI", "SABR"]:
            raise ValueError("Model must be one of: SVI or SABR")

        _validate_permission(
            perm_key="model_parameters_model",
            value=data["model"],
            info=info,
            error_msg=f"unsupported model: {data['model']}",
        )

        cls._check_is_listed_only(
            perm_key="iv_listed_only",
            exchange=data["exchange"],
            base_asset=data["base_asset"],
            asset_type="option",
            expiry=data["expiry"],
            info=info,
        )
        return data


class MoneynessIVParams(BaseIVParams):
    feed: Literal["moneyness.iv"]
    moneyness: list[float] | float

    @classmethod
    def _check_valid(
        cls, val: list[float] | float, info: ValidationInfo
    ) -> list[float]:
        parsed_vals: list[float] = []
        list_of_vals: list[float] = val if isinstance(val, list) else [val]
        for v in list_of_vals:
            if v > 4:
                raise ValueError("Max moneyness supported is 4.0.")
            if v <= 0:
                raise ValueError("Minimum moneyness is greater than 0.")
            if round(v, 4) != v:
                raise ValueError(
                    f"Moneyness value must have a maximum of four decimal points, received {v}"
                )
            else:
                parsed_vals.append(v)
        return parsed_vals

    @field_validator("moneyness")
    def moneyness_validate(
        cls, values: list[float] | float, info: ValidationInfo
    ) -> list[float] | float:
        vals = cls._check_valid(values, info)
        # Validate above first instead of permissions as more meaningful error
        _validate_permission(
            perm_key="allowed_moneyness",
            value=values,
            info=info,
            error_msg=f"Not permissioned to use '{values}'",
        )
        return vals


class DeltaIVParams(BaseIVParams):
    feed: Literal["delta.iv"]
    delta: list[float] | float

    @classmethod
    def _check_valid(cls, v: Any) -> None:
        if v <= -1 or v >= 1:
            raise ValueError(
                f"Please enter numerical values representing delta from -1 to 1. Received {v}"
            )

        if round(v, 4) != v:
            raise ValueError(
                f"Delta value must have a maximum of four decimal points, recieved {v}"
            )

    @field_validator("delta")
    def delta_validate(cls, data: list[float] | float, info: ValidationInfo) -> object:
        if data and isinstance(data, list):
            for v in data:
                cls._check_valid(v)
        elif isinstance(data, int | float):
            cls._check_valid(data)
        # Validate above first instead of permissions as more meaningful error

        _validate_permission(
            perm_key="allowed_delta",
            value=data,
            info=info,
            error_msg=f"Not permissioned to use '{data}'",
        )

        return data


class StrikeIVParams(BaseIVParams):
    feed: Literal["strike.iv"]
    strike: list[float] | Literal["listed"]

    @model_validator(mode="before")
    def strike_iv_validator(cls, data: Any, info: ValidationInfo) -> object:
        assert info.context is not None
        dt = utils_general.from_iso(data["expiry"])
        should_check_listed = _check_permission(
            perm_key="iv_listed_only",
            value=data["base_asset"],
            info=info,
        )

        catalog_expiry_map = cls._get_relevant_catalog(
            exchange=data["exchange"],
            base_asset=data["base_asset"],
            info=info,
        )

        if data["strike"] != "listed" and should_check_listed:
            strikes_for_expiry = catalog_expiry_map[dt]
            if not strikes_for_expiry:
                raise ValueError(
                    f"No strikes found for expiry={dt}, base_asset={data['base_asset']} in the catalog"
                )
            for strike in data["strike"]:
                if strike not in strikes_for_expiry:
                    raise ValueError(
                        f"Strike should be listed on exchange, specifically {strike}."
                    )
        if data["strike"] == "listed":
            # Check if the expiry is listed in the catalog and if it has strikes
            if dt not in catalog_expiry_map or not catalog_expiry_map[dt]:
                logging.info(f"Listed expiries found {catalog_expiry_map.keys()}")
                raise ValueError(
                    f"Cannot get 'listed' strikes for expiry {data['expiry']}."
                )
        return data


IVParams = MoneynessIVParams | DeltaIVParams | StrikeIVParams


SubscriptionParamT = (
    MarkPxParams
    | IndexPxParams
    | IndexIVParams
    | SettlementPxParams
    | InterestRateParams
    | PerpPxParams
    | ModelParametersParams
    | IVParams
)


def is_perp_px_param(param: SubscriptionParamT) -> TypeGuard[PerpPxParams]:
    return param.feed in [
        "impact.bid.diff.twap.px",
        "impact.ask.diff.twap.px",
        "mid.diff.twap.px",
    ]


def is_iv_param(param: SubscriptionParamT) -> TypeGuard[IVParams]:
    return param.feed != "index.iv" and param.feed.endswith("iv")


class ClientBatch(BaseModel):
    frequency: int
    retransmit_frequency: int | None = None
    timestamp_delta: int | None = None
    batch: list[
        Annotated[
            SubscriptionParamT,
            Field(discriminator="feed"),
        ]
    ]
    options: BatchOptions
    client_id: str | None = None

    @field_validator("retransmit_frequency", mode="before")
    @classmethod
    def parse_retransmit_frequency(
        cls, v: str | None, info: ValidationInfo
    ) -> int | None:
        if v is None:
            return None

        if not v.endswith("ms"):
            raise ValueError(f"invalid: {v}")

        freq_millis = int(v[:-2])
        allowed = (
            info.context["batch_frequency_ms"]
            if info.context is not None and "batch_frequency_ms" in info.context
            else []
        )
        if freq_millis in allowed:
            return freq_millis * int(1e6)
        else:
            raise ValueError(f"invalid: {v}")

    @field_validator("timestamp_delta", mode="before")
    @classmethod
    def parse_timestamp_delta(cls, v: str | None, info: ValidationInfo) -> int | None:
        if v is None:
            return None

        if not v.endswith("ms"):
            raise ValueError(f"invalid: {v}")

        # if info.context is None or
        min_delta = (
            info.context["min_timestamp_delta_ms"]
            if info.context is not None and "min_timestamp_delta_ms" in info.context
            else 0
        )

        delta_millis = int(v[:-2])

        if not (min_delta <= delta_millis < 0):
            raise ValueError(f"must be between: {min_delta} and 0 milliseconds")

        # Format in nanoseconds like every other timestamp
        # unless otherwise stated
        return delta_millis * int(1e6)

    @field_validator("frequency", mode="before")
    @classmethod
    def validate_frequency(cls, v: str, info: ValidationInfo) -> int:
        if not v.endswith("ms"):
            raise ValueError(f"invalid: {v}")

        freq_millis = int(v[:-2])
        allowed = (
            info.context["batch_frequency_ms"]
            if info.context is not None and "batch_frequency_ms" in info.context
            else []
        )
        if freq_millis in allowed:
            return freq_millis * int(1e6)
        else:
            raise ValueError(f"invalid: {v}")

    @field_validator("batch")
    def validate_batch(
        cls, v: list[SubscriptionParamT], info: ValidationInfo
    ) -> list[Any]:
        # Limits for batch size will be enforced separately by the subscription
        # handler. However we should ensure that the batch is not empty here.
        assert info.context is not None
        if len(v) == 0:
            raise ValueError("Batch must have at least one item.")

        for batch in v:
            # Keep exchange unmodified for the index.iv feed
            if isinstance(batch, IndexIVParams):
                continue

            # Convert composite to internal exchange for use throughout
            # TODO revisit as the below will break the SID validation if removed/changed
            # Considering the exchange will not match what is in the db
            if hasattr(batch, "exchange") and batch.exchange == "composite":
                exchange_source_redirect_mapping = info.context[
                    "exchange_source_redirect_mapping"
                ]
                assert isinstance(
                    exchange_source_redirect_mapping, ExchangeSourceMappingConfig
                )
                asset_type = getattr(batch, "asset", None)
                if not asset_type:
                    if isinstance(batch, InterestRateParams):
                        asset_type = "future"
                    elif isinstance(batch, SettlementPxParams | IVParams):
                        asset_type = "option"
                    else:
                        raise ValueError(
                            f"Unsupported batch type: {type(batch).__name__}"
                        )

                batch.exchange = exchange_source_redirect_mapping.get_exchange(
                    asset_type=asset_type,
                    exchange=batch.exchange,
                    currency=batch.base_asset,
                )
        return v

    @model_validator(mode="before")
    def validate_sids(
        cls, data: dict[str, Any], info: ValidationInfo
    ) -> dict[str, Any]:
        # Validate uniqueness of sids
        sids: set[str] = set()
        batch_length = len(data["batch"])
        for batch_item in data["batch"]:
            sid = batch_item.get("sid")
            # If batch length is greater than 1, sid must be defined for all items. Check before skipping when not defined
            if batch_length > 1 and not sid:
                raise ValueError("Sid must be defined for all items in batch.")
            elif not sid:
                continue
            if sid in sids:
                raise ValueError("All sids must be unique.")
            sids.add(sid)

        # Skip further validation if no permissioning context or if assert_sid_equals_contract is False
        if info.context is None or not info.context.get("signing_config", {}).get(
            "assert_sid_equals_contract", False
        ):
            return data

        # Validate that the signature domain equals sid if necessary
        verifying_contract = (
            data.get("options", {})
            .get("signature", {})
            .get("domain", {})
            .get("verifying_contract")
        )

        if verifying_contract:
            verifying_contract = verifying_contract.lower()
            # Should really be of length one due to the unique constraint so n items cannot match the contract
            for sid in sids:
                if verifying_contract != sid.lower():
                    raise ValueError(
                        "When both sid and verifying_contract are present, they must match."
                    )

        return data

    @model_validator(mode="after")
    def check_sid_in_decimals(self) -> Self:
        if isinstance(self.options.format.decimals, int):
            return self
        for feed_req in self.batch:
            if hasattr(feed_req, "sid") and not feed_req.sid:
                raise ValueError(
                    "Could not find subscription id and a decimals object was defined. Either provide an sid or set decimals to an int."
                )
            if (
                hasattr(feed_req, "sid")
                and feed_req.sid not in self.options.format.decimals
            ):
                raise ValueError(
                    f"Could not find subscription id {feed_req.sid} in decimals object. Decimal keys supplied {list(self.options.format.decimals.keys())}"
                )

        return self


class ActiveBatchSubscription(BaseModel):
    batch: ClientBatch
    next_flush: int
    flush_delta: int
    function_ids: set[str] | None = None


"""
    Comprehensive types for the subscriptions
    * Postfix *Pre is for types pre-formatting for transmission
    * Postfix *Post is for types representing what is sent over the wire
"""

ProcessedNumberT = str | int | float
StreamUpdateT = dict[str, Any]
# asset -> currency -> CatalogExpiryStrikesMap
Instruments = dict[str, dict[str, CatalogExpiryStrikesMap]]
# exchange -> Instruments
CatalogCache = dict[str, Instruments]

"""
    Derive
"""


class DeriveBasePre(TypedDict):
    confidence: Number


class DeriveBasePost(TypedDict):
    confidence: ProcessedNumberT


class DeriveGenericPxPre(DeriveBasePre):
    v: Number


class DeriveGenericPxPost(DeriveBasePost):
    v: ProcessedNumberT


class DeriveSettlementPxPre(DeriveBasePre):
    expiry: Number
    twp_start: Number
    twp_current: Number
    fwd_basis: Number


class DeriveSettlementPxPost(DeriveBasePost):
    expiry: ProcessedNumberT
    twp_start: ProcessedNumberT
    twp_current: ProcessedNumberT
    fwd_basis: ProcessedNumberT


class DeriveInterestRatePre(DeriveBasePre):
    expiry: Number
    v: Number


class DeriveInterestRatePost(DeriveBasePost):
    expiry: ProcessedNumberT
    v: ProcessedNumberT


class DeriveModelParamsPre(DeriveBasePre):
    expiry: Number
    alpha: Number
    beta: Number
    rho: Number
    m: Number
    sigma: Number
    forward: Number
    ref_expiry: Number


class DeriveModelParamsPost(DeriveBasePost):
    expiry: ProcessedNumberT
    alpha: ProcessedNumberT
    beta: ProcessedNumberT
    rho: ProcessedNumberT
    m: ProcessedNumberT
    sigma: ProcessedNumberT
    forward: ProcessedNumberT
    ref_expiry: ProcessedNumberT


DerivePreT = (
    DeriveGenericPxPre
    | DeriveSettlementPxPre
    | DeriveInterestRatePre
    | DeriveModelParamsPre
)
DerivePostT = (
    DeriveGenericPxPost
    | DeriveSettlementPxPost
    | DeriveInterestRatePost
    | DeriveModelParamsPost
)


class DeriveBatchPre(TypedDict):
    data: ByteArray
    deadline: Number
    timestamp: Number


class DeriveBatchPost(TypedDict):
    data: DerivePostT
    deadline: ProcessedNumberT
    timestamp: ProcessedNumberT


"""
    Grvt
"""


class GrvtBasePre(TypedDict):
    sid: NotRequired[str | Number]


class GrvtBasePost(TypedDict):
    sid: str


class GrvtGenericPxPre(GrvtBasePre):
    v: Number


class GrvtGenericPxPost(GrvtBasePre):
    v: Number


class GrvtSettlementPxPre(GrvtBasePre):
    twp_start: Number
    twp_current: Number
    fwd_basis: Number
    expiry: Number


class GrvtSettlementPxPost(GrvtBasePost):
    twp_start: ProcessedNumberT
    twp_current: ProcessedNumberT
    fwd_basis: ProcessedNumberT
    expiry: ProcessedNumberT


class GrvtModelParamsPre(GrvtBasePre):
    alpha: Number
    beta: Number
    rho: Number
    m: Number
    sigma: Number


class GrvtModelParamsSABRPre(GrvtBasePre):
    alpha: Number
    rho: Number
    volvol: Number


class GrvtModelParamsPost(GrvtBasePost):
    alpha: ProcessedNumberT
    beta: ProcessedNumberT
    rho: ProcessedNumberT
    m: ProcessedNumberT
    sigma: ProcessedNumberT


class GrvtModelParamsSABRPost(GrvtBasePost):
    alpha: ProcessedNumberT
    rho: ProcessedNumberT
    volvol: ProcessedNumberT


class GreeksPre(TypedDict):
    delta: NotRequired[Number | list[Number]]
    gamma: NotRequired[Number | list[Number]]
    vega: NotRequired[Number | list[Number]]
    theta: NotRequired[Number | list[Number]]
    volga: NotRequired[Number | list[Number]]
    vanna: NotRequired[Number | list[Number]]


class GrvtMarkPxPre(GrvtBasePre, GreeksPre):
    v: Number | list[Number]
    strike: NotRequired[list[Number]]


class GrvtMarkPxMoneynessPre(GrvtBasePre, GreeksPre):
    v: Number | list[Number]
    moneyness: NotRequired[list[Number]]


class GreeksPost(TypedDict):
    delta: NotRequired[ProcessedNumberT | list[ProcessedNumberT]]
    gamma: NotRequired[ProcessedNumberT | list[ProcessedNumberT]]
    vega: NotRequired[ProcessedNumberT | list[ProcessedNumberT]]
    theta: NotRequired[ProcessedNumberT | list[ProcessedNumberT]]
    volga: NotRequired[ProcessedNumberT | list[ProcessedNumberT]]
    vanna: NotRequired[ProcessedNumberT | list[ProcessedNumberT]]


class GrvtMarkPxPost(GrvtBasePost, GreeksPost):
    v: ProcessedNumberT | list[ProcessedNumberT]
    strike: NotRequired[list[ProcessedNumberT]]


class GrvtMarkPxMoneynessPost(GrvtBasePost, GreeksPost):
    v: ProcessedNumberT | list[ProcessedNumberT]
    moneyness: NotRequired[list[ProcessedNumberT]]


GrvtPreT = (
    GrvtGenericPxPre
    | GrvtSettlementPxPre
    | GrvtModelParamsPre
    | GrvtModelParamsSABRPre
    | GrvtMarkPxPre
    | GrvtMarkPxMoneynessPre
)

GrvtPostT = (
    GrvtGenericPxPost
    | GrvtSettlementPxPost
    | GrvtModelParamsPost
    | GrvtModelParamsSABRPost
    | GrvtMarkPxPost
    | GrvtMarkPxMoneynessPost
)


class GrvtBatchPre(TypedDict):
    values: list[GrvtPreT]
    timestamp: Number


class GrvtBatchPost(TypedDict):
    values: GrvtPostT
    timestamp: ProcessedNumberT


"""
    Generic Subscriptions
"""


class OptionalSidPre(TypedDict):
    sid: NotRequired[str | Number | None]


class GenericPxPre(OptionalSidPre):
    v: Number


"""
    EthosX
"""


class BaseIVPre(OptionalSidPre):
    v: list[Number]


class StrikeIVPre(BaseIVPre):
    strike: list[Number]


class MoneynessIVPre(BaseIVPre):
    moneyness: list[Number]


class DeltaIVPre(BaseIVPre):
    delta: list[Number]


AllIVPre = StrikeIVPre | MoneynessIVPre | DeltaIVPre


class IVBatchPre(TypedDict):
    values: list[StrikeIVPre] | list[MoneynessIVPre] | list[DeltaIVPre]
    timestamp: Number


class BaseIVPost(GrvtBasePre):
    v: list[ProcessedNumberT]


class StrikeIVPost(BaseIVPost):
    strike: list[ProcessedNumberT]


class MoneynessIVPost(BaseIVPost):
    moneyness: list[ProcessedNumberT]


class DeltaIVPost(BaseIVPost):
    delta: list[ProcessedNumberT]


IVPostT = StrikeIVPost | MoneynessIVPost | DeltaIVPost


class IVBatchPost(TypedDict):
    values: IVPostT
    timestamp: ProcessedNumberT


"""
    D2x
"""


class D2xModelParamsPre(TypedDict):
    expiry: Number
    alpha: Number
    beta: Number
    rho: Number
    m: Number
    sigma: Number
    forward: Number


D2xIVPre = dict[str, Number]

D2xPreT = D2xModelParamsPre | D2xIVPre

"""
    Common / Union Types
"""

BatchPreT = GrvtBatchPre | DeriveBatchPre | IVBatchPre
BatchPostT = GrvtBatchPost | DeriveBatchPost | IVBatchPost

ValPreT = (
    DerivePreT
    | GrvtPreT
    | StrikeIVPre
    | MoneynessIVPre
    | DeltaIVPre
    | StrikeIVPre
    | GenericPxPre
    | D2xPreT
)


class ComputedSignature(TypedDict):
    r: str
    s: str
    v: str


class SignedPayload(TypedDict, total=False):
    data: BatchPostT
    signature: ComputedSignature
    client_id: str | None


class UnsignedPayload(TypedDict, total=False):
    data: BatchPostT
    client_id: str | None


class SettlementPxTTL(TypedDict):
    enabled: bool
    seconds: int


class SettlementPxLambdaArgs(TypedDict):
    currency: str
    expiry: str
    ttl: NotRequired[SettlementPxTTL]


class SettlementPxLambdaResp(TypedDict, total=False):
    qualified_name: str
    timestamp: int
    is_final: bool
    twap_start: float
    twap_current: float
    total_twp_start: float
    total_twp_current: float

    # Fields enriched at a later stage from model params
    fwd: float | None
    fwd_basis: float | None
    confidence: float | None


class ImmutableSubscriptionReq(BaseModel):
    instrument: str
    qualified_name: str
    format: BatchFormat | None = (
        None  # TODO Remove the optionality once GRVT have migrated to their new API key
    )

    model_config = ConfigDict(extra="allow")


class ImmutableIdentifierCacheKey(NamedTuple):
    qualified_name: str
    instrument_id: str


ValidFlexCalcFunctions = Literal["volSmileFlex", "theoreticalPriceFlex"]


class VolSmileFlexData(TypedDict):
    expiry: str
    exchange: str
    base_asset: str
    model: str
    type: str
    values: NotRequired[str | float | dict[str, list[float]]]


class TheoreticalPxFlexData(TypedDict):
    asset_class: str
    base: str
    quote: str
    expiry: NotRequired[str]
    ref_expiry: NotRequired[str]
    strikes: NotRequired[list[float | Literal["atm_spot", "atm_forward"]]]
    moneyness: NotRequired[list[float]]
    types: NotRequired[list[str]]
    greeks: NotRequired[list[Greeks]]


LiveFunctionData = VolSmileFlexData | TheoreticalPxFlexData


class InternalSubscriptionReq(TypedDict):
    calc_name: ValidFlexCalcFunctions
    data: LiveFunctionData


@dataclass
class LiveFunction:
    function_id: str
    calc: ValidFlexCalcFunctions
    data: LiveFunctionData
    subscribers: set[str]


class DynamoPollQN(TypedDict):
    frequency: int
    qualified_names: list[str]
    next_poll: NotRequired[int]


MapIvReturns = AllIVPre | D2xIVPre | None


def _is_permission_obj(s: Any) -> TypeGuard[Permission]:
    return isinstance(s, dict) and "type" in s


def _check_permission(
    perm_key: str,
    value: str | float | list[str] | list[float] | datetime,
    info: ValidationInfo,
    default_perm: Permission | None = None,
) -> bool:
    """
    Returns True if the permission is valid for the given value else False.

    :param perm_key: Key we use too lookup the permission
    :param value: Value to check against the permission
    :param info: ValidationInfo containing permissions
    :param default_perm (Optional): Default permission to use if not set in client layer

    :return bool
    """

    if info.context is not None and info.context.get(perm_key):
        permissions: Permission | list[str] | bool | PermissionDict = info.context.get(
            perm_key
        )
        if isinstance(permissions, dict) and "permission_data_key" in permissions:
            permissions = cast(PermissionDict, permissions)
            permission_data_key = permissions.get("permission_data_key")
            if permission_data_key in info.data:
                permissions = permissions["data"].get(
                    info.data[permission_data_key], False
                )
    elif default_perm is not None:
        permissions = default_perm
    else:
        return False

    if isinstance(permissions, list):
        if (
            "*" in permissions or value in permissions
        ):  # TODO migrate hard coded permissions to use the below format instead of '*' in list
            return True
    elif isinstance(permissions, bool):
        return permissions
    if _is_permission_obj(permissions):
        if permissions["type"] == "any":
            return True
        elif permissions["type"] == "in":
            if (
                (
                    isinstance(value, list)
                    and all(v in permissions["value"] for v in value)
                )
                or (value in permissions["value"])
                or (not value and permissions["optional"])
            ):
                return True
        elif permissions["type"] == "range":

            def _check_in_range(v: float | str | datetime) -> bool:
                start: float | datetime
                end: float | datetime
                if isinstance(v, str):
                    logging.warning("Cannot check string is in range")
                    return False

                if isinstance(v, datetime):
                    assert isinstance(permissions["value"]["start"], timedelta)
                    assert isinstance(permissions["value"]["end"], timedelta)
                    start = datetime.now(tz=UTC) + permissions["value"]["start"]
                    end = datetime.now(tz=UTC) + permissions["value"]["end"]
                    return start <= v <= end

                else:
                    assert isinstance(permissions["value"]["start"], float)
                    assert isinstance(permissions["value"]["end"], float)
                    return (
                        permissions["value"]["start"]
                        <= v
                        <= permissions["value"]["end"]
                    )

            if isinstance(value, float | datetime) and _check_in_range(value):
                return True
            elif isinstance(value, list):
                if all(_check_in_range(v) for v in value):
                    return True

        else:
            logging.warning(f"Incorrect permission type found {permissions=}")
            raise ValueError("Incorrect type found for permission.")

    return False


def _validate_permission(
    perm_key: str,
    value: str | float | list[str] | list[float] | datetime,
    info: ValidationInfo,
    error_msg: str | None = "Permission denied, please contact us.",
    default_perm: Permission | None = None,
) -> bool:
    """
    Validates the permission for a given key. Otherwise throws ValueError.

    :param perm_key: Key we use too lookup the permission
    :param value: string to check if its in the list, short circuits '*' and returns True
    :param info: ValidationInfo containing permissions
    :param error_msg (Optional): Message to propagate to the user
    :param default_perm (Optional): Default permission to use if not set in client layer

    :return bool
    """

    # We only want to return when True otherwise we can raise the error at the end.
    if _check_permission(perm_key, value, info, default_perm):
        return True
    else:
        raise ValueError(error_msg)


class MissingDataError(LookupError):
    pass


class DataSanityCheckError(ValueError):
    pass


class DataSanityCheckCapped(DataSanityCheckError):
    # NOTE: the intention is that by default (not handling this case),
    # any users of the _sanity_check function will raise.
    # extra handling is optional
    def __init__(self, capped: float, *args: Any, **kwargs: Any) -> None:
        self.capped = capped
        super().__init__(*args, **kwargs)
