from __future__ import annotations

import asyncio
import logging
import multiprocessing as mp
import os
import time
from datetime import UTC, datetime
from typing import Protocol, TypeVar

import pydantic
import utils_api
import utils_general
from aiobotocore.session import get_session
from datagrabber import (
    CatalogAssetType,
    CatalogItem,
    ExchangeSourceMappingConfig,
    get_instruments_async,
)
from types_aiobotocore_dynamodb import DynamoDBClient
from utils_api import IndexDataSetConfig
from utils_aws import AsyncSSMManager, MultiSSMParameter
from utils_general import log_bsdebug, setup_python_logger

from wsapi.api_types import CatalogCache, CatalogExpiryStrikesMap
from wsapi.config import INDICES_SSM_PARAM, SOURCE_REDIRECT_MAP_PATH
from wsapi.utils.ssm_loader import ConfigHolder

_T = TypeVar("_T", covariant=True)


class _Queue(Protocol[_T]):
    """Queue interface to allow for mocking in tests."""

    def get(self) -> _T: ...


class CatalogInstrumentManager:
    def __init__(self) -> None:
        # Catalog -> instruments
        self._catalog_instruments: CatalogCache = utils_general.nested_dict()

    @property
    def catalog_instruments(self) -> CatalogCache:
        return self._catalog_instruments

    async def queue_consumer(
        self,
        catalog_queue: _Queue[CatalogCache | None],
    ) -> None:
        """
        This function is intended to be run in the server process to consume the
        catalog updates from the catalog worker process.
        """
        logging.info("Starting catalog queue consumer")

        while True:
            try:
                catalog = await asyncio.to_thread(catalog_queue.get)
                if catalog is None:
                    break

                log_bsdebug(
                    "Catalog queue consumer received %d instruments", len(catalog)
                )
                self._catalog_instruments = catalog
            except Exception:
                logging.exception("Catalog queue consumer failed")
                await asyncio.sleep(1)


def _construct_expiry_map(
    instruments: list[CatalogItem],
) -> CatalogExpiryStrikesMap:
    """
    Stores relevant information about expiries in our catalog. Used for options & futures.
    """
    expiry_map: CatalogExpiryStrikesMap = {}
    for inst in instruments:
        dt = utils_general.from_iso(inst["expiry"])
        strike = inst["strike"] if "strike" in inst else None
        if not strike:
            # handle futures and synthetic options
            expiry_map[dt] = set()
            continue

        if dt not in expiry_map:
            expiry_map[dt] = {strike}
        else:
            expiry_map[dt].add(strike)
    return expiry_map


async def _fetch_and_apply_catalog_expiry_set(
    dynamodb_client: DynamoDBClient,
    catalog_cache: CatalogCache,
    exchanges: list[str],
    asset_types: list[CatalogAssetType],
    currencies: list[str],
    timestamp: str,
) -> None:
    """
    Fetches each slice individually to save resources looping through the instruments to bucket into nested dict.
    """
    for exchange in exchanges:
        for asset in asset_types:
            for ccy in currencies:
                catalog_entries = await get_instruments_async(
                    exchanges=[exchange],
                    start=timestamp,
                    end=timestamp,
                    asset_types=[asset],
                    fields=[],
                    base_assets=[ccy],
                    dynamodb_client=dynamodb_client,
                )
                catalog_cache[exchange][asset][ccy] = _construct_expiry_map(
                    catalog_entries
                )


async def get_bs_index_currencies_by_asset_type(
    asset_types: list[CatalogAssetType],
    index_data_set_config: ConfigHolder[IndexDataSetConfig],
) -> list[str]:

    index_config = index_data_set_config.get_config()
    ccys: set[str] = set()

    for asset_type in asset_types:
        ccys.update(index_config[asset_type].base_assets())

    return list(ccys)


def extract_redirect_exchanges(
    source_redirect_mapping_config: ConfigHolder[ExchangeSourceMappingConfig],
    asset_types: list[CatalogAssetType],
) -> set[str]:
    """
    Extracts all unique exchange from the ExchangeSourceMappingConfig mapping.

    Args:
        mapping: The nested dictionary structure containing exchange mappings

    Returns:
        A list of unique exchanges
    """
    unique_exchanges = set()
    source_redirect_mapping = source_redirect_mapping_config.get_config().root

    for asset_type, exchange_redirect in source_redirect_mapping.items():
        if asset_type in asset_types:
            for currency_to_redirect_exchange in exchange_redirect.values():
                for redirect_exchange in currency_to_redirect_exchange.values():
                    unique_exchanges.add(redirect_exchange)

    return unique_exchanges


async def _get_updated_catalog(
    dynamodb_client: DynamoDBClient,
    index_data_set_config: ConfigHolder[IndexDataSetConfig],
    source_redirect_mapping_config: ConfigHolder[ExchangeSourceMappingConfig],
) -> CatalogCache:
    """
    This worker is used to update the cached catalog instruments so that
    we can check against the listed instruments
    """

    start = datetime.now(UTC).isoformat()
    catalog_cache = utils_general.nested_dict()
    _blockscholes_asset_types: list[CatalogAssetType] = ["option", "future"]

    bs_index_currencies = await get_bs_index_currencies_by_asset_type(
        _blockscholes_asset_types, index_data_set_config
    )
    redirect_exchanges = extract_redirect_exchanges(
        source_redirect_mapping_config=source_redirect_mapping_config,
        asset_types=_blockscholes_asset_types,
    )

    await _fetch_and_apply_catalog_expiry_set(
        dynamodb_client,
        catalog_cache,
        currencies=bs_index_currencies,
        # no need for both v2composite and blockscholes
        exchanges=["blockscholes", *(redirect_exchanges - {"v2composite"})],
        asset_types=_blockscholes_asset_types,
        timestamp=start,
    )

    await _fetch_and_apply_catalog_expiry_set(
        dynamodb_client,
        catalog_cache,
        currencies=["PT-SUSDE", "YT-SUSDE"],
        exchanges=["blockscholes"],
        asset_types=["future"],
        timestamp=start,
    )
    return catalog_cache


def run_catalog_worker(
    catalog_queue: mp.SimpleQueue[CatalogCache],
    interval_s: int,
    offset_s: int,
) -> None:
    """
    Worker function for retrieving the catalog instruments - intended to be
    run in a separate process.

    :param interval_s: frequency of worker calls (in seconds)
    :param offset_s: offset of workers (in seconds)
    :return: None
    """
    setup_python_logger(level=os.environ.get("LOG_LEVEL", logging.INFO))

    logging.info(f"Initialising catalog worker {interval_s=} {offset_s=}")
    asyncio.run(_run_catalog_worker_async(catalog_queue, interval_s, offset_s))


async def _run_catalog_worker_async(
    catalog_queue: mp.SimpleQueue[CatalogCache],
    interval_s: int,
    offset_s: int,
) -> None:
    next_refresh = int((time.time() // interval_s + 1) * interval_s) + offset_s
    session = get_session()

    async with (
        session.create_client("dynamodb") as dynamodb_client,
        session.create_client("ssm") as ssm_client,
    ):
        index_data_set_config = ConfigHolder(
            validator_fn=pydantic.TypeAdapter(IndexDataSetConfig).validate_json,
        )
        source_redirect_mapping_config = ConfigHolder(
            validator_fn=ExchangeSourceMappingConfig.model_validate_json,
        )
        ssm_params = [
            MultiSSMParameter(
                path=INDICES_SSM_PARAM,
                setter=index_data_set_config.set_config,
                merge_fn=utils_api.merge_config_with_deviation_threshold,
            ),
            MultiSSMParameter(
                path=SOURCE_REDIRECT_MAP_PATH,
                setter=source_redirect_mapping_config.set_config,
            ),
        ]
        ssm_manager = AsyncSSMManager(ssm_client, ssm_params)

        async with ssm_manager:
            while True:
                try:
                    # Ensure that the first iteration runs before any delays in order to reach a steady
                    # state as quick as possible
                    instruments = await _get_updated_catalog(
                        dynamodb_client=dynamodb_client,
                        index_data_set_config=index_data_set_config,
                        source_redirect_mapping_config=source_redirect_mapping_config,
                    )
                    catalog_queue.put(instruments)
                except Exception:
                    logging.exception("Catalog worker iteration failed")

                wait_time = next_refresh - time.time()
                next_refresh += interval_s
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
