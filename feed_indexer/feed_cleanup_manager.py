import logging
import time
from collections import defaultdict
from typing import Literal, cast

import utils_general
import utils_oracle
from utils_oracle import Confi<PERSON><PERSON><PERSON><PERSON>, DecodedFeed, Network

from .catalog_filter_manager import CatalogFilterManager
from .config import EXPIRED_INSTRUMENTS_TIMEDELTA
from .typings import (
    BaseFeedMetaData,
    FeedKeyToIndexMap,
)
from .utils import extract_feed_metadata, get_parent_asset_types


class FeedCleanupManager:
    """
    A class responsible for determining which feeds should be removed from the chain.

    This class encapsulates the logic for deciding which feeds should be kept or removed
    based on various criteria such as expiry dates, constant tenors, and custom expiry ranges.
    It can also extract constant tenor information directly from the ChainCatalogFilters.
    """

    def __init__(
        self,
        catalog_filter_manager: CatalogFilterManager,
        feed_config: utils_oracle.ConfigModel | None = None,
    ):
        """
        Initialize the FeedCleanupManager.

        Args:
            catalog_filter_manager: Manager for handling catalog filters
            feed_config: Optional feed configuration model
        """
        self._catalog_filter_manager = catalog_filter_manager
        self._feed_config = feed_config or ConfigLoader.get_feed_definitions()

    def get_feeds_to_remove_for_chain(
        self,
        chain_id: Network,
        chain_decimals: int,
        confirmed_feeds: FeedKeyToIndexMap,
    ) -> FeedKeyToIndexMap:
        """
        Get feeds to remove records for a specific chain ID.

        Args:
            chain_id: The chain ID to process
            chain_decimals: The number of decimals used to convert the expiry value
            confirmed_feeds: A dictionary of confirmed feeds to check for expiry

        Returns:
            A dictionary of feed keys to feed indices that should be removed
        """
        feed_records_to_remove: FeedKeyToIndexMap = {}
        feed_removal_reasons_map: dict[str, list[str]] = defaultdict(list)

        for feed_key, feed_index in confirmed_feeds.items():
            decoded_feed = feed_index.feed.get_decoded_feed()

            if self._should_remove_feed(
                decoded_feed=decoded_feed,
                chain_id=chain_id,
                chain_decimals=chain_decimals,
                reasons_map=feed_removal_reasons_map,
            ):
                feed_records_to_remove[feed_key] = feed_index

        if feed_removal_reasons_map:
            # log one grouped warning per chain
            msg_lines = [
                f" • {reason}: {', '.join(strs)}"
                for reason, strs in feed_removal_reasons_map.items()
            ]
            logging.warning(
                f"Feeds removed for chain {chain_id}:\n" + "\n".join(msg_lines)
            )

        return feed_records_to_remove

    def _feed_has_expiry(self, decoded_feed: DecodedFeed) -> bool:
        return bool(decoded_feed.parameters.get_param_value("expiry", "other"))

    def _is_feed_expired(
        self, decoded_feed: DecodedFeed, chain_decimals: int
    ) -> bool:
        # Add buffer time before considering a feed expired
        ref_timestamp = int(
            (
                utils_general.to_datetime(time.time_ns())
                - EXPIRED_INSTRUMENTS_TIMEDELTA
            ).timestamp()
            * 1e9
        )

        return decoded_feed.is_expired_feed(
            chain_decimals=chain_decimals,
            unix_timestamp_ns=ref_timestamp,
        )

    def _is_within_tenor_range(
        self,
        chain_id: Network,
        chain_decimals: int,
        decoded_feed: DecodedFeed,
        base_feed_metadata: BaseFeedMetaData,
    ) -> bool:
        """
        Check if a feed with a date-based expiry falls within configured tenor ranges.

        Args:
            decoded_feed: The decoded feed object
            chain_id: The chain ID
            chain_decimals: The number of decimals used for the chain
            base_feed_metadata: Extracted feed metadata

        Returns:
            True if the feed's tenor is within configured ranges, False otherwise
        """
        exchange, base_asset, parent_asset_classes = base_feed_metadata

        # Get all filters that match the basic criteria
        matching_filters = []
        for filter in self._catalog_filter_manager.get_filters_for_chain(
            chain_id
        ):
            if (
                filter.asset_class in parent_asset_classes
                and exchange in filter.exchanges
                and base_asset in filter.base_assets
            ):
                matching_filters.append(filter)

        # No filters with listing_filter to check against
        if not any(filter.listing_filter for filter in matching_filters):
            return True

        # Get expiry date from feed
        expiry_timestamp = decoded_feed.parameters.get_param_value(
            "expiry", "other"
        )
        assert isinstance(expiry_timestamp, int)

        expiry_timestamp_scaled = expiry_timestamp / 10**chain_decimals
        expiry_timestamp_ns = expiry_timestamp_scaled * 1e9

        # todo: lookup the catalog matching catalog from the catalog filter managers
        #  internal set of catalogs.

        tenor_days = (expiry_timestamp_ns - time.time_ns()) / (
                24 * 3600 * 1e9
        )



        try:
            # Get expiry date from feed
            expiry_timestamp = decoded_feed.parameters.get_param_value(
                "expiry", "other"
            )
            assert isinstance(expiry_timestamp, int)

            expiry_timestamp_scaled = expiry_timestamp / 10**chain_decimals
            expiry_timestamp_ns = expiry_timestamp_scaled * 1e9

            # todo: lookup the catalog matching catalog from the catalog filter managers
            #  internal set of catalogs.

            tenor_days = (expiry_timestamp_ns - time.time_ns()) / (
                24 * 3600 * 1e9
            )

            for filter in matching_filters:
                if (
                    filter.listing_filter
                    and filter.listing_filter.is_tenor_within_range(
                        tenor_days=tenor_days,
                    )
                ):
                    return True

            return False
        except Exception as e:
            logging.warning(f"Error checking tenor range: {e}")
            # In case of error, keep the feed to be safe
            return True

    def _should_remove_feed(
        self,
        decoded_feed: DecodedFeed,
        chain_id: Network,
        chain_decimals: int,
        reasons_map: dict[str, list[str]] | None = None,
    ) -> bool:
        """
        Determine whether a feed should be removed based on its expiry status,
        constant tenor configuration, expiry range configuration, and whether it matches
        any entry in the ChainCatalogFilters.

        The checks are performed in the following optimized order:

        1. First, check if the feed matches any catalog filter:
           - This is the most fundamental check and can quickly filter out feeds that don't match
             any filter regardless of other attributes.

        2. For feeds with an expiry:
           - Check if the feed is expired or outside the configured expiry range.

        3. For feeds that qualify as constant tenor feeds:
           - Check if the feed is defined by the constant tenor configuration.

        Args:
            decoded_feed: The decoded feed object
            chain_id: The chain ID
            chain_decimals: The number of decimals used to convert the expiry value

        Returns:
            True if the feed should be removed, False otherwise
        """
        if reasons_map is None:
            reasons_map = defaultdict(list)

        feed_str = decoded_feed.feed_to_string(chain_decimals=chain_decimals)
        base_feed_metadata = extract_feed_metadata(
            decoded_feed=decoded_feed, feed_config=self._feed_config
        )

        # Use CatalogFilterManager for filter matching
        if not self._catalog_filter_manager.feed_matches_base_catalog_filter(
            chain_id=chain_id,
            base_feed_metadata=base_feed_metadata,
            decoded_feed=decoded_feed,
            chain_decimals=chain_decimals,
        ):
            reasons_map["No Matching Catalog Filter"].append(feed_str)
            return True

        # If the feed has an expiry, perform additional checks
        if self._feed_has_expiry(decoded_feed):

            if decoded_feed.is_constant_tenor_feed():
                if not self._is_valid_constant_tenor(
                    decoded_feed=decoded_feed,
                    chain_decimals=chain_decimals,
                    chain_id=chain_id,
                ):
                    reasons_map["Undefined Constant Tenor"].append(feed_str)
                    return True
            else:
                if self._is_feed_expired(
                    decoded_feed=decoded_feed, chain_decimals=chain_decimals
                ):
                    reasons_map["Feed is Expired"].append(feed_str)
                    return True

                elif not self._is_within_tenor_range(
                    decoded_feed=decoded_feed,
                    chain_decimals=chain_decimals,
                    chain_id=chain_id,
                    base_feed_metadata=base_feed_metadata,
                ):
                    reasons_map["Tenor out of Range"].append(feed_str)
                    return True

        # If we reach here, the feed passed all checks and should be kept
        return False

    def _is_valid_constant_tenor(
        self,
        decoded_feed: DecodedFeed,
        chain_decimals: int,
        chain_id: Network,
    ) -> bool:
        """
        Return ``True`` if *decoded_feed* represents an **explicitly-configured
        constant-tenor derivative** (e.g. ``7d``, ``480m``); otherwise
        return ``False``.

        The decision is performed in two consecutive stages:

        1. **Reconstruct the tenor from the expiry**

           * Search ``decoded_feed.parameters.other`` for the key ``"expiry"``.
           * If present, treat the integer value (scaled by *chain_decimals*)
             as *days to maturity* and

           * If the key is missing *or* the value is not an ``int``, the feed
             cannot be mapped to a constant tenor and the function immediately
             returns ``False``.

        2. **Verify that the tenor is allowed by configuration**

           * Obtain the feed's *parent asset types* with
            ``get_parent_asset_types(decoded_feed.feed_type, self._feed_config)``.
           * Only the parents ``"future"`` and ``"option"`` are relevant;
             all others are ignored.
           * For each relevant parent:

             * Retrieve its list of allowed tenors from
               *asset_type_to_constant_tenors*.
             * If the reconstructed tenor appears in that list,
               the feed is considered configured and the function returns
               ``True`.

        If no relevant parent lists the tenor, the feed is assumed to be
        unconfigured and the function returns ``False``.

        .. note::

           * The assertion ``assert isinstance(value, int)`` guarantees that the
             expiry value is suitable for numeric conversion.
           * Spot, index, and other non-derivative asset types are deliberately
             excluded from this check.

        :param DecodedFeed decoded_feed: Fully decoded oracle feed, including
            its ``feed_type``.
        :param int chain_decimals: Number of on-chain decimals used when the
            raw expiry value was stored.
        :param AssetTypeToCurrencyConstantTenors asset_type_to_constant_tenors: Mapping
            such as ``{"future": {"BTC": ["7d", "14d"]}}`` that
            enumerates the constant tenors explicitly permitted for each
            derivative family.
        :returns: ``True`` if *any* relevant parent asset type explicitly
            permits the feed's reconstructed tenor; ``False`` otherwise.
        :rtype: bool
        """

        expiry_val = decoded_feed.parameters.get_param_value("expiry", "other")
        if not expiry_val:
            return False

        decoded_feed_str = decoded_feed.feed_to_string(
            chain_decimals=chain_decimals
        )
        expiry_part = decoded_feed_str.split("expiry:")[1]

        if "+" in expiry_part:
            constant_tenor = expiry_part.split("+")[0]
        else:
            constant_tenor = expiry_part

        # extract currency
        currency = decoded_feed.parameters.get_param_value(
            "BaseAssetEnum", "enumerable"
        )
        if currency is None:
            currency = ""

        assert isinstance(currency, str)
        currency = currency.upper()

        # Options and futures produce many feed_types as defined in the config. We need to ensure that we are properly cleaning
        # up these constant tenor feeds generated by the parent assets. e.g interest_rate constant tenor feeds need to be
        # cleaned up when neither options nor futures specify any constant tenors
        parent_asset_types = get_parent_asset_types(
            feed_type=decoded_feed.feed_type, feed_config=self._feed_config
        )

        asset_type_constant_tenors = (
            self._catalog_filter_manager.get_constant_tenors_for_chain(
                chain_id=chain_id
            )
        )
        # Check if the feed's type and tenor are in the allowed list
        for asset_type in parent_asset_types:
            if asset_type == "future" or asset_type == "option":

                asset_type = cast(Literal["future", "option"], asset_type)
                allowed_constant_tenors = asset_type_constant_tenors.get(
                    asset_type, {}
                ).get(currency, [])

                if constant_tenor in allowed_constant_tenors:
                    return True

        return False
