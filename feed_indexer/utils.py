import logging
import os
from collections import defaultdict
from collections.abc import Generator
from datetime import datetime
from typing import TypeVar

import utils_oracle
from utils_general import from_iso
from utils_oracle import DecodedFeed, FeedIndex

from .typings import (
    BaseFeedMetaData,
    ChainIdGroupedFeedIndices,
    FeedIndexerInternalFeeds,
)


def is_expired_date(reference_date: datetime, target_date: datetime) -> bool:
    """Check if a target date is expired relative to a reference date.

    A target date is considered expired if it's more than the reference date.

    Args:
        reference_date: The date to compare against (usually current time)
        target_date: The date to check for expiration

    Returns:
        bool: True if the target date is expired, False otherwise
    """

    if reference_date > target_date:
        return True
    return False


T = TypeVar("T")


def chunked(
    iterable: list[T], chunk_size: int
) -> Generator[list[T], None, None]:
    if chunk_size <= 0:
        raise ValueError("Chunk size must be greater than 0")
    for i in range(0, len(iterable), chunk_size):
        yield iterable[i : i + chunk_size]


def group_feed_indices_by_chain_id(
    feed_indices: list[FeedIndex],
) -> ChainIdGroupedFeedIndices:
    grouped = defaultdict(list)
    for fi in feed_indices:
        grouped[utils_oracle.Network(fi.chain_id)].append(fi)
    return dict(grouped)


def create_message_group_id(
    chain_id: utils_oracle.Network, version: int
) -> str:
    return f"{chain_id.value}_{version}"


def log_feed_state_summary(
    feed_indexer_internal_state: FeedIndexerInternalFeeds,
    log_str: str | None = None,
) -> None:
    # Optional: Log a summary
    summary = {}
    for chain_id, chain_feed_state in feed_indexer_internal_state.items():
        summary[chain_id] = {
            feed_state: len(feed_key_to_index)
            for feed_state, feed_key_to_index in chain_feed_state.items()
        }

    if log_str:
        logging.info(
            "%s, Feed states summary: %s",
            log_str,
            summary,
        )
    else:
        logging.info(
            "Feed states summary: %s",
            summary,
        )
    return


def get_run_environment() -> str:
    return os.environ.get("RUN_ENVIRONMENT", "LOCAL")


def is_iso_date(date_string: str) -> bool:
    try:
        from_iso(date_string)
        return True
    except (ValueError, TypeError):
        return False


def get_parent_asset_types(
    feed_type: str,
    feed_config: utils_oracle.ConfigModel,
) -> list[str]:
    """
    Get the parent asset types for a given feed type based on the feed configuration.
    E.g feed_types of futures will have their parent asset class as the option and future
    as defined in the feed config

    Args:
        feed_type: The feed type to look up
        feed_config: The feed configuration model containing related_catalog_asset_lookup

    Returns:
        A list of parent asset types that can produce this feed type
    """
    parent_types = []
    parent_mapping = feed_config.related_catalog_asset_lookup["qualified_name"]

    for parent_key, allowed_feed_types in parent_mapping.items():
        if feed_type in allowed_feed_types:
            parent_types.append(parent_key)

    return parent_types


def extract_feed_metadata(
    feed_config: utils_oracle.ConfigModel,
    decoded_feed: DecodedFeed,
) -> BaseFeedMetaData:
    """
    Extract metadata from a decoded feed, including exchange, base asset.

    Args:
        decoded_feed: The decoded feed object
        chain_decimals: The number of decimals used to convert values if needed

    Returns:
        A tuple containing (exchange, base_asset, asset_class)
    """
    feed_type = decoded_feed.feed_type.lower()

    parent_asset_types = get_parent_asset_types(
        feed_type=feed_type, feed_config=feed_config
    )

    exchange = ""
    base_asset = ""

    # todo: add a feedfilter class capable of discriminating
    #  individual feeds rather that catalog driven ones
    for enum_type, enum_value in decoded_feed.parameters.enumerable:
        # todo: add quote asset if/when supported
        if enum_type == "ExchangeEnum":
            exchange = enum_value.lower()
            continue

        if enum_type == "BaseAssetEnum":
            base_asset = enum_value.upper()
            continue

    return exchange, base_asset, parent_asset_types
