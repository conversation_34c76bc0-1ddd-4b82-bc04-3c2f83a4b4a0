import hashlib

from eth_abi import decode
from hexbytes import HexBytes
from pydantic import (
    BaseModel,
    Field,
    model_validator,
)
from typing import Annotated, Literal
import eth_pydantic_types
from .constants import (
    ConfigLoader,
    FeedConfig,
)
import logging
import time
import utils_general
from decimal import Decimal, ROUND_HALF_UP
from utils_general.utils_general_types import ConstantMaturityEnum
from .typings import FeedEnumMismatchError, InvalidFeedError, Uint8, Uint32, Uint256


class DecodedFeedParameters(BaseModel):
    enumerable: list[tuple[str, str]]
    other: list[tuple[str, str | int]]

    def get_param_value(
        self, key: str, field_type: Literal["enumerable", "other"]
    ) -> str | int | None:
        """
        Retrieve the value of a parameter by its key from either enumerable or other field.

        Args:
            key: The parameter key to look up
            field_type: Field to search in, either 'enumerable' or 'other'

        Returns:
            The value of the parameter if found, None otherwise
        """
        field = getattr(self, field_type)
        for param_key, param_value in field:
            if param_key == key:
                return param_value

        return None


class DecodedFeed(BaseModel):
    feed_type: str
    parameters: DecodedFeedParameters

    def is_constant_tenor_feed(self) -> bool:
        """
        Check if a feed is a constant tenor feed.

        A feed is considered a constant tenor feed if it has a "TENOR" indicator
        in its enumerable parameters.

        Returns:
            True if the feed is a constant tenor feed, otherwise False
        """
        return (
            self.parameters.get_param_value("ExpiryTypeEnum", "enumerable") == "TENOR"
        )

    def is_expired_feed(
        self, chain_decimals: int, unix_timestamp_ns: int | None = None
    ) -> bool:
        """
        Check if a feed is expired based on its parameters.

        Args:
            chain_decimals: The number of decimals used to convert the expiry value
            unix_timestamp_ns: Reference timestamp in nanoseconds

        Returns:
            True if the feed is expired, otherwise False
        """
        expiry_type = self.parameters.get_param_value("ExpiryTypeEnum", "enumerable")
        if expiry_type != "TIMESTAMP":
            return False

        timestamp = self.parameters.get_param_value("expiry", "other")
        if timestamp is None:
            return False

        if unix_timestamp_ns:
            ref_timestamp = unix_timestamp_ns
        else:
            ref_timestamp = time.time_ns()

        timestamp_scaled = timestamp / 10**chain_decimals
        timestamp_ns = timestamp_scaled * 1e9

        return int(timestamp_ns) < ref_timestamp

    @staticmethod
    def reconstruct_approximated_constant_tenor(tenor_str: str) -> str:
        """
        Given a tenor string (e.g. "7d" or "1440m"), this utility reconstructs the tenor
        after applying conversion adjustments. If the input tenor contains more than 4 decimal places,
        we assume precision loss and the value is rounded, assuming the original tenor was meant to be an integer.

        Parameters:
            tenor_str (str): The input tenor string (e.g., "7d" or "1440m").

        Returns:
            str: The reconstructed tenor string with the appropriate time unit (e.g., "7d").
        """

        try:
            maturity, time_unit = utils_general.string_to_decimal(tenor_str)
        except Exception:
            logging.exception("Failed to reconstruct tenor_str=%s", tenor_str)
            raise

        # Convert maturity to a Decimal regardless of whether it's originally an int or float.
        # Using str(maturity) for floats preserves the original textual representation which is needed
        # to accurately determine the number of decimal places.
        if isinstance(maturity, int):
            maturity_decimal = Decimal(maturity)
        else:
            maturity_decimal = Decimal(str(maturity))

        exponent = maturity_decimal.as_tuple().exponent
        value: float | int

        if isinstance(exponent, int) and exponent <= -4:
            value = int(maturity_decimal.quantize(Decimal("1"), rounding=ROUND_HALF_UP))
            utils_general.log_bsdebug(
                "rounding constant maturity to nearest whole number, tenor_str=%s",
                tenor_str,
            )
        else:
            # 8.0 -> 8
            if maturity_decimal == maturity_decimal.to_integral_value():
                value = int(maturity_decimal)
            else:
                value = float(maturity_decimal)

        # Determine the corresponding enum value based on the time unit.
        if time_unit == "minutes":
            enum_value = ConstantMaturityEnum.MINUTE.value
        elif time_unit == "days":
            enum_value = ConstantMaturityEnum.DAY.value
        else:
            raise NotImplementedError("Unrecognised time unit. time_unit=%s", time_unit)

        return utils_general.decimal_to_string(float(value), enum_value)

    def feed_to_string(self, chain_decimals: int) -> str:
        # Process the "other" parameters, applying the appropriate conversion for the 'expiry' key.
        other_params = []

        for key, val in self.parameters.other:
            if key == "expiry":
                # Find the ExpiryTypeEnum value from enumerable parameters, if it exists.
                expiry_type = self.parameters.get_param_value(
                    "ExpiryTypeEnum", "enumerable"
                )
                if expiry_type == "TIMESTAMP":
                    # Convert expiry value to ISO format.
                    val_scaled = val / 10**chain_decimals
                    timestamp_ns = val_scaled * 1e9
                    converted_val = f"{utils_general.to_iso(timestamp_ns)[:19]}Z"
                elif expiry_type == "TENOR":
                    # val is an expiry in years
                    # Scale down by chain_decimals and convert to tenor string.
                    converted_val = (
                        utils_general.convert_tenor_days_to_constant_maturity(
                            float(Decimal(val * 365) / Decimal(10**chain_decimals))
                        )
                    )
                    converted_val = self.reconstruct_approximated_constant_tenor(
                        converted_val
                    )
                else:
                    raise NotImplementedError("Invalid ExpiryTypeEnum specified")
                other_params.append(f"{key}:{converted_val}")
            else:
                other_params.append(f"{key}:{val}")

        enumerable_str = "+".join(
            f"{key}:{val}" for key, val in self.parameters.enumerable
        )
        other_str = "+".join(other_params)

        return f"{self.feed_type}_{enumerable_str}_{other_str}"


class FeedParameters(BaseModel):
    enumerable: Annotated[list[Uint8], Field()]  # list of uint8 (0-255 range)
    other: eth_pydantic_types.HexBytes  # Arbitrary byte data

    def decode_params(self, config: FeedConfig) -> DecodedFeedParameters:
        decoded_enum = []
        for param_name, enum_value in zip(config.ordered_enum_params, self.enumerable):
            param_value = ConfigLoader.get_feed_parameter_enums()[param_name](enum_value).name  # type: ignore[operator]
            decoded_enum.append((param_name, param_value))

        other_types = [o.type for o in config.ordered_other_params]
        decoded_other = []
        for param, param_value in zip(
            config.ordered_other_params, decode(other_types, self.other)
        ):
            decoded_other.append((param.name, param_value))

        return DecodedFeedParameters(enumerable=decoded_enum, other=decoded_other)


class Feed(BaseModel):
    id: Uint32
    parameters: FeedParameters

    def get_decoded_feed(self) -> DecodedFeed:
        feed_type = ConfigLoader.get_feed_id_to_type().get(self.id)
        if feed_type is None:
            raise InvalidFeedError(f"Feed ID {self.id} is not valid")
        # Retrieve the expected enum parameters
        feed_config = ConfigLoader.get_feed_definitions().feed_type_to_config[feed_type]
        parameters = self.parameters.decode_params(feed_config)
        return DecodedFeed(feed_type=feed_type, parameters=parameters)

    @model_validator(mode="after")
    def validate_feed(cls, values):
        feed_id = values.id
        parameters = values.parameters

        # Directly get feed_type from feed_id
        feed_type = ConfigLoader.get_feed_id_to_type().get(feed_id)
        if feed_type is None:
            raise InvalidFeedError(f"Feed ID {feed_id} is not valid")

        # Retrieve the expected enum parameters
        feed_config = ConfigLoader.get_feed_definitions().feed_type_to_config[feed_type]
        expected_enum_params = feed_config.ordered_enum_params

        if not expected_enum_params:
            return values

        # Validate length of enumerable
        if parameters and len(parameters.enumerable) != len(expected_enum_params):
            raise FeedEnumMismatchError(
                f"Feed ID {feed_id} expects {len(expected_enum_params)} parameters in 'enumerable', but got {len(parameters.enumerable)}"
            )

        # Validate enumerable values
        for idx, enum_class_name in enumerate(expected_enum_params):
            enum_value = parameters.enumerable[idx]
            feed_parameter_enums = ConfigLoader.get_feed_parameter_enums()
            if enum_class_name not in feed_parameter_enums:
                raise FeedEnumMismatchError(
                    f"'{enum_class_name}' is not a valid ENUM. Please check the feed configs",
                )

            # Validate the enum value
            try:
                feed_parameter_enums[enum_class_name](enum_value)
            except ValueError:
                raise FeedEnumMismatchError(
                    f"Invalid value '{enum_value}' for enum '{enum_class_name}' at position {idx} for {feed_id=}"
                )

        return values


class FeedData(BaseModel):
    value: Annotated[int, Field(ge=-(2**63), le=2**63 - 1)]  # int64 range
    timestamp: Annotated[int, Field(ge=0, le=2**32 - 1)]  # uint32 range


class FeedIndex(BaseModel):
    version: Uint256
    chain_id: Annotated[int, Field(ge=0)]
    feed: Feed
    index: Annotated[int, Field(ge=0)]

    def generate_hash(self) -> str:
        hash_input = f"{self.version}{self.chain_id}{self.feed.id}{self.feed.parameters.model_dump_json()}{self.index}"
        return hashlib.sha256(hash_input.encode()).hexdigest()

    def log_index_details(self) -> str:
        return (
            f"FeedIndex(version={self.version}, chain_id={self.chain_id}, feed_id={self.feed.id},"
            f"parameters={self.feed.parameters.model_dump()}, index={self.index})"
        )


FeedKeyToFeed = dict[HexBytes, Feed]
