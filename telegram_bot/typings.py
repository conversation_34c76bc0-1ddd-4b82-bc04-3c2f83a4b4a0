import io
import logging
from typing import (
    Any,
    Generic,
    Literal,
    NotRequired,
    TypedDict,
    TypeVar,
)

import numpy as np
import utils_general
from pydantic import BaseModel, model_validator
from telegram.ext import Application, CallbackContext, ExtBot, JobQueue
from utils_aws import Frequency

Bot = Application[
    ExtBot[None],
    CallbackContext[
        ExtBot[None], dict[Any, Any], dict[Any, Any], dict[Any, Any]
    ],
    dict[Any, Any],
    dict[Any, Any],
    dict[Any, Any],
    JobQueue[
        CallbackContext[
            ExtBot[None], dict[Any, Any], dict[Any, Any], dict[Any, Any]
        ]
    ],
]

PlotTypes = Literal["v2timeseries", "v2smiles"]
# rvd chart_type -> chart_name -> encodedChart
EncodedCharts = dict[PlotTypes, dict[str, str]]
# rvd chart_type -> chart_name -> decodedchart
DecodedCharts = dict[PlotTypes, dict[str, io.BytesIO]]
CommandType = Literal["chart", "price", "run"]
Stage = Literal["staging", "prod"]

AllowedDeltas = Literal[
    "-1delta",
    "-2delta",
    "-3delta",
    "-4delta",
    "-5delta",
    "-10delta",
    "-15delta",
    "-20delta",
    "-25delta",
    "-30delta",
    "-35delta",
    "-40delta",
    "-45delta",
    "-50delta",
    "atm",
    "50delta",
    "45delta",
    "40delta",
    "35delta",
    "30delta",
    "25delta",
    "20delta",
    "15delta",
    "10delta",
    "5delta",
    "4delta",
    "3delta",
    "2delta",
    "1delta",
]

AllowedConstantTenors = Literal["1d", "7d", "14d", "30d", "60d", "90d", "180d"]

AllowedChartTypes = Literal[
    "vol", "smile", "skew", "butterfly", "spot", "fr", "perp"
]
AllowedCurrencies = str
VolTimeseriesChartTypes = Literal["vol", "skew", "butterfly"]


ChartName = str
ChartCategory = TypeVar("ChartCategory")

ExchangeToCurrencies = dict[str, list[AllowedCurrencies]]
CurrencyToExchange = dict[AllowedCurrencies, str]


class BaseRequest(BaseModel):
    currency: AllowedCurrencies


class NetGreeksResponse(BaseModel):
    """
    Model representing the output of handle_option_pricer_request.
    """

    spot: float | int
    pricing_timestamp: int
    implied_vol: float | int
    price: float | int
    delta: float | int
    theta: float | int
    gamma: float | int
    vega: float | int


class AggregatedNetGreeksResponse(BaseModel):
    """
    Model representing the aggregated output of multiple NetGreeksResponse objects.

    # todo: consider how pair trades would be formatted
    """

    spot: float | int
    pricing_timestamp: int
    price: float | int
    delta: float | int
    theta: float | int
    gamma: float | int
    vega: float | int

    @classmethod
    def _validate(
        cls, responses: list[NetGreeksResponse], logger: logging.Logger
    ) -> None:
        """
        Validates that all pricing timestamps are within 2 minutes of each other.

        Raises:
            ValueError: If nor NetGreeksResponse objects are provided
            ValueError: If the timestamp difference exceeds 120 seconds.
        """

        if not responses:
            raise ValueError(
                "No NetGreeksResponse objects provided for aggregation"
            )

        if len(responses) == 1:
            raise ValueError(
                "Only One NetGreeksResponse provided. Can only aggregate multiple responses"
            )

        pricing_timestamps = [resp.pricing_timestamp for resp in responses]
        min_timestamp = utils_general.to_datetime(min(pricing_timestamps))
        max_timestamp = utils_general.to_datetime(max(pricing_timestamps))

        timestamp_diff = (max_timestamp - min_timestamp).total_seconds()
        if timestamp_diff > 120:
            logger.error(
                "Pricing timestamps differ by %s seconds, which exceeds the 2-minute threshold.",
                timestamp_diff,
            )
            raise ValueError(
                "Pricing timestamps are not within 2 minutes of each other."
            )

    @classmethod
    def _aggregate_single_currency_fields(
        cls, responses: list[NetGreeksResponse]
    ) -> "AggregatedNetGreeksResponse":
        """
        Aggregates multiple NetGreeksResponse objects into a single AggregatedNetGreeksResponse.

        Args:
            responses (list[NetGreeksResponse]): The list of responses to aggregate.

        Returns:
            AggregatedNetGreeksResponse: The aggregated result.
        """

        # Aggregate fields
        spot = float(
            round(np.median([response.spot for response in responses]), 5)
        )
        pricing_timestamp = min(resp.pricing_timestamp for resp in responses)
        price = round(sum(response.price for response in responses), 5)
        delta = round(sum(response.delta for response in responses), 5)
        theta = round(sum(response.theta for response in responses), 5)
        gamma = round(sum(response.gamma for response in responses), 5)
        vega = round(sum(response.vega for response in responses), 5)

        return cls(
            spot=spot,
            pricing_timestamp=pricing_timestamp,
            price=price,
            delta=delta,
            theta=theta,
            gamma=gamma,
            vega=vega,
        )

    @classmethod
    def aggregate_single_currency_position(
        cls, responses: list[NetGreeksResponse], logger: logging.Logger
    ) -> "AggregatedNetGreeksResponse":
        """
        Aggregates a list of NetGreeksResponse objects into a single AggregatedNetGreeksResponse.

        Args:
            responses (list[NetGreeksResponse]): The list of responses to aggregate.

        Returns:
            AggregatedNetGreeksResponse: The aggregated result.
        """
        cls._validate(responses, logger)
        return cls._aggregate_single_currency_fields(responses)


class ValidatedPriceRequest(BaseRequest):
    currency: AllowedCurrencies
    strikes: list[float | int]
    expiry: str
    op_type: str


class PricerRequestDict(BaseModel):
    currency: AllowedCurrencies
    strike: str
    expiry: str
    quantity: float | int
    style: str
    op_type: str


class ValidatedVolRequest(BaseRequest):
    chart_type: VolTimeseriesChartTypes
    chart_target: AllowedDeltas


class ValidatedSpotRequest(BaseRequest):
    chart_type: Literal["spot"]
    exchange: str


class ValidatedFundingRateRequest(BaseRequest):
    chart_type: Literal["fr"]
    exchange: str


class ValidatedSmileRequest(BaseRequest):
    chart_type: Literal["smile"]
    chart_target: str


class ValidatedPerpPriceRequest(BaseRequest):
    chart_type: Literal["perp"]
    exchange: str


class ValidatedVolRunRequest(BaseRequest):
    pass


ChartRequest = (
    ValidatedVolRequest
    | ValidatedSmileRequest
    | ValidatedSpotRequest
    | ValidatedFundingRateRequest
    | ValidatedPerpPriceRequest
)


class BaseChartDetails(TypedDict):
    yaxis_title: str
    chart_title: str


class GenericChartItem(TypedDict, Generic[ChartCategory]):
    charts: dict[ChartName, ChartCategory]


class FlexTimeSeriesTarget(TypedDict):
    qualified_name: str
    target: str
    trace_title: str
    color: NotRequired[str]
    resample_config: NotRequired[Frequency]
    sub_query_interval: NotRequired[int]


class FlexTimeseriesChart(BaseChartDetails):
    calculation: NotRequired[str]
    chart_type: NotRequired[
        Literal[
            "bar",
            "line",
        ]
    ]
    targets: list[FlexTimeSeriesTarget]
    ticksuffix: NotRequired[str]
    tickprefix: NotRequired[str]


class FlexTimeSeries(GenericChartItem[FlexTimeseriesChart]):
    pass


class FlexSmilesTarget(TypedDict):
    tenor: NotRequired[int]
    listed_expiry: NotRequired[str]
    exchange: str
    currency: str
    model: str
    snapshot: str
    trace_title: str
    color: str


class FlexSmilesChart(BaseChartDetails):
    targets: list[FlexSmilesTarget]


class V2Smiles(TypedDict):
    charts: dict[ChartName, FlexSmilesChart]


class PlotObjects(TypedDict, total=False):
    v2timeseries: FlexTimeSeries
    v2smiles: V2Smiles


class WhitelistedUser(BaseModel):
    id: int | None = None
    name: str
    username: str | None = None

    @model_validator(mode="after")
    def check_id_or_username(self) -> "WhitelistedUser":
        if self.id is None and self.username is None:
            raise ValueError("Either id or username must be provided")
        return self


class WhitelistedChat(BaseModel):
    id: int | None = None
    name: str

    @model_validator(mode="after")
    def check_id(self) -> "WhitelistedChat":
        if self.id is None:
            raise ValueError("id must be provided")
        return self
