import os
from collections.abc import Callable
from typing import Any, TypeVar

import boto3
import utils_aws
from cachetools import T<PERSON><PERSON><PERSON>

from telegram_bot.constants import BASE_SSM_PATH, EXCHANGE_MAPPING_CONFIG_PATH

# Cache for SSM parameters with 30 minutes TTL
_params_cache: TTLCache[str, dict[str, Any]] = TTLCache(maxsize=100, ttl=1800)

# Mapping of parameter keys to SSM paths
_key_to_path = {
    "EXCHANGE_MAPPING": EXCHANGE_MAPPING_CONFIG_PATH,
    "ANALYTICS": "/config/telegram-bot/analytics",
}

STAGE: str = os.environ.get("STAGE", "local")

T = TypeVar("T")


def get_params(
    key: str,
    merge_fn: Callable[[list[T]], T] | None = None,
    by_path: bool = True,
) -> dict[str, Any]:
    """
    Get cached parameters from SSM with 30 minute TTL.

    Args:
        key: The key for the parameter to fetch
        merge_fn: Optional function to merge multiple parameters
        by_path: Whether to fetch by path or by name

    Returns:
        The parameter value as a dictionary
    """
    if key not in _params_cache:
        if by_path:
            if merge_fn is not None:
                _params_cache[key] = utils_aws.load_multi_ssm_params_by_path(
                    _key_to_path[key], merge_fn=merge_fn
                )
            else:
                _params_cache[key] = utils_aws.load_multi_ssm_params_by_path(
                    _key_to_path[key]
                )
        else:
            _params_cache[key] = utils_aws.load_ssm_params(
                {key: _key_to_path[key]}
            )[key]
    return _params_cache[key]


def get_bot_config_ssm_params() -> dict[str, Any]:
    """
    Get all bot configuration parameters from SSM.

    Returns:
        A dictionary of bot configuration parameters
    """
    ssm = boto3.Session().client("ssm")
    ssm_params = utils_aws.get_ssm_params_by_path(ssm, BASE_SSM_PATH)
    params = {
        path.replace(BASE_SSM_PATH, ""): param
        for path, param in ssm_params.items()
    }
    return params
