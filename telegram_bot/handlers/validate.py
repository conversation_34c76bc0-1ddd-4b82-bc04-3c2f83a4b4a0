from typing import Any, cast, get_args

from telegram_bot.configs.bot_config import (
    Botfig,
)
from telegram_bot.configs.option_config import SUPPORTED_OP_TYPES
from telegram_bot.exceptions import (
    ChartValidationError,
    PriceValidationError,
    UnsupportedRunCurrencyException,
)
from telegram_bot.handlers.utils import (
    is_beyond_max_tenor_cutoff,
    is_constant_tenor,
    is_expired,
    is_listed_expiry,
)
from telegram_bot.typings import (
    AllowedConstantTenors,
    AllowedDeltas,
    ChartRequest,
    ValidatedFundingRateRequest,
    ValidatedPerpPriceRequest,
    ValidatedPriceRequest,
    ValidatedSmileRequest,
    ValidatedSpotRequest,
    ValidatedVolRequest,
    ValidatedVolRunRequest,
    VolTimeseriesChartTypes,
)


class BaseValidator:
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig
    ) -> ChartRequest | ValidatedPriceRequest | ValidatedVolRunRequest:
        """Validate arguments for a specific input type."""
        raise NotImplementedError(
            "Validator must implement the `validate` method."
        )


class OptionPriceValidator(BaseValidator):
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedPriceRequest:

        op_type = args[0].upper()
        if op_type not in SUPPORTED_OP_TYPES:
            raise PriceValidationError(
                f"Unsupported option type '{op_type}'. Supported option types are: {', '.join(SUPPORTED_OP_TYPES)}."
            )

        if op_type in ["C", "P", "DC", "DP"]:
            if len(args) != 4:
                raise PriceValidationError(
                    "Invalid number of arguments provided for single call option."
                )

        elif op_type in ["CS", "PS", "RR"]:
            if len(args) != 5:
                raise PriceValidationError(
                    "Invalid number of arguments provided for spread or risk-reversal."
                )
        elif op_type == "BF":
            if len(args) != 6:
                raise PriceValidationError(
                    "Invalid number of arguments provided for butterfly."
                )
        currency = args[1].upper()
        if currency not in botfig.supported_pricing_currencies:
            raise PriceValidationError(
                f"Unsupported currency '{currency}'. Supported currencies are: {', '.join(botfig.supported_pricing_currencies)}."
            )

        expiry = args[2]
        if is_expired(expiry):
            raise ChartValidationError(f"Option has expired, {expiry}")

        # Check if expiry is beyond max tenor cutoff
        max_cutoff = botfig.get_max_tenor_cutoff(currency)
        if is_beyond_max_tenor_cutoff(expiry, max_cutoff):
            raise ChartValidationError(
                f"Expiry {expiry} is beyond the maximum tenor cutoff of {max_cutoff} days for {currency}"
            )

        # validate strikes
        try:
            strikes = [float(i) for i in args[3:]]
        except Exception as _e:
            raise ChartValidationError(
                "Invalid strikes provided. Please provide numerical strikes"
            ) from _e

        return ValidatedPriceRequest(
            currency=currency,
            op_type=op_type,
            strikes=strikes,
            expiry=expiry,
        )


class VolatilityValidatorBase(BaseValidator):
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedVolRequest:

        chart_type = kwargs.get("chart_type")
        if chart_type not in get_args(VolTimeseriesChartTypes):
            raise ChartValidationError(
                f"Invalid chart type. Supported chart types are {', '.join(get_args(VolTimeseriesChartTypes))}"
            )

        if len(args) != 3:
            raise ChartValidationError(
                f"Invalid number of arguments provided for '{chart_type}' chart."
            )

        currency = args[1].upper()
        if currency not in botfig.supported_vol_chart_currencies:
            raise ChartValidationError(
                f"Unsupported currency '{currency}'. Supported currencies are: {', '.join(botfig.supported_vol_chart_currencies)}."
            )

        chart_target = args[2].lower()
        allowed_deltas = get_args(AllowedDeltas)
        if chart_target not in allowed_deltas:
            raise ChartValidationError(
                f"Invalid delta supplied for '{chart_type}' chart. Supported deltas are: {', '.join(allowed_deltas)}"
            )

        # skew specific validation
        if chart_type == "skew" or chart_type == "butterfly":
            if chart_target == "atm" or chart_target == "50delta":
                raise ChartValidationError(
                    f"Cannot plot 'atm' or '50delta' for '{chart_type}' chart. Please try other deltas"
                )
            if "-" in chart_target:
                raise ChartValidationError(
                    f"Cannot plot {chart_type} charts with a negative delta '{chart_target}'. Please provide a positive delta"
                )

        # cast valid objects
        return ValidatedVolRequest(
            chart_type=cast(VolTimeseriesChartTypes, chart_type),
            currency=currency,
            chart_target=cast(AllowedDeltas, chart_target),
        )


class VolChartValidator(VolatilityValidatorBase):
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedVolRequest:
        return VolatilityValidatorBase.validate(
            args, chart_type="vol", botfig=botfig
        )


class SkewChartValidator(VolatilityValidatorBase):
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedVolRequest:
        return VolatilityValidatorBase.validate(
            args, chart_type="skew", botfig=botfig
        )


class ButterflyChartValidator(VolatilityValidatorBase):
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedVolRequest:
        return VolatilityValidatorBase.validate(
            args, chart_type="butterfly", botfig=botfig
        )


class SmileChartValidator(BaseValidator):

    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedSmileRequest:

        if len(args) != 3:
            raise ChartValidationError(
                "Invalid number of arguments provided for 'smile' chart."
            )

        currency = args[1].upper()
        if currency not in botfig.supported_vol_chart_currencies:
            raise ChartValidationError(
                f"Unsupported currency '{currency}'. Supported currencies are: {', '.join(botfig.supported_vol_chart_currencies)}."
            )

        chart_target = args[2]
        if is_constant_tenor(chart_target):
            allowed_constant_tenors = botfig.get_currency_tenor_target(currency)
            if chart_target not in allowed_constant_tenors:
                raise ChartValidationError(
                    f"Unsupported chart target '{chart_target}' for 'smile' chart. Supported constant tenors are: {', '.join(botfig.get_currency_tenor_target(currency))}."
                )
            chart_target = cast(AllowedConstantTenors, chart_target)

        elif is_listed_expiry(chart_target):
            if is_expired(chart_target.upper()):
                raise ChartValidationError(
                    f"Expiry is in the past, {chart_target}"
                )

        elif chart_target.lower() != "surface":
            raise ChartValidationError(
                f"Unrecognised tenor '{chart_target}', for 'smile' chart"
            )

        return ValidatedSmileRequest(
            chart_type="smile",
            currency=currency,
            chart_target=chart_target,
        )


class SpotValidator:
    @staticmethod
    def validate(args: list[str], botfig: Botfig) -> ValidatedSpotRequest:
        """
        Validate arguments for a spot chart request.

        Expects exactly two arguments:
          - args[0]: should be 'spot'
          - args[1]: the currency
          - args[2]: the exchange

        Returns:
            ValidatedSpotRequest: a validated request object.
        """
        if len(args) != 3:
            raise ChartValidationError(
                "Invalid number of arguments provided for 'spot' chart."
            )

        currency = args[1].upper()
        exchange = args[2].lower()
        supported_exchanges = [i.lower() for i in botfig.spot_config.keys()]
        if exchange not in supported_exchanges:
            raise ChartValidationError(
                f"Unsupported exchange for 'spot' chart. Supported exchanges are {', '.join(botfig.get_supported_exchanges_for_config_for_display('spot'))}"
            )

        exchange_supported_currencies = [
            i.upper() for i in botfig.spot_config[exchange]
        ]
        if currency not in exchange_supported_currencies:
            raise ChartValidationError(
                f"Unsupported currency '{currency}' for '{exchange.replace('v2lyra', 'derive')}'. Supported currencies for '{exchange.replace('v2lyra', 'derive')}' are {', '.join(exchange_supported_currencies)}"
            )
        return ValidatedSpotRequest(
            chart_type="spot",
            currency=currency,
            exchange=exchange,
        )


class PerpPriceValidator(BaseValidator):
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedPerpPriceRequest:
        """
        Validate arguments for a perpetual price (perp) chart request.

        Expects exactly three arguments:
          - args[0]: should be 'perp'
          - args[1]: the currency code
          - args[2]: the exchange (lowercase)

        Returns:
            ValidatedPerpPriceRequest: a validated request object.
        """
        if len(args) != 3:
            raise ChartValidationError(
                "Invalid number of arguments provided for 'perp' chart."
            )
        currency = args[1].upper()
        exchange = args[2].lower()
        supported_exchanges = [
            i.lower() for i in botfig.funding_rate_config.keys()
        ]
        if exchange not in supported_exchanges:
            raise ChartValidationError(
                f"Unsupported exchange for 'perp' chart. Supported exchanges are {', '.join(botfig.get_supported_exchanges_for_config_for_display('perpetual'))}"
            )

        exchange_supported_currencies = [
            i.upper() for i in botfig.funding_rate_config[exchange]
        ]
        if currency not in exchange_supported_currencies:
            raise ChartValidationError(
                f"Unsupported currency '{currency}' for '{exchange.replace('v2lyra', 'derive')}'. Supported currencies for '{exchange.replace('v2lyra', 'derive')}' are {', '.join(exchange_supported_currencies)}"
            )
        return ValidatedPerpPriceRequest(
            chart_type="perp", exchange=exchange, currency=currency
        )


class FundingRateValidator(BaseValidator):
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedFundingRateRequest:
        """
        Validate arguments for a funding rate (fr) chart request.

        Expects exactly three arguments:
          - args[0]: should be 'fr'
          - args[1]: the currency code
          - args[2]: the exchange (lowercase)

        Returns:
            ValidatedFundingRateRequest: a validated request object.
        """
        if len(args) != 3:
            raise ChartValidationError(
                "Invalid number of arguments provided for 'fr' chart."
            )
        currency = args[1].upper()
        exchange = args[2].lower()

        if exchange == "arbitrage":
            union_currencies = {
                cur.upper()
                for curr_list in botfig.funding_rate_config.values()
                for cur in curr_list
            }
            if currency not in union_currencies:
                raise ChartValidationError(
                    f"Unsupported currency '{currency}' for arbitrage mode. Supported currencies are: {', '.join(sorted(union_currencies))}"
                )
            return ValidatedFundingRateRequest(
                chart_type="fr", exchange=exchange, currency=currency
            )

        supported_exchanges = [
            ex.lower() for ex in botfig.funding_rate_config.keys()
        ]
        if exchange not in supported_exchanges:
            raise ChartValidationError(
                f"Unsupported exchange for 'fr' chart. Supported exchanges are {', '.join(botfig.get_supported_exchanges_for_config_for_display('perpetual'))}"
            )

        exchange_supported_currencies = [
            curr.upper() for curr in botfig.funding_rate_config[exchange]
        ]
        if currency not in exchange_supported_currencies:
            raise ChartValidationError(
                f"Unsupported currency '{currency}' for '{exchange.replace('v2lyra', 'derive')}'. Supported currencies for '{exchange.replace('v2lyra', 'derive')}' are {', '.join(exchange_supported_currencies)}"
            )
        return ValidatedFundingRateRequest(
            chart_type="fr", exchange=exchange, currency=currency
        )


class VolRunValidator(BaseValidator):
    @staticmethod
    def validate(
        args: list[str], botfig: Botfig, **kwargs: Any
    ) -> ValidatedVolRunRequest:

        if len(args) != 1:
            raise ChartValidationError(
                "Invalid number of arguments provided for '/run' command. Please specify one currency"
            )

        currency = args[0].upper()
        if currency not in botfig.supported_vol_chart_currencies:
            # vol run shows vol related metrics (atm, vol, skew) and so the supported currencies should
            # be anything we can produce vol charts for.
            raise UnsupportedRunCurrencyException(
                f"Unsupported currency '{currency}'. Supported currencies are: {', '.join(botfig.supported_vol_chart_currencies)}."
            )

        return ValidatedVolRunRequest(currency=currency)
