import logging
import time
from collections import deque

from telegram import Update
from telegram.ext import ApplicationHandlerStop, ContextTypes

from telegram_bot.constants import BLOCKSCHOLES_TG_BOT_ADMIN_IDS
from telegram_bot.logger import current_user_id

_MAX_MESSAGES_IN_WINDOW = 7  # messages allowed in the evaluation window
_RATE_LIMIT_EVALUATION_WINDOW_SECONDS = 30  # time window to check for rate


async def rate_limit_callback(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    logger: logging.Logger,
) -> None:

    if context.user_data is None or update.effective_message is None:
        logger.error(
            "User data or effective message is None. Ignoring rate limit check. user_data=%s, effective_message=%s",
            context.user_data,
            update.effective_message,
        )
        return

    user = update.effective_user
    if user is not None:
        if user.id in BLOCKSCHOLES_TG_BOT_ADMIN_IDS:
            return

    # Retrieve or initialize a deque to store timestamps for this user
    timestamps: deque[float] = context.user_data.get("timestamps", deque())
    context.user_data["timestamps"] = timestamps

    now = time.time()
    window = _RATE_LIMIT_EVALUATION_WINDOW_SECONDS

    # Remove timestamps that are older than the time window
    while timestamps and (now - timestamps[0] > window):
        timestamps.popleft()

    # If the user has exceeded the limit in this window, throttle them
    if len(timestamps) >= _MAX_MESSAGES_IN_WINDOW:
        # Determine how many messages need to expire before user is under limit
        messages_to_expire = len(timestamps) - int(_MAX_MESSAGES_IN_WINDOW) + 1

        # Identify the timestamp at which the necessary number of messages will expire
        # (since timestamps are ordered, the `messages_to_expire`-th oldest message will expire first)
        reference_time = list(timestamps)[
            messages_to_expire - 1
        ]  # Convert deque to list for indexing
        wait_until = reference_time + window
        wait_time = max(wait_until - now, 0)

        log_id: int | str
        if update.effective_user is not None:
            log_id = update.effective_user.id
        else:
            # other bots may not have id's
            log_id = "Unknown"

        logger.info(
            "User %s hit rate limit, must wait %.2f seconds",
            log_id,
            wait_time,
        )

        # Notify admins about this rate-limited event
        message_text = (
            update.message.text
            if update.message is not None
            else "<no message text>"
        )
        if update.effective_user:

            admin_message = (
                f"A User has been rate-limited.\n"
                f"Recent message: {message_text}\n"
                f"Required wait time: {wait_time:.1f} seconds."
            )
        else:

            admin_message = (
                f"A Potential bot has been rate-limited.\n"
                f"Recent message: {message_text}\n"
                f"Required wait time: {wait_time:.1f} seconds."
            )

        for admin_chat_id in BLOCKSCHOLES_TG_BOT_ADMIN_IDS:
            try:
                await context.bot.send_message(
                    chat_id=admin_chat_id, text=admin_message
                )
            except Exception as e:
                logger.error(
                    f"Failed to send rate limit alert to admin {admin_chat_id}: {e}"
                )

        await update.effective_message.reply_text(
            f"Rate limit exceeded. Please wait {wait_time:.1f} seconds."
        )
        raise ApplicationHandlerStop  # blocks other handlers from processing the update

    # If under the limit, record the current request timestamp and proceed
    timestamps.append(now)


async def capture_user_id_callback(
    update: Update, context: ContextTypes.DEFAULT_TYPE, logger: logging.Logger
) -> None:
    """
    A special handler that runs first to set current_user_id.
    """
    if update.effective_user:
        uid = update.effective_user.id
    else:
        # Tg user id's cannot be negative. Only chat id's can
        uid = -1
        logger.error(f"Failed set user. Defaulting to {uid}")

    current_user_id.set(uid)
