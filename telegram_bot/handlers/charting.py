import io
import json
import logging
import time
from datetime import UTC, datetime, timedelta
from typing import Any, cast, get_args

import aioboto3
import matplotlib.pyplot as plt
import pandas as pd
import utils_general
from aiobotocore.config import AioConfig
from datagrabber import (
    construct_timeseries_queries,
    get_instruments_async,
    grab_async,
)
from matplotlib.transforms import Bbox
from telegram import Update
from telegram.constants import ParseMode
from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.configs.chart_config import (
    CHART_TYPE_TO_PLOT_TYPE,
    CHART_TYPE_TO_TIMESERIES_LOOKBACK,
)
from telegram_bot.constants import COMMAND_DELAYS
from telegram_bot.datagrab import grab_instruments
from telegram_bot.exceptions import (
    ChartValidationError,
    ImageNotFoundError,
    NoChartArgumentsException,
)
from telegram_bot.handlers.chart_builder import (
    build_funding_rate_plot_objects,
    build_perp_price_plot_objects,
    build_smile_plot_objects,
    build_spot_price_plot_objects,
    build_vol_based_timeseries_plot_objects,
    construct_chart_request,
)
from telegram_bot.handlers.messaging import (
    send_failed_request_message,
    send_message_suggestion,
)
from telegram_bot.handlers.utils import (
    apply_time_delay,
    decode_image,
    get_chart_request_key,
    should_apply_delay,
)
from telegram_bot.handlers.validate import (
    ButterflyChartValidator,
    FundingRateValidator,
    PerpPriceValidator,
    SkewChartValidator,
    SmileChartValidator,
    SpotValidator,
    VolChartValidator,
)
from telegram_bot.typings import (
    AllowedChartTypes,
    AllowedCurrencies,
    AllowedDeltas,
    ChartRequest,
    DecodedCharts,
    PlotObjects,
    ValidatedFundingRateRequest,
    ValidatedPerpPriceRequest,
    ValidatedSmileRequest,
    ValidatedSpotRequest,
    ValidatedVolRequest,
    VolTimeseriesChartTypes,
)


async def handle_chart_request(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    botfig: Botfig,
    logger: logging.Logger,
    **kwargs: Any,
) -> None:
    """
    Handles the incoming chart request from a Telegram user.

    Validates the user's input, processes the chart request,
    invokes the appropriate AWS Lambda function to generate the chart,
    and sends the chart back to the user.

    Parameters:
    - update (Update): Incoming Telegram update that triggered the handler.
    - context (ContextTypes.DEFAULT_TYPE): Context provided by the Telegram bot.
    - timeseries_version (str, optional): Version of the timeseries data to use.
    - **kwargs (Any): Additional keyword arguments.

    Returns:
    - None
    """
    if update.effective_chat is None or update.effective_user is None:
        logger.error("Effective User not found")
        return

    apply_delay = should_apply_delay(
        is_premium_bot=botfig.is_premium_bot,
        user_id=update.effective_user.id,
        user_username=update.effective_user.username,
        whitelist=botfig.get_whitelist,
    )

    try:

        chart_request = _process_chart_arguments(context, botfig=botfig)

    except NoChartArgumentsException as _err:

        await send_message_suggestion(
            context=context,
            update=update,
            error=_err,
            request_type="chart",
            botfig=botfig,
        )

        return

    except ChartValidationError as _err:

        logger.exception("Invalid message format encountered")
        await send_message_suggestion(
            context=context,
            update=update,
            error=_err,
            request_type="chart",
            botfig=botfig,
        )

        return

    except Exception as _err:

        error_message = str(_err)
        logger.exception("Unexpected error occurred")
        await send_failed_request_message(
            context, update, error=error_message, request_type="chart"
        )

        return

    try:

        plot_objects = await _handle_chart_type(
            chart_request=chart_request,
            botfig=botfig,
            context=context,
            update=update,
            apply_delay=apply_delay,
        )

        if plot_objects is None:
            # request already handled
            return

    except NotImplementedError:

        logger.exception("Invalid request recieved")
        # return, message already sent
        return

    except Exception as _err:
        error_message = str(_err)
        logger.exception("Error handling chart type")
        await send_failed_request_message(
            context, update, error=error_message, request_type="chart"
        )

        return

    # global async cache - check membership - use key
    # else call lambda
    # assign response to new key

    # pay attention to cacheing the previous hours charts when the current hours charts isnt available
    request_key = get_chart_request_key(chart_request)

    try:

        # Get the exchange for this currency from the mapping
        exchange = botfig.get_exchange_for_currency(chart_request.currency)

        request_event = {
            "lambda_name": "ResearchVolDashboardLambdaFunction",
            "name": request_key,
            "arguments": await construct_chart_request(
                chart_request=chart_request,
                plot_objects=plot_objects,
                version=botfig.timeseries_version,
                exchange=exchange,
                apply_delay=apply_delay,
                timeseries_lookback=CHART_TYPE_TO_TIMESERIES_LOOKBACK.get(
                    chart_request.chart_type, None
                ),
            ),
        }

    except Exception as _err:
        error_message = str(_err)
        logger.exception("Error constructing chart request")
        await send_failed_request_message(
            context, update, error=error_message, request_type="chart"
        )

        return

    # Call Lambda
    response_body = await _call_lambda(request_event, logger)
    if not response_body:
        await send_failed_request_message(
            context, update, "No charts returned", request_type="chart"
        )
        return

    # Parse images
    try:

        decoded_images = await _get_decoded_images_from_response(
            response_body, chart_type=chart_request.chart_type
        )

    except Exception as _err:
        error_message = str(_err)
        logger.exception("Error parsing images")
        await send_failed_request_message(
            context, update, error=error_message, request_type="chart"
        )

        return

    # Send the images to the user
    for _chart_category, charts in decoded_images.items():
        for chart_name, image_file in charts.items():
            caption = chart_name
            if apply_delay:
                caption += (
                    f"\n\nAll charts are delayed by {int(COMMAND_DELAYS['chart'].total_seconds() // 3600)} hours.\n"
                    f"To access live charting, please /contact a member of the BlockScholes team."
                )
            else:
                caption += (
                    "\n\nThis is charted with the latest available data.\n"
                )

            await context.bot.send_photo(
                chat_id=update.effective_chat.id,
                photo=image_file,
                caption=caption,
            )


def _process_chart_arguments(
    context: ContextTypes.DEFAULT_TYPE,
    botfig: Botfig,
) -> ChartRequest:
    """
    Processes and validates the chart arguments provided by the user.

    Parameters:
    - context (ContextTypes.DEFAULT_TYPE): The context containing user arguments.

    Returns:
    - ChartRequest: An object containing the validated chart request parameters.

    Raises:
    - ChartValidationError: If the provided arguments are invalid.
    """
    args = context.args
    if not args or args is None:
        raise NoChartArgumentsException("No chart arguments provided")
    chart_type = args[0].lower()

    if chart_type == "vol":
        return VolChartValidator.validate(args, botfig=botfig)
    elif chart_type == "skew":
        return SkewChartValidator.validate(args, botfig=botfig)
    elif chart_type == "butterfly":
        return ButterflyChartValidator.validate(args, botfig=botfig)
    elif chart_type == "smile":
        return SmileChartValidator.validate(args, botfig=botfig)
    elif chart_type == "spot":
        return SpotValidator.validate(args, botfig=botfig)
    elif chart_type == "fr":
        return FundingRateValidator.validate(args, botfig=botfig)
    elif chart_type == "perp":
        return PerpPriceValidator.validate(args, botfig=botfig)
    else:
        raise ChartValidationError(
            f"Unsupported chart type '{chart_type}'. Supported chart types are: {get_args(AllowedChartTypes)}."
        )


async def _handle_chart_type(
    chart_request: ChartRequest,
    botfig: Botfig,
    context: ContextTypes.DEFAULT_TYPE,
    update: Update,
    apply_delay: bool,
) -> PlotObjects | None:
    """
    Handles the chart request based on the chart type.

    Constructs the plot objects required for the Lambda function
    based on the chart type and sends an acknowledgement message to the user.

    Parameters:
    - chart_request (ChartRequest): The validated chart request.
    - context (ContextTypes.DEFAULT_TYPE): The context from the Telegram bot.
    - update (Update): The update object representing the incoming message.

    Returns:
    - PlotObjects: The plot objects required for the chart.
    - None: When the request type is 'fr' arbitrage, the results are sent to the user directly

    Raises:
    - NotImplementedError: If the chart type is not supported.
    """
    chart_type = chart_request.chart_type
    currency = chart_request.currency
    chart_target: str | AllowedDeltas

    if isinstance(chart_request, ValidatedVolRequest):
        chart_target = chart_request.chart_target
        # Get the exchange for this currency from the mapping
        await _send_chart_acknowledgement_message(
            context=context,
            update=update,
            chart_type=chart_type,
            currency=currency,
        )
        return await build_vol_based_timeseries_plot_objects(
            currency=currency,
            chart_target=chart_target,
            chart_type=cast(VolTimeseriesChartTypes, chart_type),
            botfig=botfig,
        )
    elif isinstance(chart_request, ValidatedSmileRequest):
        chart_target = chart_request.chart_target
        # Get the exchange for this currency from the mapping
        await _send_chart_acknowledgement_message(
            context=context,
            update=update,
            chart_type=chart_type,
            currency=currency,
        )
        return await build_smile_plot_objects(
            currency=currency,
            chart_target=chart_target,
            apply_delay=apply_delay,
            botfig=botfig,
        )
    elif isinstance(chart_request, ValidatedSpotRequest):

        # grab instruments
        instruments = await grab_instruments(
            exchange=chart_request.exchange,
            currency=chart_request.currency,
            asset_type="spot",
        )

        await _send_chart_acknowledgement_message(
            context=context,
            update=update,
            chart_type=chart_type,
            currency=currency,
        )
        return await build_spot_price_plot_objects(
            instruments=instruments,
            exchange=chart_request.exchange,
            currency=currency,
        )
    elif isinstance(chart_request, ValidatedPerpPriceRequest):
        await _send_chart_acknowledgement_message(
            context=context,
            update=update,
            chart_type=chart_type,
            currency=currency,
        )

        # grab instruments
        instruments = await grab_instruments(
            exchange=chart_request.exchange,
            currency=chart_request.currency,
            asset_type="perpetual",
        )

        return await build_perp_price_plot_objects(
            exchange=chart_request.exchange,
            instruments=instruments,
            currency=chart_request.currency,
        )

    elif isinstance(chart_request, ValidatedFundingRateRequest):
        await _send_chart_acknowledgement_message(
            context=context,
            update=update,
            chart_type=chart_type,
            currency=currency,
        )
        return await _process_funding_rate_request(
            botfig=botfig,
            chart_request=chart_request,
            apply_delay=apply_delay,
            context=context,
            update=update,
        )

    else:
        if update.effective_chat:
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text=f"Chart type '{chart_type}' is not supported.",
            )
            raise NotImplementedError(
                f"Chart type '{chart_type}' is not supported."
            )
        raise NotImplementedError(
            f"Chart type '{chart_type}' is not supported."
        )


async def _process_funding_rate_request(
    botfig: Botfig,
    chart_request: ValidatedFundingRateRequest,
    apply_delay: bool,
    context: ContextTypes.DEFAULT_TYPE,
    update: Update,
) -> PlotObjects | None:

    chart_target = chart_request.exchange
    if chart_target == "arbitrage":
        await _create_funding_rate_table_and_send_message(
            botfig=botfig,
            chart_request=chart_request,
            apply_delay=apply_delay,
            context=context,
            update=update,
        )
        return None

    else:
        # grab instruments
        instruments = await grab_instruments(
            exchange=chart_request.exchange,
            currency=chart_request.currency,
            asset_type="perpetual",
        )

        return await build_funding_rate_plot_objects(
            exchange=chart_request.exchange,
            instruments=instruments,
            currency=chart_request.currency,
        )


async def _create_funding_rate_table_and_send_message(
    botfig: Botfig,
    chart_request: ValidatedFundingRateRequest,
    apply_delay: bool,
    context: ContextTypes.DEFAULT_TYPE,
    update: Update,
) -> None:

    if not update.effective_chat:
        return

    qns = []
    instr_key_to_quote = utils_general.nested_dict()
    exchanges_for_currency = [
        exchange
        for exchange, ccys in botfig.funding_rate_config.items()
        if chart_request.currency in ccys
    ]

    # no need to clamp delays to nearest hour as we fetch latest set of ticks within some time range
    snapshot = apply_time_delay(
        timestamp=datetime.now(UTC),
        command_type="chart",
        apply_delay=apply_delay,
    )

    catalog_items = await get_instruments_async(
        fields=[],
        start=utils_general.to_iso(snapshot),
        end=utils_general.to_iso(snapshot),
        exchanges=exchanges_for_currency,
        asset_types=["perpetual"],
        base_assets=[chart_request.currency],
    )

    for instr in catalog_items:
        qn = f"{instr['qualified_name'].split('.')[0]}.perpetual.{instr['instrument']}.tick.funding.rate"
        qns.append(qn)
        instr_key_to_quote[
            f"{instr['qualified_name'].split('.')[0]}.{instr['instrument']}"
        ] = instr["quoteAsset"]

    queries = construct_timeseries_queries(
        qualified_names=qns,
        start=int(
            (snapshot - timedelta(days=1)).timestamp() * 1e9
        ),  # ticks can be sparce
        end=int(snapshot.timestamp() * 1e9),
        lookup_options={
            "order_by": {"pkey": "asc", "skey": "desc"},
            "limit": 1,
        },
        fields=[],
    )

    latest_functing_rate_ticks = await grab_async(queries)

    for res in latest_functing_rate_ticks:
        qn_tokens = str(res["qualified_name"]).split(".")
        instr_key = f"{qn_tokens[0]}.{qn_tokens[2]}"
        # add key for easier aggregation
        res["quoteAsset"] = instr_key_to_quote[instr_key]

    fr_table = _create_funding_rate_table(
        funding_data=latest_functing_rate_ticks, currency=chart_request.currency
    )
    fr_image = _create_funding_rate_table_image(fr_table)

    recent_timestamp_pretty = snapshot.strftime("%B %d, %Y %H:%M")
    message_text = f"{chart_request.currency.upper()} Funding Rate Table"

    if apply_delay:
        message_text += (
            f"\n\nAll funding rates are delayed by {int(COMMAND_DELAYS['chart'].total_seconds() // 3600)!s} hours ({recent_timestamp_pretty}).\n"
            f"To access live data, please /contact a member of the BlockScholes team."
        )
    else:
        message_text += f"\n\nLatest Data ({recent_timestamp_pretty} UTC)."

    await context.bot.send_photo(
        chat_id=update.effective_chat.id,
        photo=fr_image,
        caption=message_text,
        parse_mode=ParseMode.HTML,
    )

    return


def _create_funding_rate_table(
    funding_data: list[dict[str, Any]], currency: AllowedCurrencies
) -> list[dict[str, Any]]:
    """
    Constructs a table from funding rate data.
    Each row corresponds to an exchange and each column (other than 'Exchange' and
    'request_timestamp') represents a quote currency.

    Each record in funding_data is expected to contain:
      - 'exchange': the exchange name.
      - 'quoteAsset': the quote currency.
      - 'funding_rate': the funding rate value.

    If an exchange does not have a funding rate for a given quote currency,
    that cell will be marked as 'N/A'.
    """
    table = {}
    all_quotes = set()

    # Build rows keyed by exchange and collect all quote currencies
    for record in funding_data:

        exchange = record["qualified_name"].split(".")[0]
        quote = record["quoteAsset"]

        raw_fr = float(record["rate"]) * 100
        formatted_str = format(raw_fr, ".2g")
        rate = float(formatted_str)

        all_quotes.add(f"{currency}-{quote}")
        if exchange not in table:
            table[exchange] = {"Exchange": exchange.capitalize()}
        table[exchange][f"{currency}-{quote}"] = rate

    # Ensure each exchange row contains all quote columns
    for exchange in table:
        for quote in all_quotes:
            if quote not in table[exchange]:
                table[exchange][quote] = "N/A"

    return sorted(table.values(), key=lambda row: row["Exchange"])


def _create_funding_rate_table_image(
    table_data: list[dict[str, Any]]
) -> io.BytesIO:
    """
    Creates an image of the funding rate table.
    The table's columns are the different quote currencies and the rows are the exchanges.
    The styling mimics the tenor table style:
      - Dark blue background (#101A2E)
      - White text with cell borders in white
    The image is saved to an in-memory bytes buffer.
    """
    # Convert table data to a DataFrame.
    df = pd.DataFrame(table_data)
    df.columns = pd.Index(
        [col if col == "Exchange" else f"{col} (%)" for col in df.columns]
    )
    num_rows = len(df)
    fig_height = num_rows * 0.6 + 1
    fig, ax = plt.subplots(figsize=(12, fig_height))
    fig.patch.set_facecolor("#101A2E")
    ax.axis("off")

    # Create table filling the entire figure.
    bbox = Bbox([[0, 0], [1, 1]])
    table = ax.table(
        cellText=df.astype(str).values.tolist(),
        colLabels=df.columns.tolist(),
        cellLoc="center",
        loc="center",
        bbox=bbox,
    )

    table.auto_set_font_size(False)
    table.set_fontsize(18)

    # Set styling for all cells.
    for cell in table.get_celld().values():
        cell.set_edgecolor("white")
        cell.set_linewidth(0.7)
        cell.set_facecolor("#101A2E")
        cell.get_text().set_color("white")

    # Bold the header row.
    for j in range(len(df.columns)):
        table[(0, j)].get_text().set_fontweight("bold")

    # Save the figure to an in-memory bytes buffer.
    buf = io.BytesIO()
    plt.savefig(buf, format="png", bbox_inches="tight", dpi=150)
    buf.seek(0)
    plt.close(fig)
    return buf


async def _call_lambda(
    input_arguments: dict[str, Any], logger: logging.Logger
) -> dict[str, Any]:
    """
    Invokes the specified AWS Lambda function with the given arguments.

    Parameters:
    - input_arguments (dict[str, Any]): Arguments including lambda name and payload.

    Returns:
    - dict[str, Any]: The response body from the Lambda function.

    Notes:
    - Retries the invocation if a Runtime.ExitError is encountered.
    """
    exit_error_retries = 1
    t = time.time()

    async with aioboto3.Session().client(
        "lambda",
        config=AioConfig(
            max_pool_connections=200,
            retries={"total_max_attempts": 50, "mode": "legacy"},
        ),
    ) as lambda_client:
        while exit_error_retries:
            try:
                logger.info(
                    f"Executing : {input_arguments['lambda_name']}, {input_arguments['name']}",
                )
                invoke_response = await lambda_client.invoke(
                    FunctionName=input_arguments["lambda_name"],
                    InvocationType="RequestResponse",
                    Payload=utils_general.json_dumps(
                        input_arguments["arguments"]
                    ),
                )
                response_payload = await invoke_response["Payload"].read()
                json_response = utils_general.json_loads(response_payload)

                # Only accept 200 status codes
                if "statusCode" in json_response:
                    status = json_response["statusCode"]
                    if status != 200:
                        logger.error(
                            f"Lambda did not return successfully. Name : {input_arguments['lambda_name']}, Status : {status}, Output : {json_response}",
                        )
                    else:
                        logger.info(
                            f"{input_arguments['name']} execution took: {round(time.time() - t)}s",
                        )
                        return cast(
                            dict[str, Any], json.loads(json_response["body"])
                        )
                elif json_response.get("errorType", "") == "Runtime.ExitError":
                    logger.info(
                        f"{input_arguments['lambda_name']} exited prematurely. Likely reboot due to socket leak. Retries left: {exit_error_retries}",
                    )
                else:
                    logger.error(
                        f"Error Invoking Lambda. Name : {input_arguments['lambda_name']}, Output : {json_response}",
                    )
            except Exception:
                logger.exception(
                    f"Exception Invoking Lambda. Name : {input_arguments['lambda_name']}",
                )
            exit_error_retries -= 1

    return {}


async def _get_decoded_images_from_response(
    response_body: dict[str, Any], chart_type: AllowedChartTypes
) -> DecodedCharts:
    """
    Extracts and decodes images from the Lambda response.

    Parameters:
    - response_body (dict[str, Any]): The response body from the Lambda function.
    - chart_type (AllowedChartTypes): The type of chart ('vol' or 'smile').

    Returns:
    - DecodedCharts: A nested dictionary of decoded images.

    Raises:
    - ImageNotFoundError: If no images are found in the response body.
    """
    encoded_images = response_body.get("output", {}).get("results", {})
    if not encoded_images:
        raise ImageNotFoundError("No images found in the response body")

    decoded_images: DecodedCharts = utils_general.nested_dict()
    plot_type_encoded_images = encoded_images.get(
        CHART_TYPE_TO_PLOT_TYPE[chart_type], {}
    )
    for chart_name, encoded_image in plot_type_encoded_images.items():
        _chart_name = chart_name.split(".")[0].replace(
            "v2smiles", ""
        )  # todo: fix this in the rvd
        decoded_images[CHART_TYPE_TO_PLOT_TYPE[chart_type]][_chart_name] = (
            decode_image(encoded_image)
        )

    return decoded_images


async def _send_chart_acknowledgement_message(
    context: ContextTypes.DEFAULT_TYPE,
    update: Update,
    chart_type: AllowedChartTypes,
    currency: AllowedCurrencies,
) -> None:
    """
    Sends an acknowledgement message to the user indicating that the chart is being generated.

    Parameters:
    - context (ContextTypes.DEFAULT_TYPE): The context from the Telegram bot.
    - update (Update): The update object representing the incoming message.
    - chart_type (AllowedChartTypes): The type of chart requested.
    - currency (AllowedCurrencies): The currency code.

    Returns:
    - None
    """
    if update.effective_chat:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=f"Generating {chart_type.upper()} chart for {currency}.",
        )
