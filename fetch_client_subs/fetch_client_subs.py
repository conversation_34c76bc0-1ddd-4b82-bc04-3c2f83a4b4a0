import asyncio
import json
import logging
import os
import re
import sys
import time
from collections import OrderedDict, defaultdict
from contextlib import asynccontextmanager
from dataclasses import dataclass
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, AsyncGenerator, Literal, Optional, Sequence, TypedDict, cast

import aiobotocore.client
import aiobotocore.session
import backoff
import utils_general

utils_general.setup_python_logger(os.getenv("LOG_LEVEL", "INFO"))


def get_field_value(result: list[dict[str, str]], field_name: str) -> Optional[str]:
    """
    Extract a field value from a CloudWatch Logs result.

    Args:
        result: List of dictionaries containing field-value pairs
        field_name: Name of the field to extract

    Returns:
        The value of the field if found, None otherwise
    """
    for field in result:
        if field["field"] == field_name:
            return field["value"]
    return None


def parse_timestamp(timestamp_str: str) -> Optional[str]:
    """
    Parse different timestamp formats and convert to ISO format.

    Args:
        timestamp_str: Timestamp string in either milliseconds since epoch or datetime format

    Returns:
        ISO formatted timestamp string if parsing successful, None otherwise
    """
    try:
        # Try to parse as float (milliseconds since epoch)
        timestamp_ms = float(timestamp_str)
        timestamp_dt = datetime.fromtimestamp(timestamp_ms / 1000.0, tz=timezone.utc)
        return timestamp_dt.isoformat()
    except ValueError:
        # Try parsing as datetime string with microseconds
        try:
            timestamp_dt = datetime.strptime(
                timestamp_str, "%Y-%m-%d %H:%M:%S.%f"
            ).replace(tzinfo=timezone.utc)
            return timestamp_dt.isoformat()
        except ValueError:
            # Try parsing as datetime string without microseconds
            try:
                timestamp_dt = datetime.strptime(
                    timestamp_str, "%Y-%m-%d %H:%M:%S"
                ).replace(tzinfo=timezone.utc)
                return timestamp_dt.isoformat()
            except ValueError:
                # Unable to parse timestamp
                return None


class CloudWatchQueryError(Exception):
    pass


QueryStatus = Literal[
    "Complete", "Running", "Scheduled", "Failed", "Cancelled", "Timeout", "Unknown"
]


class QueryStatistics(TypedDict):
    bytesScanned: float
    recordsMatched: float
    recordsScanned: float


class QueryResponse(TypedDict):
    status: QueryStatus
    statistics: QueryStatistics
    results: list[dict[str, Any]]


@dataclass
class CloudWatchQuery:
    logs_client: aiobotocore.client.AioBaseClient
    query_id: str

    @classmethod
    async def start(
        cls,
        logs_client: aiobotocore.client.AioBaseClient,
        log_group: str,
        query: str,
        start_time: datetime,
        end_time: datetime,
    ) -> "CloudWatchQuery":
        """
        Start a new CloudWatch Logs Insights query.

        Args:
            logs_client: AWS Logs client
            log_group: CloudWatch Logs group name
            query: Query string to execute
            start_time: Query start time
            end_time: Query end time

        Returns:
            CloudWatchQuery instance for the started query
        """
        response = await logs_client.start_query(
            logGroupName=log_group,
            startTime=int(start_time.timestamp()),
            endTime=int(end_time.timestamp()),
            queryString=query,
        )
        return cls(logs_client=logs_client, query_id=response["queryId"])

    @backoff.on_exception(backoff.expo, Exception, max_tries=10, max_time=10)
    async def get_results(self) -> QueryResponse:
        """
        Retrieve and wait for query results with exponential backoff.

        Returns:
            Query response containing status, statistics, and results

        Raises:
            CloudWatchQueryError: If query fails or times out
        """
        wait = 3
        while True:
            response = cast(
                QueryResponse,
                await self.logs_client.get_query_results(queryId=self.query_id),
            )
            status = response["status"]
            if status in ["Running", "Scheduled"]:
                await asyncio.sleep(wait)
                logging.info(
                    "Query %s is %s. Re-checking results in %ds",
                    self.query_id,
                    status,
                    wait,
                )
                wait *= 2
            elif status == "Complete":
                return response
            else:
                raise CloudWatchQueryError(f"Query failed with status: {status}")


@dataclass
class EnvConfig:
    aws_region: str
    aws_env: str
    start_time: datetime
    end_time: datetime
    search_string: str
    output_path: Path
    sub_search_string_include: Optional[str]
    sub_search_string_exclude: Optional[str]

    @property
    def log_group_name(self) -> str:
        return f"/ecs/websocketApi-containers-{self.aws_env}"

    @classmethod
    def from_env(cls) -> "EnvConfig":
        """
        Create configuration from environment variables.

        Returns:
            EnvConfig instance

        Raises:
            SystemExit: If required environment variables are missing
        """
        start_time_str = os.environ.get("QUERY_START")
        end_time_str = os.environ.get("QUERY_END")
        search_string = os.environ.get("API_KEY_NAME_SEARCH_STRING")
        output_path = Path(os.environ.get("OUTPUT_PATH", "client_subs"))
        aws_env = os.environ.get("AWS_ENV", "prod")

        required_vars = {
            "QUERY_START": start_time_str,
            "QUERY_END": end_time_str,
            "API_KEY_NAME_SEARCH_STRING": search_string,
            "OUTPUT_PATH": output_path,
        }
        missing_vars = [var for var, value in required_vars.items() if not value]
        if missing_vars:
            logging.error(
                "Missing required environment variables: %s", ", ".join(missing_vars)
            )
            sys.exit(1)

        try:
            start_time = datetime.strptime(
                start_time_str, "%Y-%m-%dT%H:%M:%SZ"
            ).replace(tzinfo=timezone.utc)
            end_time = datetime.strptime(end_time_str, "%Y-%m-%dT%H:%M:%SZ").replace(
                tzinfo=timezone.utc
            )
        except ValueError as e:
            logging.error("Error parsing date/time: %s", e)
            sys.exit(1)

        return cls(
            aws_region=os.environ.get("AWS_REGION", "eu-west-2"),
            aws_env=aws_env,
            start_time=start_time,
            end_time=end_time,
            search_string=search_string,
            output_path=output_path,
            sub_search_string_include=os.getenv("SUB_SEARCH_STRING_INCLUDE"),
            sub_search_string_exclude=os.getenv("SUB_SEARCH_STRING_EXCLUDE"),
        )

    def output_file_name(self) -> Path:
        sanitized_name = re.sub(r"[^\w]+", "_", self.search_string)
        return self.output_path / f"{sanitized_name}.json"


class QueryBuilder:
    """Builds CloudWatch Logs Insights query strings."""

    @staticmethod
    def create_batch_query(
        client_ids: Sequence[str],
        sub_search_include: Optional[str],
        sub_search_exclude: Optional[str],
    ) -> str:
        """
        Create a query string for batch subscribe messages.

        Args:
            client_ids: List of client IDs to search for
            sub_search_include: Optional string that must be in messages
            sub_search_exclude: Optional string that must not be in messages

        Returns:
            Query string for CloudWatch Logs Insights
        """
        escaped_ids = [re.escape(cid) for cid in client_ids]
        id_pattern = "|".join(escaped_ids)
        query_string = f"""
        fields @timestamp, @message
        | filter @message like /Handling batch subscribe: ({id_pattern})/
        """
        if sub_search_include:
            escaped_sub_search_string = re.escape(sub_search_include)
            query_string += f"| filter @message like /{escaped_sub_search_string}/"
        if sub_search_exclude:
            escaped_sub_search_string = re.escape(sub_search_exclude)
            query_string += f"| filter @message not like /{escaped_sub_search_string}/"
        query_string += "| sort @timestamp asc"
        return query_string


class RateLimiter:
    """Rate limiter for API calls."""

    def __init__(self, rate_limit: int = 5) -> None:
        self.rate_limit = rate_limit
        self.tokens: asyncio.Queue[int] = asyncio.Queue(maxsize=rate_limit)
        for _ in range(rate_limit):
            self.tokens.put_nowait(1)

    @asynccontextmanager
    async def acquire(self) -> AsyncGenerator[None, None]:
        """Acquire a token for API call."""
        token = await self.tokens.get()
        try:
            yield
        finally:
            await self.tokens.put(token)


class QueryRunner:
    """Executes CloudWatch Logs Insights queries as a singleton."""

    _instance: Optional["QueryRunner"] = None

    def __new__(cls) -> "QueryRunner":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._rate_limiter = None
        return cls._instance

    def __init__(self) -> None:
        # Initialize only if not already initialized
        if not hasattr(self, "_rate_limiter"):
            self._rate_limiter: Optional[RateLimiter] = None

    @property
    def rate_limiter(self) -> RateLimiter:
        if self._rate_limiter is None:
            self._rate_limiter = RateLimiter()
        return self._rate_limiter

    async def run_query(
        self,
        logs_client: aiobotocore.client.AioBaseClient,
        log_group_name: str,
        query_string: str,
        start_time: datetime,
        end_time: datetime,
    ) -> tuple[list[dict[str, Any]], float]:
        """Execute a CloudWatch Logs Insights query with retries."""
        async with self.rate_limiter.acquire():
            try:
                query = await CloudWatchQuery.start(
                    logs_client, log_group_name, query_string, start_time, end_time
                )
                response = await query.get_results()
                return response["results"], response["statistics"]["bytesScanned"]
            except Exception:
                logging.exception("Query failed for: %s", query_string)
                return [], 0.0


class LogsProcessor:
    """Processes CloudWatch logs in batches."""

    def __init__(self) -> None:
        self.query_runner = QueryRunner()

    @staticmethod
    def split_into_bounded_batches(
        connection_ids: set[str],
        max_query_size: int = 9000,
    ) -> list[list[str]]:
        """Split connection IDs into batches that fit within CloudWatch query size limits."""
        batches: list[list[str]] = []
        current_batch: list[str] = []
        current_pattern_size = 0
        base_query_overhead = len(
            """
        fields @timestamp, @message
        | filter @message like /Handling batch subscribe: ()/
        | sort @timestamp asc
        """
        )

        available_size = max_query_size - base_query_overhead

        for connection_id in connection_ids:
            escaped_id = re.escape(connection_id)
            id_size = len(escaped_id) + (2 if current_batch else 0)

            if current_pattern_size + id_size > available_size:
                if current_batch:
                    batches.append(current_batch)
                current_batch = [connection_id]
                current_pattern_size = len(escaped_id)
            else:
                current_batch.append(connection_id)
                current_pattern_size += id_size

        if current_batch:
            batches.append(current_batch)

        return batches

    def extract_client_ids(
        self, results: list[dict[str, Any]]
    ) -> tuple[set[str], float]:
        """Extract unique connection IDs from query results."""
        extract_start_time = time.time()
        connection_ids: set[str] = set()
        auth_pattern = re.compile(r"Authenticated:\s+([^\s]+)")
        for result in results:
            message = get_field_value(result, "@message")
            if message:
                match = auth_pattern.search(message)
                if match:
                    connection_id = match.group(1)
                    connection_ids.add(connection_id)
        extract_time = time.time() - extract_start_time
        logging.info(
            "Extracted %d connection IDs in %.2f seconds",
            len(connection_ids),
            extract_time,
        )
        return connection_ids, extract_time

    @backoff.on_exception(backoff.expo, Exception, max_tries=10, max_time=10)
    async def process_client_ids_batch(
        self,
        connection_ids: Sequence[str],
        logs_client: aiobotocore.client.AioBaseClient,
        log_group_name: str,
        start_time: datetime,
        end_time: datetime,
        sub_search_string_include: Optional[str],
        sub_search_string_exclude: Optional[str],
    ) -> tuple[list[tuple[str, dict[str, Any]]], float]:
        """Process a batch of connection IDs to extract their subscribe messages."""
        logging.info(
            "Searching for logs for %d connection IDs in batch", len(connection_ids)
        )
        query_string = QueryBuilder.create_batch_query(
            connection_ids, sub_search_string_include, sub_search_string_exclude
        )

        try:
            results, bytes_scanned = await self.query_runner.run_query(
                logs_client, log_group_name, query_string, start_time, end_time
            )
            timestamped_results: list[tuple[str, dict[str, Any]]] = []

            for result in results:
                message = get_field_value(result, "@message")
                timestamp = get_field_value(result, "@timestamp")
                if message and timestamp:
                    if (
                        sub_search_string_include
                        and sub_search_string_include not in message
                    ):
                        continue

                    connection_id_match = re.search(
                        r"Handling batch subscribe: ([^\s]+)", message
                    )
                    if not connection_id_match:
                        continue
                    connection_id = connection_id_match.group(1)

                    json_match = re.search(r"(\{.*\})", message, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(1)
                        try:
                            json_obj = json.loads(json_str)
                            timestamp_iso = parse_timestamp(timestamp)
                            if not timestamp_iso:
                                logging.warning(
                                    "Unable to parse timestamp '%s' for connection ID %s",
                                    timestamp,
                                    connection_id,
                                )
                                continue
                            timestamped_results.append((timestamp_iso, json_obj))
                        except json.JSONDecodeError as e:
                            logging.error(
                                "Error parsing JSON for connection ID %s: %s",
                                connection_id,
                                e,
                            )
                    else:
                        logging.warning(
                            "No JSON object found in message for connection ID %s",
                            connection_id,
                        )

            return timestamped_results, bytes_scanned
        except Exception as e:
            logging.error("Batch processing generated an exception: %s", e)
            return [], 0.0

    async def process_batches_with_semaphore(
        self,
        batches: list[list[str]],
        logs_client: aiobotocore.client.AioBaseClient,
        log_group_name: str,
        start_time: datetime,
        end_time: datetime,
        sub_search_string_include: Optional[str],
        sub_search_string_exclude: Optional[str],
        max_concurrency: int = 20,
        chunk_size: int = 1000,
    ) -> tuple[list[tuple[str, dict[str, Any]]], float]:
        semaphore = asyncio.Semaphore(max_concurrency)
        total_bytes_scanned = 0.0
        total_batches = len(batches)

        async def process_chunk(
            chunk: list[list[str]],
        ) -> list[tuple[list[tuple[str, dict[str, Any]]], float]]:
            tasks = [
                process_with_semaphore(batch, i, total_batches)
                for i, batch in enumerate(chunk)
            ]
            return await asyncio.gather(*tasks)

        async def process_with_semaphore(
            batch: list[str], batch_num: int, total: int
        ) -> tuple[list[tuple[str, dict[str, Any]]], float]:
            async with semaphore:
                result = await self.process_client_ids_batch(
                    batch,
                    logs_client,
                    log_group_name,
                    start_time,
                    end_time,
                    sub_search_string_include,
                    sub_search_string_exclude,
                )
                logging.info(
                    "Batch processed %d/%d (size: %d)",
                    batch_num + 1,
                    total,
                    len(batch),
                )
                return result

        flattened_results = []
        for i in range(0, len(batches), chunk_size):
            chunk = batches[i : i + chunk_size]
            chunk_results = await process_chunk(chunk)
            for results, bytes_scanned in chunk_results:
                flattened_results.extend(results)
                total_bytes_scanned += bytes_scanned

        return flattened_results, total_bytes_scanned

    async def run_batch_processing(
        self,
        logs_client: aiobotocore.client.AioBaseClient,
        config: EnvConfig,
        client_ids: set[str],
    ) -> tuple[list[tuple[str, dict[str, Any]]], float, float]:
        """Process all client IDs in batches to extract their subscribe messages."""
        batches = self.split_into_bounded_batches(client_ids)
        logging.info(
            "Split %d client IDs into %d batches", len(client_ids), len(batches)
        )

        processing_start_time = time.time()
        (
            timestamped_results,
            batch_bytes_scanned,
        ) = await self.process_batches_with_semaphore(
            batches,
            logs_client,
            config.log_group_name,
            config.start_time,
            config.end_time,
            config.sub_search_string_include,
            config.sub_search_string_exclude,
        )
        processing_time = time.time() - processing_start_time
        logging.info("Processed all batches in %.2f seconds", processing_time)
        return timestamped_results, batch_bytes_scanned, processing_time


async def run_initial_query(
    logs_client: aiobotocore.client.AioBaseClient,
    config: EnvConfig,
) -> tuple[list[dict[str, Any]], float, float]:
    """
    Run the initial query to find entries matching the search string.

    Args:
        logs_client: AWS Logs client
        config: Environment configuration

    Returns:
        Tuple of (results, bytes scanned, query time)
    """
    escaped_search_string = re.escape(config.search_string)
    query_string = f"""
    fields @timestamp, @message
    | filter @message like /{escaped_search_string}/
    | sort @timestamp asc
    """
    logging.info("Running initial query to find '%s' entries...", config.search_string)
    query_start_time = time.time()
    results, initial_bytes_scanned = await QueryRunner().run_query(
        logs_client,
        config.log_group_name,
        query_string,
        config.start_time,
        config.end_time,
    )

    query_time = time.time() - query_start_time
    logging.info(
        "Initial query completed in %.2f seconds. Found %d authentication events.",
        query_time,
        len(results),
    )
    return results, initial_bytes_scanned, query_time


def write_results(
    items: list[tuple[str, dict[str, Any]]],
    config: EnvConfig,
) -> float:
    """Write results to a JSON file."""
    write_start_time = time.time()
    items.sort(key=lambda x: x[0])
    ordered_dict = OrderedDict((ts, obj) for ts, obj in items)

    if ordered_dict:
        output_path = config.output_file_name()
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Write to temporary file first
        temp_path = output_path.with_suffix(".tmp")
        try:
            with temp_path.open("w") as f:
                json.dump(ordered_dict, f, indent=4)

            # Validate JSON file
            with temp_path.open("r") as f:
                json.load(f)

            # Atomic rename
            temp_path.rename(output_path)

            elapsed = time.time() - write_start_time
            logging.info(
                "Wrote %d JSON objects to %s in %.2f seconds",
                len(ordered_dict),
                output_path,
                elapsed,
            )
            return elapsed
        except Exception as e:
            logging.error("Failed to write results: %s", e)
            if temp_path.exists():
                temp_path.unlink()
            raise
    else:
        logging.warning("No JSON objects were extracted")
        return time.time() - write_start_time


def shorten_client_ids(connection_ids: set[str]) -> set[str]:
    prefix_map: dict[str, set[str]] = defaultdict(set)

    # Group by first token
    for cid in connection_ids:
        tokens = cid.split("-")
        prefix_map[tokens[0]].add(cid)

    result: set[str] = set()
    for prefix, colliding_ids in prefix_map.items():
        if len(colliding_ids) == 1:
            result.add(prefix)
            continue

        # Find minimum length needed for uniqueness
        current_length = 1
        while True:
            shortened = {
                "-".join(cid.split("-")[:current_length]) for cid in colliding_ids
            }
            if len(shortened) == len(colliding_ids):
                result.update(shortened)
                break
            current_length += 1

    return result


async def main() -> None:
    """Main execution function that coordinates the log extraction process."""
    try:
        total_start_time = time.time()
        config = EnvConfig.from_env()
        logs_processor = LogsProcessor()

        session = aiobotocore.session.get_session()
        async with session.create_client(
            "logs", region_name=config.aws_region
        ) as logs_client:
            logs_client = cast(aiobotocore.client.AioBaseClient, logs_client)
            results, initial_bytes_scanned, query_time = await run_initial_query(
                logs_client=logs_client,
                config=config,
            )

            client_ids, extract_time = logs_processor.extract_client_ids(results)
            if not client_ids:
                logging.warning("No connection IDs found")
                return

            shortened_client_ids = shorten_client_ids(client_ids)
            if len(client_ids) != len(shortened_client_ids):
                logging.warning(
                    "Shortened %d connection IDs to %d unique prefixes",
                    len(client_ids),
                    len(shortened_client_ids),
                )

            timestamped_results, batch_bytes_scanned, processing_time = (
                await logs_processor.run_batch_processing(
                    logs_client, config, shortened_client_ids
                )
            )

            write_time = write_results(timestamped_results, config)

            total_time = time.time() - total_start_time
            total_bytes_scanned = initial_bytes_scanned + batch_bytes_scanned
            gb_scanned = total_bytes_scanned / (1024 * 1024 * 1024)
            estimated_cost = gb_scanned * 0.0059

            logging.info(
                """Process completed
Total time:        %.2fs
Initial query:     %.2fs
ID extraction:     %.2fs
Batch processing:  %.2fs
Results writing:   %.2fs
Data scanned:      %.2f GB
Estimated cost:    $%.4f""",
                total_time,
                query_time,
                extract_time,
                processing_time,
                write_time,
                gb_scanned,
                estimated_cost,
            )

    except Exception as e:
        logging.error("Fatal error: %s", e)
        sys.exit(1)


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    asyncio.run(main())
