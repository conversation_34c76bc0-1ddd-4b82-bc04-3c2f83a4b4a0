{"containerDefinitions": [{"name": "s3Saver-container", "image": "public.ecr.aws/docker/library/python:3.11", "cpu": 0, "portMappings": [{"containerPort": 8080, "hostPort": 8080, "protocol": "tcp"}], "essential": true, "environment": [{"name": "FLAGS", "value": "-log_level INFO -stage 2 -eagerness 0.5 -max_inflight 15 -timeout_extend_by 60"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/s3Saver-2-containers-staging", "awslogs-region": "#{AWS_REGION}#", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "s3Saver-task-definition-2", "taskRoleArn": "arn:aws:iam::273532302533:role/s3SaverContainerRole", "executionRoleArn": "arn:aws:iam::273532302533:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "ARM64"}}