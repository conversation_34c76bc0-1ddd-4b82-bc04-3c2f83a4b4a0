name: Staging Function Deployment
on:
  workflow_dispatch:
    inputs:
      region:
        description: "AWS Region to deploy to"
        type: choice
        default: eu-west-2
        options:
          - eu-west-2
          - eu-west-1
      cpuArchitecture:
        description: "CPU architecture to deploy"
        type: choice
        default: X86_64
        options:
          - X86_64
          - ARM64
  push:
    branches:
      - main

jobs:
  deploy-function:
    name: Deploy ModelParamsCalc Function
    uses: blockscholes/workflows/.github/workflows/lambda-function-deploy.yml@main
    with:
      environment: staging
      repository: ${{ github.repository }}
      ref: ${{ github.ref }}
      template: staging-template.yaml
      cpuArchitecture: ${{ github.event.inputs.cpuArchitecture || 'X86_64' }}
      region: ${{ github.event.inputs.region || 'eu-west-2' }}
      python_version: 3.11
    secrets: inherit
