name: Staging Container Deployment
on:
  workflow_dispatch:
    inputs:
      region:
        description: "AWS Region to deploy to"
        type: choice
        default: eu-west-2
        options:
          - eu-west-2
          - eu-west-1
      cpuArchitecture:
        description: "CPU architecture to deploy"
        type: choice
        default: ARM64
        options:
          - X86_64
          - ARM64

  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy WebsocketAPI to Staging
    uses: blockscholes/workflows/.github/workflows/stream-deploy.yml@main
    with:
      environment: staging
      repository: ${{ github.repository }}
      ref: ${{ github.ref }}
      cpuArchitecture: ${{ inputs.cpuArchitecture || 'ARM64' }}
      region: ${{ inputs.region || 'eu-west-2' }}
      ecr_repository: websocketapi-staging
      ecs_service: websocketApi-service
      task_def: task-definitions/staging.json
      container: websocketApi-container
      python_version: 3.11
      docker_file: Dockerfile
    secrets: inherit
