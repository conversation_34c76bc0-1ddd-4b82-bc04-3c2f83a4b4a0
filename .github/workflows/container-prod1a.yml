name: Prod Container Deployment
on:
  workflow_dispatch:
  pull_request:
    types:
      - closed
    branches:
      - production

env:
  AWS_REGION: eu-west-2
  ECR_REPOSITORY: websocketapitester-production
  ECS_SERVICE: websocketApiTester-service
  ECS_CLUSTER: Production
  ECS_TASK_DEFINITION: task-definitions/prod1a.json
  CONTAINER_NAME: websocketApiTester-container

permissions:
  contents: read

jobs:
  deploy:
    if: (github.event_name == 'workflow_dispatch' && (contains(github.ref_name, 'production') || contains(from<PERSON>son('["dfiltz", "Cody-G-G", "Elachance", "lorincbalassa", "tonymaynard97"]'), github.actor))) || github.event.pull_request.merged
    name: Deploy
    runs-on: ubuntu-24.04
    environment: prod

    steps:

    - name: Get current date
      id: date
      run: echo "::set-output name=date::$(date +'%Y-%m-%d_%H.%M')"

    - name: Checkout
      uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ACCESS_TOKEN: ${{ secrets.ACCESS_TOKEN }}
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
        DATE: ${{ steps.date.outputs.date }}
      run: |
        # Build a docker container and
        # push it to ECR so that it can
        # be deployed to ECS.
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -t $ECR_REGISTRY/$ECR_REPOSITORY:$DATE -t $ECR_REGISTRY/$ECR_REPOSITORY:latest --build-arg AWS_REGION=$AWS_REGION --build-arg ACCESS_TOKEN=$ACCESS_TOKEN .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$DATE
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
        echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"

    - name: Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: ${{ env.ECS_TASK_DEFINITION }}
        container-name: ${{ env.CONTAINER_NAME }}
        image: ${{ steps.build-image.outputs.image }}

    - name: Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: false
