import logging
import sys
import time

from block_stream import Agent

logging.basicConfig(stream=sys.stdout, level=logging.INFO)
logger = logging.getLogger()
app = Agent("SimpleProducer", endpoint="localhost")


def first_agent() -> None:
    x = 0
    first_channel = app.channel("first")

    while True:
        data: dict[str, object] = {"x": x, "2y": 2 * x}
        did_send = first_channel.put(data, key="x")

        if did_send:
            logger.info(f"Sent {data} to channel")

        else:
            logger.info("Queued up data but did not send")

        x += 1
        time.sleep(1)


if __name__ == "__main__":
    first_agent()
