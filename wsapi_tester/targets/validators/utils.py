import logging
import math
from collections.abc import Iterable
from typing import cast

import datagrabber
from scipy.optimize import fsolve  # type: ignore
from scipy.special import ndtr  # type: ignore

from wsapi_tester import batch

from .typings import MarkPriceResults, StrikeIVResults

_FIVE_MIN_IN_YEARS = 5 / 60 / 24 / 365  # equal to 1e-5 aprox
_PRICE_DECIMAL_PRECISION = 12
_MAX_ITERATIONS = 200
_PRECISION = 1.0e-5


def price_option(
    option_type: int,
    spot: float,
    fwd: float,
    expiry_in_years: float,
    domestic_rate: float,
    strike: float,
    implied_vol: float,
) -> float:
    """
    Calculate the option price for a list of strikes

    Following same format as: https://github.com/blockscholes/vixCalc/blob/main/api/calc_vix.py#L75

    :param option_type: option type. 1 for a Call and -1 for a Put
    :param spot: underlying spot price
    :param fwd: underlying forward price
    :param expiry_in_years: time to expiry (express in years)
    :param domestic_rate: domestic interest rate
    :param strike: list of strikes price to calculate from
    :param implied_vol: implied volatility associated with the strikes
    :return: option price
    """
    # For options close to expiry, we simply return the difference between the spot and strike; 0 if negative
    if expiry_in_years < _FIVE_MIN_IN_YEARS:
        if option_type == 1:
            price = spot - strike
        else:
            price = strike - spot
        return price if price > 0 else 0

    sub = implied_vol * math.sqrt(expiry_in_years)

    d_p = (
        math.log(fwd / strike) + ((implied_vol**2) / 2) * expiry_in_years
    ) / sub
    d_m = (
        math.log(fwd / strike) - ((implied_vol**2) / 2) * expiry_in_years
    ) / sub

    # Note: we start using scipy.special.ndtr instead of scipy.stats.norm.cdf for performance.
    # See discussion: https://github.com/scipy/scipy/issues/9855
    price = (
        option_type
        * math.exp(-domestic_rate * expiry_in_years)
        * (fwd * ndtr(option_type * d_p) - strike * ndtr(option_type * d_m))
    )

    return round(price, _PRICE_DECIMAL_PRECISION)


def get_asset(item: batch.Item) -> datagrabber.CatalogAssetType:
    if isinstance(item, batch.InterestRate):
        return "future"
    if isinstance(item, batch.StrikeIV | batch.ModelParams):
        return "option"
    if isinstance(item, batch.PerpPrice):
        return "perpetual"
    if isinstance(item, batch.SettlementPrice):
        return "spot"
    else:
        return item.asset


def log_results_summary(
    item: batch.Item, results: Iterable[StrikeIVResults | MarkPriceResults]
) -> None:
    if results:
        if isinstance(item, batch.MarkPriceOption | batch.MarkPriceFuture):
            feed_id = "mark.px"
            key = f"{item.asset}.{item.base_asset}"
            prefix = []
        elif isinstance(item, batch.StrikeIV):
            feed_id = "strike.iv"
            key = (
                f"{item.exchange}.{item.base_asset}.{item.model}.{item.strike}"
            )
            prefix = ["cap_iv", "market.iv"]
        else:
            raise RuntimeError("Not supported item for summary")

        # Sort results by instrument key name (easy to read)
        results = sorted(results, key=lambda x: x[0])

        log_entries = [
            f"{result[0]}{f' ({prefix[idx]})' if prefix else ''}: {r}"
            for result in results
            for idx, r in enumerate(result[1:])
            if r
        ]

        if log_entries:
            logging.warning(
                f"[{feed_id}] {key} results summary: \n"
                + "\n".join(log_entries)
            )


def _option_premium(
    phi: int, f: float, K: float, r_d: float, vol: float, t: float
) -> float:
    d_1 = (math.log(f / K) + (((vol**2) / 2) * t)) / (vol * (math.sqrt(t)))
    d_2 = (math.log(f / K) - (((vol**2) / 2) * t)) / (vol * (math.sqrt(t)))
    n_d_1 = ndtr(phi * d_1)
    n_d_2 = ndtr(phi * d_2)
    return cast(
        float, (phi * (math.exp(-r_d * t))) * ((f * n_d_1) - (K * n_d_2))
    )


def _equation_vol(
    vol: float,
    phi: int,
    f: float,
    K: float,
    r_d: float,
    t: float,
    market_price: float,
) -> float:
    return market_price - _option_premium(phi, f, K, r_d, vol, t)


# TODO: We need to move this share code to utils_calc to avoid duplications
def find_vol(
    phi: int,
    forward: float,
    strike: float,
    domestic_rate: float,
    expiry: float,
    market_px: float,
) -> float:
    x0_iv = 2.1 if expiry > (2 / 365) else 2.9
    solution = fsolve(
        _equation_vol,
        x0=x0_iv,
        args=(
            phi,
            forward,
            strike,
            domestic_rate,
            expiry,
            market_px,
        ),
    )

    return cast(float, solution[0])


def retrieve_near_strikes(
    fwd_px: float,
    strikes_set: dict[float, set[str]],
    exp: str,
    warning_msgs: list[str] | None = None,
) -> list[float]:
    """
    Aims to choose strikes which have more than 1 data point near the money. Chooses strikes with 3 or more exchanges if possible. If not enough, chooses strikes with 2 or more exchanges.
    If still not enough, chooses all strikes.
    """
    MIN_STRIKES = 15
    strikes_with_enough_exchanges = {
        strike: exchanges
        for strike, exchanges in strikes_set.items()
        if len(exchanges) > 2
    }
    if len(strikes_with_enough_exchanges) < MIN_STRIKES:
        if warning_msgs is not None:
            warning_msgs.append(
                f"Not enough strikes with 3 or more exchanges, adding strikes with 2 exchanges. {exp=}"
            )
        strikes_with_enough_exchanges.update(
            {
                strike: exchanges
                for strike, exchanges in strikes_set.items()
                if len(exchanges) == 2
            }
        )
    if len(strikes_with_enough_exchanges) < MIN_STRIKES:
        if warning_msgs is not None:
            warning_msgs.append(
                f"Not enough strikes with 2 or more exchanges, adding all strikes. {exp=}"
            )
        strikes_with_enough_exchanges.update(strikes_set)

    return sorted(
        strikes_with_enough_exchanges.keys(),
        key=lambda s: abs(s - fwd_px),
    )[
        : min(
            len(strikes_with_enough_exchanges),
            MIN_STRIKES,
        )
    ]
