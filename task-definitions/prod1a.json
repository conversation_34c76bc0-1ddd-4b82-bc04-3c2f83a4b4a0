{"containerDefinitions": [{"name": "feedIndexerStream-container", "image": "public.ecr.aws/docker/library/python:3.11", "cpu": 0, "essential": true, "environment": [{"name": "OUTPUT_QUEUE_NAME", "value": "oracle-feed-indexer.fifo"}, {"name": "OUTPUT_DEADLETTER_QUEUE_NAME", "value": "oracle-feed-indexer-deadletter.fifo"}, {"name": "LOG_LEVEL", "value": "INFO"}, {"name": "MIN_KINESIS_FETCH_DELAY_S", "value": "5"}, {"name": "FEED_TARGETS_SSM_PATH", "value": "/config/oracle/feed_targets"}, {"name": "RUN_ENVIRONMENT", "value": "prod"}, {"name": "METRIC_S", "value": "600"}, {"name": "FEED_INDEXER_MESSAGE_BATCH_SIZE", "value": "150"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/feedIndexerStream-containers-prod1a", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "feedIndexerStream-task-definition", "taskRoleArn": "arn:aws:iam::685767522279:role/feedIndexerStreamContainerRole", "executionRoleArn": "arn:aws:iam::685767522279:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "ARM64"}}