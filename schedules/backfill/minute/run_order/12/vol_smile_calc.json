[{"name": "volSmileCalc - V4 blockscholes-syn ARB SVI", "lambda_name": "_#VOL_SMILE_CALC_V4VOLSURFACE_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "volSmileCalc", "args": {"consistent_read": false, "exchanges": ["blockscholes-syn"], "currencies": ["ARB", "OP"], "models": ["SVI"], "types": ["params", "smile", "skew", "butterfly", "moneyness"], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}, "include_listed_expiries": true, "smooth": true, "tenor_mode": "standard"}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": "V4"}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}, "by_field": {"exclude": ["isodate", "to_smooth", "window_fully_marked_to_smooth", "from_lookback", "api_version"]}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}]