[{"name": "priceIndexCalc - DEFI/3", "lambda_name": "_#PRICE_INDEX_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "priceIndexCalc", "args": {"data_sets": [{"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "v3uniswap-ethereum", "base": "SOLVBTC-BBN", "quote": "SOLVBTC"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}, "debug": false, "consistent_read": false}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}]