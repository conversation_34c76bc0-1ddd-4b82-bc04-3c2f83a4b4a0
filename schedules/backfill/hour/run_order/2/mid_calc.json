[{"name": "midCalc - deribit", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["option"], "exchanges": ["deribit"], "currency_pairs": [{"base": "BTC", "quote": "BTC"}, {"base": "ETH", "quote": "ETH"}, {"base": "SOL", "quote": "USDC"}, {"base": "XRP", "quote": "USDC"}, {"base": "BNB", "quote": "USDC"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - bybit", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["option"], "exchanges": ["bybit"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}, {"base": "ETH", "quote": "USDT"}, {"base": "SOL", "quote": "USDT"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - v2lyra", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["option"], "exchanges": ["v2lyra"], "currency_pairs": [{"base": "BTC", "quote": "USDC"}, {"base": "ETH", "quote": "USDC"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - okx", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["option"], "exchanges": ["okx"], "currency_pairs": [{"base": "BTC", "quote": "BTC"}, {"base": "ETH", "quote": "ETH"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - eurex", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["option"], "exchanges": ["eurex"], "currency_pairs": [{"base": "BTC", "quote": "USD"}, {"base": "ETH", "quote": "USD"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "BNB", "quote": "USD"}, {"base": "XRP", "quote": "USD"}, {"base": "SUI", "quote": "USD"}, {"base": "DOGE", "quote": "USD"}, {"base": "LINK", "quote": "USD"}, {"base": "TON", "quote": "USD"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - kraken+bitstamp+bitfinex+cryptocom+coinbase", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["spot"], "exchanges": ["bitstamp", "kraken", "bitfinex", "cryptocom", "coinbase"], "currency_pairs": [{"base": "USDC", "quote": "USD"}, {"base": "USDC", "quote": "ZUSD"}, {"base": "USDT", "quote": "USD"}, {"base": "USDT", "quote": "ZUSD"}, {"base": "BTC", "quote": "USD"}, {"base": "BTC", "quote": "ZUSD"}, {"base": "ETH", "quote": "USD"}, {"base": "ETH", "quote": "ZUSD"}, {"base": "SNX", "quote": "USD"}, {"base": "SNX", "quote": "ZUSD"}, {"base": "SOL", "quote": "USD"}, {"base": "SOL", "quote": "ZUSD"}, {"base": "DOGE", "quote": "USD"}, {"base": "DOGE", "quote": "ZUSD"}, {"base": "XRP", "quote": "USD"}, {"base": "XRP", "quote": "ZUSD"}, {"base": "ARB", "quote": "USD"}, {"base": "ARB", "quote": "ZUSD"}, {"base": "SUI", "quote": "USD"}, {"base": "SUI", "quote": "ZUSD"}, {"base": "OP", "quote": "USD"}, {"base": "OP", "quote": "ZUSD"}, {"base": "TON", "quote": "USD"}, {"base": "TON", "quote": "ZUSD"}, {"base": "LINK", "quote": "USD"}, {"base": "LINK", "quote": "ZUSD"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - binance+okx spot", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["spot"], "exchanges": ["okx", "binance"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}, {"base": "ETH", "quote": "USDT"}, {"base": "DOGE", "quote": "USDT"}, {"base": "XRP", "quote": "USDT"}, {"base": "ARB", "quote": "USDT"}, {"base": "SUI", "quote": "USDT"}, {"base": "OP", "quote": "USDT"}, {"base": "TON", "quote": "USDT"}, {"base": "LINK", "quote": "USDT"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - deribit+bybit+okx+binance+eurex perpetuals & futures", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["perpetual"], "exchanges": ["bybit", "okx", "binance"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}, {"base": "ETH", "quote": "USDT"}]}, {"asset_types": ["perpetual"], "exchanges": ["deribit"], "currency_pairs": [{"base": "BTC", "quote": "USD"}, {"base": "ETH", "quote": "USD"}]}, {"asset_types": ["future"], "exchanges": ["deribit", "okx", "binance", "eurex"], "currency_pairs": [{"base": "BTC", "quote": "USD"}, {"base": "ETH", "quote": "USD"}]}, {"asset_types": ["future"], "exchanges": ["bybit"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}, {"base": "ETH", "quote": "USDT"}, {"base": "SOL", "quote": "USDT"}]}], "frequency": {"interval": "_#DATA_FREQ_INTERVAL_", "periods": "_#DATA_FREQ_PERIODS_"}, "date_range": {"bucket": {"frequency": "_#WINDOW_FREQ_", "reference": "_#DATE_BUCKET_REFERENCE_", "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}]