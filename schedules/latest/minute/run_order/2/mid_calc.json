[{"name": "midCalc - deribit BTC Option C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["deribit"], "currency_pairs": [{"base": "BTC", "quote": "BTC"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - deribit BTC Option P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["deribit"], "currency_pairs": [{"base": "BTC", "quote": "BTC"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - deribit ETH Option C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["deribit"], "currency_pairs": [{"base": "ETH", "quote": "ETH"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - deribit ETH Option P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["deribit"], "currency_pairs": [{"base": "ETH", "quote": "ETH"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - bybit BTC Option C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["bybit"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}], "instrument_suffixes": ["C-USDT"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - bybit BTC Option P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["bybit"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}], "instrument_suffixes": ["P-USDT"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - bybit ETH Option C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["bybit"], "currency_pairs": [{"base": "ETH", "quote": "USDT"}], "instrument_suffixes": ["C-USDT"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - bybit ETH Option P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["bybit"], "currency_pairs": [{"base": "ETH", "quote": "USDT"}], "instrument_suffixes": ["P-USDT"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - okx BTC Option C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["okx"], "currency_pairs": [{"base": "BTC", "quote": "BTC"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - okx BTC Option P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["okx"], "currency_pairs": [{"base": "BTC", "quote": "BTC"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - okx ETH Option C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["okx"], "currency_pairs": [{"base": "ETH", "quote": "ETH"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - okx ETH Option P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["okx"], "currency_pairs": [{"base": "ETH", "quote": "ETH"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - deribit ALT Option C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["deribit"], "currency_pairs": [{"base": "SOL", "quote": "USDC"}, {"base": "XRP", "quote": "USDC"}, {"base": "BNB", "quote": "USDC"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - deribit ALT Option P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["deribit"], "currency_pairs": [{"base": "SOL", "quote": "USDC"}, {"base": "XRP", "quote": "USDC"}, {"base": "BNB", "quote": "USDC"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - bybit ALT Option C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["bybit"], "currency_pairs": [{"base": "SOL", "quote": "USDT"}], "instrument_suffixes": ["C-USDT"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - bybit ALT Option P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["bybit"], "currency_pairs": [{"base": "SOL", "quote": "USDT"}], "instrument_suffixes": ["P-USDT"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - v2lyra BTC", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["v2lyra"], "currency_pairs": [{"base": "BTC", "quote": "USDC"}]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - v2lyra ETH", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["v2lyra"], "currency_pairs": [{"base": "ETH", "quote": "USDC"}]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - eurex BTC", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["eurex"], "currency_pairs": [{"base": "BTC", "quote": "USD"}]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - eurex ETH", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["eurex"], "currency_pairs": [{"base": "ETH", "quote": "USD"}]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall BNB C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "BNB", "quote": "USD"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall BNB P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "BNB", "quote": "USD"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall XRP C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "XRP", "quote": "USD"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall XRP P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "XRP", "quote": "USD"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall SUI C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "SUI", "quote": "USD"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall SUI P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "SUI", "quote": "USD"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall DOGE C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "DOGE", "quote": "USD"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall DOGE P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "DOGE", "quote": "USD"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall TON C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "TON", "quote": "USD"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall TON P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "TON", "quote": "USD"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall LINK C", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "LINK", "quote": "USD"}], "instrument_suffixes": ["C"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - coincall LINK P", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": true, "data_sets": [{"asset_types": ["option"], "exchanges": ["coincall"], "currency_pairs": [{"base": "LINK", "quote": "USD"}], "instrument_suffixes": ["P"]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - kraken+bitstamp+bitfinex+cryptocom+coinbase", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["spot"], "exchanges": ["bitstamp", "kraken", "bitfinex", "cryptocom", "coinbase"], "currency_pairs": [{"base": "USDC", "quote": "USD"}, {"base": "USDC", "quote": "ZUSD"}, {"base": "USDT", "quote": "USD"}, {"base": "USDT", "quote": "ZUSD"}, {"base": "BTC", "quote": "USD"}, {"base": "BTC", "quote": "ZUSD"}, {"base": "BTC", "quote": "USDT"}, {"base": "ETH", "quote": "USD"}, {"base": "XETH", "quote": "ZUSD"}, {"base": "XETH", "quote": "USDT"}]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - kraken+bitstamp+bitfinex+cryptocom+coinbase ALTS", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["spot"], "exchanges": ["bitstamp", "kraken", "cryptocom", "coinbase"], "currency_pairs": [{"base": "SNX", "quote": "USD"}, {"base": "SNX", "quote": "ZUSD"}, {"base": "SOL", "quote": "USD"}, {"base": "SOL", "quote": "ZUSD"}, {"base": "DOGE", "quote": "USD"}, {"base": "DOGE", "quote": "ZUSD"}, {"base": "XRP", "quote": "USD"}, {"base": "XRP", "quote": "ZUSD"}, {"base": "ARB", "quote": "USD"}, {"base": "ARB", "quote": "ZUSD"}, {"base": "SUI", "quote": "USD"}, {"base": "SUI", "quote": "ZUSD"}, {"base": "OP", "quote": "USD"}, {"base": "OP", "quote": "ZUSD"}, {"base": "TON", "quote": "USD"}, {"base": "TON", "quote": "ZUSD"}, {"base": "LINK", "quote": "USD"}, {"base": "LINK", "quote": "ZUSD"}]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - okx+binance+gateio+bybit - spot ALTS", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["spot"], "exchanges": ["binance", "okx", "gateio"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}, {"base": "ETH", "quote": "USDT"}, {"base": "DOGE", "quote": "USDT"}, {"base": "XRP", "quote": "USDT"}, {"base": "ARB", "quote": "USDT"}, {"base": "BNB", "quote": "USDT"}, {"base": "SOL", "quote": "USDT"}, {"base": "SUI", "quote": "USDT"}, {"base": "OP", "quote": "USDT"}, {"base": "TON", "quote": "USDT"}, {"base": "LINK", "quote": "USDT"}]}, {"asset_types": ["spot"], "exchanges": ["bybit"], "currency_pairs": [{"base": "BNB", "quote": "USDT"}]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}, {"name": "midCalc - deribit+bybit+okx+binance+eurex perpetuals & futures", "lambda_name": "_#MID_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "midCalc", "args": {"consistent_read": false, "data_sets": [{"asset_types": ["perpetual"], "exchanges": ["bybit", "okx", "binance"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}, {"base": "ETH", "quote": "USDT"}]}, {"asset_types": ["perpetual"], "exchanges": ["deribit"], "currency_pairs": [{"base": "BTC", "quote": "USD"}, {"base": "ETH", "quote": "USD"}]}, {"asset_types": ["future"], "exchanges": ["deribit", "okx", "binance", "eurex"], "currency_pairs": [{"base": "BTC", "quote": "USD"}, {"base": "ETH", "quote": "USD"}]}, {"asset_types": ["future"], "exchanges": ["bybit"], "currency_pairs": [{"base": "BTC", "quote": "USDT"}, {"base": "ETH", "quote": "USDT"}, {"base": "SOL", "quote": "USDT"}]}], "frequency": {"interval": "minute", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "minute", "periods": 1}, "left_offset": 0, "right_offset": 0}}}, "output_options": {"type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"replace": {"field_names": {"mid": "px", "raw_mid": "raw_px"}}, "filter": {"by_row": {"exclude": {"field_to_values": {"from_lookback": ["True"]}}}}, "split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}, {"filter": {"by_field": {"include": ["timestamp", "px", "raw_px", "ffilled", "smoothed"]}}}]]}}]