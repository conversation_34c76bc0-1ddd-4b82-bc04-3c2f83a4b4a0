[{"name": "priceIndexCalc - spot DEFI/4", "lambda_name": "_#PRICE_INDEX_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "priceIndexCalc", "args": {"data_sets": [{"asset_class": "spot", "index_currency": "USD", "global_exchange_weights": [{"exchange": "v3uniswap-ethereum", "global_volume_weight": 0.4}, {"exchange": "kyberswap-ethereum", "global_volume_weight": 0.2}, {"exchange": "kyberswap-bsc", "global_volume_weight": 0.2}, {"exchange": "curve-ethereum", "global_volume_weight": 0.2}], "components": [{"exchange": "v3uniswap-ethereum", "base": "SOLVBTC-BBN", "quote": "SOLVBTC"}, {"exchange": "kyberswap-ethereum", "base": "SOLVBTC-BBN", "quote": "WBTC"}, {"exchange": "kyberswap-bsc", "base": "SOLVBTC-BBN", "quote": "BNB"}, {"exchange": "curve-ethereum", "base": "SOLVBTC-BBN", "quote": "USD"}]}], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "hour", "periods": 1}, "left_offset": 0, "right_offset": 0}}, "debug": false, "consistent_read": true}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}, {"name": "priceIndexCalc - spot DEFI/5", "lambda_name": "_#PRICE_INDEX_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "priceIndexCalc", "args": {"data_sets": [{"asset_class": "spot", "index_currency": "USD", "global_exchange_weights": [{"base": ["EGL1", "CA", "TAG", "BC"], "exchange": "kyberswap-bsc", "global_volume_weight": 0.47}, {"base": ["EGL1", "CA", "TAG", "BC"], "exchange": "zerox-bsc", "global_volume_weight": 0.47}, {"base": ["GASS", "OMALLEY", "ZEUS"], "exchange": "kyberswap-ethereum", "global_volume_weight": 0.5}, {"base": ["GASS", "OMALLEY", "ZEUS"], "exchange": "zerox-ethereum", "global_volume_weight": 0.5}, {"base": ["TIG", "AAA", "MAMO"], "exchange": "kyberswap-base", "global_volume_weight": 0.35}, {"base": ["TIG", "AAA", "MAMO"], "exchange": "zerox-base", "global_volume_weight": 0.35}, {"base": ["TIG", "MAMO"], "exchange": "v3uniswap-base", "global_volume_weight": 0.3}], "components": [{"exchange": "kyberswap-bsc", "base": "EGL1", "quote": "USD1"}, {"exchange": "zerox-bsc", "base": "EGL1", "quote": "USD1"}, {"exchange": "kyberswap-bsc", "base": "CA", "quote": "USD1"}, {"exchange": "zerox-bsc", "base": "CA", "quote": "USD1"}, {"exchange": "kyberswap-bsc", "base": "TAG", "quote": "USD1"}, {"exchange": "zerox-bsc", "base": "TAG", "quote": "USD1"}, {"exchange": "bitget", "base": "TAG", "quote": "USDT"}, {"exchange": "kyberswap-bsc", "base": "BC", "quote": "BNB"}, {"exchange": "zerox-bsc", "base": "BC", "quote": "BNB"}, {"exchange": "kyberswap-ethereum", "base": "GASS", "quote": "ETH"}, {"exchange": "zerox-ethereum", "base": "GASS", "quote": "ETH"}, {"exchange": "kyberswap-ethereum", "base": "OMALLEY", "quote": "ETH"}, {"exchange": "zerox-ethereum", "base": "OMALLEY", "quote": "ETH"}, {"exchange": "kyberswap-ethereum", "base": "ZEUS", "quote": "ETH"}, {"exchange": "zerox-ethereum", "base": "ZEUS", "quote": "ETH"}, {"exchange": "jupiter-solana", "base": "ZENAI", "quote": "SOL"}, {"exchange": "jupiter-solana", "base": "BALL", "quote": "SOL"}, {"exchange": "jupiter-solana", "base": "KLED", "quote": "SOL"}, {"exchange": "kyberswap-base", "base": "TIG", "quote": "USDC"}, {"exchange": "zerox-base", "base": "TIG", "quote": "USDC"}, {"exchange": "v3uniswap-base", "base": "TIG", "quote": "USDC"}, {"exchange": "kyberswap-base", "base": "AAA", "quote": "USDC"}, {"exchange": "zerox-base", "base": "AAA", "quote": "USDC"}, {"exchange": "kyberswap-base", "base": "MAMO", "quote": "CBBTC"}, {"exchange": "zerox-base", "base": "MAMO", "quote": "CBBTC"}, {"exchange": "v3uniswap-base", "base": "MAMO", "quote": "USDC"}]}], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "hour", "periods": 1}, "left_offset": 0, "right_offset": 0}}, "debug": false, "consistent_read": true}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}, {"name": "priceIndexCalc - spot Curve extra Weighted", "lambda_name": "_#PRICE_INDEX_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "priceIndexCalc", "args": {"data_sets": [{"asset_class": "spot", "index_currency": "USD", "global_exchange_weights": [{"exchange": "kyberswap-ethereum", "global_volume_weight": 0.4}, {"exchange": "curve-ethereum", "global_volume_weight": 0.2}, {"exchange": "v3uniswap-ethereum", "global_volume_weight": 0.2}, {"exchange": "zerox-ethereum", "global_volume_weight": 0.2}], "components": [{"exchange": "kyberswap-ethereum", "base": "EBTC", "quote": "WBTC"}, {"exchange": "curve-ethereum", "base": "EBTC", "quote": "USD"}, {"exchange": "v3uniswap-ethereum", "base": "EBTC", "quote": "LBTC"}, {"exchange": "zerox-ethereum", "base": "EBTC", "quote": "WBTC"}]}], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "hour", "periods": 1}, "left_offset": 0, "right_offset": 0}}, "debug": false, "consistent_read": true}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}]