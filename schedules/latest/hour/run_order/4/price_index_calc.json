[{"name": "priceIndexCalc - spot non USD quoted", "lambda_name": "_#PRICE_INDEX_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "priceIndexCalc", "args": {"data_sets": [{"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "BTC", "quote": "USD"}, {"exchange": "cryptocom", "base": "BTC", "quote": "USD"}, {"exchange": "bitstamp", "base": "BTC", "quote": "USD"}, {"exchange": "kraken", "base": "BTC", "quote": "ZUSD"}, {"exchange": "binance", "base": "BTC", "quote": "USDT"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "ETH", "quote": "USD"}, {"exchange": "cryptocom", "base": "ETH", "quote": "USD"}, {"exchange": "bitstamp", "base": "ETH", "quote": "USD"}, {"exchange": "kraken", "base": "XETH", "quote": "ZUSD"}, {"exchange": "binance", "base": "ETH", "quote": "USDT"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "DOGE", "quote": "USD"}, {"exchange": "cryptocom", "base": "DOGE", "quote": "USD"}, {"exchange": "bitstamp", "base": "DOGE", "quote": "USD"}, {"exchange": "binance", "base": "DOGE", "quote": "USDT"}, {"exchange": "okx", "base": "DOGE", "quote": "USDT"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "XRP", "quote": "USD"}, {"exchange": "cryptocom", "base": "XRP", "quote": "USD"}, {"exchange": "bitstamp", "base": "XRP", "quote": "USD"}, {"exchange": "kraken", "base": "XRP", "quote": "ZUSD"}, {"exchange": "binance", "base": "XRP", "quote": "USDT"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "ARB", "quote": "USD"}, {"exchange": "cryptocom", "base": "ARB", "quote": "USD"}, {"exchange": "okx", "base": "ARB", "quote": "USDT"}, {"exchange": "binance", "base": "ARB", "quote": "USDT"}, {"exchange": "kraken", "base": "ARB", "quote": "ZUSD"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "SUI", "quote": "USD"}, {"exchange": "cryptocom", "base": "SUI", "quote": "USD"}, {"exchange": "okx", "base": "SUI", "quote": "USDT"}, {"exchange": "binance", "base": "SUI", "quote": "USDT"}, {"exchange": "kraken", "base": "SUI", "quote": "ZUSD"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "LINK", "quote": "USD"}, {"exchange": "cryptocom", "base": "LINK", "quote": "USD"}, {"exchange": "okx", "base": "LINK", "quote": "USDT"}, {"exchange": "binance", "base": "LINK", "quote": "USDT"}, {"exchange": "kraken", "base": "LINK", "quote": "ZUSD"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "binance", "base": "BNB", "quote": "USDT"}, {"exchange": "okx", "base": "BNB", "quote": "USDT"}, {"exchange": "bybit", "base": "BNB", "quote": "USDT"}, {"exchange": "gateio", "base": "BNB", "quote": "USDT"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "OP", "quote": "USD"}, {"exchange": "cryptocom", "base": "OP", "quote": "USD"}, {"exchange": "okx", "base": "OP", "quote": "USDT"}, {"exchange": "binance", "base": "OP", "quote": "USDT"}, {"exchange": "kraken", "base": "OP", "quote": "ZUSD"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "CRV", "quote": "USD"}, {"exchange": "cryptocom", "base": "CRV", "quote": "USD"}, {"exchange": "okx", "base": "CRV", "quote": "USDT"}, {"exchange": "binance", "base": "CRV", "quote": "USDT"}, {"exchange": "kraken", "base": "CRV", "quote": "ZUSD"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "TON", "quote": "USD"}, {"exchange": "cryptocom", "base": "TON", "quote": "USD"}, {"exchange": "okx", "base": "TON", "quote": "USDT"}, {"exchange": "binance", "base": "TON", "quote": "USDT"}, {"exchange": "kraken", "base": "TON", "quote": "ZUSD"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "ATOM", "quote": "USD"}, {"exchange": "cryptocom", "base": "ATOM", "quote": "USD"}, {"exchange": "okx", "base": "ATOM", "quote": "USDT"}, {"exchange": "binance", "base": "ATOM", "quote": "USDT"}, {"exchange": "kraken", "base": "ATOM", "quote": "ZUSD"}]}, {"asset_class": "spot", "index_currency": "USD", "components": [{"exchange": "coinbase", "base": "ADA", "quote": "USD"}, {"exchange": "cryptocom", "base": "ADA", "quote": "USD"}, {"exchange": "okx", "base": "ADA", "quote": "USDT"}, {"exchange": "binance", "base": "ADA", "quote": "USDT"}, {"exchange": "kraken", "base": "ADA", "quote": "ZUSD"}]}], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "hour", "periods": 1}, "left_offset": 0, "right_offset": 0}}, "debug": false, "consistent_read": false}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}, {"name": "priceIndexCalc - perpetuals", "lambda_name": "_#PRICE_INDEX_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "priceIndexCalc", "args": {"data_sets": [{"asset_class": "perpetual", "index_currency": "USD", "components": [{"exchange": "binance", "base": "BTC", "quote": "USDT"}, {"exchange": "bybit", "base": "BTC", "quote": "USDT"}, {"exchange": "deribit", "base": "BTC", "quote": "USD"}, {"exchange": "okx", "base": "BTC", "quote": "USDT"}]}, {"asset_class": "perpetual", "index_currency": "USD", "components": [{"exchange": "binance", "base": "ETH", "quote": "USDT"}, {"exchange": "bybit", "base": "ETH", "quote": "USDT"}, {"exchange": "deribit", "base": "ETH", "quote": "USD"}, {"exchange": "okx", "base": "ETH", "quote": "USDT"}]}], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "hour", "periods": 1}, "left_offset": 0, "right_offset": 0}}, "debug": false, "consistent_read": false}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}, {"name": "priceIndexCalc - futures", "lambda_name": "_#PRICE_INDEX_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "priceIndexCalc", "args": {"data_sets": [{"asset_class": "future", "index_currency": "USD", "components": [{"exchange": "binance", "base": "BTC", "quote": "USD"}, {"exchange": "bybit", "base": "BTC", "quote": "USDT"}, {"exchange": "deribit", "base": "BTC", "quote": "USD"}, {"exchange": "okx", "base": "BTC", "quote": "USD"}]}, {"asset_class": "future", "index_currency": "USD", "components": [{"exchange": "binance", "base": "ETH", "quote": "USD"}, {"exchange": "bybit", "base": "ETH", "quote": "USDT"}, {"exchange": "deribit", "base": "ETH", "quote": "USD"}, {"exchange": "okx", "base": "ETH", "quote": "USD"}]}], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "hour", "periods": 1}, "left_offset": 0, "right_offset": 0}}, "debug": false, "consistent_read": false}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": ""}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}, {"name": "priceIndexCalc - spot DEFI/1", "lambda_name": "_#PRICE_INDEX_CALC_", "active": true, "arguments": {"requestContext": {"stage": "dev"}, "body": {"calc": {"version": "1.0.0", "type": "priceIndexCalc", "args": {"data_sets": [{"asset_class": "spot", "index_currency": "USD", "global_exchange_weights": [{"exchange": "v3uniswap-ethereum", "global_volume_weight": 0.3}, {"exchange": "kyberswap-ethereum", "global_volume_weight": 0.3}, {"exchange": "curve-ethereum", "global_volume_weight": 0.4}], "components": [{"exchange": "v3uniswap-ethereum", "base": "SUSDE", "quote": "USDT"}, {"exchange": "kyberswap-ethereum", "base": "SUSDE", "quote": "USDT"}, {"exchange": "curve-ethereum", "base": "SUSDE", "quote": "USD"}]}, {"asset_class": "spot", "index_currency": "USD", "global_exchange_weights": [{"exchange": "v3uniswap-ethereum", "global_volume_weight": 0.4}, {"exchange": "kyberswap-ethereum", "global_volume_weight": 0.2}, {"exchange": "curve-ethereum", "global_volume_weight": 0.2}, {"exchange": "zerox-ethereum", "global_volume_weight": 0.2}], "components": [{"exchange": "v3uniswap-ethereum", "base": "USDE", "quote": "USDT"}, {"exchange": "kyberswap-ethereum", "base": "USDE", "quote": "USDT"}, {"exchange": "zerox-ethereum", "base": "USDE", "quote": "USDT"}, {"exchange": "curve-ethereum", "base": "USDE", "quote": "USD"}]}, {"asset_class": "spot", "index_currency": "USD", "global_exchange_weights": [{"exchange": "v3uniswap-ethereum", "global_volume_weight": 0.3}, {"exchange": "kyberswap-ethereum", "global_volume_weight": 0.25}, {"exchange": "curve-ethereum", "global_volume_weight": 0.2}, {"exchange": "zerox-ethereum", "global_volume_weight": 0.25}], "components": [{"exchange": "v3uniswap-ethereum", "base": "PYUSD", "quote": "USDC"}, {"exchange": "kyberswap-ethereum", "base": "PYUSD", "quote": "USDT"}, {"exchange": "zerox-ethereum", "base": "PYUSD", "quote": "USDT"}, {"exchange": "curve-ethereum", "base": "PYUSD", "quote": "USD"}, {"exchange": "v3uniswap-ethereum", "base": "DEUSD", "quote": "USDC"}, {"exchange": "kyberswap-ethereum", "base": "DEUSD", "quote": "USDT"}, {"exchange": "zerox-ethereum", "base": "DEUSD", "quote": "USDT"}, {"exchange": "curve-ethereum", "base": "DEUSD", "quote": "USD"}]}], "frequency": {"interval": "hour", "periods": 1}, "date_range": {"bucket": {"frequency": {"interval": "hour", "periods": 1}, "left_offset": 0, "right_offset": 0}}, "debug": false, "consistent_read": false}, "output_options": {"version": "", "type": "csv", "format": "timeseries", "do_store": {"s3_bucket": "_#S3_BUCKET_", "s3_object_suffix": "", "s3_object_prefix": "defi"}}}}}, "sqs_message": {"source": {"s3": ""}, "destination": {"dynamodb": {"table": "", "partition_key_name": "qualified_name", "partition_key_val": "#QN"}}, "action_sets": [[{"split": {"by_group": {"on": {"field": {"name": "qualified_name"}}, "tag": "#QN"}}}]]}}]