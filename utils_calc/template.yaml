AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  Block Scholes Calc Python utilities

Resources:
  BsPythonUtilsCalc:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: bs_python_utils_calc
      Description: bs_python_utils_calc layer
      ContentUri: ./
      CompatibleRuntimes:
        - python3.11
    Metadata:
      BuildMethod: makefile

Outputs:
  BsPythonUtilsCalc:
    Description: "BlockScholes Calc Python utilities layer ARN"
    Value: !Ref BsPythonUtilsCalc
    Export:
      Name: BsPythonUtilsCalc
