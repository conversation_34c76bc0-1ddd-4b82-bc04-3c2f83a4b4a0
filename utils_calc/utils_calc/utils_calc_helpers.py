import logging
import math
import warnings
from statistics import NormalDist
from typing import (
    cast,
    get_type_hints,
    Literal,
    overload,
    Callable,
    Tuple,
)

import numpy as np
import pandas as pd
from scipy import interpolate  # type: ignore
from scipy.optimize import fsolve, newton  # type: ignore
from scipy.stats import norm  # type: ignore

from utils_calc.spline import spline_model_vol
from utils_calc.utils_calc_constants import (
    BSDEBUG,
    CALIBRATION_DELTA_BOUNDARY_FOR_LONG_EXPIRY,
    CALIBRATION_DELTA_BOUNDARY_FOR_SHORT_EXPIRY,
    DEFAULT_SURFACE_DELTAS,
    INTERPOLATED_EXPIRY_CALENDAR_ARB_THRESHOLD,
    MONEYNESS_STR,
    SHORT_EXPIRY_CUT_OFF,
)
from utils_calc.utils_calc_sabr import sabr_vol
from utils_calc.utils_calc_svi import svi_vol
from utils_calc.utils_calc_types import (
    <PERSON>ubicSpline,
    CubicSplineVectorized,
    Model,
    ModelParamsBase,
    ModelParamsTypes,
    ModelParamsVectorized,
    NDArrayFloat64,
    SabrParams,
    SabrParamsVectorized,
    SviCalibrationReferenceParams,
    SviParams,
    SviParamsVectorized,
    InvalidVolatilityError,
)


def spline_fit(
    df_vol_matrix: pd.DataFrame,
    tenor_completed: list[float],
    tenor_in_ex_right: list[float],
    domain_cols: list[str] = MONEYNESS_STR,
) -> pd.DataFrame:
    """
    This function is used to extrapolate vols in a vol matrix using a cubic
    spline technique. Separate splines are fitted on:
    - the ATM IVs
    - the ratio of vols at different moneyness/strike/delta levels to the ATM

    The splines are then used to extrapolate the ATM and ratio values, and the
    two are multiplied to get a final vol matrix.
    """
    ############Temp BLock Spline #####################
    ############ ATM #########################
    train_x = sorted(
        df_vol_matrix[df_vol_matrix.index.isin(tenor_completed)].index.tolist()
    )
    index_ref = (np.abs(np.asarray(train_x) - 30)).argmin()
    min_required = 6
    if np.abs(index_ref - len(train_x) - 1) < min_required:
        index_ref = max(0, len(train_x) - min_required)
    train_x = train_x[index_ref:]
    ########## ATM Extrapolation Terminal Value ######################
    train_y = df_vol_matrix[df_vol_matrix.index.isin(tenor_completed)]["1"].tolist()
    train_y = train_y[index_ref:]
    train_x.append(10 * 365)
    # Set terminal point to guide extrapolation of atm to converge smoothly
    # towards the same value as present on the furthest expiry. This is to prevent
    # a blowup in vol at very long dated tenors, keeping it more stable. It also
    # aligns with an expectation that vol reverts to some mean over long periods.
    train_y.append(train_y[-1])
    #######

    deri_x_y = []
    for x0, x1, y0, y1 in zip(train_x, train_x[1:], train_y, train_y[1:]):
        deri_x_y.append((y1 - y0) / (x1 - x0))

    deri_x_y.append(0)
    func_spline = interpolate.CubicHermiteSpline(train_x, train_y, deri_x_y)
    test_y_right = func_spline(tenor_in_ex_right)
    df_vol_matrix.loc[tenor_in_ex_right, "1"] = test_y_right
    ###############################################
    ###### ATM Values and Ratio DataFrame#########
    atm_val = df_vol_matrix["1"].values
    df_vol_matrix_ratio = df_vol_matrix.div(atm_val, axis=0)  # type: ignore
    ############################################
    #### Ratio Decay Spline #############
    for col in domain_cols:
        train_y = df_vol_matrix_ratio[df_vol_matrix_ratio.index.isin(tenor_completed)][
            col
        ].tolist()
        train_y = train_y[index_ref:]
        # Set terminal point to guide extrapolation of ratio to converge
        # smoothly towards 1 as tenor increases. This is because the ratio
        # is expected to converge to this over long expiry time periods.
        train_y.append(1.00001)
        deri_x_y = []
        for x0, x1, y0, y1 in zip(train_x, train_x[1:], train_y, train_y[1:]):
            deri_x_y.append((y1 - y0) / (x1 - x0))
        deri_x_y.append(0)
        func_spline = interpolate.CubicHermiteSpline(train_x, train_y, deri_x_y)
        test_y_right = func_spline(tenor_in_ex_right)
        df_vol_matrix_ratio.loc[tenor_in_ex_right, col] = test_y_right
    ##################################################
    df_vol_matrix = df_vol_matrix_ratio.mul(atm_val, axis=0)  # type: ignore
    ####################################################
    return df_vol_matrix


def extrapolate_left(df_vol_matrix: pd.DataFrame) -> pd.DataFrame:
    df_vol_matrix.replace(to_replace=0, method="bfill", inplace=True)
    return df_vol_matrix


def _has_duplicates(input_list: list[int]) -> bool:
    return len(input_list) != len(set(input_list))


def _indices_have_gaps(input_list: list[int]) -> bool:

    if len(input_list) <= 1:
        return False

    sorted_indices = sorted(input_list)
    return bool(np.any(np.diff(sorted_indices) >= 1))


def _validate_indices(input_list: list[int]):
    """Validate indices passed in for smoothing on dataframes"""
    if _has_duplicates(input_list):
        warnings.warn(
            "Duplicate indices detected in the input list",
            UserWarning,
            stacklevel=2,
        )
        input_list = list(set(input_list))

    if _indices_have_gaps(input_list):
        warnings.warn(
            "Non-consecutive indices detected. This may lead to unexpected smoothing behavior "
            "as the calculation will not represent a proper sequential window.",
            UserWarning,
            stacklevel=2,
        )


def smooth_ema(
    df: pd.DataFrame,
    idx_to_smooth: int | list[int],
    fields_to_replace: list[str],
    span: int,
    adjust: bool = True,
) -> pd.DataFrame:
    """
    Smooths a DataFrame using an Exponential Moving Average (EMA) on a specific row
    Warning: Also mutates input DataFrame.
    Raises if empty / missing columns

    :param df: DataFrame to smooth
    :param idx_to_smooth: Index or indicies of row to smooth. When passing a list of indices,
                         they should be consecutive for meaningful results. Non-consecutive
                         indices may lead to unexpected smoothing behavior where the SMA
                         calculation uses data points that don't represent the intended
                         sequential window, potentially creating misleading smoothed values.
    :param fields_to_replace: List of fields to smooth
    :param span: Span of EMA
    :param adjust: Changes the calculation of the EMW function. Given an infinite number of datapoints,
        both should behave similarly see https://pandas.pydata.org/docs/user_guide/window.html#window-exponentially-weighted
    :return: Complete DataFrame with select row/fields mutated
    """

    if _indices_have_gaps(idx_to_smooth):
        warnings.warn(
            "Non-consecutive indices detected. This may lead to unexpected smoothing behavior "
            "as the SMA calculation may not represent a proper sequential window.",
            UserWarning,
            stacklevel=2,
        )

    df.loc[idx_to_smooth, fields_to_replace] = (
        df[fields_to_replace].ewm(span=span, adjust=adjust).mean().loc[idx_to_smooth]
    )
    return df


def smooth_sma(
    df: pd.DataFrame,
    idx_to_smooth: int | list[int],
    fields_to_replace: list[str],
    window: int,
) -> pd.DataFrame:
    """
    Smooths a DataFrame using a Simple Moving Average (SMA) on a specific row.
    Warning: Also mutates input DataFrame.
    Raises if empty / missing columns

    :param df: DataFrame to smooth
    :param idx_to_smooth: Index or indicies of row to smooth. When passing a list of indices,
                         they should be consecutive for meaningful results. Non-consecutive
                         indices may lead to unexpected smoothing behavior where the SMA
                         calculation uses data points that don't represent the intended
                         sequential window, potentially creating misleading smoothed values.
    :param fields_to_replace: List of fields to smooth
    :param window: Window of SMA
    :return: Complete DataFrame with select row/fields mutated
    """

    if _indices_have_gaps(idx_to_smooth):
        warnings.warn(
            "Non-consecutive indices detected. This may lead to unexpected smoothing behavior "
            "as the SMA calculation may not represent a proper sequential window.",
            UserWarning,
            stacklevel=2,
        )

    df.loc[idx_to_smooth, fields_to_replace] = (
        df[fields_to_replace].rolling(window=window).mean().loc[idx_to_smooth]
    )
    return df


def zscore_sma(df: pd.DataFrame, window: int) -> pd.DataFrame:
    """
    Computes the rolling z-score of a DF using a simple moving average (SMA)

    :param df: DataFrame to compute z-score for
    :param window: window size for SMA
    :return: DataFrame of z-scores
    """

    r = df.rolling(window=window)
    m = r.mean().shift(1)
    s = r.std(ddof=0).shift(1)
    s.replace(to_replace=[0], value=float("inf"), inplace=True)
    return ((df - m) / s).replace([np.inf, -np.inf], 0)


def zscore_ema(df: pd.DataFrame, span: int) -> pd.DataFrame:
    """
    Computes the z-score of a DF using an exponential moving average (EMA)

    :param df: DataFrame to compute z-score for
    :param span: span size for EMA
    :return: DataFrame of z-scores
    """

    emw = df.ewm(span=span)
    m = emw.mean().shift(1)
    s = emw.std().shift(1)
    s.replace(to_replace=[0], value=float("inf"), inplace=True)
    return ((df - m) / s).replace([np.inf, -np.inf], 0)


def num_observations_for_weight(span: int, threshold: float = 0.95) -> int:
    """
    Computes the number of observations that contribute to the EMA given a span parameter
    when adjust=False

    :param span: span size for EMA
    :param threshold: target percentage of the EMA's cumulative weight to be achieved by the observations
    :return: number of observations
    """

    # Calculate alpha from span
    alpha = 2 / (span + 1)

    # Initialize total weight and number of observations
    total_weight = 0.0
    num_observations = 0

    # Standard EMA formula
    while total_weight < threshold:
        weight = (1 - alpha) ** num_observations * alpha
        total_weight += weight
        num_observations += 1

    return num_observations


def ema_params_for_desired_observations(
    num_observations: int, threshold: float = 0.95
) -> tuple[float, float]:
    """
    Computes the optimal span parameter such that a preceding 'num_observations'
    rows contribute a 'threshold' weight to the EMA when adjust=False

    :param num_observations: desired number of observations for the threshold weight
    :param threshold: target percentage of the EMA's cumulative weight to be achieved by the observations
    :return: Desired span parameter
    """

    def cumulative_weight(span: float, threshold: float) -> float:
        # Calculate alpha from span
        alpha = 2 / (span + 1)

        # Standard EMA formula
        weight = 0.0
        for i in range(num_observations):
            weight += alpha * (1 - alpha) ** i
        return weight - threshold

    # Use Newton's method to find the span that gives the desired cumulative weight
    # Start with an initial guess for the span
    initial_guess = num_observations
    optimal_span = newton(cumulative_weight, initial_guess, args=(threshold,))
    optimal_alpha = 2 / (optimal_span + 1)
    return optimal_span, optimal_alpha


def strike_from_delta_vol(
    s: float, f: float, r_d: float, t: float, vol: float, delta: float
) -> float:
    """
    Estimates the strike of an option at a specific delta.

    :param s: Spot price
    :param f: Forward price
    :param r_d: Domestic interest rate
    :param t: Time to expiry in years
    :param vol: Implied volatility of the option
    :param delta: Delta who's strike we want to estimate
    :return: Returns the strike of the option
    """

    phi = 1 if delta >= 0 else -1
    r_f = r_d - (1 / t) * math.log(f / s)
    norm_inv_term = phi * delta * math.exp(r_f * t)
    term_1 = -phi * NormalDist().inv_cdf(norm_inv_term) * vol * math.sqrt(t)
    theta_p = ((r_d - r_f) / vol) + (vol / 2)
    term_2 = vol * theta_p * t
    strike = s * math.exp(term_1 + term_2)
    return strike


def option_delta(
    s: float,
    f: float,
    K: float,
    t: float,
    r_d: float,
    vol: float,
    phi: Literal[-1, 1],
) -> float:
    """
    Calculates the option delta using the Garman-Kohlhagen model.

    :param s : Spot price of the underlying asset
    :param f : Forward price of the underlying asset
    :param K : Strike price of the option
    :param t : Time to expiration (in years)
    :param r_d : The discount rate
    :param vol : Volatility of the underlying asset's returns
    :param phi : Parameter representing whether the option is a call (phi = 1) or a put (phi = -1)
    """

    # Ensure time and volatility are positive
    if s <= 0 or f <= 0 or t <= 0 or vol <= 0:
        raise ValueError(
            f"Spot Price, Forward Price, Time to maturity and volatility must be positive. {s=:}, {f=:} {t=:}, {vol=:}"
        )

    K = abs(K)
    d_p = (math.log(f / K) + ((vol**2) / 2) * t) / (vol * math.sqrt(t))
    r_f = r_d - (1 / t) * math.log(f / s)
    delta = phi * math.exp(-r_f * t) * norm.cdf(phi * d_p)
    return delta


@overload
def model_vol(
    strike: NDArrayFloat64 | list[list[float]],
    forward: NDArrayFloat64,
    exp: NDArrayFloat64,
    model: Model,
    model_params: ModelParamsVectorized,
) -> NDArrayFloat64 | None: ...


@overload
def model_vol(
    strike: float | NDArrayFloat64 | list[float],
    forward: float,
    exp: float,
    model: Model,
    model_params: ModelParamsBase,
) -> float | list[float] | NDArrayFloat64 | None: ...


def model_vol(
    strike: float | NDArrayFloat64 | list[float] | list[list[float]],
    forward: float | NDArrayFloat64,
    exp: float | NDArrayFloat64,
    model: Model,
    model_params: ModelParamsTypes,
) -> float | list[float] | NDArrayFloat64 | None:
    vol = None

    if model == "SABR":
        if isinstance(forward, float):
            model_params = cast(SabrParams, model_params)
        else:
            model_params = cast(SabrParamsVectorized, model_params)

        vol = sabr_vol(
            k=strike,  # type: ignore
            f=forward,
            t=exp,
            alpha=model_params["sabr_alpha"],
            beta=1,
            rho=model_params["sabr_rho"],
            volvol=model_params["sabr_volvol"],
        )

    elif model == "SVI":
        if isinstance(forward, float):
            model_params = cast(SviParams, model_params)
        else:
            model_params = cast(SviParamsVectorized, model_params)

        vol = svi_vol(  # type: ignore
            a=model_params["svi_a"],
            b=model_params["svi_b"],
            rho=model_params["svi_rho"],
            m=model_params["svi_m"],
            sigma=model_params["svi_sigma"],
            f=forward,
            exp=exp,
            k=strike,
        )

    elif model == "spline":
        if isinstance(forward, float):
            model_params = cast(CubicSpline, model_params)
            if isinstance(strike, float):
                return spline_model_vol([strike], model_params)[0]
            else:
                strike_as_list = list(strike)
                return spline_model_vol(
                    strike_as_list, model_params
                )  # TODO: support np.array type in spline interface
        else:
            model_params = cast(CubicSplineVectorized, model_params)

            assert isinstance(strike, (np.ndarray, list))
            assert len(strike) == len(
                model_params
            ), f"Mismatch between the number of strikes sets and spline parameter sets, {len(strike)=}, {len(model_params)=}"

            vols = []
            for strikes_set, model_params_set in zip(strike, model_params):
                strike_as_list = list(strikes_set)
                vols.append(
                    np.array(spline_model_vol(strike_as_list, model_params_set))
                )
            return np.array(
                vols, dtype=object
            )  # TODO: support np.array type in spline interface
    else:
        raise NotImplementedError(f"Unrecognised Volatility Model, {model=}")

    return vol


def estimate_strike_from_delta(
    model: Model,
    model_params: ModelParamsBase,
    s: float,
    f: float,
    t: float,
    delta: float,
    rd: float,
    max_iteration: int = 500,
    str_tol=1e-9,
) -> float:
    """
    Estimates the strike price from the delta using iterative methods.

    :param model: The option pricing model ('SABR' or 'SVI')
    :param model_params: The parameters for the chosen model
    :param s: Spot price
    :param f: Forward Price
    :param t: Time to expiry in years
    :param delta: The delta of the option
    :param rd: The discount rate
    :param max_iteration: Number of times to try to estimate the strike price
        from an input delta using `strike_from_delta_vol`
    :param str_tol: The tol parameter represents a threshold such that optimization stops when the
        change in the objective function value between iterations is smaller than this threshold
    """

    try:
        if delta >= 0:
            start = float("inf")
            ini = 1.5 * f
        else:
            start = 0
            ini = 0.5 * f

        iteration = 0
        vol = None

        while abs(start - ini) > str_tol and iteration < max_iteration:
            iteration += 1
            start = ini
            vol = model_vol(
                strike=ini, forward=f, exp=t, model=model, model_params=model_params
            )
            ini = strike_from_delta_vol(
                s=s,
                f=f,
                r_d=rd,
                t=t,
                vol=cast(float, vol),
                delta=delta,
            )
            if np.isnan(ini) or np.isinf(ini):
                raise ValueError(
                    f"Invalid strike derived from delta. {ini=}, {delta=}, {vol=}, {model=}, {s=}, {f=}, {rd=}, {t=}"
                )

    except Exception as e:
        logging.log(
            BSDEBUG,
            f"Primary strike estimation failed, initiating fallback. {e=}",
        )
        if model == "spline":
            spline_params = cast(CubicSpline, model_params)

            def func(K: NDArrayFloat64 | float) -> float:
                return _eq_delta_strike(K, model, spline_params, s, f, t, rd, delta)

            lower_bound, upper_bound = _find_valid_bracket_spline(
                func, f, spline_params, delta
            )
            start_strike = 0.5 * (lower_bound + upper_bound)

        else:
            start_strike = (s / 2) if delta < 0 else 2 * s

        ini = fsolve(
            _eq_delta_strike,
            x0=start_strike,
            args=(
                model,
                model_params,
                s,
                f,
                t,
                rd,
                delta,
            ),
        )[0]

        if ini < 0:
            logging.error(
                f"Negative strike derived from delta using fsolve fallback, returning absolute strike. {ini=}, {delta=}, {model=}, {s=}, {f=}, {rd=}, {t=}"
            )
            ini = abs(ini)

    return ini


def _eq_delta_strike(
    K: NDArrayFloat64 | float,
    model: Model,
    model_params: ModelParamsBase,
    s: float,
    f: float,
    t: float,
    r_d: float,
    delta: float,
) -> float:
    """
    Calculates the equality of the delta strike given the parameters and option model.

    This function calculates the difference between input delta
    and the Delta implied by a strike price K calculated by the 'option_delta' function.
    This function is to be used in tandem with an optimisation function that iteratively
    tries to find a strike K such that eq==0, meaning that strike K equals 'delta'


    :param K: The strike price. Passed in as an array from fsolve in estimate_strike_from_delta
    :param model: The option pricing model ('SABR' or 'SVI')
    :param model_params: The parameters for the chosen model
    :param s: Spot price
    :param f: Forward Price
    :param t: Time to expiry in years
    :param r_d: The discount rate
    :param delta: The delta of the option
    """

    if isinstance(K, np.ndarray):
        assert len(K) == 1
        K = K.item()

    assert isinstance(K, float)

    if s <= 0 or f <= 0 or t <= 0:
        raise ValueError(
            f"Spot Price, Forward Price and Time to maturity must be positive. {s=:}, {f=:}, {t=:}"
        )

    K = abs(K)
    vol = model_vol(strike=K, forward=f, exp=t, model=model, model_params=model_params)

    ## Handle root finder failure when vol is NaN/Inf; fallback to default strikes if provided.
    vol = cast(float, vol)
    if np.isnan(vol) or np.isinf(vol):
        raise InvalidVolatilityError(
            f"Failed generating vol for strike {K=} {f=} {t=} {model=}"
        )

    phi = cast(Literal[1, -1], 1 if delta >= 0 else -1)
    eq = delta - option_delta(s, f, K, t, r_d, cast(float, vol), phi)
    return eq


def _find_valid_bracket_spline(
    func: Callable[[float], float],
    initial_guess: float,
    model_params: CubicSpline,
    delta: float,
    max_expansion: int = 60,
) -> Tuple[float, float]:
    """
    Dynamically finds a valid bracket [a, b] such that func(a) * func(b) < 0.
    Starts from initial_guess / scale and expands outward exponentially, in either
    direction depending on the sign of delta.

    For delta < 0 (put side), expansion proceeds downward (toward min_x),
    and for delta >= 0 (call side), expansion proceeds upward (toward max_x),
    using logarithmic scaling.

    max_expansion = 60 provides:
    • Sufficient granularity to resolve non-monotonic spline regions
    • Smooth exponential coverage from approximately 1× forward down to min_x (put side)
      and up to max_x (call side)
    • A good balance between computational speed and robustness for spline-based
      implied vol delta-to-strike inversion

    Note: The scan range is logarithmically spaced and may not reach
    min_x or max_x exactly unless max_expansion is large enough.
    """
    spline_info = model_params["spline_info"]

    if not isinstance(spline_info, list) or not all(
        isinstance(seg, dict) for seg in spline_info
    ):
        logging.warning(
            f"Expected list of dicts for spline model params, got: {type(spline_info)} with value: {spline_info}"
        )
        return initial_guess, initial_guess

    min_x = min(seg["x_right"] for seg in spline_info)
    max_x = max(seg["x_right"] for seg in spline_info)

    # Delta-aware: put deltas need downside, call deltas need upside
    if delta < 0:
        min_factor = initial_guess / min_x
        scan_factors = np.logspace(0, np.log10(min_factor), max_expansion)
        upper = initial_guess
        for factor in scan_factors:
            lower = max(initial_guess / factor, min_x)
            try:
                f_lower = func(lower)
                f_upper = func(upper)
                if (
                    np.isfinite(f_lower)
                    and np.isfinite(f_upper)
                    and f_lower * f_upper <= 0
                ):
                    return lower, upper
            except Exception:
                continue

    else:  # delta >= 0 → call side
        max_factor = max_x / initial_guess
        scan_factors = np.logspace(0, np.log10(max_factor), max_expansion)
        lower = initial_guess
        for factor in scan_factors:
            upper = min(initial_guess * factor, max_x)
            try:
                f_lower = func(lower)
                f_upper = func(upper)
                if (
                    np.isfinite(f_lower)
                    and np.isfinite(f_upper)
                    and f_lower * f_upper <= 0
                ):
                    return lower, upper
            except Exception:
                continue

    logging.warning(
        "Could not find a valid bracket for secondary strike estimation. Using forward as starting point"
    )

    return initial_guess, initial_guess


def get_calibration_delta_boundary(expiry: float) -> float:
    """
    :param expiry: The expiry of the option in years

    """
    if expiry > SHORT_EXPIRY_CUT_OFF:
        calibration_boundary = CALIBRATION_DELTA_BOUNDARY_FOR_LONG_EXPIRY
    elif expiry < 0.8 * SHORT_EXPIRY_CUT_OFF:
        calibration_boundary = CALIBRATION_DELTA_BOUNDARY_FOR_SHORT_EXPIRY
    else:
        calibration_boundary = CALIBRATION_DELTA_BOUNDARY_FOR_SHORT_EXPIRY + (
            expiry - 0.8 * SHORT_EXPIRY_CUT_OFF
        ) / (0.2 * SHORT_EXPIRY_CUT_OFF) * (
            CALIBRATION_DELTA_BOUNDARY_FOR_LONG_EXPIRY
            - CALIBRATION_DELTA_BOUNDARY_FOR_SHORT_EXPIRY
        )

    return calibration_boundary


def get_put_and_call_wing_delta_boundaries(expiry: float) -> dict[str, float]:
    calibration_boundary = get_calibration_delta_boundary(expiry)

    return {
        "put_wing_boundary": -1 * calibration_boundary,
        "call_wing_boundary": calibration_boundary,
    }  # *-1 for put delta


def get_atm_deltas_from_boundaries(
    put_wing_delta: float, call_wing_delta: float
) -> list[float]:
    """
    This function filters out deltas that lie outside the specified put_wing_delta and call_wing_delta deltas
    and returns a list of deltas that we want to recalibrate with. The deltas returned ar always deltas that are
    close to the atm.
    """

    relevant_deltas = [
        delta
        for delta in DEFAULT_SURFACE_DELTAS
        if delta <= put_wing_delta or delta >= call_wing_delta
    ]
    return relevant_deltas


def safe_div(
    a: pd.Series | np.ndarray | int | float,
    b: pd.Series | np.ndarray | int | float,
) -> np.ndarray | float:
    a_values = a if not isinstance(a, pd.Series) else cast(np.ndarray, a.values)
    b_values = b if not isinstance(b, pd.Series) else cast(np.ndarray, b.values)

    if isinstance(a_values, np.ndarray) or isinstance(b_values, np.ndarray):
        return np.asarray(np.where(b_values != 0, a_values / b_values, 0))
    else:
        return float(b_values and a_values / b_values or 0)


def get_bordering_expiries(
    snap_df: pd.DataFrame, target_expiry: float
) -> tuple[pd.Series | None, pd.Series | None]:
    lower_expiry_snap, upper_expiry_snap = None, None

    below_target_expiry = snap_df[snap_df["expiry"] < target_expiry]
    above_target_expiry = snap_df[snap_df["expiry"] > target_expiry]

    if not below_target_expiry.empty:
        idx_closest_left_exp = below_target_expiry["expiry"].idxmax()
        lower_expiry_snap = snap_df.loc[idx_closest_left_exp]

    if not above_target_expiry.empty:
        idx_closest_right_exp = above_target_expiry["expiry"].idxmin()
        upper_expiry_snap = snap_df.loc[idx_closest_right_exp]

    return lower_expiry_snap, upper_expiry_snap  # type: ignore


def get_closest_expiry_ref_params(
    params_calibrated: pd.DataFrame | list[SviParams],
    current_expiry: float,
    threshold: float = INTERPOLATED_EXPIRY_CALENDAR_ARB_THRESHOLD,
) -> SviCalibrationReferenceParams:
    """
    This function return parameters of the closest expiry which had been calibrated for current Timestamp.
    Threshold is for the Calendar Arb constraints to be added in optimizer

    :param params_calibrated: Tenors/Expiries calibrated till now successfully
    :param current_expiry: Current Expiry being calibrated
    """

    reference_parameters: SviCalibrationReferenceParams

    if isinstance(params_calibrated, pd.DataFrame):
        svi_keys = list(get_type_hints(SviParams))
        # strict because of type casting below
        assert set(params_calibrated.columns) == set(
            svi_keys
        ), f"There are extra columns in the DataFrame. Keys={set(params_calibrated.columns) - set(svi_keys)}"

        expiry_array = params_calibrated["expiry"].to_numpy()
    elif isinstance(params_calibrated, list):
        expiry_array = np.array([param["expiry"] for param in params_calibrated])
    else:
        raise NotImplementedError(f"{type(params_calibrated)} is not allowed")

    nearest_expiry_index = np.argmin(np.abs(expiry_array - current_expiry))

    if isinstance(params_calibrated, pd.DataFrame):
        reference_parameters = cast(
            SviCalibrationReferenceParams,
            dict(params_calibrated.iloc[nearest_expiry_index]),
        )
    elif isinstance(params_calibrated, list):
        reference_parameters = cast(
            SviCalibrationReferenceParams,
            params_calibrated[nearest_expiry_index],
        )

    reference_parameters["threshold"] = threshold
    return reference_parameters
