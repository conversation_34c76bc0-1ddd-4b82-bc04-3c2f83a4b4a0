import numpy as np
import pandas as pd
from sklearn.linear_model import Ridge  # type: ignore
import logging
from utils_calc.utils_calc_types import (
    CalibrationQuery,
    CubicSpline,
    CubicSplineSegment,
    SplineCalibrationHyperparameters,
)

from utils_calc.utils_calc.option.greeks import option_delta


def _evaluate_spline_segment(spline: CubicSplineSegment, x: float) -> float:
    """
    :param spline: The spline to evaluate at given point
    :param x: Real point to evaluate spline at
    """

    h = spline["x_right"] - spline["x_left"]
    x_normalized = (x - spline["x_left"]) / h

    square = x_normalized * x_normalized
    cube = square * x_normalized

    return (
        spline["constant_coef"]
        + spline["linear_coef"] * x_normalized
        + spline["square_coef"] * square
        + spline["cube_coef"] * cube
    )


def _add_segments(
    one: CubicSplineSegment,
    two: CubicSplineSegment,
    w_one: float = 1.0,
    w_two: float = 1.0,
) -> CubicSplineSegment:
    assert one["x_left"] == two["x_left"]
    assert one["x_right"] == two["x_right"]

    return CubicSplineSegment(
        x_left=one["x_left"],
        x_right=one["x_right"],
        constant_coef=w_one * one["constant_coef"] + w_two * two["constant_coef"],
        linear_coef=w_one * one["linear_coef"] + w_two * two["linear_coef"],
        square_coef=w_one * one["square_coef"] + w_two * two["square_coef"],
        cube_coef=w_one * one["cube_coef"] + w_two * two["cube_coef"],
    )


def _add_splines(
    one: CubicSpline, two: CubicSpline, w_one: float = 1.0, w_two: float = 1.0
) -> CubicSpline:
    assert len(one["spline_info"]) == len(two["spline_info"])

    spline_info = []
    for index in range(len(one["spline_info"])):
        spline_info.append(
            _add_segments(
                one["spline_info"][index],
                two["spline_info"][index],
                w_one,
                w_two,
            )
        )

    return CubicSpline(spline_info=spline_info)


def _evaluate_spline_vectorized(
    strikes: np.ndarray | list[float], params: CubicSpline
) -> list[float]:
    spline_info = np.array(params["spline_info"])
    right_boundaries = np.array([spline["x_right"] for spline in spline_info])

    # Find the indices for the right boundaries for each strike
    indices = np.searchsorted(right_boundaries, strikes, side="right")
    indices = np.clip(indices, 0, len(spline_info) - 1)

    results = [
        _evaluate_spline_segment(spline_info[index], strike)
        for index, strike in zip(indices, strikes)
    ]

    return results


def _zero_segment(x_left: float, x_right: float) -> CubicSplineSegment:
    return CubicSplineSegment(
        x_left=x_left,
        x_right=x_right,
        constant_coef=0,
        linear_coef=0,
        square_coef=0,
        cube_coef=0,
    )


def _build_basis_spline_left_segment(
    x_0: float, x_1: float, scale_factor: float
) -> CubicSplineSegment:
    h_1 = x_1 - x_0
    cube_coef = h_1 * h_1 / 6.0

    return CubicSplineSegment(
        x_left=x_0,
        x_right=x_1,
        constant_coef=0,
        linear_coef=0,
        square_coef=0,
        cube_coef=cube_coef / scale_factor,
    )


def _build_basis_spline_right_segment(
    x_0: float, x_1: float, x_2: float, scale_factor: float
) -> CubicSplineSegment:
    h_1 = x_1 - x_0
    h_2 = x_2 - x_1
    return CubicSplineSegment(
        x_left=x_1,
        x_right=x_2,
        constant_coef=h_1 * h_1 / 6.0 / scale_factor,
        linear_coef=h_1 * h_2 / 2.0 / scale_factor,
        square_coef=h_2 * h_2 / 2.0 / scale_factor,
        cube_coef=-1.0 / (6.0 * h_2) / scale_factor,
    )


def _calculate_constant_term_and_coef_for_basis_spline_right_extrapolation(
    x_0: float, x_1: float, x_2: float
) -> tuple[float, float]:
    h_1 = x_1 - x_0
    h_2 = x_2 - x_1
    coef = (h_1 * h_2 / 2 + h_2 * h_2 - 1.0 / (2.0 * h_2)) / h_2
    constant_term_at_x2 = (
        h_1 * h_1 / 6.0 + h_1 * h_2 / 2.0 + h_2 * h_2 / 2.0 - 1.0 / (6.0 * h_2)
    )
    constant_term = constant_term_at_x2 - coef * x_2

    return constant_term, coef


def _make_linear_segment(
    x_left: float,
    x_right: float,
    constant_term: float,
    coef: float,
    scale_factor: float = 1.0,
) -> CubicSplineSegment:
    constant_coef = x_left * coef + constant_term
    linear_coef = (x_right - x_left) * coef
    return CubicSplineSegment(
        x_left=x_left,
        x_right=x_right,
        constant_coef=constant_coef / scale_factor,
        linear_coef=linear_coef / scale_factor,
        square_coef=0,
        cube_coef=0,
    )


def _make_linear_spline(
    constant_term: float, coef: float, points: list[float]
) -> CubicSpline:
    spline_info = [
        _make_linear_segment(points[index], points[index + 1], constant_term, coef)
        for index in range(len(points) - 1)
    ]

    return CubicSpline(spline_info=spline_info)


def _build_basis_spline(
    index: int, points: list[float], approx_fwd: float
) -> CubicSpline:
    """
    Construct C2 cubic spline with zero second derivative
    at every node point except one defined by index argument.
    Such spline has linear "wings" and a convex shape near the
    specified node. Cf. company documentation for more details:
    https://www.notion.so/Cubic-spline-model-5ba8d6dbd40642e29ef79b3bd29cbc0b#ad3d1787548e494f9d37b72386c71682

    :param index: index of node point where a non-zero second derivative is required
    :param points: list of node points
    :param approx_fwd: approximate forward value to determine reasonable scale factor for the spine
    """

    assert index > 0
    assert index < (len(points) - 2)

    x_0 = points[index]
    x_1 = points[index + 1]
    x_2 = points[index + 2]
    constant_term, coef = (
        _calculate_constant_term_and_coef_for_basis_spline_right_extrapolation(
            x_0, x_1, x_2
        )
    )

    scale_factor = approx_fwd * approx_fwd / 100

    spline_info = []
    for sub_index in range(0, index):
        spline_info.append(_zero_segment(points[sub_index], points[sub_index + 1]))
    spline_info.append(_build_basis_spline_left_segment(x_0, x_1, scale_factor))
    spline_info.append(_build_basis_spline_right_segment(x_0, x_1, x_2, scale_factor))
    for sub_index in range(index + 2, len(points) - 1):
        spline_info.append(
            _make_linear_segment(
                points[sub_index],
                points[sub_index + 1],
                constant_term,
                coef,
                scale_factor,
            )
        )

    return CubicSpline(spline_info=spline_info)


def _build_and_process_linear_spline(
    smile_df: pd.DataFrame, node_strikes: list[float]
) -> tuple[CubicSpline, CubicSpline, pd.DataFrame]:
    constant_spline = _make_linear_spline(1, 0, node_strikes)
    strike = float(-smile_df.loc[0]["strike"])
    linear_spline = _make_linear_spline(strike, 1, node_strikes)

    smile_df["b_constant"] = 1
    smile_df["b_linear"] = smile_df["strike"] - smile_df.loc[0]["strike"]

    const_coef = smile_df["value"][0] / smile_df["b_constant"][0]
    smile_df["value_adj"] = smile_df["value"] - smile_df["b_constant"] * const_coef

    linear_coef = smile_df["value_adj"][1] / smile_df["b_linear"][1]
    smile_df["value_adj"] = smile_df["value_adj"] - smile_df["b_linear"] * linear_coef

    result = _add_splines(
        constant_spline,
        linear_spline,
        const_coef,
        linear_coef,
    )

    return result, linear_spline, smile_df


def _build_basis_splines(
    smile_df: pd.DataFrame,
    node_strikes: list[float],
    linear_spline: CubicSpline,
    fwd: float,
) -> tuple[list[CubicSpline], list[str], pd.DataFrame]:
    assert len(smile_df) >= 3

    basis_functions = []
    basis_function_names = []
    for index in range(0, len(smile_df) - 2):
        smile_spline = _build_basis_spline(index + 1, node_strikes, fwd)
        basis_functions.append(smile_spline)
        smile_df["b_" + str(index)] = _evaluate_spline_vectorized(
            smile_df["strike"].tolist(), smile_spline
        )
        basis_function_names.append("b_" + str(index))

    # We are going to subtract some linear functions from the input
    # data and basis splines. Why: such functions do not add to the
    # second derivative (= convexity), and should not impact the penalty
    # term during the calibration. Therefore we calculate the linear part
    # separately and take out the linear part for the regression.
    #
    # This linear part can be any linear function consistent across
    # the data and the splines. We choose it to be the function passing
    # through the first two points. And if you take it out, the values
    # at the first two points become zero.
    #
    # Such a choice is to simplify things: most of the basis splines
    # are already zero at the first two points and we just need to adjust
    # the input data and the first basis spline.
    #
    # Cf. company documentation for more details:
    # https://www.notion.so/Cubic-spline-model-5ba8d6dbd40642e29ef79b3bd29cbc0b#ad3d1787548e494f9d37b72386c71682
    #
    # Values are zero at first two points
    # already hold for every spline except the first one, we need to amend it:
    basis_adj_coef = smile_df["b_0"][1] / smile_df["b_linear"][1]
    smile_df["b_0"] = smile_df["b_0"] - smile_df["b_linear"] * basis_adj_coef
    basis_functions[0] = _add_splines(
        basis_functions[0],
        linear_spline,
        1,
        -basis_adj_coef,
    )

    return basis_functions, basis_function_names, smile_df


def spline_model_vol(
    strikes: np.ndarray | list[float], params: CubicSpline
) -> list[float]:
    """
    Calculate volatility for given cubis spline parameters and given list
    of strikes. Cf. company documentation for more details:
    https://www.notion.so/Cubic-spline-model-5ba8d6dbd40642e29ef79b3bd29cbc0b#ad3d1787548e494f9d37b72386c71682

    :param strikes: list of strikes, not necessary sorted
    :param params: cubis spline parameters

    """
    result = _evaluate_spline_vectorized(strikes, params)
    result = np.sqrt(np.array(result)).tolist()

    return result


def perform_spline_calibration(
    query: CalibrationQuery,
    custom_hyperparam: SplineCalibrationHyperparameters | None = None,
) -> CubicSpline:
    """
    Perform nodel calibration for CubicSpline volatility mode.
    Cf. company documentation for more details:
    https://www.notion.so/Cubic-spline-model-5ba8d6dbd40642e29ef79b3bd29cbc0b#ad3d1787548e494f9d37b72386c71682

    :param query: calibration query containing sorted list of strikes,
        corresponding volatilities and forward
    :param custom_hyperparam: optional dictionary of parameters to control
        fitting. Parameter "ridge_alpha" balances penalty of accuracy and total
        absolute second derivative at node points. When custom_hyperparam is not provided,
        default choice is assigned.

    """

    if len(query["strikes"]) < 2:
        raise ValueError(f"The query should contain at least two stikes, {query=:}")

    if custom_hyperparam is None:
        custom_hyperparam = SplineCalibrationHyperparameters(ridge_alpha=0.00001)

    smile_df = pd.DataFrame()
    smile_df["strike"] = np.array(query["strikes"], dtype=float)
    smile_df["value"] = np.square(np.array(query["LNvols"], dtype=float))

    node_strikes = [0.0, *query["strikes"], 2.0 * query["strikes"][-1]]

    result, linear_spline, smile_df = _build_and_process_linear_spline(
        smile_df, node_strikes
    )

    if len(smile_df) <= 2:
        return result

    basis_functions, basis_function_names, smile_df = _build_basis_splines(
        smile_df, node_strikes, linear_spline, query["forward"]
    )

    alpha = custom_hyperparam["ridge_alpha"]
    y = smile_df["value_adj"].values[2:]
    X = smile_df[basis_function_names].loc[2:].values
    model = Ridge(alpha=alpha, fit_intercept=False).fit(X, y)

    for index in range(0, len(smile_df) - 2):
        result = _add_splines(result, basis_functions[index], 1, model.coef_[index])

    return result


def _compute_total_var_slope(
    k1: float, k2: float, vol1: float, vol2: float, expiry: float
) -> float:
    """Return *clipped* total‑variance slope ∂(σ²T)/∂lnK in [0, 2]."""
    slope = ((vol2**2) * expiry - (vol1**2) * expiry) / np.log(k2 / k1)
    return float(np.clip(slope, 0.0, 2.0))


def _generate_call_wing_extrapolation(
    strikes: list[float],
    vols: list[float],
    *,
    spot: float,
    forward: float,
    expiry: float,
    rd: float,
    target_delta: float,
    max_new_strikes: int = 50,
    accelerate: bool = True,
    trim_after: int = 10,
    slope_trim_threshold: float = 1.0,
    trim_factor: float = 0.9,
) -> tuple[list[float], list[float]]:
    """Extend *in‑place* ``strikes`` & ``vols`` until *target_delta* reached.

    Parameters
    ----------
    strikes / vols
        Lists **must** contain at least two points in ascending strike order.
    target_delta
        Stopping criterion – loop stops once the minimum *call* delta in the
        list is **≤ target_delta**.
    max_new_strikes
        Hard safety‑cap so we never create an absurd amount of synthetic data.
    accelerate
        If *True* (default) the strike spacing doubles each iteration (the same
        logic the previous list helper used) ⇒ fewer loops.
    trim_after
        Number of extra points after which variance‑slope trimming begins.
    slope_trim_threshold
        If the *initial* slope exceeds this value we start trimming straight
        away – prevents explosive slopes on long‑dated tenors.
    trim_factor
        Multiplicative reduction applied to the slope when trimming is active.

    Notes
    -----
    The function mutates the *original* ``strikes`` & ``vols`` lists and then
    returns them (allows use as drop‑in replacement).
    """
    if len(vols) <= 1:
        # Nothing we can do – return inputs untouched.
        return strikes, vols

    max_strike = strikes[-1]
    second_max_strike = strikes[-2]
    strike_step = max_strike - second_max_strike
    if strike_step <= 0:
        raise ValueError("Input strikes must be strictly increasing.")

    total_var_slope = _compute_total_var_slope(
        second_max_strike, max_strike, vols[-2], vols[-1], expiry
    )

    # Current min call‑delta of tail strike
    min_delta = option_delta(
        s=spot,
        f=forward,
        K=max_strike,
        t=expiry,
        r_d=rd,
        vol=vols[-1],
        phi=1,
    )

    new_strike_counter = 0
    trim_slope = total_var_slope >= slope_trim_threshold  # early trim if needed
    last_total_var = (vols[-1] ** 2) * expiry
    last_strike = max_strike

    try:
        while min_delta > target_delta and new_strike_counter < max_points:
            new_strike_counter += 1

            # 1) Determine next strike level
            if accelerate:
                strike_step += strike_step  # geometric growth (doubling)
            new_strike = last_strike + strike_step
            if new_strike <= last_strike:
                logging.warning("Strike spacing stalled – forcing monotonicity")
                new_strike = last_strike * 1.05  # fallback 5 % step

            # 2) Decide whether to trim the slope
            if new_strike_counter >= trim_after:
                trim_slope = True
            if trim_slope:
                total_var_slope *= trim_factor

            # 3) Compute new vol point (variance additive against previous point)
            new_total_var = last_total_var + total_var_slope * np.log(
                new_strike / last_strike
            )
            new_vol = float(np.sqrt(new_total_var / expiry))

            # 4) Append & update trackers
            strikes.append(float(new_strike))
            vols.append(new_vol)
            last_strike = new_strike
            last_total_var = new_total_var

            # Delta of *this* new strike (for stopping rule)
            new_delta = option_delta(
                s=spot,
                f=forward,
                K=new_strike,
                t=expiry,
                r_d=rd,
                vol=new_vol,
                phi=1,
            )
            min_delta = min(min_delta, new_delta)

    except Exception as e:
        logging.exception("Call‑wing extrapolation failed (expiry=%s): %s", expiry, e)

    return strikes, vols
