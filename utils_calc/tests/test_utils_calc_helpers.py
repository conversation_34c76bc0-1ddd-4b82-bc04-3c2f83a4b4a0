from unittest.mock import patch

import numpy as np
import pandas as pd
import pytest
from pandas.testing import assert_frame_equal

from utils_calc import sabr_vol, svi_vol
from utils_calc.utils_calc_constants import (
    CALIBRATION_DELTA_BOUNDARY_FOR_LONG_EXPIRY,
    CALIBRATION_DELTA_BOUNDARY_FOR_SHORT_EXPIRY,
    MONEYNESS_STR,
)
from utils_calc.utils_calc_helpers import (
    _eq_delta_strike,
    ema_params_for_desired_observations,
    estimate_strike_from_delta,
    extrapolate_left,
    get_atm_deltas_from_boundaries,
    get_bordering_expiries,
    get_calibration_delta_boundary,
    get_closest_expiry_ref_params,
    _indices_have_gaps,
    model_vol,
    num_observations_for_weight,
    option_delta,
    safe_div,
    smooth_ema,
    smooth_sma,
    spline_fit,
    strike_from_delta_vol,
    zscore_ema,
    zscore_sma,
)
from utils_calc.utils_calc_types import (
    CubicSp<PERSON>,
    SviCalibrationReferenceParams,
    SviParams,
)


@pytest.fixture
def sample_tenor_completed_data():
    tenor_completed = [7, 14, 30, 60, 90, 120, 180, 270]
    return tenor_completed


@pytest.fixture
def sample_tenor_in_ex_right_data():
    tenor_in_ex_right = [365, 547, 730]
    return tenor_in_ex_right


@pytest.fixture
def sample_tenor_in_ex_left_data():
    tenor_completed = [2, 3, 5]
    return tenor_completed


@pytest.fixture
def sample_SVI_data():
    return {
        "svi_a": 0.050775976813232634,
        "svi_b": 0.21995347054607112,
        "svi_m": -0.05432599377858235,
        "svi_rho": -0.6087764643577055,
        "svi_sigma": 0.01,
    }


@pytest.fixture
def sample_SABR_data():
    return {
        "sabr_alpha": 0.40926685997246565,
        "sabr_rho": -0.298409644313633,
        "sabr_volvol": 2.1665539260726097,
    }


@pytest.fixture
def sample_SABR_data_bad():
    return {
        "sabr_alpha": 0.3549394888814451,
        "sabr_rho": 0.05914419160299113,
        "sabr_volvol": 1.34001672119048,
    }


@pytest.fixture
def sample_SABR_failed_estimated_strike_data():
    return {
        "forward": 49108.384991070576,
        "tenor_days": 730.0,
        "spot": 40926.18320918602,
        "timestamp": 1702444980000000000,
        "R2_calib": 0.9997335126307374,
        "atm_vol": 0.39484982413493896,
        "qualified_name": "bybit.option.BTC.SABR.730d.1m.params",
        "expiry": 2.0,
        "sabr_alpha": 0.2865837776945571,
        "sabr_rho": -0.0775482629097943,
        "sabr_volvol": 1.5464031152052744,
    }


@pytest.fixture
def sample_left_extrapolation_data(
    sample_tenor_completed_data, sample_tenor_in_ex_left_data
):
    # fmt: off
    data_values = [
        [0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000],
        [0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000],
        [0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000],
        [1.96687, 1.64608, 1.42549, 1.24570, 1.08624, 0.93651, 0.78914, 0.63751, 0.47842, 0.38972, 0.52600, 0.66828,
         0.78211, 0.87586, 0.95544, 1.02457, 1.08564, 1.14030, 1.18976, 1.23488, 1.27634, 1.31467, 1.35030, 1.38356,
         1.41473, 1.44406, 1.47173, 1.49791, 1.52275, 1.54637],
        [1.69873, 1.42240, 1.23258, 1.07809, 0.94140, 0.81361, 0.68897, 0.56376, 0.44408, 0.40332, 0.48321, 0.57350,
         0.65092, 0.71682, 0.77382, 0.82391, 0.86851, 0.90868, 0.94517, 0.97858, 1.00937, 1.03790, 1.06446, 1.08930,
         1.11262, 1.13458, 1.15532, 1.17497, 1.19363, 1.21138],
        [1.49212, 1.25099, 1.08580, 0.95190, 0.83419, 0.72540, 0.62170, 0.52300, 0.44131, 0.41752, 0.45886, 0.51724,
         0.57273, 0.62227, 0.66623, 0.70546, 0.74076, 0.77276, 0.80199, 0.82886, 0.85370, 0.87677, 0.89830, 0.91847,
         0.93743, 0.95530, 0.97221, 0.98823, 1.00346, 1.01797],
        [1.34430, 1.12869, 0.98142, 0.86254, 0.75873, 0.66395, 0.57580, 0.49661, 0.44023, 0.43083, 0.46075, 0.50242,
         0.54372, 0.58177, 0.61623, 0.64743, 0.67580, 0.70173, 0.72555, 0.74756, 0.76799, 0.78702, 0.80484, 0.82156,
         0.83731, 0.85220, 0.86629, 0.87967, 0.89241, 0.90455],
        [1.29795, 1.09059, 0.94892, 0.83450, 0.73448, 0.64300, 0.55791, 0.48305, 0.43840, 0.44377, 0.47408, 0.50812,
         0.54032, 0.56969, 0.59633, 0.62054, 0.64265, 0.66295, 0.68168, 0.69904, 0.71521, 0.73033, 0.74452, 0.75787,
         0.77047, 0.78241, 0.79373, 0.80450, 0.81476, 0.82456],
        [1.22821, 1.03299, 0.89984, 0.79256, 0.69917, 0.61449, 0.53748, 0.47464, 0.44649, 0.45746, 0.48324, 0.51089,
         0.53708, 0.56115, 0.58316, 0.60329, 0.62178, 0.63883, 0.65464, 0.66934, 0.68307, 0.69594, 0.70804, 0.71946,
         0.73025, 0.74049, 0.75022, 0.75948, 0.76832, 0.77677],
        [1.15344, 0.97145, 0.84753, 0.74792, 0.66156, 0.58401, 0.51561, 0.46667, 0.45528, 0.47007, 0.49160, 0.51335,
         0.53376, 0.55257, 0.56983, 0.58571, 0.60036, 0.61393, 0.62656, 0.63835, 0.64939, 0.65978, 0.66957, 0.67882,
         0.68759, 0.69592, 0.70385, 0.71142, 0.71865, 0.72557],
        [0.99757, 0.84526, 0.74398, 0.66558, 0.60182, 0.55048, 0.51251, 0.48982, 0.48210, 0.48554, 0.49542, 0.50830,
         0.52224, 0.53628, 0.54994, 0.56302, 0.57545, 0.58723, 0.59836, 0.60890, 0.61887, 0.62833, 0.63731, 0.64585,
         0.65399, 0.66175, 0.66917, 0.67627, 0.68308, 0.68961],
    ]
    # fmt: on

    index = sample_tenor_in_ex_left_data + sample_tenor_completed_data
    df = pd.DataFrame(data_values, columns=MONEYNESS_STR[0:30], index=index)

    return df


@pytest.fixture
def sample_spline_fit_data(sample_tenor_completed_data, sample_tenor_in_ex_right_data):
    # fmt: off
    data_values = [
        [1.96687, 1.64608, 1.42549, 1.24570, 1.08624, 0.93651, 0.78914, 0.63751, 0.47842, 0.38972, 0.52600, 0.66828,
         0.78211, 0.87586, 0.95544, 1.02457, 1.08564, 1.14030, 1.18976, 1.23488, 1.27634, 1.31467, 1.35030, 1.38356,
         1.41473, 1.44406, 1.47173, 1.49791, 1.52275, 1.54637],
        [1.69873, 1.42240, 1.23258, 1.07809, 0.94140, 0.81361, 0.68897, 0.56376, 0.44408, 0.40332, 0.48321, 0.57350,
         0.65092, 0.71682, 0.77382, 0.82391, 0.86851, 0.90868, 0.94517, 0.97858, 1.00937, 1.03790, 1.06446, 1.08930,
         1.11262, 1.13458, 1.15532, 1.17497, 1.19363, 1.21138],
        [1.49212, 1.25099, 1.08580, 0.95190, 0.83419, 0.72540, 0.62170, 0.52300, 0.44131, 0.41752, 0.45886, 0.51724,
         0.57273, 0.62227, 0.66623, 0.70546, 0.74076, 0.77276, 0.80199, 0.82886, 0.85370, 0.87677, 0.89830, 0.91847,
         0.93743, 0.95530, 0.97221, 0.98823, 1.00346, 1.01797],
        [1.34430, 1.12869, 0.98142, 0.86254, 0.75873, 0.66395, 0.57580, 0.49661, 0.44023, 0.43083, 0.46075, 0.50242,
         0.54372, 0.58177, 0.61623, 0.64743, 0.67580, 0.70173, 0.72555, 0.74756, 0.76799, 0.78702, 0.80484, 0.82156,
         0.83731, 0.85220, 0.86629, 0.87967, 0.89241, 0.90455],
        [1.29795, 1.09059, 0.94892, 0.83450, 0.73448, 0.64300, 0.55791, 0.48305, 0.43840, 0.44377, 0.47408, 0.50812,
         0.54032, 0.56969, 0.59633, 0.62054, 0.64265, 0.66295, 0.68168, 0.69904, 0.71521, 0.73033, 0.74452, 0.75787,
         0.77047, 0.78241, 0.79373, 0.80450, 0.81476, 0.82456],
        [1.22821, 1.03299, 0.89984, 0.79256, 0.69917, 0.61449, 0.53748, 0.47464, 0.44649, 0.45746, 0.48324, 0.51089,
         0.53708, 0.56115, 0.58316, 0.60329, 0.62178, 0.63883, 0.65464, 0.66934, 0.68307, 0.69594, 0.70804, 0.71946,
         0.73025, 0.74049, 0.75022, 0.75948, 0.76832, 0.77677],
        [1.15344, 0.97145, 0.84753, 0.74792, 0.66156, 0.58401, 0.51561, 0.46667, 0.45528, 0.47007, 0.49160, 0.51335,
         0.53376, 0.55257, 0.56983, 0.58571, 0.60036, 0.61393, 0.62656, 0.63835, 0.64939, 0.65978, 0.66957, 0.67882,
         0.68759, 0.69592, 0.70385, 0.71142, 0.71865, 0.72557],
        [0.99757, 0.84526, 0.74398, 0.66558, 0.60182, 0.55048, 0.51251, 0.48982, 0.48210, 0.48554, 0.49542, 0.50830,
         0.52224, 0.53628, 0.54994, 0.56302, 0.57545, 0.58723, 0.59836, 0.60890, 0.61887, 0.62833, 0.63731, 0.64585,
         0.65399, 0.66175, 0.66917, 0.67627, 0.68308, 0.68961],
        [0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000],
        [0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000],
        [0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000,
         0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000]
    ]
    # fmt: on
    index = sample_tenor_completed_data + sample_tenor_in_ex_right_data
    df = pd.DataFrame(data_values, columns=MONEYNESS_STR[0:30], index=index)

    return df


@pytest.fixture
def sample_perform_sabr_calibration_query():
    strikes = [
        2679.5863502535026,
        5359.172700507005,
        8038.759050760506,
        10718.34540101401,
        13397.931751267512,
        16077.518101521013,
        18757.104451774514,
        21436.69080202802,
        24116.27715228152,
        26795.863502535023,
        29475.449852788526,
        32155.036203042026,
        34834.62255329553,
        37514.20890354903,
        40193.795253802535,
        42873.38160405604,
        45552.96795430954,
        48232.55430456304,
        50912.14065481654,
        53591.727005070046,
        56271.31335532355,
        58950.89970557705,
        61630.48605583055,
        64310.07240608405,
        66989.65875633757,
        69669.24510659106,
        72348.83145684456,
        75028.41780709806,
        77708.00415735156,
        80387.59050760507,
    ]
    query = {
        "expiry": 0.01918,
        "forward": 26795.863502535023,
        "spot": 26755.85,
        "domestic_rate": 0,
        "test_type": "strike",
        "vol_test_type": "vol_lognormal",
        "model": "SABR",
        "LNvols": [
            3.286126067364212,
            2.505584445224139,
            2.02370983330125,
            1.6652632179962177,
            1.3740590488635769,
            1.1244568057016704,
            0.9024488773050189,
            0.7001833086643481,
            0.5187105305675214,
            0.41807068127665714,
            0.519474717676501,
            0.6577150856339452,
            0.7852232308586341,
            0.9000954814880178,
            1.0041251245375085,
            1.0991062732647736,
            1.1864851051768468,
            1.26739829415015,
            1.3427457167514205,
            1.4132498007577512,
            1.4794990112909074,
            1.5419792025609016,
            1.6010963923971515,
            1.6571935468943628,
            1.7105631496942557,
            1.7614567624953188,
            1.8100924036968717,
            1.8566603197050184,
            1.9013275543174604,
            1.9442416067719464,
        ],
        "strikes": strikes,
        "biv": [1] * len(strikes),
        "aiv": [1] * len(strikes),
    }
    return query


@pytest.fixture
def sample_get_closest_expiry_ref_params() -> list[SviParams]:
    return [
        # fmt: off
        {"expiry": 1.0, "svi_a": 0.1, "svi_b": 0.5, "svi_rho": 0.2, "svi_m": 0.3, "svi_sigma": 0.4},
        {"expiry": 2.0, "svi_a": 0.2, "svi_b": 0.6, "svi_rho": 0.3, "svi_m": 0.4, "svi_sigma": 0.5},
        {"expiry": 3.0, "svi_a": 0.3, "svi_b": 0.7, "svi_rho": 0.4, "svi_m": 0.5, "svi_sigma": 0.6},
        {"expiry": 4.0, "svi_a": 0.4, "svi_b": 0.8, "svi_rho": 0.5, "svi_m": 0.6, "svi_sigma": 0.7},
        # fmt: on
    ]


@pytest.fixture
def sample_spline_eq_delta_strike_data():
    # fmt: off
    return CubicSpline(spline_info=[
                {'x_left': 0.0, 'x_right': 20000, 'constant_coef': 0.5555928958616284, 'linear_coef': -0.08886417784411932, 'square_coef': 0.0, 'cube_coef': 0.0},
                {'x_left': 20000, 'x_right': 30000, 'constant_coef': 0.466728718017509, 'linear_coef': -0.04443208892205966, 'square_coef': 0.0, 'cube_coef': 0.005602585462188199},
                {'x_left': 30000, 'x_right': 40000, 'constant_coef': 0.4278992145576375, 'linear_coef': -0.02762433253549506, 'square_coef': 0.0168077563865646, 'cube_coef': -0.007660485526833297},
                {'x_left': 40000, 'x_right': 45000, 'constant_coef': 0.4094221528818738, 'linear_coef': -0.008495138171432881, 'square_coef': -0.005745364145120772, 'cube_coef': 0.0033603929680212823},
                {'x_left': 45000, 'x_right': 50000, 'constant_coef': 0.39854204353334133, 'linear_coef': -0.009904687557610575, 'square_coef': 0.010081178904017885, 'cube_coef': -0.0029298897306222083},
                {'x_left': 50000, 'x_right': 55000, 'constant_coef': 0.39578864514912654, 'linear_coef': 0.0014680010585585625, 'square_coef': -0.008789669191785978, 'cube_coef': 0.0018239286591320272},
                {'x_left': 55000, 'x_right': 65000, 'constant_coef': 0.39029090567503105, 'linear_coef': -0.021279102695234614, 'square_coef': 0.021887143909303058, 'cube_coef': -0.011754829074842179},
                {'x_left': 65000, 'x_right': 70000, 'constant_coef': 0.3791441178142574, 'linear_coef': -0.006384651050577516, 'square_coef': -0.008816121806126164, 'cube_coef': 0.00570304844991124},
                {'x_left': 70000, 'x_right': 75000, 'constant_coef': 0.369646393407465, 'linear_coef': -0.006907749313096116, 'square_coef': 0.017109145349663193, 'cube_coef': -0.003159644737656504},
                {'x_left': 75000, 'x_right': 80000, 'constant_coef': 0.3766881447063755, 'linear_coef': 0.017831607173260745, 'square_coef': -0.00947893421283264, 'cube_coef': 0.0012722203539803076},
                {'x_left': 80000, 'x_right': 100000, 'constant_coef': 0.3863130380207839, 'linear_coef': 0.010761599238145583, 'square_coef': 0.06106657698984147, 'cube_coef': -0.02387743222810575},
                {'x_left': 100000, 'x_right': 110000, 'constant_coef': 0.43426378202066496, 'linear_coef': 0.030631228266755635, 'square_coef': -0.01790807417107741, 'cube_coef': 0.004973977847198767},
                {'x_left': 110000, 'x_right': 120000, 'constant_coef': 0.451960913963542, 'linear_coef': 0.009737013466197129, 'square_coef': 0.014921933541578395, 'cube_coef': -0.004976130365882676},
                {'x_left': 120000, 'x_right': 130000, 'constant_coef': 0.47164373060543513, 'linear_coef': 0.024652489451705888, 'square_coef': -0.014928391097633107, 'cube_coef': 0.0019619611226485705},
                {'x_left': 130000, 'x_right': 160000, 'constant_coef': 0.4833297900821564, 'linear_coef': 0.0020447718731560934, 'square_coef': 0.052972950311377054, 'cube_coef': -0.018521519681651576},
                {'x_left': 160000, 'x_right': 180000, 'constant_coef': 0.5198259925850376, 'linear_coef': 0.03495074230063705, 'square_coef': -0.024695359575534565, 'cube_coef': 0.005583565596614529},
                {'x_left': 180000, 'x_right': 220000, 'constant_coef': 0.5356649409067545, 'linear_coef': 0.004621439878823033, 'square_coef': 0.067002787159362, 'cube_coef': -0.012092692509317507},
                {'x_left': 220000, 'x_right': 300000, 'constant_coef': 0.595196475435622, 'linear_coef': 0.20469787333918904, 'square_coef': -0.14511231011180592, 'cube_coef': 0.032425965893071944},
                {'x_left': 300000, 'x_right': 360000, 'constant_coef': 0.687208004556078, 'linear_coef': 0.00881336309609465, 'square_coef': 0.05471881744455874, 'cube_coef': -0.009444109258431562},
                {'x_left': 360000, 'x_right': 480000, 'constant_coef': 0.7412960758382994, 'linear_coef': 0.1798373404198349, 'square_coef': -0.11332931110117773, 'cube_coef': 0.01883662255943414},
                {'x_left': 480000, 'x_right': 600000, 'constant_coef': 0.8266407277163904, 'linear_coef': 0.009688585895781816, 'square_coef': 0.05650986767830236, 'cube_coef': -1.0900823240413264e-17},
                {'x_left': 600000, 'x_right': 1200000.0, 'constant_coef': 0.8928391812904772, 'linear_coef': 0.6135416062619349, 'square_coef': 0.0, 'cube_coef': 0.0}
            ]
        )
    # fmt: off


def test_spline_fit(
    sample_spline_fit_data,
    sample_tenor_completed_data,
    sample_tenor_in_ex_right_data,
):
    test_result_df = spline_fit(
        sample_spline_fit_data,
        sample_tenor_completed_data,
        sample_tenor_in_ex_right_data,
        domain_cols=MONEYNESS_STR[0:30],
    )
    # fmt: off
    raw_expected_result = [
        [1.96687, 1.64608, 1.42549, 1.2457 , 1.08624, 0.93651, 0.78914, 0.63751, 0.47842, 0.38972, 0.526  , 0.66828,
         0.78211, 0.87586, 0.95544, 1.02457, 1.08564, 1.1403 , 1.18976, 1.23488, 1.27634, 1.31467, 1.3503 , 1.38356,
         1.41473, 1.44406, 1.47173, 1.49791, 1.52275, 1.54637],
        [1.69873, 1.4224 , 1.23258, 1.07809, 0.9414 , 0.81361, 0.68897, 0.56376, 0.44408, 0.40332, 0.48321, 0.5735,
         0.65092, 0.71682, 0.77382, 0.82391, 0.86851, 0.90868, 0.94517, 0.97858, 1.00937, 1.0379 , 1.06446, 1.0893,
         1.11262, 1.13458, 1.15532, 1.17497, 1.19363, 1.21138],
        [1.49212, 1.25099, 1.0858 , 0.9519 , 0.83419, 0.7254 , 0.6217 , 0.523  , 0.44131, 0.41752, 0.45886, 0.51724,
         0.57273, 0.62227, 0.66623, 0.70546, 0.74076, 0.77276, 0.80199, 0.82886, 0.8537 , 0.87677, 0.8983 , 0.91847,
         0.93743, 0.9553 , 0.97221, 0.98823, 1.00346, 1.01797],
        [1.3443 , 1.12869, 0.98142, 0.86254, 0.75873, 0.66395, 0.5758 , 0.49661, 0.44023, 0.43083, 0.46075, 0.50242,
         0.54372, 0.58177, 0.61623, 0.64743, 0.6758 , 0.70173, 0.72555, 0.74756, 0.76799, 0.78702, 0.80484, 0.82156,
         0.83731, 0.8522 , 0.86629, 0.87967, 0.89241, 0.90455],
        [1.29795, 1.09059, 0.94892, 0.8345 , 0.73448, 0.643, 0.55791, 0.48305, 0.4384 , 0.44377, 0.47408, 0.50812,
         0.54032, 0.56969, 0.59633, 0.62054, 0.64265, 0.66295, 0.68168, 0.69904, 0.71521, 0.73033, 0.74452, 0.75787,
         0.77047, 0.78241, 0.79373, 0.8045 , 0.81476, 0.82456],
        [1.22821, 1.03299, 0.89984, 0.79256, 0.69917, 0.61449, 0.53748, 0.47464, 0.44649, 0.45746, 0.48324, 0.51089,
         0.53708, 0.56115, 0.58316, 0.60329, 0.62178, 0.63883, 0.65464, 0.66934, 0.68307, 0.69594, 0.70804, 0.71946,
         0.73025, 0.74049, 0.75022, 0.75948, 0.76832, 0.77677],
        [1.15344, 0.97145, 0.84753, 0.74792, 0.66156, 0.58401, 0.51561, 0.46667, 0.45528, 0.47007, 0.4916 , 0.51335,
         0.53376, 0.55257, 0.56983, 0.58571, 0.60036, 0.61393, 0.62656, 0.63835, 0.64939, 0.65978, 0.66957, 0.67882,
         0.68759, 0.69592, 0.70385, 0.71142, 0.71865, 0.72557],
        [ 0.99757, 0.84526, 0.74398, 0.66558, 0.60182, 0.55048, 0.51251, 0.48982, 0.4821 , 0.48554, 0.49542, 0.5083,
         0.52224, 0.53628, 0.54994, 0.56302, 0.57545, 0.58723, 0.59836, 0.6089 , 0.61887, 0.62833, 0.63731, 0.64585,
         0.65399, 0.66175, 0.66917, 0.67627, 0.68308, 0.68961],
        [0.98604327, 0.83813111, 0.7397755 , 0.66363924, 0.60172027, 0.55186267, 0.51498903, 0.49295419, 0.4854571,
         0.48879777, 0.49839249, 0.51090059, 0.52443808, 0.53807269, 0.55133827, 0.56404059, 0.57611168, 0.58755154,
         0.59836017, 0.60859584, 0.61827796, 0.62746481, 0.63618552, 0.64447893, 0.6523839 , 0.65991983, 0.66712558,
         0.67402058, 0.68063394, 0.68697539],
        [0.96239275, 0.82350407, 0.73114871, 0.6596572 , 0.60151564, 0.55469964, 0.5200755 , 0.49938491, 0.49234518,
         0.49548206, 0.50449144, 0.51623648, 0.52894811, 0.54175092, 0.55420722, 0.56613463, 0.57746932, 0.58821129,
         0.59836053, 0.60797176, 0.61706322, 0.62568962, 0.63387831, 0.64166578, 0.6490885 , 0.6561647 , 0.66293086,
         0.66940522, 0.67561513, 0.68156972],
        [0.93685278, 0.80770848, 0.72183273, 0.65535704, 0.60129466, 0.55776325, 0.52556833, 0.50632938, 0.49978356,
         0.50270035, 0.51107764, 0.52199865, 0.53381844, 0.54572301, 0.55730538, 0.56839597, 0.57893542, 0.58892374,
         0.59836091, 0.60729782, 0.61575143, 0.6237726 , 0.63138678, 0.63862788, 0.64552983, 0.65210956, 0.65840101,
         0.66442113, 0.67019536, 0.67573218]]
    # fmt: o
    index = sample_tenor_completed_data + sample_tenor_in_ex_right_data
    expected_result_df = pd.DataFrame(
        raw_expected_result, columns=MONEYNESS_STR[0:30], index=index
    )
    # these are calibrations done using optimisation functions
    # they may not always converge to values with an accuracy of 6 significant figures
    assert_frame_equal(test_result_df, expected_result_df, rtol=1e-5, atol=1e-5)


def test_extrapolate_left(
    sample_tenor_completed_data,
    sample_tenor_in_ex_left_data,
    sample_left_extrapolation_data,
):
    test_result_df = extrapolate_left(
        sample_left_extrapolation_data,
    )
    # fmt: off
    raw_expected_result = [
        [1.96687, 1.64608, 1.42549, 1.24570, 1.08624, 0.93651, 0.78914, 0.63751, 0.47842, 0.38972, 0.52600, 0.66828,
         0.78211, 0.87586, 0.95544, 1.02457, 1.08564, 1.14030, 1.18976, 1.23488, 1.27634, 1.31467, 1.35030, 1.38356,
         1.41473, 1.44406, 1.47173, 1.49791, 1.52275, 1.54637],
        [1.96687, 1.64608, 1.42549, 1.24570, 1.08624, 0.93651, 0.78914, 0.63751, 0.47842, 0.38972, 0.52600, 0.66828,
         0.78211, 0.87586, 0.95544, 1.02457, 1.08564, 1.14030, 1.18976, 1.23488, 1.27634, 1.31467, 1.35030, 1.38356,
         1.41473, 1.44406, 1.47173, 1.49791, 1.52275, 1.54637],
        [1.96687, 1.64608, 1.42549, 1.24570, 1.08624, 0.93651, 0.78914, 0.63751, 0.47842, 0.38972, 0.52600, 0.66828,
         0.78211, 0.87586, 0.95544, 1.02457, 1.08564, 1.14030, 1.18976, 1.23488, 1.27634, 1.31467, 1.35030, 1.38356,
         1.41473, 1.44406, 1.47173, 1.49791, 1.52275, 1.54637],
        [1.96687, 1.64608, 1.42549, 1.24570, 1.08624, 0.93651, 0.78914, 0.63751, 0.47842, 0.38972, 0.52600, 0.66828,
         0.78211, 0.87586, 0.95544, 1.02457, 1.08564, 1.14030, 1.18976, 1.23488, 1.27634, 1.31467, 1.35030, 1.38356,
         1.41473, 1.44406, 1.47173, 1.49791, 1.52275, 1.54637],
        [1.69873, 1.42240, 1.23258, 1.07809, 0.94140, 0.81361, 0.68897, 0.56376, 0.44408, 0.40332, 0.48321, 0.57350,
         0.65092, 0.71682, 0.77382, 0.82391, 0.86851, 0.90868, 0.94517, 0.97858, 1.00937, 1.03790, 1.06446, 1.08930,
         1.11262, 1.13458, 1.15532, 1.17497, 1.19363, 1.21138],
        [1.49212, 1.25099, 1.08580, 0.95190, 0.83419, 0.72540, 0.62170, 0.52300, 0.44131, 0.41752, 0.45886, 0.51724,
         0.57273, 0.62227, 0.66623, 0.70546, 0.74076, 0.77276, 0.80199, 0.82886, 0.85370, 0.87677, 0.89830, 0.91847,
         0.93743, 0.95530, 0.97221, 0.98823, 1.00346, 1.01797],
        [1.34430, 1.12869, 0.98142, 0.86254, 0.75873, 0.66395, 0.57580, 0.49661, 0.44023, 0.43083, 0.46075, 0.50242,
         0.54372, 0.58177, 0.61623, 0.64743, 0.67580, 0.70173, 0.72555, 0.74756, 0.76799, 0.78702, 0.80484, 0.82156,
         0.83731, 0.85220, 0.86629, 0.87967, 0.89241, 0.90455],
        [1.29795, 1.09059, 0.94892, 0.83450, 0.73448, 0.64300, 0.55791, 0.48305, 0.43840, 0.44377, 0.47408, 0.50812,
         0.54032, 0.56969, 0.59633, 0.62054, 0.64265, 0.66295, 0.68168, 0.69904, 0.71521, 0.73033, 0.74452, 0.75787,
         0.77047, 0.78241, 0.79373, 0.80450, 0.81476, 0.82456],
        [1.22821, 1.03299, 0.89984, 0.79256, 0.69917, 0.61449, 0.53748, 0.47464, 0.44649, 0.45746, 0.48324, 0.51089,
         0.53708, 0.56115, 0.58316, 0.60329, 0.62178, 0.63883, 0.65464, 0.66934, 0.68307, 0.69594, 0.70804, 0.71946,
         0.73025, 0.74049, 0.75022, 0.75948, 0.76832, 0.77677],
        [1.15344, 0.97145, 0.84753, 0.74792, 0.66156, 0.58401, 0.51561, 0.46667, 0.45528, 0.47007, 0.49160, 0.51335,
         0.53376, 0.55257, 0.56983, 0.58571, 0.60036, 0.61393, 0.62656, 0.63835, 0.64939, 0.65978, 0.66957, 0.67882,
         0.68759, 0.69592, 0.70385, 0.71142, 0.71865, 0.72557],
        [0.99757, 0.84526, 0.74398, 0.66558, 0.60182, 0.55048, 0.51251, 0.48982, 0.48210, 0.48554, 0.49542, 0.50830,
         0.52224, 0.53628, 0.54994, 0.56302, 0.57545, 0.58723, 0.59836, 0.60890, 0.61887, 0.62833, 0.63731, 0.64585,
         0.65399, 0.66175, 0.66917, 0.67627, 0.68308, 0.68961]
    ]
    # fmt: on
    index = sample_tenor_in_ex_left_data + sample_tenor_completed_data
    expected_result_df = pd.DataFrame(
        raw_expected_result, columns=MONEYNESS_STR[0:30], index=index
    )
    # these are calibrations done using optimisation functions
    # they may not always converge to values with an accuracy of 6 significant figures
    assert_frame_equal(test_result_df, expected_result_df, rtol=1e-5, atol=1e-5)


def test_smooth_ema_empty_dataframe():
    df = pd.DataFrame()
    with pytest.raises(KeyError):
        smooth_ema(df=df, idx_to_smooth=5, fields_to_replace=["A", "B"], span=2)


def test_smooth_ema_basic_input():
    df = pd.DataFrame({"A": [1, 2, 3, 4, 5], "B": [5, 4, 3, 2, 1]})
    result_df = smooth_ema(df, 2, ["A", "B"], 2)
    expected_df = pd.DataFrame({"A": [1, 2, 2.61538, 4, 5], "B": [5, 4, 3.38461, 2, 1]})
    pd.testing.assert_frame_equal(result_df, expected_df)


def test_smooth_ema_duplicate_input():
    df = pd.DataFrame({"A": [1, 2, 3, 4, 5], "B": [5, 4, 3, 2, 1]})
    result_df = smooth_ema(df, [2, 2, 2, 2], ["A", "B"], 2)
    expected_df = pd.DataFrame({"A": [1, 2, 2.61538, 4, 5], "B": [5, 4, 3.38461, 2, 1]})
    pd.testing.assert_frame_equal(result_df, expected_df)


def test_smooth_ema_multiple_indices():
    df = pd.DataFrame({"A": [1, 2, 3, 4, 5], "B": [5, 4, 3, 2, 1]})
    result_df = smooth_ema(df, [3, 4], ["A", "B"], 2)
    expected_df = pd.DataFrame({"A": [1, 2, 3, 3.4, 4.6], "B": [5, 4, 3, 2.4, 1.4]})
    pd.testing.assert_frame_equal(result_df, expected_df)


def test_smooth_sma_empty_dataframe():
    df = pd.DataFrame()
    with pytest.raises(KeyError):
        smooth_sma(df=df, idx_to_smooth=5, fields_to_replace=["A", "B"], window=2)


def test_smooth_sma_basic_input():
    df = pd.DataFrame({"A": [1, 2, 3, 4, 5], "B": [5, 4, 3, 2, 1]})
    result_df = smooth_sma(df, 2, ["A", "B"], 2)
    expected_df = pd.DataFrame({"A": [1, 2, 2.5, 4, 5], "B": [5, 4, 3.5, 2, 1]})
    pd.testing.assert_frame_equal(result_df, expected_df)


def test_smooth_sma_multiple_indices():
    df = pd.DataFrame({"A": [1, 2, 3, 4, 5], "B": [5, 4, 3, 2, 1]})
    result_df = smooth_sma(
        df=df, idx_to_smooth=[3, 4], fields_to_replace=["A", "B"], window=2
    )
    expected_df = pd.DataFrame({"A": [1, 2, 3, 3.5, 4.5], "B": [5, 4, 3, 2.5, 1.5]})
    pd.testing.assert_frame_equal(result_df, expected_df)


def test_zscore_sma_empty_dataframe():
    df = pd.DataFrame()
    result = zscore_sma(df, 3)
    assert result.empty is True


def test_zscore_sma_different_window_sizes():
    data = {"value": [1, 2, 3, 4, 5, 6]}
    df = pd.DataFrame(data)

    # window size of 2
    result = zscore_sma(df, 2)
    expected_data = {"value": [np.nan, np.nan, 3.0, 3.0, 3.0, 3.0]}
    expected_df = pd.DataFrame(expected_data)
    assert_frame_equal(
        result, expected_df, check_exact=False, check_datetimelike_compat=True
    )

    # window size of 3
    result = zscore_sma(df, 3)
    expected_data = {"value": [np.nan, np.nan, np.nan, 2.44948, 2.44948, 2.44948]}
    expected_df = pd.DataFrame(expected_data)
    assert_frame_equal(
        result, expected_df, check_exact=False, check_datetimelike_compat=True
    )


def test_zscore_ema_empty_dataframe():
    df = pd.DataFrame()
    result = zscore_ema(df, 3)
    assert result.empty is True


def test_zscore_ema_different_window_sizes():
    data = {"value": [1, 2, 3, 4, 5, 6]}
    df = pd.DataFrame(data)

    # window size of 2
    result = zscore_ema(df, 2)
    expected_data = {"value": [np.nan, np.nan, 1.76776, 1.50523, 1.36824, 1.29581]}
    expected_df = pd.DataFrame(expected_data)
    assert_frame_equal(
        result, expected_df, check_exact=False, check_datetimelike_compat=True
    )

    # window size of 3
    result = zscore_ema(df, 3)
    expected_data = {"value": [np.nan, np.nan, 1.88561, 1.63074, 1.47246, 1.36682]}
    expected_df = pd.DataFrame(expected_data)
    assert_frame_equal(
        result, expected_df, check_exact=False, check_datetimelike_compat=True
    )


def test_num_observations_for_weight():
    threshold = 0.95
    for expected_span in range(1, 50):
        expected_alpha = 2 / (expected_span + 1)
        estimated_num_observations = num_observations_for_weight(
            expected_span, threshold
        )

        estimated_span, estimated_alpha = ema_params_for_desired_observations(
            estimated_num_observations, threshold
        )
        assert round(estimated_span) == expected_span
        assert np.isclose(estimated_alpha, expected_alpha, atol=1e-1)


def test_strike_from_delta_vol():
    s = 1803.8399999999997
    f = 1810.4228261236935
    r_d = 0
    t = 0.328767123288
    vol = 0.7057217366666592
    delta = -0.01

    expected_result = 766.0751324485088
    test_result = strike_from_delta_vol(s, f, r_d, t, vol, delta)
    assert np.isclose(test_result, expected_result, atol=1e-6)


def test_option_delta():
    K = 910.07
    s = 1820.1399999999999
    t = 0.328767123288
    f = 1825.7468837040171
    vol = 0.9194438531190378
    r_d = 0
    phi = -1

    expected_result = -0.05674615466353089
    test_result = option_delta(s, f, K, t, r_d, vol, phi)
    assert np.isclose(test_result, expected_result, atol=1e-6)


def test_estimate_strike_from_delta(sample_SVI_data, sample_SABR_data):
    delta = -0.01
    r_d = 0
    expiry = 0.328767123288
    forward = 1825.7468837040171
    spot = 1820.1399999999999

    svi_expected_result = 431.4766258839442
    svi_test_result = estimate_strike_from_delta(
        "SVI", sample_SVI_data, spot, forward, expiry, delta, r_d
    )
    assert np.isclose(svi_test_result, svi_expected_result, atol=1e-6)

    forward = 1860.4759107069306
    spot = 1854.8899999999999
    expiry = 0.328767123288
    delta = -0.01
    r_d = 0

    sabr_expected_result = 383.8876202443126
    sabr_test_result = estimate_strike_from_delta(
        "SABR", sample_SABR_data, spot, forward, expiry, delta, r_d
    )
    assert np.isclose(sabr_test_result, sabr_expected_result, atol=1e-6)


def test_eq_delta_strike(sample_SVI_data, sample_SABR_data):
    K = 910.07
    delta = -0.01
    r_d = 0
    expiry = 0.328767123288
    forward = 1825.7468837040171
    spot = 1820.1399999999999

    svi_expected_result = 0.04674615466353089
    svi_test_result = _eq_delta_strike(
        K, "SVI", sample_SVI_data, spot, forward, expiry, r_d, delta
    )
    assert np.isclose(svi_test_result, svi_expected_result, atol=1e-6)

    K = 927.4449999999999
    forward = 1860.4759107069306
    delta = -0.01
    spot = 1854.8899999999999
    r_d = 0

    sabr_expected_result = 0.04404462322656564
    sabr_test_result = _eq_delta_strike(
        K, "SABR", sample_SABR_data, spot, forward, expiry, r_d, delta
    )
    assert np.isclose(sabr_test_result, sabr_expected_result, atol=1e-6)


def test_estimate_strike_from_delta_overflow_fsolve_fallback():
    delta = 0.01
    r_d = 0
    expiry = 0.7397260273972602
    forward = 27170.792128222605
    spot = 26855.229999999992

    model_params = {
        "sabr_alpha": 0.3804285287938358,
        "sabr_rho": -0.11973330529810278,
        "sabr_volvol": 1.5134161862010826,
    }
    expected_error_message = "math range error"
    expected_result = 3417100.1788604
    with patch("logging.log") as mock_log:
        result_strike = estimate_strike_from_delta(
            model="SABR",
            model_params=model_params,
            s=spot,
            f=forward,
            t=expiry,
            delta=delta,
            rd=r_d,
        )

    mock_log.assert_called_once()
    assert expected_error_message in mock_log.call_args[0][1]
    assert np.isclose(expected_result, result_strike, atol=1e-6)


def test_estimate_strike_from_delta_valueerror_fsolve_fallback(
    sample_SABR_data_bad,
):
    forward = 1912.4689559875164
    spot = 1849.8300000000002
    expiry = 1.0
    delta = 0.03
    r_d = 0
    expected_error_message = "Invalid strike derived from delta"
    sabr_expected_result = 98294.53474263428

    with patch("logging.log") as mock_log:
        sabr_test_result = estimate_strike_from_delta(
            "SABR", sample_SABR_data_bad, spot, forward, expiry, delta, r_d
        )

    mock_log.assert_called_once()
    assert expected_error_message in mock_log.call_args[0][1]
    assert np.isclose(sabr_test_result, sabr_expected_result, atol=1e-6)


def test_negative_estimated_strike(sample_SABR_failed_estimated_strike_data):
    spot = sample_SABR_failed_estimated_strike_data["spot"]
    forward = sample_SABR_failed_estimated_strike_data["forward"]
    expiry = sample_SABR_failed_estimated_strike_data["expiry"]
    r_d = 0
    delta = 0.01

    with patch("logging.error") as mock_error:
        strike = estimate_strike_from_delta(
            "SABR",
            sample_SABR_failed_estimated_strike_data,
            spot,
            forward,
            expiry,
            delta,
            r_d,
        )

    expected_error_message = "Negative strike derived from delta using fsolve fallback, returning absolute strike"
    mock_error.assert_called_once()
    assert expected_error_message in mock_error.call_args[0][0]
    assert strike > 0


@pytest.mark.parametrize(
    "expiry, expected_boundary",
    [
        (6 / 365, CALIBRATION_DELTA_BOUNDARY_FOR_LONG_EXPIRY),
        (2 / 365, CALIBRATION_DELTA_BOUNDARY_FOR_SHORT_EXPIRY),
        (
            4.5 / 365,
            (
                CALIBRATION_DELTA_BOUNDARY_FOR_LONG_EXPIRY
                + CALIBRATION_DELTA_BOUNDARY_FOR_SHORT_EXPIRY  # 4.5 should be halfway between boundaries
            )
            / 2,
        ),
    ],
)
def test_get_calibration_delta_boundary(expiry, expected_boundary):
    result = get_calibration_delta_boundary(expiry)
    assert np.isclose(result, expected_boundary)


class TestSafeDiv:
    def test_division_with_integers(self):
        assert safe_div(10, 2) == 5
        assert safe_div(10, 0) == 0

    def test_division_with_floats(self):
        assert safe_div(9.0, 3.0) == 3.0
        assert safe_div(9.0, 0.0) == 0

    @pytest.mark.parametrize(
        "a, b, expected",
        [
            (np.array([10, 20, 30]), np.array([2, 5, 10]), np.array([5, 4, 3])),
            (np.array([10, 20, 30]), np.array([2, 0, 10]), np.array([5, 0, 3])),
        ],
    )
    def test_division_with_numpy_arrays(self, a, b, expected):
        np.testing.assert_array_equal(safe_div(a, b), expected)

    def test_division_with_pandas_series(self):
        s1 = pd.Series([10, 20, 30])
        s2 = pd.Series([2, 5, 10])
        s3 = pd.Series([2, 0, 10])
        expected1 = np.asarray([5, 4, 3])
        expected2 = np.asarray([5, 0, 3])
        np.testing.assert_array_equal(safe_div(s1, s2), expected1)
        np.testing.assert_array_equal(safe_div(s1, s3), expected2)

    def test_mixed_types(self):
        a = 10
        b = np.array([2, 0, 10])
        expected = np.array([5, 0, 1])
        np.testing.assert_array_equal(safe_div(a, b), expected)
        np.testing.assert_array_equal(safe_div(b, a), np.array([0.2, 0, 1]))


def test_get_bordering_expiries():
    data = {
        "expiry": [0.1, 0.2, 0.3, 0.5, 0.8],
    }
    snap_df = pd.DataFrame(data)

    target_expiry = 0.4
    lower_expiry, upper_expiry = get_bordering_expiries(snap_df, target_expiry)
    assert lower_expiry["expiry"] == 0.3
    assert upper_expiry["expiry"] == 0.5

    target_expiry = 0.9
    lower_expiry, upper_expiry = get_bordering_expiries(snap_df, target_expiry)
    assert lower_expiry["expiry"] == 0.8
    assert upper_expiry is None or pd.isna(upper_expiry["expiry"])

    target_expiry = 0.2
    lower_expiry, upper_expiry = get_bordering_expiries(snap_df, target_expiry)
    assert lower_expiry["expiry"] == 0.1
    assert upper_expiry["expiry"] == 0.3

    target_expiry = 0.05
    lower_expiry, upper_expiry = get_bordering_expiries(snap_df, target_expiry)
    assert lower_expiry is None
    assert upper_expiry["expiry"] == 0.1

    target_expiry = 0.9
    lower_expiry, upper_expiry = get_bordering_expiries(snap_df, target_expiry)
    assert lower_expiry["expiry"] == 0.8
    assert upper_expiry is None


@pytest.mark.parametrize(
    "is_dataframe, current_expiry, expected_result_dict",
    [
        # fmt: off
        (
            True,
            1.5,
            {"expiry": 1.0, "svi_a": 0.1, "svi_b": 0.5, "svi_rho": 0.2, "svi_m": 0.3, "svi_sigma": 0.4, "threshold": 1.0},
        ),
        (
            True,
            2.5,
            {"expiry": 2.0, "svi_a": 0.2, "svi_b": 0.6, "svi_rho": 0.3, "svi_m": 0.4, "svi_sigma": 0.5, "threshold": 1.0},
        ),
        (
            False,
            3.6,
            {"expiry": 4.0, "svi_a": 0.4, "svi_b": 0.8, "svi_rho": 0.5, "svi_m": 0.6, "svi_sigma": 0.7, "threshold": 1.0},
        ),
        (
            False,
            4.0,
            {"expiry": 4.0, "svi_a": 0.4, "svi_b": 0.8, "svi_rho": 0.5, "svi_m": 0.6, "svi_sigma": 0.7, "threshold": 1.0},
        ),
        (
            False,
            0.9,
            {"expiry": 1.0, "svi_a": 0.1, "svi_b": 0.5, "svi_rho": 0.2, "svi_m": 0.3, "svi_sigma": 0.4, "threshold": 1.0},
        ),
        # fmt: on
    ],
)
def test_get_closest_expiry_ref_params(
    is_dataframe: bool,
    current_expiry: float,
    expected_result_dict: SviCalibrationReferenceParams,
    sample_get_closest_expiry_ref_params: list[SviParams],
) -> None:
    calibrated_params: list[SviParams] | pd.DataFrame

    if is_dataframe:
        calibrated_params = pd.DataFrame(sample_get_closest_expiry_ref_params)
    else:
        calibrated_params = sample_get_closest_expiry_ref_params
    result = get_closest_expiry_ref_params(calibrated_params, current_expiry)
    assert result == expected_result_dict


@pytest.mark.parametrize("K", [np.array([133232.7587310483]), 133232.7587310483])
def test_eq_delta_strike_spline_types(
    K: np.ndarray | float, sample_spline_eq_delta_strike_data: CubicSpline
) -> None:
    s = 66616.37936552415
    f = 73942.5
    t = 0.93698630137
    r_d = 0
    delta = 0.03
    result = _eq_delta_strike(
        K=K,
        model="spline",
        model_params=sample_spline_eq_delta_strike_data,
        s=s,
        f=f,
        t=t,
        r_d=r_d,
        delta=delta,
    )
    expected_result = -0.2979694690356288
    assert isinstance(result, float)
    assert np.isclose(result, expected_result)


@pytest.mark.parametrize(
    "input_fixture, model",
    [
        ("sample_svi_vectorized_data", "SVI"),
        ("sabr_test_data_vectorized", "SABR"),
    ],
)
def test_model_vol_vectorized(
    input_fixture: str, model: str, request: pytest.FixtureRequest
) -> None:
    input_model_data = request.getfixturevalue(input_fixture)
    model_keys = set(input_model_data.keys()) - {"forward", "expiry"}
    model_params = {key: input_model_data[key].values for key in model_keys}
    model_prefix = model.lower() + "_"
    model_dict = {
        "SVI": svi_vol,
        "SABR": sabr_vol,
    }
    if model == "SABR":
        expiry_param = "t"
    elif model == "SVI":
        expiry_param = "exp"

    result = model_vol(  # type: ignore
        strike=input_model_data["strikes"].values,
        forward=input_model_data["forward"].values,
        exp=input_model_data["expiry"].values,
        model=model,
        model_params=model_params,
    )

    assert isinstance(result, np.ndarray)  # resolves mypy non-iterable error below
    for row_result, (idx, row_test) in zip(result, input_model_data.iterrows()):
        test_data_strikes = row_test["strikes"]
        model_kwargs = {
            key.replace(model_prefix, ""): row_test[key]
            for key in row_test.keys()
            if key.startswith(model_prefix)
        }

        # Add beta only if it's the SABR model
        if model == "SABR":
            model_kwargs["beta"] = 1

        expected_vol_result = model_dict[model](  # type: ignore
            k=np.array(test_data_strikes),
            f=row_test["forward"],
            **{expiry_param: row_test["expiry"]},
            **model_kwargs,
        )
        assert np.all(
            np.isclose(
                row_result,
                expected_vol_result,
                atol=1e-10,
            ),
        )
    return


def test_get_atm_deltas_from_boundaries() -> None:
    min_delta = -0.1
    max_delta = 0.1
    expected = [
        -0.1,
        -0.15,
        -0.2,
        -0.25,
        -0.3,
        -0.35,
        -0.4,
        -0.45,
        -0.5,
        0.5,
        0.45,
        0.4,
        0.35,
        0.3,
        0.25,
        0.2,
        0.15,
        0.1,
    ]
    assert (
        get_atm_deltas_from_boundaries(
            put_wing_delta=min_delta, call_wing_delta=max_delta
        )
        == expected
    )

    min_delta = -0.3
    max_delta = 0.2
    expected = [
        -0.3,
        -0.35,
        -0.4,
        -0.45,
        -0.5,
        0.5,
        0.45,
        0.4,
        0.35,
        0.3,
        0.25,
        0.2,
    ]
    assert (
        get_atm_deltas_from_boundaries(
            put_wing_delta=min_delta, call_wing_delta=max_delta
        )
        == expected
    )


def test_indices_have_haps():
    # Test consecutive values - should return False
    assert _indices_have_gaps([1, 2, 3, 4, 5]) is False
    assert (
        _indices_have_gaps([5, 3, 4, 1, 2]) is False
    )  # unsorted but consecutive when sorted

    # Test with gaps - should return True
    assert _indices_have_gaps([1, 3, 4, 5]) is True  # missing 2
    assert _indices_have_gaps([1, 2, 4, 5]) is True  # missing 3
    assert _indices_have_gaps([1, 5]) is True  # big gap
    assert _indices_have_gaps([2, 4, 6]) is True  # all gaps

    # Test edge cases - should return False
    assert _indices_have_gaps([]) is False  # empty list
    assert _indices_have_gaps([5]) is False  # single element
    assert _indices_have_gaps("not_a_list") is False  # not a list
    assert _indices_have_gaps(None) is False  # None input

    # Test duplicates - should return False (consecutive after sorting)
    assert _indices_have_gaps([1, 2, 2, 3]) is False
    assert _indices_have_gaps([1, 1, 2, 3]) is False
