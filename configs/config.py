import os

import boto3
from aiobotocore.config import AioConfig
from aiobotocore.session import AioSession
from utils_aws import get_ssm_params

# Cognito params
REGION = os.environ.get("REGION", "eu-west-2")
AIOCONFIG = AioConfig(
    max_pool_connections=200,
    retries={"total_max_attempts": 50, "mode": "legacy"},
    region_name=REGION,
)
AIOBOTOCORE_SESSION = AioSession()
USERPOOL_ID = os.environ.get("USERPOOL_ID", "eu-west-2_lmZwWX7J2")
APP_CLIENT_ID = os.environ.get("APP_CLIENT_ID", "2kg6m9frchqhoqd1ipgaobjkaf")
DISABLE_EXPIRY_CHECK = False
API_KEY_LIMIT = 25
BLOCKSCHOLES_INTERNAL_API_KEY_PARAM_NAME = (
    "/restAPI/blockscholesInternal/api_key"
)
SSM_CLIENT = boto3.client("ssm")
SSM_PARAMS = get_ssm_params(
    SSM_CLIENT, [BLOCKSCHOLES_INTERNAL_API_KEY_PARAM_NAME]
)
BLOCKSCHOLES_INTERNAL_API_KEY = SSM_PARAMS[
    BLOCKSCHOLES_INTERNAL_API_KEY_PARAM_NAME
]

SETTLEMENT_PRICE_LAMBDA = os.environ.get(
    "SETTLEMENT_PRICE_LAMBDA",
    "SettlementPriceCalcLambda-settlementPriceCalcFunct-1KQ3tVWbKdSe:staging",
)
AGGREGATE_LAMBDA = os.environ.get(
    "AGGREGATE_LAMBDA",
    "AggregateApiLambdaFunctio-aggregateAPICalcFunction-4t4hdZJ3aZck:staging",
)
PERMISSION_LAMBDA = os.environ.get(
    "PERMISSION_LAMBDA",
    "PermissionManagerLambdaFu-PermissionManagerLambdaF-SQe0PLH9RnDF:staging",
)
S3_BUCKET = os.getenv("S3_BUCKET", "blockscholes-test-staging")
FREQUENCY_TO_DELTA_DAYS = {
    "1h": 31,
    "1m": 1,
}
