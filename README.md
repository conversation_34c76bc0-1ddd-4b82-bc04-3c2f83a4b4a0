# volSmileCalc

Lambda to create volatility smiles

# Accepted input format and parameters

```
{
  "body": {
    "calc": {
      "version": "1.0.0",
      "type": "volSmileCalc",
      "args": {
        "consistent_read": false,
        "exchanges": [
          "bybit"
        ],
        "currencies": [
          "BTC",
          "ETH"
        ],
        "models": [
          "SVI"
        ],
        "types": [
          "params",
          "smile",
          "moneyness",
          "butterfly",
          "skew",
          "strike"
        ],
        "include_listed_expiries": true,
        "estimate_params": true,
        "smooth": true,
        "debug": false,
        "frequency": {
          "interval": "hour",
          "periods": 1
        },
        "date_range": {
          "absolute": {
            "start": "2024-07-27T00:00:00Z",
            "end": "2024-07-30T13:00:00Z"
          }
        }
      },
      "output_options": {
        "version": "",
        "type": "csv",
        "format": "timeseries",
        "do_store": {
          "s3_bucket": "blockscholes-test-staging",
          "s3_object_suffix": "",
          "s3_object_prefix": ""
        }
      }
    }
  }
}

```

AWS ECR log in : aws ecr get-login-password --region eu-west-2 | docker login --username AWS \
--password-stdin 844157133169.dkr.ecr.eu-west-2.amazonaws.com

# General Notes

# Calibration

This calc retrieves the model parameters for traded expiries based on a specified model and exchange pairing. These
model parameters are produced either by the `modelParametersCalc` or the `compositeCalc`. From these traded expiries, we
interpolate and extrapolate to form our standard grid of model parameters, defined in `utils_calc`. In the live system,
users are able to arbitrarily subscribe to non-traded arbitrary expiries that are arbitrarily subscribed to are also
interpolated/extrapolated in this process.

Using the above model parameters, we generate volatility surfaces for each vol space defined by `VolSurfaceCalcType`.
For the moneyness and delta calc types, we generate volatility surfaces based on a standard moneyness and delta grid.
Similarly, for the strike surface, we look for the traded strikes per expiry, we do not generate a strike surface for
constant maturities as there are no market-defined strikes.

In the live system, these surfaces could include any arbitrarily subscribed expiries, as well as
arbitrarily subscribed moneyness/delta/strikes levels specified by the client, the subscription/communication logic
between the WebSocket and calc is handled in the `FlexConsumerSubscriptionManager`. Its role is to pass the necessary
additional parameters each time we call the calc.

# Post calibration processing

# Smoothing Pipeline

The smoothing pipeline is designed to detect and correct any mis-calibrations that occur as a result of the params
surface and volatility surface generation. We use the following techniques to address data retrieval:

## Lookback historical data

Different frequencies (1h, 1m, live) have minimum data point requirements in order for smoothing to take place

- Preloading Historical Data - Lambda or first iteration of live version or backfill mode
    - Historical Listed expiries and Standard Constant tenors are loaded in the `retrieve_data` function.
    - When a warm cache is available (in the live system, or in backfill mode), the system retrieves lookback data from
      dataframe passed in from its previous iterations.

### Qualified names that have no historical data (newly listed/subscribed, or a problem with data storage in dynamo).

The system includes logic (`lookback_and_append`) to populate lookback data for both listed and arbitrary expiries that
do not have sufficient data in their lookback to gain enough confidence in our smoothing process:

`_populate_lookback_for_listed_expiries`,
`_populate_lookback_for_arbitrary_expiries`

For each expiry that does not have sufficient data points for smoothing, the general logic is to use the closest expiry
with enough data. When populating expiries, we consider both Standard Constant Tenors and Listed Expiries
(both with sufficient datapoint
to fill the 'hole' in the lookback) that were successfully calibrated in the chunk that is being processed.

When populating Arbitrary expiries, in addition to the above standard constant tenors and listed expiries that are
normally considered, we also consider arbitrary expiries that other users are subscribing to for use in 'plugging the
hole'.

**Their implementations differ ever so slightly due to the fact that we cannot make the assumption that qualified names
exist for all calc_types
e.g, someone may only be subscribed to `.moneyness` for an arbitrarily subscribed value, but not `.strike`** as the
strike calc_type is optional.

We do the same for params as we want to make them available for the below **_Arbitrary Values population_** section.

## Arbitrary Values population in the lookback

### Overview

The lookback functionality for arbitrary values focuses on dynamically generating historical volatilities for
strikes/moneyness/deltas for the different calc_types (strike, moneyness, delta) that lack sufficient historical data.
This ensures that the system can effectively smooth and recalibrate volatilities, even for newly subscribed
strikes/moneyness/deltas (in the live system) or recently exchange-listed strikes.

### Key Rationales

1. Dynamic History Generation: The system uses the history of model parameters to calculate a historical dataset of
   volatilities. This approach allows for the smoothing of volatility surfaces even when the direct historical data for
   certain calculation types is sparse or missing.
2. Exclusion of Recalibration Spikes: Parameters containing recalibration spikes are excluded to maintain the integrity
   of the historical data. This ensures that only reliable data is used for generating historical volatilities. See
   the `Recalibration Spike Catching logic` section for more details

### High level steps

The `identify_and_populate_arbitrary_values_for_smoothing` function is the main entry point for this process.

1. **Check for Unique Indices:** The function ensures that the `current_and_lookback_results` DataFrame has unique
   indices to avoid conflicts during the population process.
2. **Extract Calculation Types and Parameters:** For each calculation type (strike, moneyness, delta), the function
   extracts relevant data and parameters. It groups the data by model type to handle each model separately.
3. **Calculate Missing Values:**
    - The function identifies missing calc_type values for each qualified name (qn) in the DataFrame.
    - It uses the model parameters to estimate the missing volatilities
4. **Merge and Populate Data:** The calculated volatilities are merged back into the main DataFrame, populating the
   historical data for the identified calculation types.

#### Special Considerations

- Delta (Smile)

    - To get historical delta implied vols, we firstly need to calculate the equivalent strike for that timestamp for
      which we
      need the model parameters, spot, forwards and expiry to feed into our `estimate_strike_from_delta` function.
      After we have the strikes for each timestamp, we are then able to calculate the vols.
    - In extreme cases, the strike estimation process can fail. To handle these cases, we pre-compute a fallback strike
      from
      the **_EARLIEST_** datapoint we have successfully calculated a strike for.

## Smoothing

Smoothing is performed on the precomputed volatility surfaces. Each strike, money and delta is smoothed with respect to
its individual vol history.

Configuration Setup:

- The `construct_smoothing_config` function sets up the necessary configuration for smoothing for each
  exchange-frequency pair, including parameters like the z-score span, smoothing span, and z-score limits.

- The system includes logic to handle cases where data points exceed the smoothing count limit, ensuring that excessive
  smoothing does not occur. It also considers potential inconsistencies between backfill and scheduled runs, addressing
  minor divergences that may arise.


## Recalibration

Overview
The parameter estimation process involves recalibrating the model parameters using the smoothed volatility data. This
ensures that the model parameters are consistent with the behavior of the smoothed vols.

1. Initialization and Configuration:

The `estimate_params_helper` function iterates through the smoothed results, extracting the necessary configuration for
each qualified name prefix

2. Extract Relevant Data:

The `estimate_new_params` function begins by extracting relevant moneyness and delta values using the
`get_relevant_moneys_and_deltas` function. The `utils_calc` package defines delta boundaries depending on the expiry,
wherein strikes whose deltas fall outside of this boundary, are excluded from calibration.

These delta boundaries are also used to select "relevant moneyness range" whose strikes are used for calibration.

_**Edge Case:**_
In the backfill mode, the forward price, expiry, and spot price can differ significantly across the chunk of data
being processed. The `get_relevant_moneys_and_deltas` uses the relevant money's and delta's estimated from the latest
timestamp of the chunk
and uses that across the whole chunk that is being processed.

3. Recalibration Process:

The re-estimation of model parameters from smoothed vols only considers the moneyness and delta vol spaces.
The function identifies timestamps where both the moneyness and delta volatilities have been smoothed.
For these overlapping indices we:

- Recalibrate model parameters for both volatility spaces
- Selects the set of parameters with the lowest Mean Absolute Error (MAE) to use as the canonical set going forward.

The rationale behind this is to select the model parameters that have fit the smoothed volatilities the best.

For non-overlapping indices, the function only performs one set of recalibration using only the vol-space volatilities
that were smoothed

## Recalibration Spike Catching logic

In optimal scenarios, the post-smoothing model calibration logic should be able to closely capture the newly smoothed
volatilities. Sometimes, the calibration process is unable to correctly capture the shape of the smoothed volatilities
and may itself introduce
some recalibration errors.

The rationale behind this spike catching logic is to identify model parameters which, after calibration, fail to produce
implied volatilities
that fall within our z-score limits w.r.t its historical volatility series

- In the live system, the parameters that have been flagged as containing a recalibration spike are excluded
  from being put on the stream

The `_add_spiked_params_flag` considers the volspace that each re-calibrated set of modelparmeters was recalibrated on.
The function calculates the implied vols for the vols that it was calibrated on, and calculates the z-scores relative to
their histories.

In addition to this, we also exclude params whose R2 post smoothing (R2_smoothcalib) is less than a defined threshold;
`CALIB_R2_THRESHOLD`. 