# ssm-param-generator

Creates SSM params based on a series of input folders and JSON files.

Each input folder denotes a set of input data and each file an asset class for that particular folder:
- /input
- /input/indices
- /input/indices/spot.json
- /input/indices/perpetuals.json
- ...
- /input/tgbot
- /input/tgbot/spot.json
...

The files contain a JSON object with a top level "path" and an array of "items".
The path is the base path used to create or update the SSM params generated from the particular file.
The eventual parameter name will be automatically generated based on the path, asset class, and an incremental number:
- `/config/price_indices_generated/perpetual1`
- `/config/price_indices_generated/perpetual2`
- ...

The items in the items array are separated by the relevant top level keys for the particular objects.
For instance the indices objects are separated by these keys:

             "asset_class":"spot",
             "index_currency":"USD",
             "allow_wider_range":true,
             "output_frequency_ms":1000,

Each object contains an array of components that share the same top level keys:

       {
          "spot":{
             "asset_class":"spot",
             "index_currency":"USD",
             "output_frequency_ms":1000,
             "components":[
                {
                   "exchange":"bitfinex",
                   "base":"USDC",
                   "quote":"USD"
                },
                ...
             ]
          }
       }

A file can inherit and optionally  extend data from another file:
To inherit from a another file, use the "extends" top level key and specify the path to the file to inherit:

  `"extends": "input/indices/spot.json"`,

To extend the inherited file just include objects in the items array as normal.

Filters can be applied (as a top level object) to the inherited data if needed.

   `"filters": {
      "exchanges": ["pendle-ethereum", "v3uniswap-ethereum", "kyberswap-ethereum", "zerox-ethereum", "curve-ethereum", "oneinch-ethereum", "kyberswap-bsc", "jupiter-solana"],
      "global_exchange_weights": false
   }`

Four filters are available and are globally applied to the inherited data.
These three filters are exclusionary:
   `"exchanges": []`
   `"base": []`
   `"quote": []`

The global weights filter can be used to exlude or include any inherited global weights values:
   `"global_exchange_weights": true/false`

## Development Setup

### Getting started with standard uv workflow:

- install: `pip install uv`
- lock deps based on `pyproject.toml`, creating a separate lock file for reproducibility: `uv lock`
- install deps: `uv sync --all-extras --dev`

### Using pre-commit hooks:

- install tool: `pip install pre-commit`
- install hook: `pre-commit install` (will refer to `.pre-commit-config.yaml` - now this script will be run before every commit)
- running the hook ad-hoc: `pre-commit run --all-files` (if want to run all the checks/actions without waiting for a commit to trigger it)

## Modifying the SSM Params:

1.  Add/Change/Delete the component entries in the relevant JSON files under the `input/` directory and/or create a new item if the existing key values are not present. (objects with the same key values are coalesced when the parameters are created).
2.  Commit your changes to your branch. The `generate-ssm-params-yaml` pre-commit hook will automatically regenerate the `output/params_yaml/params.yaml` file based on your changes and include it in the commit.
3.  Create Pull Request.
4.  Updated params are automatically deployed on "Staging" environment when merged to `main`. To deploy without merging run `Staging London Deploy` workflow.
5.  Use `Create Production Release` workflow to create rollout PRs, which when merged will rollout updated params on prod.
6.  (Optional) Services cache the ssm params for a defined time, Restart your target service to run on updated params immediately.
7.  Profit.