import logging
from abc import ABC, abstractmethod
from collections.abc import Awaitable, Callable
from decimal import Decimal
from typing import Literal

from datagrabber import CatalogAssetType
from hexbytes import HexBytes
from pydantic import BaseModel, ConfigD<PERSON>, field_validator
from utils_oracle import <PERSON>fig<PERSON><PERSON><PERSON>, FeedIndex, Network

ChainData = tuple[list[int], list[int]]


class BaseUpdater(BaseModel):
    updater_type: str


class ScheduledUpdaterTarget(BaseUpdater):
    updater_type: Literal["last_data_schedule"]
    frequency_sec: int


class PxDeviationUpdaterTarget(BaseUpdater):
    updater_type: Literal["px_deviation"]
    threshold: float


FeedUpdater = ScheduledUpdaterTarget | PxDeviationUpdaterTarget


class Target(BaseModel):
    asset_class: CatalogAssetType
    exchange: list[str]
    base_asset: list[str]


class ChainConfigModel(BaseModel):
    id: Network
    enable: bool
    version: int
    decimals: int
    feed_updaters: list[FeedUpdater]
    targets: list[Target]

    @field_validator("id", mode="before")
    @classmethod
    def validate_network(cls, v: int) -> Network:
        return Network(v)


class FeedFilter(BaseModel):
    feed_types: list[str] = []
    exchanges: list[str] = []
    base_asset: list[str] = []

    @classmethod
    def from_target(cls, target: Target) -> "FeedFilter":

        related_feed_types_for_assets = ConfigLoader.get_feed_definitions().related_catalog_asset_lookup.get(
            "qualified_name"
        )
        allow_feed_types = []
        if related_feed_types_for_assets:
            allow_feed_types = related_feed_types_for_assets.get(
                target.asset_class, []
            )
        if not allow_feed_types:
            logging.error(
                f"Failed to load related_catalog_asset_lookup information for {target.asset_class}: {related_feed_types_for_assets}"
            )
        return cls(
            feed_types=allow_feed_types,
            exchanges=target.exchange,
            base_asset=target.base_asset,
        )

    def accept_feed(self, feed_index: FeedIndex) -> bool:
        decoded_feed = feed_index.feed.get_decoded_feed()
        if self.feed_types and decoded_feed.feed_type not in self.feed_types:
            return False
        for param_name, enum_value in decoded_feed.parameters.enumerable:
            if (
                param_name == "ExchangeEnum"
                and self.exchanges
                and enum_value.lower() not in self.exchanges
            ):
                return False
            if (
                param_name == "BaseAssetEnum"
                and self.exchanges
                and enum_value not in self.base_asset
            ):
                return False
        return True


FeedFiltersByChainID = dict[int, list[FeedFilter]]


class FeedData(BaseModel):
    value: Decimal
    timestamp: int

    def on_chain_data(self, decimals: int) -> tuple[int, int]:
        feed_value = int(self.value * pow(10, decimals))
        return feed_value, int(self.timestamp / 1000)


class FeedValueUpdate(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    feed_key: HexBytes
    value: Decimal
    timestamp: int


# Observer Pattern Interfaces
class FeedValueUpdateObserver(ABC):
    """Abstract base class for feed update observers."""

    @abstractmethod
    async def on_feed_value_updates(
        self, updates: list[FeedValueUpdate]
    ) -> None:
        pass


UpdateDataOnChainCallback = Callable[[list[FeedIndex]], Awaitable[None]]
