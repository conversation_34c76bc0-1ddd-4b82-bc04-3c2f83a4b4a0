import asyncio
import logging
import time
from typing import Any

import utils_general
import utils_oracle
from block_stream import Agent
from block_stream.channel import Channel
from datagrabber import CatalogItem
from utils_oracle import ConfigLoader

from feed_indexer.catalog_filter_manager import CatalogFilterManager
from feed_indexer.config import (
    ALARMS_THRESHOLD_SCALING_FACTOR,
    DEFAULT_ALARM_THRESHOLD,
    ENVIRONMENTS,
    SECONDS_PER_HOUR,
    Config,
    load_indexer_target_config,
)
from feed_indexer.database_handler import DatabaseHandler
from feed_indexer.exceptions import SQSMessageCreationError
from feed_indexer.feed_cleanup_manager import FeedCleanupManager
from feed_indexer.feed_indexer import (
    FeedIndexer,
)
from feed_indexer.metrics import (
    initialize_metrics,
    load_max_feed_index_alarms_thresholds,
)
from feed_indexer.sqs import FeedIndexerSQSSender
from feed_indexer.typings import (
    AuxDetails,
    ChainFeedState,
    ChainIdGroupedFeedIndices,
    ChainToAuxDetails,
    FeedIndexerInternalFeeds,
    FeedIndexerMessageTypes,
)
from feed_indexer.utils import (
    chunked,
    create_message_group_id,
    get_run_environment,
    group_feed_indices_by_chain_id,
    log_feed_state_summary,
)


async def _create_batches_and_send_batch_message(
    feeds_by_chain: ChainIdGroupedFeedIndices,
    chain_aux_details: ChainToAuxDetails,
    sqs_sender: FeedIndexerSQSSender,
    message_type: FeedIndexerMessageTypes,
    feed_indices_batch_size: int,
) -> None:
    for chain_id, fi_list in feeds_by_chain.items():
        feed_indices_sorted = sorted(fi_list, key=lambda fi: fi.index)
        feeed_indices_batches = list(
            chunked(feed_indices_sorted, feed_indices_batch_size)
        )

        for idx, feed_indices in enumerate(feeed_indices_batches, start=1):
            logging.info(
                f"Processing batch {idx}/{len(feeed_indices_batches)} for {chain_id=} with {len(feed_indices)} indices"
            )
            await _create_and_send_batch_message(
                sqs_sender=sqs_sender,
                batch_idx=idx,
                feed_indices_batch=feed_indices,
                chain_id=chain_id,
                version=chain_aux_details[chain_id].version,
                message_type=message_type,
            )

    return


async def _create_and_send_batch_message(
    sqs_sender: FeedIndexerSQSSender,
    batch_idx: int,
    feed_indices_batch: list[utils_oracle.FeedIndex],
    chain_id: utils_oracle.Network,
    version: int,
    message_type: FeedIndexerMessageTypes,
) -> None:
    try:
        # Construct the message for the current batch
        sqs_message = sqs_sender.construct_message(
            feed_indices_batch, message_type
        )
    except SQSMessageCreationError:
        logging.exception(
            f"Error while creating message for batch {batch_idx}, skipping."
        )
        return
    try:
        if get_run_environment() in ENVIRONMENTS:
            await sqs_sender.send_message(
                message=sqs_message,
                message_group_id=create_message_group_id(chain_id, version),
            )
            logging.info(
                "Sent message for batch %s: message_type=%s, indices_count=%s, sqs_message=%s",
                batch_idx,
                sqs_message.message_type,
                len(feed_indices_batch),
                sqs_message.model_dump_json(),
            )
        else:
            logging.info(
                "Local run detected. Skipping sending message for batch %s: message_type=%s, indices_count=%s",
                batch_idx,
                sqs_message.message_type,
                len(feed_indices_batch),
            )
    except Exception:
        logging.exception(f"Failed to send message for batch {batch_idx}")
        return

    return


async def _process_initial_feed_indices(
    feed_indexer: FeedIndexer,
    sqs_sender: FeedIndexerSQSSender,
    chain_aux_details: ChainToAuxDetails,
    message_batch_size: int,
) -> None:
    """
    Processes 'Initialized' feed indices for each chain for both add and removal states,
    then sends out the corresponding batch messages.
    """
    startup_dict = {
        utils_oracle.FeedState.INITIALISED_ADD: FeedIndexerMessageTypes.INDEX_ASSIGNMENT,
        utils_oracle.FeedState.INITIALISED_REMOVE: FeedIndexerMessageTypes.INDEX_REMOVAL,
    }

    for feed_state, message_type in startup_dict.items():
        for chain_id in chain_aux_details.keys():
            try:
                chain_feed_indices: ChainFeedState = (
                    await feed_indexer.get_chain_feed_indices(
                        chain_id=chain_id, states=(feed_state,)
                    )
                )
                feed_indices = list(chain_feed_indices[feed_state].values())
                if not feed_indices:
                    logging.info(
                        "Skipping sending message_type=%s, no feed indices found in feed_state=%s, chain_id=%s",
                        message_type,
                        feed_state,
                        chain_id,
                    )
                await _create_batches_and_send_batch_message(
                    feeds_by_chain={chain_id: feed_indices},
                    chain_aux_details=chain_aux_details,
                    sqs_sender=sqs_sender,
                    message_type=message_type,
                    feed_indices_batch_size=message_batch_size,
                )
            except Exception:
                logging.exception(
                    "Error retrieving feed indices on startup, state=%s, chain_id=%s",
                    feed_state,
                    chain_id,
                )


async def _process_catalog_entries(
    feed_indexer: FeedIndexer,
    chain_aux_details: ChainToAuxDetails,
    sqs_sender: FeedIndexerSQSSender,
    message_batch_size: int,
    instruments: list[CatalogItem],
    catalog_filter_manager: CatalogFilterManager,
) -> None:
    """
    Processes each catalog instrument:
      - Determines which chain(s) match the record.
      - Processes the record and aggregates feed indices.
      - Groups feed indices by chain and sends them as batch messages.
    """
    all_feed_indices = []
    for instrument in instruments:
        instrument["q"] = instrument.pop("qualified_name")  # type: ignore
        matching_chain_ids = catalog_filter_manager.accept(instrument)
        if matching_chain_ids is not None:
            feed_indices = await feed_indexer.process_record(
                record=instrument,  # type: ignore
                chain_ids=matching_chain_ids,
            )
            if feed_indices is not None:
                all_feed_indices.extend(feed_indices)

    feeds_by_chain = group_feed_indices_by_chain_id(all_feed_indices)
    await _create_batches_and_send_batch_message(
        feeds_by_chain=feeds_by_chain,
        chain_aux_details=chain_aux_details,
        sqs_sender=sqs_sender,
        message_type=FeedIndexerMessageTypes.INDEX_ASSIGNMENT,
        feed_indices_batch_size=message_batch_size,
    )


async def _on_startup(
    feed_indexer: FeedIndexer,
    sqs_sender: FeedIndexerSQSSender,
    catalog_filter_manager: CatalogFilterManager,
    chain_aux_details: ChainToAuxDetails,
    message_batch_size: int,
) -> None:
    """
    Startup function that performs the following:
      1. Processes initial feed indices (both add and remove states) and sends corresponding messages.
      2. Retrieves catalog entries (and optional constant maturity entries) from the database.
      3. Processes catalog entries and sends index assignment messages for new entries.
    """
    try:
        # Process initial feed indices based on feed state.
        await _process_initial_feed_indices(
            feed_indexer=feed_indexer,
            sqs_sender=sqs_sender,
            chain_aux_details=chain_aux_details,
            message_batch_size=message_batch_size,
        )

        logging.info("Processing valid catalog entries from DynamoDB")
        # Retrieve instruments from the DB (plus constant maturity instruments, if any)
        instruments = (
            catalog_filter_manager.get_instruments_with_constant_maturity()
        )

        # Process catalog entries and send index assignment messages.
        await _process_catalog_entries(
            feed_indexer=feed_indexer,
            catalog_filter_manager=catalog_filter_manager,
            chain_aux_details=chain_aux_details,
            sqs_sender=sqs_sender,
            message_batch_size=message_batch_size,
            instruments=instruments,
        )

        logging.info("Finished processing catalog records from DynamoDB")

    except Exception:
        logging.exception("Error while sending messages on startup")

    logging.info("FeedIndexer startup complete")


async def _cleanup_expired_feeds_and_send_message(
    feed_indexer: FeedIndexer,
    sqs_sender: FeedIndexerSQSSender,
    database_handler: DatabaseHandler,
    chain_to_aux_details: ChainToAuxDetails,
    message_batch_size: int,
) -> None:
    for chain_id, aux_details in chain_to_aux_details.items():
        expired_chain_feed_state = (
            await feed_indexer.get_feeds_to_remove_for_chain(chain_id)
        )
        if not expired_chain_feed_state:
            logging.info(f"No expired feeds for {chain_id=}")
            continue

        expired_feed_keys = list(expired_chain_feed_state.keys())
        expired_feed_indices = list(expired_chain_feed_state.values())

        # safe assertion to be sure
        confirmed_add = await feed_indexer.get_chain_feed_indices(
            chain_id=chain_id, states=(utils_oracle.FeedState.CONFIRMED_ADD,)
        )
        confirmed_add_key_to_fi = confirmed_add[
            utils_oracle.FeedState.CONFIRMED_ADD
        ]
        if not all(
            _key in confirmed_add_key_to_fi for _key in expired_feed_keys
        ):
            missing_keys = [
                key
                for key in expired_feed_keys
                if key not in confirmed_add_key_to_fi
            ]
            key_to_decoded_feed = {
                key: confirmed_add_key_to_fi[key].feed.get_decoded_feed()
                for key in missing_keys
            }
            logging.error(
                f"Feed Keys not in feed_state={utils_oracle.FeedState.CONFIRMED_ADD}, {key_to_decoded_feed}"
            )

            # remove missing keys from expired_feed_indices so we don't update their state in the DB and can investigate
            cleaned_expired_chain_feed_state = {
                key: fi
                for key, fi in expired_chain_feed_state.items()
                if key not in missing_keys
            }
            expired_feed_keys = list(cleaned_expired_chain_feed_state.keys())
            expired_feed_indices = list(
                cleaned_expired_chain_feed_state.values()
            )

        await database_handler.update_feeds_state(
            feed_indices=expired_feed_indices,
            state=utils_oracle.FeedState.INITIALISED_REMOVE,
        )

        await _create_batches_and_send_batch_message(
            feeds_by_chain={chain_id: expired_feed_indices},
            chain_aux_details=chain_to_aux_details,
            sqs_sender=sqs_sender,
            message_type=FeedIndexerMessageTypes.INDEX_REMOVAL,
            feed_indices_batch_size=message_batch_size,
        )

        await feed_indexer.clean_up_feeds_for_chain(
            feed_keys_to_remove=expired_feed_keys,
            chain_id=chain_id,
            chain_decimal=aux_details.decimals,
        )

    return


async def _process_record_and_send_message(
    sqs_sender: FeedIndexerSQSSender,
    feed_indexer: FeedIndexer,
    record: Any,
    matching_chain_ids: list[utils_oracle.Network],
    chain_aux_details: ChainToAuxDetails,
    message_batch_size: int,
) -> None:
    # todo: update function to take a list of records
    feed_indices = await feed_indexer.process_record(record, matching_chain_ids)

    if feed_indices is not None:
        feeds_by_chain = group_feed_indices_by_chain_id(feed_indices)

        await _create_batches_and_send_batch_message(
            feeds_by_chain=feeds_by_chain,
            chain_aux_details=chain_aux_details,
            sqs_sender=sqs_sender,
            message_type=FeedIndexerMessageTypes.INDEX_ASSIGNMENT,
            feed_indices_batch_size=message_batch_size,
        )

    return


async def _run(
    sqs_sender: FeedIndexerSQSSender,
    feed_indexer: FeedIndexer,
    catalog_channel: Channel,
    chain_aux_details: ChainToAuxDetails,
    message_batch_size: int,
    catalog_filter_manager: CatalogFilterManager,
) -> None:
    logging.info("Listening to stream for catalog records")

    async for record in catalog_channel:
        try:
            matching_chain_ids = catalog_filter_manager.accept(record)
            if matching_chain_ids is not None:
                utils_general.log_bsdebug(
                    "Valid record for chain IDs received, %s",
                    matching_chain_ids,
                )

                await _process_record_and_send_message(
                    sqs_sender=sqs_sender,
                    feed_indexer=feed_indexer,
                    record=record,
                    matching_chain_ids=matching_chain_ids,
                    chain_aux_details=chain_aux_details,
                    message_batch_size=message_batch_size,
                )

        except Exception as e:
            logging.error(f"Error processing record: {e}")


async def _get_internal_state_from_db(
    database_handler: DatabaseHandler,
    chain_aux_details: ChainToAuxDetails,
) -> FeedIndexerInternalFeeds:
    internal_state: FeedIndexerInternalFeeds = utils_general.nested_dict()
    # When assigning an index, the FI considers all states in its internal state as active.
    # We therefore skip loading up this state so we can re-use the indices of feeds in this state
    states: list[utils_oracle.FeedState] = [
        feed_state
        for feed_state in utils_oracle.FeedState.__members__.values()
        if feed_state != utils_oracle.FeedState.CONFIRMED_REMOVE
    ]

    for chain_id in chain_aux_details.keys():
        chain_version = chain_aux_details[chain_id].version

        state_to_feed_indices = (
            await database_handler.get_feed_indices_by_state_for_chain_id(
                chain_id=chain_id, version=chain_version, states=states
            )
        )

        if state_to_feed_indices:
            for feed_state, feed_indices in state_to_feed_indices.items():
                for feed_index in feed_indices:
                    # todo: update so that we do not have to generate feed_key each time
                    feed_key = utils_oracle.get_feed_key(feed_index.feed)
                    internal_state[chain_id][feed_state][feed_key] = feed_index

    log_feed_state_summary(internal_state)

    return internal_state


def _create_periodic_scheduler_task(
    feed_indexer: FeedIndexer,
    sqs_sender: FeedIndexerSQSSender,
    database_handler: DatabaseHandler,
    chain_aux_details: ChainToAuxDetails,
    message_batch_size: int,
    cleanup_interval_sec: int = SECONDS_PER_HOUR * 8,
    replace_interval_sec: int = SECONDS_PER_HOUR * 8,
) -> asyncio.Task[None]:
    """
    Creates the background scheduler task that periodically runs:
      1) _cleanup_expired_feeds_and_send_message
      2) feed_indexer.replace_feed_states_with_db

    and attaches a callback to log any unhandled exception.
    """

    task = asyncio.create_task(
        _schedule_periodic_tasks(
            feed_indexer=feed_indexer,
            sqs_sender=sqs_sender,
            database_handler=database_handler,
            chain_aux_details=chain_aux_details,
            message_batch_size=message_batch_size,
            cleanup_interval_sec=cleanup_interval_sec,
            replace_interval_sec=replace_interval_sec,
        )
    )

    def on_task_done(t: asyncio.Task[None]) -> None:
        exc = t.exception()
        if exc:
            logging.critical(
                "Periodic scheduler crashed: %s", exc, exc_info=exc
            )

    task.add_done_callback(on_task_done)

    return task


async def _schedule_periodic_tasks(
    feed_indexer: FeedIndexer,
    sqs_sender: FeedIndexerSQSSender,
    database_handler: DatabaseHandler,
    catalog_filter_manager: CatalogFilterManager,
    chain_aux_details: ChainToAuxDetails,
    message_batch_size: int,
    cleanup_interval_sec: int,
    replace_interval_sec: int,
    catalog_cleanup_interval_sec: int,
    scheduled_task_check_sec: int = SECONDS_PER_HOUR,
) -> None:
    """
    Periodically runs cleanup & feed-state replacement in the same loop.
    This ensures you have a single scheduling point for both tasks.
    """

    # no need to sync with DB when we create this task as we have just initialized the FI internal state
    next_db_state_sync = time.monotonic() + replace_interval_sec
    # run next feed cleanup at least once when we start
    next_feed_cleanup = time.monotonic()
    # run catalog cleanup periodically
    next_catalog_cleanup = time.monotonic()

    while True:
        now = time.monotonic()

        if now >= next_db_state_sync:
            try:
                logging.info("Syncing feed indexer internal state with DB")
                await feed_indexer.replace_feed_states_with_db()
            except Exception:
                logging.exception(
                    "Error while Syncing feed indexer internal state with DB"
                )
            finally:
                next_db_state_sync += replace_interval_sec

        if now >= next_feed_cleanup:
            try:
                logging.info("Cleaning up expired Feeds")
                await _cleanup_expired_feeds_and_send_message(
                    feed_indexer=feed_indexer,
                    sqs_sender=sqs_sender,
                    database_handler=database_handler,
                    chain_to_aux_details=chain_aux_details,
                    message_batch_size=message_batch_size,
                )
            except Exception:
                logging.exception("Error while cleaning up expired feeds")
            finally:
                next_feed_cleanup += cleanup_interval_sec

        if now >= next_catalog_cleanup:
            try:
                logging.info("Cleaning up expired catalog items")
                items_removed = await (
                    catalog_filter_manager.cleanup_expired_catalog_items()
                )
                logging.info(f"Removed {items_removed} expired catalog items")
            except Exception:
                logging.exception(
                    "Error while cleaning up expired catalog items"
                )
            finally:
                next_catalog_cleanup += catalog_cleanup_interval_sec

        await asyncio.sleep(scheduled_task_check_sec)


async def main() -> None:
    config = Config()
    utils_general.setup_python_logger(level=config.LOG_LEVEL)

    target_config = load_indexer_target_config(config)

    catalog_filter_manager = CatalogFilterManager(target_config)

    chain_alarm_thresholds = await load_max_feed_index_alarms_thresholds()

    chain_id_to_aux_details: ChainToAuxDetails = {}
    for _, _config in target_config.items():
        chain_id_to_aux_details[_config.id] = AuxDetails(
            decimals=_config.decimals,
            version=_config.version,
            max_feed_index=int(
                chain_alarm_thresholds.get(_config.id, DEFAULT_ALARM_THRESHOLD)
                * ALARMS_THRESHOLD_SCALING_FACTOR
            ),
        )

    chain_ids = catalog_filter_manager.get_chain_ids()

    agent = Agent("FeedIndexer")
    catalog_channel = agent.channel(name="tickData")

    database_handler = DatabaseHandler()
    sqs_sender = FeedIndexerSQSSender(
        queue_name=config.OUTPUT_QUEUE_NAME,
        deadletter_queue_name=config.OUTPUT_DEADLETTER_QUEUE_NAME,
    )

    feed_config = ConfigLoader.get_feed_definitions()
    feed_cleanup_manager = FeedCleanupManager(
        catalog_filter_manager=catalog_filter_manager, feed_config=feed_config
    )

    feed_indexer = FeedIndexer(
        feed_config=feed_config,
        chain_aux_details=chain_id_to_aux_details,
        database_handler=database_handler,
        preloaded_internal_state=await _get_internal_state_from_db(
            database_handler, chain_id_to_aux_details
        ),
        feed_cleanup_manager=feed_cleanup_manager,
    )

    metric_worker = initialize_metrics(feed_indexer, chain_ids)

    async with sqs_sender:
        await _on_startup(
            feed_indexer=feed_indexer,
            sqs_sender=sqs_sender,
            catalog_filter_manager=catalog_filter_manager,
            chain_aux_details=chain_id_to_aux_details,
            message_batch_size=config.FEED_INDEXER_MESSAGE_BATCH_SIZE,
        )

        _create_periodic_scheduler_task(
            feed_indexer=feed_indexer,
            sqs_sender=sqs_sender,
            database_handler=database_handler,
            chain_aux_details=chain_id_to_aux_details,
            message_batch_size=config.FEED_INDEXER_MESSAGE_BATCH_SIZE,
        )

        await asyncio.gather(
            _run(
                sqs_sender=sqs_sender,
                feed_indexer=feed_indexer,
                catalog_channel=catalog_channel,
                chain_aux_details=chain_id_to_aux_details,
                catalog_filter_manager=catalog_filter_manager,
                message_batch_size=config.FEED_INDEXER_MESSAGE_BATCH_SIZE,
            ),
            metric_worker.run(),
        )


if __name__ == "__main__":
    asyncio.run(main())
