import asyncio
import copy
import functools
import json
import logging
import os
import sys
import time
from datetime import datetime, timedelta, timezone
from itertools import groupby
from typing import Any, Callable

import pause
import utils_general
from utils_aws import Frequency, get_calc_date_range

from config import (
    ACCOUNT_ID,
    CONFIG,
    DYNAMO_CATALOG_TABLE,
    DYNAMO_TIMESERIES_TABLE,
    LAMBDA_CONFIG,
    STAGE,
    STAGE_TO_CONFIG,
    THREAD_EXECUTOR,
    get_s3_suffix,
    s3_bucket,
)
from lambda_helper import execute_schedule_group

utils_general.setup_python_logger(os.getenv("LOG_LEVEL", "INFO"))


def get_window_delta(window: Frequency):
    periods = window["periods"]
    interval = window["interval"]
    if interval == "second":
        return timedelta(seconds=periods)
    if interval == "minute":
        return timedelta(minutes=periods)
    if interval == "hour":
        return timedelta(hours=periods)
    if interval == "day":
        return timedelta(days=periods)
    else:
        raise ValueError("Could not determine interval")


def logfn(prefix, level: str, msg: str):
    level_to_num = {
        "exception": 40,
        "error": 40,
        "warning": 30,
        "info": 20,
        "bsdebug": 15,
        "debug": 10,
    }
    logging.log(
        level_to_num[level],
        f"{prefix} | {msg}",
        exc_info=(True if level == "exception" else None),
    )


def load_schedules(
    base_path: str, active_order_schedules: list[int] | None = None
) -> list[dict[str, Any]]:
    combined_data = []
    # Traverse the directory
    schedules_directory = os.path.dirname(base_path) + "/run_order"
    for dirpath, run_orders, filenames in os.walk(schedules_directory):
        for run_order in run_orders:
            if (
                active_order_schedules
                and int(run_order) not in active_order_schedules
            ):
                continue
            for run_order_dir, __, filenames in os.walk(
                os.path.join(dirpath, run_order)
            ):
                for filename in filenames:
                    if filename.endswith(".json"):
                        file_path = os.path.join(run_order_dir, filename)
                        with open(file_path) as json_file:
                            data = json.load(json_file)
                            for raw_data in data:
                                raw_data["run_order"] = int(run_order)
                            combined_data.extend(data)
    return sorted(combined_data, key=lambda x: x.get("run_order"))


# Input arguments
# schedule json file, monitor | run, [active | all | schedule ID's]
async def main(args):
    try:
        init()

        if not args or len(args) != 3:
            logging.error("Missing argument paramaters")
            return

        param = "".join(e + " " for e in args)
        logging.info("Paramaters: " + param)
        data = json.load(open(args[0]))
        data["schedules"] = load_schedules(
            args[0], data.get("config", {}).get("active_run_orders")
        )
        CONFIG.update(data["config"])
        freq = CONFIG.get("price_freq_interval")
        sqs_queue_name = STAGE_TO_CONFIG[STAGE]["sqs_queue_name"].get(
            freq if freq else CONFIG.get("profile", ""), ""
        )
        CONFIG["sqs_queue"] = (
            CONFIG["sqs_queue"]
            .replace("_#ACCOUNT_ID_", ACCOUNT_ID)
            .replace("_#SQS_QUEUE_NAME_", sqs_queue_name)
        )
        schedules = data["schedules"]
        schedules_to_run = filter_schedules(schedules, args[2])

        if args[1] == "monitor":
            await monitor_async(schedules_to_run)
        elif args[1] == "run":
            await runSchedules(schedules_to_run)
        elif args[1] == "roll":
            await runSchedulesBetweenDates(
                schedules_to_run,
                run_start="2024-07-19T00:00:00.000Z",
                run_end="2024-10-12T23:59:00.000Z",
                freq={"interval": "hour", "periods": 1},
                window={"interval": "hour", "periods": 24},
            )
        else:
            logging.error(
                "Bad input paramaters. Should be run | monitor | roll"
            )
            return

    except Exception as e:
        logging.exception(f"Exception starting HistGenScheduler: {e}")


def filter_schedules(schedules: list, schedule_type: str):
    if schedule_type == "active":
        return list(
            filter(
                lambda s: (
                    s["active"] == 1 and s["lambda_name"] in LAMBDA_CONFIG
                ),
                schedules,
            )
        )
    elif schedule_type == "all":
        return schedules
    else:
        raise Exception("Unsupported schedule type")


def get_delta(interval: str, periods: int):
    if interval == "hour":
        return timedelta(hours=periods)
    if interval == "minute":
        return timedelta(minutes=periods)


def get_next_run(now: datetime, interval: str, periods: int):
    if interval == "hour":
        return (now + get_delta(interval, periods)).replace(
            minute=0, second=0, microsecond=0, tzinfo=timezone.utc
        )
    if interval == "minute":
        return (now + get_delta(interval, periods)).replace(
            second=0, microsecond=0, tzinfo=timezone.utc
        )

    return None


def check_time_condition(logfn: Callable, schedule: object):
    fulfilled = True

    if "time_condition" in schedule:
        start, end = get_calc_date_range(
            schedule["arguments"]["body"]["calc"]["args"]["date_range"]
        )
        start_dt, end_dt = utils_general.from_iso(
            start
        ), utils_general.from_iso(end)
        fulfilled = False

        if "hour" in schedule["time_condition"]:
            hour_cond = schedule["time_condition"]["hour"]

            if "value" in hour_cond:
                hour_val = hour_cond["value"]
                while start_dt <= end_dt:
                    if start_dt.hour == hour_val:
                        fulfilled = True
                        break
                    start_dt += timedelta(hours=1)
            elif "periods" in hour_cond:
                hour_periods = hour_cond["periods"]
                while start_dt <= end_dt:
                    if start_dt.hour % hour_periods == 0:
                        fulfilled = True
                        break
                    start_dt += timedelta(hours=1)
            else:
                raise Exception(f"Unsupported hour condition: {hour_cond}")

        elif "minute" in schedule["time_condition"]:
            minute_cond = schedule["time_condition"]["minute"]

            if "value" in minute_cond:
                minute_val = minute_cond["value"]
                while start_dt <= end_dt:
                    if start_dt.minute == minute_val:
                        fulfilled = True
                        break
                    start_dt += timedelta(minutes=1)
            elif "periods" in minute_cond:
                minute_periods = minute_cond["periods"]
                while start_dt <= end_dt:
                    if start_dt.minute % minute_periods == 0:
                        fulfilled = True
                        break
                    start_dt += timedelta(minutes=1)
            else:
                raise Exception(f"Unsupported hour condition: {hour_cond}")

        else:
            raise Exception(
                f"Unsupported time condition: {schedule['time_condition']}"
            )

    if not fulfilled:
        logfn(
            "info",
            f"Schedule {schedule['name']} did not fulfill time condition",
        )

    return fulfilled


def set_static_schedule_arguments(s: object, freq: Frequency | None = None):
    # TODO: set missing only also based on frequency & window (if single snap
    # then missing only true, otherwise false)
    """
    Sets arguments that are static across runs
    """
    lambda_templated_name = s["lambda_name"]
    s["lambda_name"] = LAMBDA_CONFIG[lambda_templated_name]["arn"]
    s["arguments"]["body"]["calc"]["output_options"]["version"] = LAMBDA_CONFIG[
        lambda_templated_name
    ]["output_version"]
    freq_str = ""
    periods = 1

    if freq:
        freq_str = f"{freq['periods']}{utils_general.INTERVAL_TO_LETTER[freq['interval']]}"
        periods = freq["periods"]

    def replace_placeholders(json_obj: dict[str, Any]):
        return utils_general.json_loads(
            utils_general.json_dumps(json_obj)
            .replace("_#DATA_FREQ_INTERVAL_", freq["interval"] if freq else "")
            .replace("_#DATA_FREQ_PERIODS_", f"{periods}")
            .replace("_#S3_SUFFIX_BUCKET_", get_s3_suffix(json_obj))
            .replace("_#S3_BUCKET_", s3_bucket())
            .replace(
                "_#DATA_FREQ_STR_", freq_str
            )  # AFTER BUCKET SUFFIX AS ITS INJECTED THERE
            .replace("_#DYNAMO_CATALOG_TARGET_", DYNAMO_CATALOG_TABLE)
            .replace("_#DYNAMO_TIMESERIES_TARGET_", DYNAMO_TIMESERIES_TABLE)
        )

    s["arguments"] = replace_placeholders(s["arguments"])
    s["sqs_message"] = replace_placeholders(s["sqs_message"])
    s["sqs_message"]["metadata"] = {
        "run_order": s["run_order"],
        "calc_name": lambda_templated_name[2:-1],  # strip name for conciseness
    }


def get_unique_schedule_id(s):
    return f"{s['run_order']}_{s['name']}_{s.get('active', True)}"


async def monitor_async(schedules: list):
    logging.info("Monitor Async schedules")

    interval = CONFIG["price_freq_interval"]
    periods = CONFIG["price_freq_periods"]
    tick_wait = CONFIG["tick_data_wait"]
    group_wait = CONFIG["group_wait"]
    wait = timedelta(seconds=tick_wait)

    logging.info(
        f"Monitor. Interval : {interval}, Periods : {periods}, Wait : {tick_wait} seconds",
    )

    next_run = (
        get_next_run(datetime.now(tz=timezone.utc), interval, periods) + wait
    )
    # Uncomment if you want to pretend it is the top of the hour
    next_run = (
        datetime.now(tz=timezone.utc).replace(minute=0, second=0, microsecond=0)
        + wait
    )
    logging.info(f"Next run time : {next_run}")

    seed_date_ranges = {}
    for s in schedules:
        seed_date_ranges[get_unique_schedule_id(s)] = s["arguments"]["body"][
            "calc"
        ]["args"]["date_range"]
        set_static_schedule_arguments(s)
    schedules.sort(key=lambda d: d["run_order"])

    while True:
        try:
            now_dt = datetime.now(tz=timezone.utc)
            if now_dt >= next_run:
                schedules_copy = copy.deepcopy(schedules)
                logging.info(f"{now_dt} : Running schedules")
                cur_run = next_run
                next_run = get_next_run(now_dt, interval, periods) + wait

                for s in schedules_copy:
                    start, end = get_calc_date_range(
                        seed_date_ranges[get_unique_schedule_id(s)]
                    )
                    s["arguments"]["body"]["calc"]["args"]["date_range"] = {
                        "absolute": {"start": start, "end": end}
                    }
                    # construction of market data timeseries uses previous time
                    # buckets's worth of ticks, so ffilling end must include the
                    # right edge of that bucket
                    s["sqs_message"] = utils_general.json_loads(
                        utils_general.json_dumps(s["sqs_message"]).replace(
                            "_#FFILL_END_",
                            utils_general.to_iso(
                                utils_general.from_iso(end)
                                + timedelta(microseconds=1)
                            ),
                        )
                    )

                def execute_schedule_snapshot(
                    schedules_to_execute: list[object],
                    next_execution: datetime,
                    cur_execution: datetime,
                ):
                    log = functools.partial(logfn, f"run={cur_execution}")
                    execution_start = datetime.now(tz=timezone.utc)
                    try:
                        is_first = True
                        last_group_succeeded = True
                        for key, group in groupby(
                            list(
                                filter(
                                    functools.partial(
                                        check_time_condition, log
                                    ),
                                    schedules_to_execute,
                                )
                            ),
                            key=lambda d: d["run_order"],
                        ):
                            run_schedules = list(group)
                            if (
                                str(key) in group_wait.keys()
                                and last_group_succeeded
                                and not is_first
                            ):
                                log(
                                    "bsdebug",
                                    f"Sleeping {group_wait[str(key)]}s before group {key} execution.",
                                )
                                time.sleep(group_wait[str(key)])
                            last_group_succeeded = execute_schedule_group(
                                run_schedules, log
                            )
                            is_first = False

                        execution_time = (
                            datetime.now(tz=timezone.utc) - execution_start
                        )
                        log(
                            "info",
                            f"{execution_time=:}, {next_execution=:}",
                        )
                    except Exception as e:
                        log(
                            "exception",
                            f"Error executing schedules for snapshot. {e=:}",
                        )

                THREAD_EXECUTOR.submit(
                    functools.partial(
                        execute_schedule_snapshot,
                        schedules_copy,
                        next_run,
                        cur_run,
                    )
                )

            else:
                pause.until(next_run)

        except Exception as e:
            logging.exception(f"Exception running Schedules : {e}")


async def runSchedulesBetweenDates(
    schedules: list,
    run_start: str,
    run_end: str,
    freq: Frequency,
    window: Frequency,
):
    exec_start = time.time()

    def get_calc_end(new_calc_start: str):
        window_end = utils_general.to_iso(
            utils_general.from_iso(new_calc_start) + get_window_delta(window)
        )
        return window_end if window_end < run_end else run_end

    def set_schedule_date_ranges(s: object):
        """
        Sets date ranges that are not static across runs:
        - date range over which to retrieve/calculate on
        - tick lambda resampling date range
        """
        calc = s["arguments"]["body"]["calc"]
        args = calc["args"]

        # set date range to retrieve data & calculate over
        s_dr = args["date_range"]
        if "absolute" in s_dr:
            s_dr["absolute"] = {"start": calc_start, "end": calc_end}
        elif "bucket" in s_dr:
            s_dr["bucket"]["reference"] = calc_start
            s_dr["bucket"]["frequency"] = window
        else:
            raise Exception("Invalid date range object")
        s_start, s_end = get_calc_date_range(s_dr)
        print(f"name={s['name']}, start={s_start}, end={s_end}")

        if "TickDataCalc" in s["lambda_name"]:
            # construction of market data timeseries uses previous time
            # buckets's worth of ticks, so ffilling end must include the
            # right edge of that bucket
            sqs_msg_str = (
                utils_general.json_dumps(s["sqs_message"])
                .replace(
                    "_#RESAMPLE_DATE_RANGE_",
                    utils_general.json_dumps(
                        {
                            "absolute": {
                                "end": utils_general.to_iso(
                                    utils_general.from_iso(s_end)
                                    + timedelta(microseconds=1)
                                )
                            }
                        }
                    ),
                )
                # handle nested quoted string json due to nested json.dumps
                .replace('"{"', '{"')
                .replace('"}}"', '"}}')
            )
            s["sqs_message"] = utils_general.json_loads(sqs_msg_str)

    calc_start = run_start
    calc_end = get_calc_end(calc_start)
    group_wait = CONFIG["group_wait"]

    try:
        for s in schedules:
            set_static_schedule_arguments(s, freq)

        while calc_end <= run_end:
            log = functools.partial(logfn, f"run={calc_start}")
            schedules_copy = copy.deepcopy(schedules)
            for s in schedules_copy:
                set_schedule_date_ranges(s)

            is_first = True
            last_group_succeeded = True
            for key, group in groupby(
                list(
                    filter(
                        functools.partial(check_time_condition, log),
                        schedules_copy,
                    )
                ),
                key=lambda d: d["run_order"],
            ):
                run_schedules = list(group)
                if run_schedules:
                    if (
                        str(key) in group_wait.keys()
                        and last_group_succeeded
                        and not is_first
                    ):
                        log(
                            "bsdebug",
                            f"Sleeping {group_wait[str(key)]}s before group {key} execution.",
                        )
                        time.sleep(group_wait[str(key)])
                    last_group_succeeded = execute_schedule_group(
                        run_schedules, log
                    )
                    is_first = False

            exec_end = time.time()
            logging.info(
                f"Schedules executed for {calc_start} to {calc_end}. Took {exec_end - exec_start}s",
            )
            exec_start = exec_end
            if calc_end == run_end:
                break
            calc_start = calc_end
            calc_end = get_calc_end(calc_start)

    except Exception as e:
        logging.exception(
            f"Exception running Schedules: {e}, finished at calc_start={calc_start}, calc_end={calc_end}",
        )

    logging.info("Schedules finished.")


async def runSchedules(schedules: list):
    start = time.time()

    for key, group in groupby(schedules, key=lambda d: d["run_order"]):
        run_schedules = list(group)
        if str(key) in CONFIG["group_wait"].keys():
            sleep = CONFIG["group_wait"][str(key)]
            logging.info(f"Sleeping for {sleep}s")
            time.sleep(sleep)
        await execute_schedule_group(run_schedules, logfn)

    execution_time = time.time() - start
    logging.info(f"Schedules finished in {execution_time}  seconds")


def init():
    file_handler = logging.FileHandler(
        filename="./log/histGeneratorScheduler.log", encoding="utf-8", mode="a+"
    )
    stdout_handler = logging.StreamHandler(sys.stdout)
    handlers = [file_handler, stdout_handler]
    logging.basicConfig(
        handlers=handlers,
        format="%(levelname)s:%(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        level=logging.INFO,
    )

    logging.info("Hist Generator Scheduler started ...")


if __name__ == "__main__":
    asyncio.run(main(sys.argv[1:]))
    # main(sys.argv[1:])
