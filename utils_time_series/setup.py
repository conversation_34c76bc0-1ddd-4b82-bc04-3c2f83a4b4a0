import setuptools

with open("../README.md", encoding="utf-8") as fh:
    long_description = fh.read()

setuptools.setup(
    name="utils_time_series",
    version="2.3.1",
    description="BlockScholes time series generation Python utilies",
    long_description_content_type="text/markdown",
    url="https://github.com/blockscholes/bs-python-utils/tree/main/utils_time_series",
    packages=["utils_time_series"],
    install_requires=["pandas", "plum-dispatch", "utils_general"],
    package_data={"utils_time_series": ["py.typed"]},
    requires_python=">=3.11",
)
