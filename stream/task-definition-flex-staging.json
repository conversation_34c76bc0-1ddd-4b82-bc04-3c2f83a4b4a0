{"containerDefinitions": [{"name": "volSmileFlexStream-container", "image": "public.ecr.aws/docker/library/python:3.11", "cpu": 0, "command": ["dumb-init", "python", "-m", "run_stream_flex"], "essential": true, "environment": [{"name": "frequency_seconds", "value": "20"}, {"name": "tenor_mode", "value": "standard"}, {"name": "METRIC_S", "value": "60"}, {"name": "estimate_params", "value": "True"}, {"name": "smooth", "value": "True"}, {"name": "MIN_KINESIS_FETCH_DELAY_S", "value": "1"}, {"name": "ECS_CLUSTER", "value": "Staging"}, {"name": "ECS_SERVICE", "value": "volSmileFlexStream-service"}, {"name": "FIXED_TARGET_JOBS", "value": "[\"v2composite.BTC.SVI\", \"v2composite.ETH.SVI\",\"v2composite.BTC.spline\", \"v2composite.ETH.spline\"]"}, {"name": "SSM_INDEX_CONFIG_PATH", "value": "/config/price_indices_generated"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/volSmileFlexStream-containers-staging", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "volSmileFlexStream-task-definition", "taskRoleArn": "arn:aws:iam::273532302533:role/volSmileStreamContainerRole", "executionRoleArn": "arn:aws:iam::273532302533:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "8192", "memory": "16384", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "ARM64"}}