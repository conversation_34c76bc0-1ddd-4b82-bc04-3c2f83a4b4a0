{"containerDefinitions": [{"name": "forwardCalcStream-container", "image": "public.ecr.aws/docker/library/python:3.8", "cpu": 0, "essential": true, "environment": [{"name": "FREQUENCY_SECONDS", "value": "5"}, {"name": "MIN_KINESIS_FETCH_DELAY_S", "value": "1"}], "secrets": [{"name": "FORWARD_CATALOG_DATA_SETS", "valueFrom": "arn:aws:ssm:eu-west-2:273532302533:parameter/config/data_set/forward_construction"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/forwardCalcStream-containers-staging", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "forwardCalcStream-task-definition", "taskRoleArn": "arn:aws:iam::273532302533:role/forwardCalcStreamContainerRole", "executionRoleArn": "arn:aws:iam::273532302533:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}