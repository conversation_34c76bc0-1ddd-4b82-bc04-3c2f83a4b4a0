aiobotocore==2.19.0
aws_lambda_typing==2.19.0
debugpy>=1.0,<2
numpy==1.26.2
pandas==1.5.3
scikit-learn==1.3.2
types-aiobotocore-dynamodb==2.19.0
datagrabber @ git+https://<EMAIL>/blockscholes/datagrabber@v1.10.0
utils_general @ git+https://<EMAIL>/blockscholes/bs-python-utils@v4.1.26#subdirectory=utils_general
utils_aws @ git+https://<EMAIL>/blockscholes/bs-python-utils@v4.1.26#subdirectory=utils_aws
utils_calc @ git+https://<EMAIL>/blockscholes/bs-python-utils@v4.1.26#subdirectory=utils_calc
block_stream @ git+https://<EMAIL>/blockscholes/blockStream@v4.1.6
cachetools==5.5.0
orjson==3.10.18