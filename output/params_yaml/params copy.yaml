AWSTemplateFormatVersion: 2010-09-09
Resources:
  PriceIndicesFuture1:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//future_1
      Type: String
      Value: '{"future": {"asset_class": "future", "output_frequency_ms": 1000, "index_currency":
        "USD", "allow_wider_range": true, "components": [{"exchange": "binance", "base":
        "BTC", "quote": "USD"}, {"exchange": "bybit", "base": "BTC", "quote": "USDT"},
        {"exchange": "deribit", "base": "BTC", "quote": "USD"}, {"exchange": "okx",
        "base": "BTC", "quote": "USD"}, {"exchange": "binance", "base": "ETH", "quote":
        "USD"}, {"exchange": "bybit", "base": "ETH", "quote": "USDT"}, {"exchange":
        "deribit", "base": "ETH", "quote": "USD"}, {"exchange": "okx", "base": "ETH",
        "quote": "USD"}, {"exchange": "pendle-ethereum", "base": "PT-SUSDE", "quote":
        "SUSDE"}, {"exchange": "pendle-ethereum", "base": "YT-SUSDE", "quote": "SUSDE"}],
        "global_exchange_weights": [{"exchange": "pendle-ethereum", "global_volume_weight":
        0.3}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesOption1:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//option_1
      Type: String
      Value: '{"option": {"asset_class": "option", "output_frequency_ms": 1000, "index_currency":
        "USD", "components": [{"exchange": "bybit", "base": "BTC", "quote": "USDT"},
        {"exchange": "deribit", "base": "BTC", "quote": "BTC"}, {"exchange": "okx",
        "base": "BTC", "quote": "BTC"}, {"exchange": "bybit", "base": "ETH", "quote":
        "USDT"}, {"exchange": "deribit", "base": "ETH", "quote": "ETH"}, {"exchange":
        "okx", "base": "ETH", "quote": "ETH"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesperpetual1:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//perpetual_1
      Type: String
      Value: '{"perpetual": {"asset_class": "perpetual", "index_currency": "USD",
        "output_frequency_ms": 1000, "components": [{"exchange": "binance", "base":
        "BTC", "quote": "USDT"}, {"exchange": "bybit", "base": "BTC", "quote": "USDT"},
        {"exchange": "deribit", "base": "BTC", "quote": "USD"}, {"exchange": "okx",
        "base": "BTC", "quote": "USDT"}, {"exchange": "bitget", "base": "BTC", "quote":
        "USDT"}, {"exchange": "binance", "base": "ETH", "quote": "USDT"}, {"exchange":
        "bybit", "base": "ETH", "quote": "USDT"}, {"exchange": "deribit", "base":
        "ETH", "quote": "USD"}, {"exchange": "okx", "base": "ETH", "quote": "USDT"},
        {"exchange": "bitget", "base": "ETH", "quote": "USDT"}, {"exchange": "okx",
        "base": "SOL", "quote": "USDT"}, {"exchange": "bybit", "base": "SOL", "quote":
        "USDT"}, {"exchange": "binance", "base": "SOL", "quote": "USDT"}, {"exchange":
        "bitget", "base": "SOL", "quote": "USDT"}, {"exchange": "kraken", "base":
        "ARB", "quote": "USD"}, {"exchange": "bybit", "base": "ARB", "quote": "USDT"},
        {"exchange": "okx", "base": "ARB", "quote": "USDT"}, {"exchange": "bitget",
        "base": "ARB", "quote": "USDT"}, {"exchange": "binance", "base": "ATOM", "quote":
        "USDT"}, {"exchange": "bybit", "base": "ATOM", "quote": "USDT"}, {"exchange":
        "okx", "base": "ATOM", "quote": "USDT"}, {"exchange": "bitget", "base": "ATOM",
        "quote": "USDT"}, {"exchange": "binance", "base": "LINK", "quote": "USDT"},
        {"exchange": "bybit", "base": "LINK", "quote": "USDT"}, {"exchange": "deribit",
        "base": "LINK", "quote": "USDC"}, {"exchange": "okx", "base": "LINK", "quote":
        "USDT"}, {"exchange": "bitget", "base": "LINK", "quote": "USDT"}, {"exchange":
        "binance", "base": "BNB", "quote": "USDT"}, {"exchange": "bybit", "base":
        "BNB", "quote": "USDT"}, {"exchange": "deribit", "base": "BNB", "quote": "USDC"},
        {"exchange": "okx", "base": "BNB", "quote": "USDT"}, {"exchange": "bitget",
        "base": "BNB", "quote": "USDT"}, {"exchange": "binance", "base": "DOGE", "quote":
        "USDT"}, {"exchange": "bybit", "base": "DOGE", "quote": "USDT"}, {"exchange":
        "deribit", "base": "DOGE", "quote": "USDC"}, {"exchange": "okx", "base": "DOGE",
        "quote": "USDT"}, {"exchange": "bitget", "base": "DOGE", "quote": "USDT"},
        {"exchange": "kraken", "base": "PEPE", "quote": "USD"}, {"exchange": "okx",
        "base": "PEPE", "quote": "USDT"}, {"exchange": "bitget", "base": "PEPE", "quote":
        "USDT"}, {"exchange": "binance", "base": "NEAR", "quote": "USDT"}, {"exchange":
        "bybit", "base": "NEAR", "quote": "USDT"}, {"exchange": "deribit", "base":
        "NEAR", "quote": "USDC"}, {"exchange": "okx", "base": "NEAR", "quote": "USDT"},
        {"exchange": "bitget", "base": "NEAR", "quote": "USDT"}, {"exchange": "binance",
        "base": "TON", "quote": "USDT"}, {"exchange": "bybit", "base": "TON", "quote":
        "USDT"}, {"exchange": "okx", "base": "TON", "quote": "USDT"}, {"exchange":
        "bitget", "base": "TON", "quote": "USDT"}, {"exchange": "binance", "base":
        "OP", "quote": "USDT"}, {"exchange": "bybit", "base": "OP", "quote": "USDT"},
        {"exchange": "okx", "base": "OP", "quote": "USDT"}, {"exchange": "bitget",
        "base": "OP", "quote": "USDT"}, {"exchange": "binance", "base": "ONDO", "quote":
        "USDT"}, {"exchange": "bybit", "base": "ONDO", "quote": "USDT"}, {"exchange":
        "deribit", "base": "ONDO", "quote": "USDC"}, {"exchange": "okx", "base": "ONDO",
        "quote": "USDT"}, {"exchange": "bitget", "base": "ONDO", "quote": "USDT"},
        {"exchange": "binance", "base": "WIF", "quote": "USDT"}, {"exchange": "bybit",
        "base": "WIF", "quote": "USDT"}, {"exchange": "deribit", "base": "WIF", "quote":
        "USDC"}, {"exchange": "okx", "base": "WIF", "quote": "USDT"}, {"exchange":
        "bitget", "base": "WIF", "quote": "USDT"}, {"exchange": "binance", "base":
        "IMX", "quote": "USDT"}, {"exchange": "bybit", "base": "IMX", "quote": "USDT"},
        {"exchange": "deribit", "base": "IMX", "quote": "USDC"}, {"exchange": "okx",
        "base": "IMX", "quote": "USDT"}, {"exchange": "bitget", "base": "IMX", "quote":
        "USDT"}, {"exchange": "binance", "base": "GRT", "quote": "USDT"}, {"exchange":
        "bybit", "base": "GRT", "quote": "USDT"}, {"exchange": "okx", "base": "GRT",
        "quote": "USDT"}, {"exchange": "bitget", "base": "GRT", "quote": "USDT"},
        {"exchange": "binance", "base": "XRP", "quote": "USDT"}, {"exchange": "bybit",
        "base": "XRP", "quote": "USDT"}, {"exchange": "deribit", "base": "XRP", "quote":
        "USDC"}, {"exchange": "okx", "base": "XRP", "quote": "USDT"}, {"exchange":
        "bitget", "base": "XRP", "quote": "USDT"}, {"exchange": "binance", "base":
        "ZK", "quote": "USDT"}, {"exchange": "bybit", "base": "ZK", "quote": "USDT"},
        {"exchange": "okx", "base": "ZK", "quote": "USDT"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesPerpetual2:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//perpetual_2
      Type: String
      Value: '{"perpetual": {"asset_class": "perpetual", "index_currency": "USD",
        "output_frequency_ms": 1000, "components": [{"exchange": "bitget", "base":
        "ZK", "quote": "USDT"}, {"exchange": "okx", "base": "POL", "quote": "USDT"},
        {"exchange": "bybit", "base": "POL", "quote": "USDT"}, {"exchange": "binance",
        "base": "POL", "quote": "USDT"}, {"exchange": "bitget", "base": "POL", "quote":
        "USDT"}, {"exchange": "binance", "base": "ADA", "quote": "USDT"}, {"exchange":
        "bybit", "base": "ADA", "quote": "USDT"}, {"exchange": "deribit", "base":
        "ADA", "quote": "USDC"}, {"exchange": "okx", "base": "ADA", "quote": "USDT"},
        {"exchange": "bitget", "base": "ADA", "quote": "USDT"}, {"exchange": "binance",
        "base": "BERA", "quote": "USDT"}, {"exchange": "bybit", "base": "BERA", "quote":
        "USDT"}, {"exchange": "okx", "base": "BERA", "quote": "USDT"}, {"exchange":
        "bitget", "base": "BERA", "quote": "USDT"}, {"exchange": "binance", "base":
        "FARTCOIN", "quote": "USDT"}, {"exchange": "bybit", "base": "FARTCOIN", "quote":
        "USDT"}, {"exchange": "okx", "base": "FARTCOIN", "quote": "USDT"}, {"exchange":
        "bitget", "base": "FARTCOIN", "quote": "USDT"}, {"exchange": "binance", "base":
        "PENGU", "quote": "USDT"}, {"exchange": "bybit", "base": "PENGU", "quote":
        "USDT"}, {"exchange": "okx", "base": "PENGU", "quote": "USDT"}, {"exchange":
        "bitget", "base": "PENGU", "quote": "USDT"}, {"exchange": "binance", "base":
        "POPCAT", "quote": "USDT"}, {"exchange": "bybit", "base": "POPCAT", "quote":
        "USDT"}, {"exchange": "okx", "base": "POPCAT", "quote": "USDT"}, {"exchange":
        "bitget", "base": "POPCAT", "quote": "USDT"}, {"exchange": "binance", "base":
        "SUI", "quote": "USDT"}, {"exchange": "bybit", "base": "SUI", "quote": "USDT"},
        {"exchange": "okx", "base": "SUI", "quote": "USDT"}, {"exchange": "bitget",
        "base": "SUI", "quote": "USDT"}, {"exchange": "binance", "base": "TRUMP",
        "quote": "USDT"}, {"exchange": "bybit", "base": "TRUMP", "quote": "USDT"},
        {"exchange": "okx", "base": "TRUMP", "quote": "USDT"}, {"exchange": "bitget",
        "base": "TRUMP", "quote": "USDT"}, {"exchange": "binance", "base": "VIRTUAL",
        "quote": "USDT"}, {"exchange": "okx", "base": "VIRTUAL", "quote": "USDT"},
        {"exchange": "bitget", "base": "VIRTUAL", "quote": "USDT"}, {"exchange": "bybit",
        "base": "VIRTUAL", "quote": "USDT"}, {"exchange": "binance", "base": "XLM",
        "quote": "USDT"}, {"exchange": "okx", "base": "XLM", "quote": "USDT"}, {"exchange":
        "bitget", "base": "XLM", "quote": "USDT"}, {"exchange": "bybit", "base": "XLM",
        "quote": "USDT"}, {"exchange": "binance", "base": "WLD", "quote": "USDT"},
        {"exchange": "okx", "base": "WLD", "quote": "USDT"}, {"exchange": "bybit",
        "base": "WLD", "quote": "USDT"}, {"exchange": "bitget", "base": "WLD", "quote":
        "USDT"}, {"exchange": "binance", "base": "ENA", "quote": "USDT"}, {"exchange":
        "okx", "base": "ENA", "quote": "USDT"}, {"exchange": "bybit", "base": "ENA",
        "quote": "USDT"}, {"exchange": "bitget", "base": "ENA", "quote": "USDT"},
        {"exchange": "binance", "base": "AAVE", "quote": "USDT"}, {"exchange": "okx",
        "base": "AAVE", "quote": "USDT"}, {"exchange": "bybit", "base": "AAVE", "quote":
        "USDT"}, {"exchange": "bitget", "base": "AAVE", "quote": "USDT"}, {"exchange":
        "binance", "base": "JUP", "quote": "USDT"}, {"exchange": "okx", "base": "JUP",
        "quote": "USDT"}, {"exchange": "bybit", "base": "JUP", "quote": "USDT"}, {"exchange":
        "bitget", "base": "JUP", "quote": "USDT"}, {"exchange": "binance", "base":
        "AIXBT", "quote": "USDT"}, {"exchange": "okx", "base": "AIXBT", "quote": "USDT"},
        {"exchange": "bybit", "base": "AIXBT", "quote": "USDT"}, {"exchange": "bitget",
        "base": "AIXBT", "quote": "USDT"}, {"exchange": "binance", "base": "AI16Z",
        "quote": "USDT"}, {"exchange": "okx", "base": "AI16Z", "quote": "USDT"}, {"exchange":
        "bybit", "base": "AI16Z", "quote": "USDT"}, {"exchange": "bitget", "base":
        "AI16Z", "quote": "USDT"}, {"exchange": "binance", "base": "VINE", "quote":
        "USDT"}, {"exchange": "okx", "base": "VINE", "quote": "USDT"}, {"exchange":
        "bybit", "base": "VINE", "quote": "USDT"}, {"exchange": "bitget", "base":
        "VINE", "quote": "USDT"}, {"exchange": "binance", "base": "PENDLE", "quote":
        "USDT"}, {"exchange": "bybit", "base": "PENDLE", "quote": "USDT"}, {"exchange":
        "bitget", "base": "PENDLE", "quote": "USDT"}, {"exchange": "binance", "base":
        "UXLINK", "quote": "USDT"}, {"exchange": "okx", "base": "UXLINK", "quote":
        "USDT"}, {"exchange": "bybit", "base": "UXLINK", "quote": "USDT"}, {"exchange":
        "bitget", "base": "UXLINK", "quote": "USDT"}, {"exchange": "binance", "base":
        "KAITO", "quote": "USDT"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesPerpetual3:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//perpetual_3
      Type: String
      Value: '{"perpetual": {"asset_class": "perpetual", "index_currency": "USD",
        "output_frequency_ms": 1000, "components": [{"exchange": "okx", "base": "KAITO",
        "quote": "USDT"}, {"exchange": "bybit", "base": "KAITO", "quote": "USDT"},
        {"exchange": "bitget", "base": "KAITO", "quote": "USDT"}, {"exchange": "okx",
        "base": "BONK", "quote": "USDT"}, {"exchange": "bybit", "base": "1000BONK",
        "quote": "USDT", "o": "BONK", "m": 0.001}, {"exchange": "bitget", "base":
        "1000BONK", "quote": "USDT", "o": "BONK", "m": 0.001}, {"exchange": "binance",
        "base": "1000BONK", "quote": "USDT", "o": "BONK", "m": 0.001}, {"exchange":
        "okx", "base": "SHIB", "quote": "USDT"}, {"exchange": "bitget", "base": "SHIB",
        "quote": "USDT"}, {"exchange": "binance", "base": "1000SHIB", "quote": "USDT",
        "o": "SHIB", "m": 0.001}, {"exchange": "bybit", "base": "SHIB1000", "quote":
        "USDT", "o": "SHIB", "m": 0.001}, {"exchange": "binance", "base": "IP", "quote":
        "USDT"}, {"exchange": "okx", "base": "IP", "quote": "USDT"}, {"exchange":
        "bybit", "base": "IP", "quote": "USDT"}, {"exchange": "bybit", "base": "RED",
        "quote": "USDT"}, {"exchange": "binance", "base": "RED", "quote": "USDT"},
        {"exchange": "gateio", "base": "RED", "quote": "USDT"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesSpot1:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//spot_1
      Type: String
      Value: '{"spot": {"asset_class": "spot", "index_currency": "USD", "output_frequency_ms":
        1000, "allow_wider_range": true, "components": [{"exchange": "bitfinex", "base":
        "USDC", "quote": "USD"}, {"exchange": "bitstamp", "base": "USDC", "quote":
        "USD"}, {"exchange": "kraken", "base": "USDC", "quote": "ZUSD"}, {"exchange":
        "coinbase", "base": "USDT", "quote": "USD"}, {"exchange": "cryptocom", "base":
        "USDT", "quote": "USD"}, {"exchange": "bitfinex", "base": "USDT", "quote":
        "USD"}, {"exchange": "bitstamp", "base": "USDT", "quote": "USD"}, {"exchange":
        "kraken", "base": "USDT", "quote": "ZUSD"}, {"exchange": "coinbase", "base":
        "BTC", "quote": "USD"}, {"exchange": "cryptocom", "base": "BTC", "quote":
        "USD"}, {"exchange": "bitfinex", "base": "BTC", "quote": "USD"}, {"exchange":
        "bitstamp", "base": "BTC", "quote": "USD"}, {"exchange": "kraken", "base":
        "BTC", "quote": "ZUSD"}, {"exchange": "coinbase", "base": "ETH", "quote":
        "USD"}, {"exchange": "cryptocom", "base": "ETH", "quote": "USD"}, {"exchange":
        "bitfinex", "base": "ETH", "quote": "USD"}, {"exchange": "bitstamp", "base":
        "ETH", "quote": "USD"}, {"exchange": "kraken", "base": "XETH", "quote": "ZUSD"},
        {"exchange": "coinbase", "base": "SNX", "quote": "USD"}, {"exchange": "cryptocom",
        "base": "SNX", "quote": "USD"}, {"exchange": "bitfinex", "base": "SNX", "quote":
        "USD"}, {"exchange": "bitstamp", "base": "SNX", "quote": "USD"}, {"exchange":
        "kraken", "base": "SNX", "quote": "ZUSD"}, {"exchange": "coinbase", "base":
        "SOL", "quote": "USD"}, {"exchange": "cryptocom", "base": "SOL", "quote":
        "USD"}, {"exchange": "bitfinex", "base": "SOL", "quote": "USD"}, {"exchange":
        "bitstamp", "base": "SOL", "quote": "USD"}, {"exchange": "kraken", "base":
        "SOL", "quote": "ZUSD"}, {"exchange": "cryptocom", "base": "DOGE", "quote":
        "USD"}, {"exchange": "bitstamp", "base": "DOGE", "quote": "USD"}, {"exchange":
        "coinbase", "base": "DOGE", "quote": "USD"}, {"exchange": "okx", "base": "DOGE",
        "quote": "USDT"}, {"exchange": "binance", "base": "DOGE", "quote": "USDT"},
        {"exchange": "cryptocom", "base": "XRP", "quote": "USD"}, {"exchange": "bitstamp",
        "base": "XRP", "quote": "USD"}, {"exchange": "coinbase", "base": "XRP", "quote":
        "USD"}, {"exchange": "kraken", "base": "XRP", "quote": "ZUSD"}, {"exchange":
        "binance", "base": "XRP", "quote": "USDT"}, {"exchange": "kraken", "base":
        "ARB", "quote": "ZUSD"}, {"exchange": "coinbase", "base": "ARB", "quote":
        "USD"}, {"exchange": "cryptocom", "base": "ARB", "quote": "USD"}, {"exchange":
        "okx", "base": "ARB", "quote": "USDT"}, {"exchange": "binance", "base": "ARB",
        "quote": "USDT"}, {"exchange": "kraken", "base": "ATOM", "quote": "ZUSD"},
        {"exchange": "coinbase", "base": "ATOM", "quote": "USD"}, {"exchange": "cryptocom",
        "base": "ATOM", "quote": "USD"}, {"exchange": "okx", "base": "ATOM", "quote":
        "USDT"}, {"exchange": "binance", "base": "ATOM", "quote": "USDT"}, {"exchange":
        "cryptocom", "base": "LINK", "quote": "USD"}, {"exchange": "bitstamp", "base":
        "LINK", "quote": "USD"}, {"exchange": "kraken", "base": "LINK", "quote": "ZUSD"},
        {"exchange": "coinbase", "base": "LINK", "quote": "USD"}, {"exchange": "okx",
        "base": "LINK", "quote": "USDT"}, {"exchange": "bitstamp", "base": "NEAR",
        "quote": "USD"}, {"exchange": "kraken", "base": "NEAR", "quote": "ZUSD"},
        {"exchange": "coinbase", "base": "NEAR", "quote": "USD"}, {"exchange": "cryptocom",
        "base": "NEAR", "quote": "USD"}, {"exchange": "okx", "base": "NEAR", "quote":
        "USDT"}, {"exchange": "kraken", "base": "OP", "quote": "ZUSD"}, {"exchange":
        "coinbase", "base": "OP", "quote": "USD"}, {"exchange": "cryptocom", "base":
        "OP", "quote": "USD"}, {"exchange": "okx", "base": "OP", "quote": "USDT"},
        {"exchange": "binance", "base": "OP", "quote": "USDT"}, {"exchange": "kraken",
        "base": "ONDO", "quote": "ZUSD"}, {"exchange": "coinbase", "base": "ONDO",
        "quote": "USD"}, {"exchange": "cryptocom", "base": "ONDO", "quote": "USD"},
        {"exchange": "okx", "base": "ONDO", "quote": "USDT"}, {"exchange": "bitget",
        "base": "ONDO", "quote": "USDT"}, {"exchange": "bitstamp", "base": "GRT",
        "quote": "USD"}, {"exchange": "kraken", "base": "GRT", "quote": "ZUSD"}, {"exchange":
        "coinbase", "base": "GRT", "quote": "USD"}, {"exchange": "cryptocom", "base":
        "GRT", "quote": "USD"}, {"exchange": "okx", "base": "GRT", "quote": "USDT"},
        {"exchange": "bitstamp", "base": "ADA", "quote": "USD"}, {"exchange": "kraken",
        "base": "ADA", "quote": "ZUSD"}, {"exchange": "coinbase", "base": "ADA", "quote":
        "USD"}, {"exchange": "cryptocom", "base": "ADA", "quote": "USD"}, {"exchange":
        "okx", "base": "ADA", "quote": "USDT"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesSpot2:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//spot_2
      Type: String
      Value: '{"spot": {"asset_class": "spot", "index_currency": "USD", "output_frequency_ms":
        1000, "allow_wider_range": true, "components": [{"exchange": "binance", "base":
        "AIXBT", "quote": "USDT"}, {"exchange": "bybit", "base": "AIXBT", "quote":
        "USDT"}, {"exchange": "bitget", "base": "AIXBT", "quote": "USDT"}, {"exchange":
        "gateio", "base": "AIXBT", "quote": "USDT"}, {"exchange": "okx", "base": "AIXBT",
        "quote": "USDT"}, {"exchange": "kraken", "base": "AI16Z", "quote": "ZUSD"},
        {"exchange": "bybit", "base": "AI16Z", "quote": "USDT"}, {"exchange": "bitget",
        "base": "AI16Z", "quote": "USDT"}, {"exchange": "gateio", "base": "AI16Z",
        "quote": "USDT"}, {"exchange": "cryptocom", "base": "AI16Z", "quote": "USD"},
        {"exchange": "kraken", "base": "VINE", "quote": "ZUSD"}, {"exchange": "okx",
        "base": "VINE", "quote": "USDT"}, {"exchange": "bitget", "base": "VINE", "quote":
        "USDT"}, {"exchange": "gateio", "base": "VINE", "quote": "USDT"}, {"exchange":
        "cryptocom", "base": "VINE", "quote": "USD"}, {"exchange": "kraken", "base":
        "PENDLE", "quote": "ZUSD"}, {"exchange": "okx", "base": "PENDLE", "quote":
        "USDT"}, {"exchange": "bitget", "base": "PENDLE", "quote": "USDT"}, {"exchange":
        "bybit", "base": "PENDLE", "quote": "USDT"}, {"exchange": "binance", "base":
        "PENDLE", "quote": "USDT"}, {"exchange": "okx", "base": "UXLINK", "quote":
        "USDT"}, {"exchange": "bitget", "base": "UXLINK", "quote": "USDT"}, {"exchange":
        "bybit", "base": "UXLINK", "quote": "USDT"}, {"exchange": "gateio", "base":
        "UXLINK", "quote": "USDT"}, {"exchange": "okx", "base": "SHIB", "quote": "USDT"},
        {"exchange": "kraken", "base": "SHIB", "quote": "ZUSD"}, {"exchange": "bybit",
        "base": "SHIB", "quote": "USDT"}, {"exchange": "binance", "base": "SHIB",
        "quote": "USDT"}, {"exchange": "coinbase", "base": "SHIB", "quote": "USD"},
        {"exchange": "okx", "base": "BONK", "quote": "USDT"}, {"exchange": "kraken",
        "base": "BONK", "quote": "ZUSD"}, {"exchange": "bybit", "base": "BONK", "quote":
        "USDT"}, {"exchange": "binance", "base": "BONK", "quote": "USDT"}, {"exchange":
        "coinbase", "base": "BONK", "quote": "USD"}, {"exchange": "okx", "base": "DEGEN",
        "quote": "USDT"}, {"exchange": "coinbase", "base": "DEGEN", "quote": "USD"},
        {"exchange": "cryptocom", "base": "DEGEN", "quote": "USD"}, {"exchange": "gateio",
        "base": "DEGEN", "quote": "USDT"}, {"exchange": "bitget", "base": "DEGEN",
        "quote": "USDT"}, {"exchange": "coinbase", "base": "XLM", "quote": "USD"},
        {"exchange": "kraken", "base": "XLM", "quote": "ZUSD"}, {"exchange": "cryptocom",
        "base": "XLM", "quote": "USD"}, {"exchange": "okx", "base": "XLM", "quote":
        "USDT"}, {"exchange": "binance", "base": "XLM", "quote": "USDT"}, {"exchange":
        "coinbase", "base": "POPCAT", "quote": "USD"}, {"exchange": "bybit", "base":
        "POPCAT", "quote": "USDT"}, {"exchange": "kraken", "base": "POPCAT", "quote":
        "ZUSD"}, {"exchange": "gateio", "base": "POPCAT", "quote": "USDT"}, {"exchange":
        "cryptocom", "base": "POPCAT", "quote": "USD"}, {"exchange": "coinbase", "base":
        "PENGU", "quote": "USD"}, {"exchange": "bybit", "base": "PENGU", "quote":
        "USDT"}, {"exchange": "kraken", "base": "PENGU", "quote": "ZUSD"}, {"exchange":
        "gateio", "base": "PENGU", "quote": "USDT"}, {"exchange": "binance", "base":
        "PENGU", "quote": "USDT"}, {"exchange": "binance", "base": "JUP", "quote":
        "USDT"}, {"exchange": "bybit", "base": "JUP", "quote": "USDT"}, {"exchange":
        "kraken", "base": "JUP", "quote": "ZUSD"}, {"exchange": "gateio", "base":
        "JUP", "quote": "USDT"}, {"exchange": "okx", "base": "JUP", "quote": "USDT"},
        {"exchange": "bitstamp", "base": "IMX", "quote": "USD"}, {"exchange": "kraken",
        "base": "IMX", "quote": "ZUSD"}, {"exchange": "coinbase", "base": "IMX", "quote":
        "USD"}, {"exchange": "cryptocom", "base": "IMX", "quote": "USD"}, {"exchange":
        "okx", "base": "IMX", "quote": "USDT"}, {"exchange": "bitget", "base": "FARTCOIN",
        "quote": "USDT"}, {"exchange": "gateio", "base": "FARTCOIN", "quote": "USDT"},
        {"exchange": "kraken", "base": "FARTCOIN", "quote": "ZUSD"}, {"exchange":
        "cryptocom", "base": "FARTCOIN", "quote": "USD"}, {"exchange": "bitget", "base":
        "BERA", "quote": "USDT"}, {"exchange": "gateio", "base": "BERA", "quote":
        "USDT"}, {"exchange": "bybit", "base": "BERA", "quote": "USDT"}, {"exchange":
        "coinbase", "base": "BERA", "quote": "USD"}, {"exchange": "binance", "base":
        "BERA", "quote": "USDT"}, {"exchange": "cryptocom", "base": "AAVE", "quote":
        "USD"}, {"exchange": "bitstamp", "base": "AAVE", "quote": "USD"}, {"exchange":
        "coinbase", "base": "AAVE", "quote": "USD"}, {"exchange": "kraken", "base":
        "AAVE", "quote": "ZUSD"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesSpot3:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//spot_3
      Type: String
      Value: '{"spot": {"asset_class": "spot", "index_currency": "USD", "output_frequency_ms":
        1000, "allow_wider_range": true, "components": [{"exchange": "okx", "base":
        "AAVE", "quote": "USDT"}, {"exchange": "cryptocom", "base": "SUI", "quote":
        "USD"}, {"exchange": "bitstamp", "base": "SUI", "quote": "USD"}, {"exchange":
        "coinbase", "base": "SUI", "quote": "USD"}, {"exchange": "kraken", "base":
        "SUI", "quote": "ZUSD"}, {"exchange": "okx", "base": "SUI", "quote": "USDT"},
        {"exchange": "coinbase", "base": "TIA", "quote": "USD"}, {"exchange": "kraken",
        "base": "TIA", "quote": "ZUSD"}, {"exchange": "cryptocom", "base": "TIA",
        "quote": "USD"}, {"exchange": "okx", "base": "TIA", "quote": "USDT"}, {"exchange":
        "binance", "base": "TIA", "quote": "USDT"}, {"exchange": "binance", "base":
        "BNB", "quote": "USDT"}, {"exchange": "okx", "base": "BNB", "quote": "USDT"},
        {"exchange": "bybit", "base": "BNB", "quote": "USDT"}, {"exchange": "gateio",
        "base": "BNB", "quote": "USDT"}, {"exchange": "bitget", "base": "BNB", "quote":
        "USDT"}, {"exchange": "cryptocom", "base": "WLD", "quote": "USD"}, {"exchange":
        "binance", "base": "WLD", "quote": "USDT"}, {"exchange": "okx", "base": "WLD",
        "quote": "USDT"}, {"exchange": "gateio", "base": "WLD", "quote": "USDT"},
        {"exchange": "bybit", "base": "WLD", "quote": "USDT"}, {"exchange": "cryptocom",
        "base": "ZK", "quote": "USD"}, {"exchange": "okx", "base": "ZK", "quote":
        "USDT"}, {"exchange": "binance", "base": "ZK", "quote": "USDT"}, {"exchange":
        "gateio", "base": "ZK", "quote": "USDT"}, {"exchange": "bybit", "base": "ZK",
        "quote": "USDT"}, {"exchange": "coinbase", "base": "AVAX", "quote": "USD"},
        {"exchange": "kraken", "base": "AVAX", "quote": "ZUSD"}, {"exchange": "bitstamp",
        "base": "AVAX", "quote": "USD"}, {"exchange": "cryptocom", "base": "AVAX",
        "quote": "USD"}, {"exchange": "okx", "base": "AVAX", "quote": "USDT"}, {"exchange":
        "coinbase", "base": "UNI", "quote": "USD"}, {"exchange": "kraken", "base":
        "UNI", "quote": "ZUSD"}, {"exchange": "bitstamp", "base": "UNI", "quote":
        "USD"}, {"exchange": "cryptocom", "base": "UNI", "quote": "USD"}, {"exchange":
        "okx", "base": "UNI", "quote": "USDT"}, {"exchange": "binance", "base": "UNI",
        "quote": "USDT"}, {"exchange": "kraken", "base": "TAO", "quote": "ZUSD"},
        {"exchange": "cryptocom", "base": "TAO", "quote": "USD"}, {"exchange": "binance",
        "base": "TAO", "quote": "USDT"}, {"exchange": "gateio", "base": "TAO", "quote":
        "USDT"}, {"exchange": "bitget", "base": "TAO", "quote": "USDT"}, {"exchange":
        "coinbase", "base": "SEI", "quote": "USD"}, {"exchange": "kraken", "base":
        "SEI", "quote": "ZUSD"}, {"exchange": "cryptocom", "base": "SEI", "quote":
        "USD"}, {"exchange": "binance", "base": "SEI", "quote": "USDT"}, {"exchange":
        "bitget", "base": "SEI", "quote": "USDT"}, {"exchange": "coinbase", "base":
        "EIGEN", "quote": "USD"}, {"exchange": "kraken", "base": "EIGEN", "quote":
        "ZUSD"}, {"exchange": "cryptocom", "base": "EIGEN", "quote": "USD"}, {"exchange":
        "okx", "base": "EIGEN", "quote": "USDT"}, {"exchange": "binance", "base":
        "EIGEN", "quote": "USDT"}, {"exchange": "kraken", "base": "ENA", "quote":
        "ZUSD"}, {"exchange": "cryptocom", "base": "ENA", "quote": "USDT"}, {"exchange":
        "binance", "base": "ENA", "quote": "USDT"}, {"exchange": "gateio", "base":
        "ENA", "quote": "USDT"}, {"exchange": "bitget", "base": "ENA", "quote": "USDT"},
        {"exchange": "bybit", "base": "TRUMP", "quote": "USDT"}, {"exchange": "kraken",
        "base": "TRUMP", "quote": "ZUSD"}, {"exchange": "cryptocom", "base": "TRUMP",
        "quote": "USD"}, {"exchange": "okx", "base": "TRUMP", "quote": "USDT"}, {"exchange":
        "binance", "base": "TRUMP", "quote": "USDT"}, {"exchange": "cryptocom", "base":
        "USDT", "quote": "USD", "dpm": "USD"}, {"exchange": "cryptocom", "base": "BTC",
        "quote": "USD", "dpm": "USD"}, {"exchange": "cryptocom", "base": "ETH", "quote":
        "USD", "dpm": "USD"}, {"exchange": "cryptocom", "base": "SNX", "quote": "USD",
        "dpm": "USD"}, {"exchange": "cryptocom", "base": "SOL", "quote": "USD", "dpm":
        "USD"}, {"exchange": "cryptocom", "base": "DOGE", "quote": "USD", "dpm": "USD"},
        {"exchange": "cryptocom", "base": "XRP", "quote": "USD", "dpm": "USD"}, {"exchange":
        "cryptocom", "base": "ARB", "quote": "USD", "dpm": "USD"}, {"exchange": "cryptocom",
        "base": "ATOM", "quote": "USD", "dpm": "USD"}, {"exchange": "cryptocom", "base":
        "LINK", "quote": "USD", "dpm": "USD"}, {"exchange": "cryptocom", "base": "NEAR",
        "quote": "USD", "dpm": "USD"}, {"exchange": "cryptocom", "base": "OP", "quote":
        "USD", "dpm": "USD"}, {"exchange": "cryptocom", "base": "ONDO", "quote": "USD",
        "dpm": "USD"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesSpot4:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//spot_4
      Type: String
      Value: '{"spot": {"asset_class": "spot", "index_currency": "USD", "output_frequency_ms":
        1000, "allow_wider_range": true, "components": [{"exchange": "cryptocom",
        "base": "GRT", "quote": "USD", "dpm": "USD"}, {"exchange": "cryptocom", "base":
        "ADA", "quote": "USD", "dpm": "USD"}, {"exchange": "bitstamp", "base": "PEPE",
        "quote": "USD"}, {"exchange": "kraken", "base": "PEPE", "quote": "ZUSD"},
        {"exchange": "cryptocom", "base": "PEPE", "quote": "USD"}, {"exchange": "okx",
        "base": "PEPE", "quote": "USDT"}, {"exchange": "binance", "base": "PEPE",
        "quote": "USDT"}, {"exchange": "bitstamp", "base": "WIF", "quote": "USD"},
        {"exchange": "kraken", "base": "WIF", "quote": "ZUSD"}, {"exchange": "cryptocom",
        "base": "WIF", "quote": "USD"}, {"exchange": "okx", "base": "WIF", "quote":
        "USDT"}, {"exchange": "binance", "base": "WIF", "quote": "USDT"}, {"exchange":
        "bybit", "base": "VIRTUAL", "quote": "USDT"}, {"exchange": "kraken", "base":
        "VIRTUAL", "quote": "ZUSD"}, {"exchange": "cryptocom", "base": "VIRTUAL",
        "quote": "USD"}, {"exchange": "bitget", "base": "VIRTUAL", "quote": "USDT"},
        {"exchange": "gateio", "base": "VIRTUAL", "quote": "USDT"}, {"exchange": "kraken",
        "base": "DRV", "quote": "ZUSD"}, {"exchange": "gateio", "base": "DRV", "quote":
        "USDT"}, {"exchange": "coinbase", "base": "POL", "quote": "USDT"}, {"exchange":
        "kraken", "base": "POL", "quote": "ZUSD"}, {"exchange": "cryptocom", "base":
        "POL", "quote": "USD"}, {"exchange": "binance", "base": "POL", "quote": "USDT"},
        {"exchange": "okx", "base": "POL", "quote": "USDT"}, {"exchange": "cryptocom",
        "base": "TON", "quote": "USD"}, {"exchange": "bitfinex", "base": "TON", "quote":
        "USDT"}, {"exchange": "okx", "base": "TON", "quote": "USDT"}, {"exchange":
        "binance", "base": "TON", "quote": "USDT"}, {"exchange": "bitget", "base":
        "TON", "quote": "USDT"}, {"exchange": "coinbase", "base": "KAITO", "quote":
        "USD"}, {"exchange": "binance", "base": "KAITO", "quote": "USDT"}, {"exchange":
        "okx", "base": "KAITO", "quote": "USDT"}, {"exchange": "gateio", "base": "KAITO",
        "quote": "USDT"}, {"exchange": "bitget", "base": "KAITO", "quote": "USDT"},
        {"exchange": "bybit", "base": "IP", "quote": "USDT"}, {"exchange": "okx",
        "base": "IP", "quote": "USDT"}, {"exchange": "gateio", "base": "IP", "quote":
        "USDT"}, {"exchange": "bitget", "base": "IP", "quote": "USDT"}, {"exchange":
        "coinbase", "base": "IP", "quote": "USD"}, {"exchange": "bybit", "base": "RED",
        "quote": "USDT"}, {"exchange": "gateio", "base": "RED", "quote": "USDT"},
        {"exchange": "bitget", "base": "RED", "quote": "USDT"}, {"exchange": "binance",
        "base": "RED", "quote": "USDT"}, {"exchange": "v3uniswap-ethereum", "base":
        "WSTETH", "quote": "ETH"}, {"exchange": "kyberswap-ethereum", "base": "WSTETH",
        "quote": "ETH"}, {"exchange": "curve-ethereum", "base": "WSTETH", "quote":
        "USD"}, {"exchange": "zerox-ethereum", "base": "WSTETH", "quote": "ETH"},
        {"exchange": "v3uniswap-ethereum", "base": "RSWETH", "quote": "ETH"}, {"exchange":
        "kyberswap-ethereum", "base": "RSWETH", "quote": "ETH"}, {"exchange": "curve-ethereum",
        "base": "RSWETH", "quote": "USD"}, {"exchange": "zerox-ethereum", "base":
        "RSWETH", "quote": "ETH"}, {"exchange": "v3uniswap-ethereum", "base": "SUSDE",
        "quote": "USDT"}, {"exchange": "kyberswap-ethereum", "base": "SUSDE", "quote":
        "USDT"}, {"exchange": "curve-ethereum", "base": "SUSDE", "quote": "USD"},
        {"exchange": "v3uniswap-ethereum", "base": "WEETH", "quote": "ETH"}, {"exchange":
        "kyberswap-ethereum", "base": "WEETH", "quote": "ETH"}, {"exchange": "curve-ethereum",
        "base": "WEETH", "quote": "USD"}, {"exchange": "zerox-ethereum", "base": "WEETH",
        "quote": "ETH"}, {"exchange": "kyberswap-ethereum", "base": "DAI", "quote":
        "USDT"}, {"exchange": "curve-ethereum", "base": "DAI", "quote": "USD"}, {"exchange":
        "zerox-ethereum", "base": "DAI", "quote": "USDT"}, {"exchange": "v3uniswap-ethereum",
        "base": "DAI", "quote": "ETH"}, {"exchange": "v3uniswap-ethereum", "base":
        "SDAI", "quote": "ETH"}, {"exchange": "kyberswap-ethereum", "base": "SDAI",
        "quote": "USDT"}, {"exchange": "oneinch-ethereum", "base": "SDAI", "quote":
        "USD"}, {"exchange": "curve-ethereum", "base": "SDAI", "quote": "USD"}, {"exchange":
        "zerox-ethereum", "base": "SDAI", "quote": "USDT"}, {"exchange": "v3uniswap-ethereum",
        "base": "USDE", "quote": "USDT"}, {"exchange": "kyberswap-ethereum", "base":
        "USDE", "quote": "USDT"}, {"exchange": "curve-ethereum", "base": "USDE", "quote":
        "USD"}, {"exchange": "zerox-ethereum", "base": "USDE", "quote": "USDT"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesSpot5:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//spot_5
      Type: String
      Value: '{"spot": {"asset_class": "spot", "index_currency": "USD", "output_frequency_ms":
        1000, "allow_wider_range": true, "components": [{"exchange": "v3uniswap-ethereum",
        "base": "PYUSD", "quote": "USDC"}, {"exchange": "kyberswap-ethereum", "base":
        "PYUSD", "quote": "USDT"}, {"exchange": "curve-ethereum", "base": "PYUSD",
        "quote": "USD"}, {"exchange": "zerox-ethereum", "base": "PYUSD", "quote":
        "USDT"}, {"exchange": "v3uniswap-ethereum", "base": "LBTC", "quote": "WBTC"},
        {"exchange": "kyberswap-ethereum", "base": "LBTC", "quote": "USDT"}, {"exchange":
        "curve-ethereum", "base": "LBTC", "quote": "USD"}, {"exchange": "zerox-ethereum",
        "base": "LBTC", "quote": "USDT"}, {"exchange": "v3uniswap-ethereum", "base":
        "CBBTC", "quote": "ETH"}, {"exchange": "kyberswap-ethereum", "base": "CBBTC",
        "quote": "USDT"}, {"exchange": "curve-ethereum", "base": "CBBTC", "quote":
        "USD"}, {"exchange": "zerox-ethereum", "base": "CBBTC", "quote": "USDT"},
        {"exchange": "v3uniswap-ethereum", "base": "AGETH", "quote": "ETH"}, {"exchange":
        "kyberswap-ethereum", "base": "AGETH", "quote": "ETH"}, {"exchange": "zerox-ethereum",
        "base": "AGETH", "quote": "ETH"}, {"exchange": "kyberswap-ethereum", "base":
        "EBTC", "quote": "WBTC"}, {"exchange": "curve-ethereum", "base": "EBTC", "quote":
        "USD"}, {"exchange": "v3uniswap-ethereum", "base": "EBTC", "quote": "LBTC"},
        {"exchange": "zerox-ethereum", "base": "EBTC", "quote": "WBTC"}, {"exchange":
        "v3uniswap-ethereum", "base": "DEUSD", "quote": "USDC"}, {"exchange": "kyberswap-ethereum",
        "base": "DEUSD", "quote": "USDT"}, {"exchange": "curve-ethereum", "base":
        "DEUSD", "quote": "USD"}, {"exchange": "zerox-ethereum", "base": "DEUSD",
        "quote": "USDT"}, {"exchange": "v3uniswap-ethereum", "base": "WBTC", "quote":
        "ETH"}, {"exchange": "kyberswap-ethereum", "base": "WBTC", "quote": "USDT"},
        {"exchange": "curve-ethereum", "base": "WBTC", "quote": "USD"}, {"exchange":
        "zerox-ethereum", "base": "WBTC", "quote": "USDT"}, {"exchange": "v3uniswap-ethereum",
        "base": "SOLVBTC", "quote": "WBTC"}, {"exchange": "kyberswap-ethereum", "base":
        "SOLVBTC", "quote": "WBTC"}, {"exchange": "kyberswap-bsc", "base": "SOLVBTC",
        "quote": "BNB"}, {"exchange": "curve-ethereum", "base": "SOLVBTC", "quote":
        "USD"}, {"exchange": "zerox-ethereum", "base": "SOLVBTC", "quote": "WBTC"},
        {"exchange": "v3uniswap-ethereum", "base": "SOLVBTC-BBN", "quote": "SOLVBTC"},
        {"exchange": "kyberswap-ethereum", "base": "SOLVBTC-BBN", "quote": "WBTC"},
        {"exchange": "kyberswap-bsc", "base": "SOLVBTC-BBN", "quote": "BNB"}, {"exchange":
        "curve-ethereum", "base": "SOLVBTC-BBN", "quote": "USD"}, {"exchange": "v3uniswap-ethereum",
        "base": "RSETH", "quote": "ETH"}, {"exchange": "kyberswap-ethereum", "base":
        "RSETH", "quote": "ETH"}, {"exchange": "curve-ethereum", "base": "RSETH",
        "quote": "USD"}, {"exchange": "zerox-ethereum", "base": "RSETH", "quote":
        "ETH"}, {"exchange": "gateio", "base": "BITCOIN", "quote": "USDT"}, {"exchange":
        "kyberswap-ethereum", "base": "BITCOIN", "quote": "ETH"}, {"exchange": "v3uniswap-ethereum",
        "base": "BITCOIN", "quote": "ETH"}]}}'
    Type: AWS::SSM::Parameter
  PriceIndicesSpot6:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/price_indices_test//spot_6
      Type: String
      Value: '{"spot": {"asset_class": "spot", "index_currency": "USD", "output_frequency_ms":
        1000, "allow_wider_range": true, "global_exchange_weights": [{"exchange":
        "v3uniswap-ethereum", "global_volume_weight": 0.3}, {"exchange": "kyberswap-ethereum",
        "global_volume_weight": 0.25}, {"exchange": "curve-ethereum", "global_volume_weight":
        0.2}, {"exchange": "zerox-ethereum", "global_volume_weight": 0.25}, {"exchange":
        "v3uniswap-ethereum", "global_volume_weight": 0.2, "base": ["DAI", "EBTC",
        "RSETH", "SOLVBTC"]}, {"exchange": "kyberswap-ethereum", "global_volume_weight":
        0.2, "base": ["LBTC", "SDAI", "SOLVBTC-BBN", "USDE", "WBTC"]}, {"exchange":
        "zerox-ethereum", "global_volume_weight": 0.2, "base": ["EBTC", "SDAI", "USDE",
        "WBTC"]}, {"exchange": "v3uniswap-ethereum", "global_volume_weight": 0.05,
        "base": ["SDAI"]}, {"exchange": "oneinch-ethereum", "global_volume_weight":
        0.25, "base": ["SDAI"]}, {"exchange": "curve-ethereum", "global_volume_weight":
        0.3, "base": ["DAI", "SDAI"]}, {"exchange": "v3uniswap-ethereum", "global_volume_weight":
        0.4, "base": ["AGETH", "BITCOIN", "SOLVBTC-BBN", "USDE", "WBTC"]}, {"exchange":
        "curve-ethereum", "global_volume_weight": 0.2, "base": ["CBBTC", "DEUSD",
        "EBTC", "PYUSD", "RSETH", "SOLVBTC-BBN", "USDE", "WBTC"]}, {"exchange": "v3uniswap-ethereum",
        "global_volume_weight": 0.3, "base": ["CBBTC", "DEUSD", "LBTC", "PYUSD"]},
        {"exchange": "zerox-ethereum", "global_volume_weight": 0.25, "base": ["CBBTC",
        "DAI", "DEUSD", "LBTC", "PYUSD"]}, {"exchange": "kyberswap-bsc", "global_volume_weight":
        0.1, "base": ["LBTC"]}, {"exchange": "curve-ethereum", "global_volume_weight":
        0.25, "base": ["LBTC"]}, {"exchange": "kyberswap-ethereum", "global_volume_weight":
        0.25, "base": ["CBBTC", "DAI", "DEUSD", "PYUSD"]}, {"exchange": "kyberswap-ethereum",
        "global_volume_weight": 0.3, "base": ["AGETH", "RSETH"]}, {"exchange": "zerox-ethereum",
        "global_volume_weight": 0.3, "base": ["AGETH", "RSETH"]}, {"exchange": "kyberswap-ethereum",
        "global_volume_weight": 0.4, "base": ["EBTC"]}, {"exchange": "kyberswap-ethereum",
        "global_volume_weight": 0.15, "base": ["SOLVBTC"]}, {"exchange": "kyberswap-bsc",
        "global_volume_weight": 0.4, "base": ["SOLVBTC"]}, {"exchange": "curve-ethereum",
        "global_volume_weight": 0.1, "base": ["SOLVBTC"]}, {"exchange": "zerox-ethereum",
        "global_volume_weight": 0.15, "base": ["SOLVBTC"]}, {"exchange": "kyberswap-bsc",
        "global_volume_weight": 0.2, "base": ["SOLVBTC-BBN"]}, {"exchange": "kyberswap-ethereum",
        "global_volume_weight": 0.1, "base": ["BITCOIN"]}]}}'
    Type: AWS::SSM::Parameter
  TGBotSpot1:
    Properties:
      DataType: text
      Description: Auto generated price index param
      Name: /config/tg_bot_test//spot_1
      Type: String
      Value: '{"spot": {"asset_class": "spot", "components": [{"exchange": "binance",
        "base": "BTC", "quote": "USD"}, {"exchange": "bybit", "base": "BTC", "quote":
        "USDT"}, {"exchange": "deribit", "base": "BTC", "quote": "USD"}]}}'
    Type: AWS::SSM::Parameter
