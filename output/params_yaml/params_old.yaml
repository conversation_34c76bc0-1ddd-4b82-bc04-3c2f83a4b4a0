AWSTemplateFormatVersion: 2010-09-09
Resources:
  priceindicestestfuture1:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/future1
      Type: String
      Value: '{"future":{"asset_class":"future","output_frequency_ms":1000,"index_currency":"USD","allow_wider_range":true,"components":[{"exchange":"binance","base":"BTC","quote":"USD"},{"exchange":"bybit","base":"BTC","quote":"USDT"},{"exchange":"deribit","base":"BTC","quote":"USD"},{"exchange":"okx","base":"BTC","quote":"USD"},{"exchange":"binance","base":"ETH","quote":"USD"},{"exchange":"bybit","base":"ETH","quote":"USDT"},{"exchange":"deribit","base":"ETH","quote":"USD"},{"exchange":"okx","base":"ETH","quote":"USD"},{"exchange":"pendle-ethereum","base":"PT-SUSDE","quote":"SUSDE"},{"exchange":"pendle-ethereum","base":"YT-SUSDE","quote":"SUSDE"}],"global_exchange_weights":[{"exchange":"pendle-ethereum","global_volume_weight":0.3}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestoption1:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/option1
      Type: String
      Value: '{"option":{"asset_class":"option","output_frequency_ms":1000,"index_currency":"USD","components":[{"exchange":"bybit","base":"BTC","quote":"USDT"},{"exchange":"deribit","base":"BTC","quote":"BTC"},{"exchange":"okx","base":"BTC","quote":"BTC"},{"exchange":"bybit","base":"ETH","quote":"USDT"},{"exchange":"deribit","base":"ETH","quote":"ETH"},{"exchange":"okx","base":"ETH","quote":"ETH"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestperpetual1:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/perpetual1
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","index_currency":"USD","output_frequency_ms":1000,"components":[{"exchange":"binance","base":"BTC","quote":"USDT"},{"exchange":"bybit","base":"BTC","quote":"USDT"},{"exchange":"deribit","base":"BTC","quote":"USD"},{"exchange":"okx","base":"BTC","quote":"USDT"},{"exchange":"bitget","base":"BTC","quote":"USDT"},{"exchange":"binance","base":"ETH","quote":"USDT"},{"exchange":"bybit","base":"ETH","quote":"USDT"},{"exchange":"deribit","base":"ETH","quote":"USD"},{"exchange":"okx","base":"ETH","quote":"USDT"},{"exchange":"bitget","base":"ETH","quote":"USDT"},{"exchange":"okx","base":"SOL","quote":"USDT"},{"exchange":"bybit","base":"SOL","quote":"USDT"},{"exchange":"binance","base":"SOL","quote":"USDT"},{"exchange":"bitget","base":"SOL","quote":"USDT"},{"exchange":"kraken","base":"ARB","quote":"USD"},{"exchange":"bybit","base":"ARB","quote":"USDT"},{"exchange":"okx","base":"ARB","quote":"USDT"},{"exchange":"bitget","base":"ARB","quote":"USDT"},{"exchange":"binance","base":"ATOM","quote":"USDT"},{"exchange":"bybit","base":"ATOM","quote":"USDT"},{"exchange":"okx","base":"ATOM","quote":"USDT"},{"exchange":"bitget","base":"ATOM","quote":"USDT"},{"exchange":"binance","base":"LINK","quote":"USDT"},{"exchange":"bybit","base":"LINK","quote":"USDT"},{"exchange":"deribit","base":"LINK","quote":"USDC"},{"exchange":"okx","base":"LINK","quote":"USDT"},{"exchange":"bitget","base":"LINK","quote":"USDT"},{"exchange":"binance","base":"BNB","quote":"USDT"},{"exchange":"bybit","base":"BNB","quote":"USDT"},{"exchange":"deribit","base":"BNB","quote":"USDC"},{"exchange":"okx","base":"BNB","quote":"USDT"},{"exchange":"bitget","base":"BNB","quote":"USDT"},{"exchange":"binance","base":"DOGE","quote":"USDT"},{"exchange":"bybit","base":"DOGE","quote":"USDT"},{"exchange":"deribit","base":"DOGE","quote":"USDC"},{"exchange":"okx","base":"DOGE","quote":"USDT"},{"exchange":"bitget","base":"DOGE","quote":"USDT"},{"exchange":"kraken","base":"PEPE","quote":"USD"},{"exchange":"okx","base":"PEPE","quote":"USDT"},{"exchange":"bitget","base":"PEPE","quote":"USDT"},{"exchange":"binance","base":"NEAR","quote":"USDT"},{"exchange":"bybit","base":"NEAR","quote":"USDT"},{"exchange":"deribit","base":"NEAR","quote":"USDC"},{"exchange":"okx","base":"NEAR","quote":"USDT"},{"exchange":"bitget","base":"NEAR","quote":"USDT"},{"exchange":"binance","base":"TON","quote":"USDT"},{"exchange":"bybit","base":"TON","quote":"USDT"},{"exchange":"okx","base":"TON","quote":"USDT"},{"exchange":"bitget","base":"TON","quote":"USDT"},{"exchange":"binance","base":"OP","quote":"USDT"},{"exchange":"bybit","base":"OP","quote":"USDT"},{"exchange":"okx","base":"OP","quote":"USDT"},{"exchange":"bitget","base":"OP","quote":"USDT"},{"exchange":"binance","base":"ONDO","quote":"USDT"},{"exchange":"bybit","base":"ONDO","quote":"USDT"},{"exchange":"deribit","base":"ONDO","quote":"USDC"},{"exchange":"okx","base":"ONDO","quote":"USDT"},{"exchange":"bitget","base":"ONDO","quote":"USDT"},{"exchange":"binance","base":"WIF","quote":"USDT"},{"exchange":"bybit","base":"WIF","quote":"USDT"},{"exchange":"deribit","base":"WIF","quote":"USDC"},{"exchange":"okx","base":"WIF","quote":"USDT"},{"exchange":"bitget","base":"WIF","quote":"USDT"},{"exchange":"binance","base":"IMX","quote":"USDT"},{"exchange":"bybit","base":"IMX","quote":"USDT"},{"exchange":"deribit","base":"IMX","quote":"USDC"},{"exchange":"okx","base":"IMX","quote":"USDT"},{"exchange":"bitget","base":"IMX","quote":"USDT"},{"exchange":"binance","base":"GRT","quote":"USDT"},{"exchange":"bybit","base":"GRT","quote":"USDT"},{"exchange":"okx","base":"GRT","quote":"USDT"},{"exchange":"bitget","base":"GRT","quote":"USDT"},{"exchange":"binance","base":"XRP","quote":"USDT"},{"exchange":"bybit","base":"XRP","quote":"USDT"},{"exchange":"deribit","base":"XRP","quote":"USDC"},{"exchange":"okx","base":"XRP","quote":"USDT"},{"exchange":"bitget","base":"XRP","quote":"USDT"},{"exchange":"binance","base":"ZK","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestperpetual2:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/perpetual2
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","index_currency":"USD","output_frequency_ms":1000,"components":[{"exchange":"bybit","base":"ZK","quote":"USDT"},{"exchange":"okx","base":"ZK","quote":"USDT"},{"exchange":"bitget","base":"ZK","quote":"USDT"},{"exchange":"okx","base":"POL","quote":"USDT"},{"exchange":"bybit","base":"POL","quote":"USDT"},{"exchange":"binance","base":"POL","quote":"USDT"},{"exchange":"bitget","base":"POL","quote":"USDT"},{"exchange":"binance","base":"ADA","quote":"USDT"},{"exchange":"bybit","base":"ADA","quote":"USDT"},{"exchange":"deribit","base":"ADA","quote":"USDC"},{"exchange":"okx","base":"ADA","quote":"USDT"},{"exchange":"bitget","base":"ADA","quote":"USDT"},{"exchange":"binance","base":"BERA","quote":"USDT"},{"exchange":"bybit","base":"BERA","quote":"USDT"},{"exchange":"okx","base":"BERA","quote":"USDT"},{"exchange":"bitget","base":"BERA","quote":"USDT"},{"exchange":"binance","base":"FARTCOIN","quote":"USDT"},{"exchange":"bybit","base":"FARTCOIN","quote":"USDT"},{"exchange":"okx","base":"FARTCOIN","quote":"USDT"},{"exchange":"bitget","base":"FARTCOIN","quote":"USDT"},{"exchange":"binance","base":"PENGU","quote":"USDT"},{"exchange":"bybit","base":"PENGU","quote":"USDT"},{"exchange":"okx","base":"PENGU","quote":"USDT"},{"exchange":"bitget","base":"PENGU","quote":"USDT"},{"exchange":"binance","base":"POPCAT","quote":"USDT"},{"exchange":"bybit","base":"POPCAT","quote":"USDT"},{"exchange":"okx","base":"POPCAT","quote":"USDT"},{"exchange":"bitget","base":"POPCAT","quote":"USDT"},{"exchange":"binance","base":"SUI","quote":"USDT"},{"exchange":"bybit","base":"SUI","quote":"USDT"},{"exchange":"okx","base":"SUI","quote":"USDT"},{"exchange":"bitget","base":"SUI","quote":"USDT"},{"exchange":"binance","base":"TRUMP","quote":"USDT"},{"exchange":"bybit","base":"TRUMP","quote":"USDT"},{"exchange":"okx","base":"TRUMP","quote":"USDT"},{"exchange":"bitget","base":"TRUMP","quote":"USDT"},{"exchange":"binance","base":"VIRTUAL","quote":"USDT"},{"exchange":"okx","base":"VIRTUAL","quote":"USDT"},{"exchange":"bitget","base":"VIRTUAL","quote":"USDT"},{"exchange":"bybit","base":"VIRTUAL","quote":"USDT"},{"exchange":"binance","base":"XLM","quote":"USDT"},{"exchange":"okx","base":"XLM","quote":"USDT"},{"exchange":"bitget","base":"XLM","quote":"USDT"},{"exchange":"bybit","base":"XLM","quote":"USDT"},{"exchange":"binance","base":"WLD","quote":"USDT"},{"exchange":"okx","base":"WLD","quote":"USDT"},{"exchange":"bybit","base":"WLD","quote":"USDT"},{"exchange":"bitget","base":"WLD","quote":"USDT"},{"exchange":"binance","base":"ENA","quote":"USDT"},{"exchange":"okx","base":"ENA","quote":"USDT"},{"exchange":"bybit","base":"ENA","quote":"USDT"},{"exchange":"bitget","base":"ENA","quote":"USDT"},{"exchange":"binance","base":"AAVE","quote":"USDT"},{"exchange":"okx","base":"AAVE","quote":"USDT"},{"exchange":"bybit","base":"AAVE","quote":"USDT"},{"exchange":"bitget","base":"AAVE","quote":"USDT"},{"exchange":"binance","base":"JUP","quote":"USDT"},{"exchange":"okx","base":"JUP","quote":"USDT"},{"exchange":"bybit","base":"JUP","quote":"USDT"},{"exchange":"bitget","base":"JUP","quote":"USDT"},{"exchange":"binance","base":"AIXBT","quote":"USDT"},{"exchange":"okx","base":"AIXBT","quote":"USDT"},{"exchange":"bybit","base":"AIXBT","quote":"USDT"},{"exchange":"bitget","base":"AIXBT","quote":"USDT"},{"exchange":"binance","base":"AI16Z","quote":"USDT"},{"exchange":"okx","base":"AI16Z","quote":"USDT"},{"exchange":"bybit","base":"AI16Z","quote":"USDT"},{"exchange":"bitget","base":"AI16Z","quote":"USDT"},{"exchange":"binance","base":"VINE","quote":"USDT"},{"exchange":"okx","base":"VINE","quote":"USDT"},{"exchange":"bybit","base":"VINE","quote":"USDT"},{"exchange":"bitget","base":"VINE","quote":"USDT"},{"exchange":"binance","base":"PENDLE","quote":"USDT"},{"exchange":"bybit","base":"PENDLE","quote":"USDT"},{"exchange":"bitget","base":"PENDLE","quote":"USDT"},{"exchange":"binance","base":"UXLINK","quote":"USDT"},{"exchange":"okx","base":"UXLINK","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestperpetual3:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/perpetual3
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","index_currency":"USD","output_frequency_ms":1000,"components":[{"exchange":"bybit","base":"UXLINK","quote":"USDT"},{"exchange":"bitget","base":"UXLINK","quote":"USDT"},{"exchange":"binance","base":"KAITO","quote":"USDT"},{"exchange":"okx","base":"KAITO","quote":"USDT"},{"exchange":"bybit","base":"KAITO","quote":"USDT"},{"exchange":"bitget","base":"KAITO","quote":"USDT"},{"exchange":"okx","base":"BONK","quote":"USDT"},{"exchange":"bybit","base":"1000BONK","quote":"USDT","o":"BONK","m":0.001},{"exchange":"bitget","base":"1000BONK","quote":"USDT","o":"BONK","m":0.001},{"exchange":"binance","base":"1000BONK","quote":"USDT","o":"BONK","m":0.001},{"exchange":"okx","base":"SHIB","quote":"USDT"},{"exchange":"bitget","base":"SHIB","quote":"USDT"},{"exchange":"binance","base":"1000SHIB","quote":"USDT","o":"SHIB","m":0.001},{"exchange":"bybit","base":"SHIB1000","quote":"USDT","o":"SHIB","m":0.001},{"exchange":"binance","base":"IP","quote":"USDT"},{"exchange":"okx","base":"IP","quote":"USDT"},{"exchange":"bybit","base":"IP","quote":"USDT"},{"exchange":"bybit","base":"RED","quote":"USDT"},{"exchange":"binance","base":"RED","quote":"USDT"},{"exchange":"gateio","base":"RED","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestspot1:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/spot1
      Type: String
      Value: '{"spot":{"asset_class":"spot","index_currency":"USD","output_frequency_ms":1000,"allow_wider_range":true,"components":[{"exchange":"bitfinex","base":"USDC","quote":"USD"},{"exchange":"bitstamp","base":"USDC","quote":"USD"},{"exchange":"kraken","base":"USDC","quote":"ZUSD"},{"exchange":"coinbase","base":"USDT","quote":"USD"},{"exchange":"cryptocom","base":"USDT","quote":"USD"},{"exchange":"bitfinex","base":"USDT","quote":"USD"},{"exchange":"bitstamp","base":"USDT","quote":"USD"},{"exchange":"kraken","base":"USDT","quote":"ZUSD"},{"exchange":"coinbase","base":"BTC","quote":"USD"},{"exchange":"cryptocom","base":"BTC","quote":"USD"},{"exchange":"bybit","base":"BTC","quote":"USDT"},{"exchange":"binance","base":"BTC","quote":"USDT"},{"exchange":"okx","base":"BTC","quote":"USDT"},{"exchange":"kraken","base":"BTC","quote":"ZUSD"},{"exchange":"coinbase","base":"ETH","quote":"USD"},{"exchange":"cryptocom","base":"ETH","quote":"USD"},{"exchange":"binance","base":"ETH","quote":"USDT"},{"exchange":"okx","base":"ETH","quote":"USDT"},{"exchange":"kraken","base":"ETH","quote":"ZUSD"},{"exchange":"coinbase","base":"SNX","quote":"USD"},{"exchange":"cryptocom","base":"SNX","quote":"USD"},{"exchange":"bitfinex","base":"SNX","quote":"USD"},{"exchange":"bitstamp","base":"SNX","quote":"USD"},{"exchange":"kraken","base":"SNX","quote":"ZUSD"},{"exchange":"coinbase","base":"SOL","quote":"USD"},{"exchange":"cryptocom","base":"SOL","quote":"USD"},{"exchange":"bitfinex","base":"SOL","quote":"USD"},{"exchange":"bitstamp","base":"SOL","quote":"USD"},{"exchange":"kraken","base":"SOL","quote":"ZUSD"},{"exchange":"cryptocom","base":"DOGE","quote":"USD"},{"exchange":"bitstamp","base":"DOGE","quote":"USD"},{"exchange":"coinbase","base":"DOGE","quote":"USD"},{"exchange":"okx","base":"DOGE","quote":"USDT"},{"exchange":"binance","base":"DOGE","quote":"USDT"},{"exchange":"cryptocom","base":"XRP","quote":"USD"},{"exchange":"bitstamp","base":"XRP","quote":"USD"},{"exchange":"coinbase","base":"XRP","quote":"USD"},{"exchange":"kraken","base":"XRP","quote":"ZUSD"},{"exchange":"binance","base":"XRP","quote":"USDT"},{"exchange":"kraken","base":"ARB","quote":"ZUSD"},{"exchange":"coinbase","base":"ARB","quote":"USD"},{"exchange":"cryptocom","base":"ARB","quote":"USD"},{"exchange":"okx","base":"ARB","quote":"USDT"},{"exchange":"binance","base":"ARB","quote":"USDT"},{"exchange":"kraken","base":"ATOM","quote":"ZUSD"},{"exchange":"coinbase","base":"ATOM","quote":"USD"},{"exchange":"cryptocom","base":"ATOM","quote":"USD"},{"exchange":"okx","base":"ATOM","quote":"USDT"},{"exchange":"binance","base":"ATOM","quote":"USDT"},{"exchange":"cryptocom","base":"LINK","quote":"USD"},{"exchange":"bitstamp","base":"LINK","quote":"USD"},{"exchange":"kraken","base":"LINK","quote":"ZUSD"},{"exchange":"coinbase","base":"LINK","quote":"USD"},{"exchange":"okx","base":"LINK","quote":"USDT"},{"exchange":"bitstamp","base":"NEAR","quote":"USD"},{"exchange":"kraken","base":"NEAR","quote":"ZUSD"},{"exchange":"coinbase","base":"NEAR","quote":"USD"},{"exchange":"cryptocom","base":"NEAR","quote":"USD"},{"exchange":"okx","base":"NEAR","quote":"USDT"},{"exchange":"kraken","base":"OP","quote":"ZUSD"},{"exchange":"coinbase","base":"OP","quote":"USD"},{"exchange":"cryptocom","base":"OP","quote":"USD"},{"exchange":"okx","base":"OP","quote":"USDT"},{"exchange":"binance","base":"OP","quote":"USDT"},{"exchange":"kraken","base":"ONDO","quote":"ZUSD"},{"exchange":"coinbase","base":"ONDO","quote":"USD"},{"exchange":"cryptocom","base":"ONDO","quote":"USD"},{"exchange":"okx","base":"ONDO","quote":"USDT"},{"exchange":"bitget","base":"ONDO","quote":"USDT"},{"exchange":"bitstamp","base":"GRT","quote":"USD"},{"exchange":"kraken","base":"GRT","quote":"ZUSD"},{"exchange":"coinbase","base":"GRT","quote":"USD"},{"exchange":"cryptocom","base":"GRT","quote":"USD"},{"exchange":"okx","base":"GRT","quote":"USDT"},{"exchange":"binance","base":"AIXBT","quote":"USDT"},{"exchange":"bybit","base":"AIXBT","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestspot2:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/spot2
      Type: String
      Value: '{"spot":{"asset_class":"spot","index_currency":"USD","output_frequency_ms":1000,"allow_wider_range":true,"components":[{"exchange":"bitget","base":"AIXBT","quote":"USDT"},{"exchange":"gateio","base":"AIXBT","quote":"USDT"},{"exchange":"okx","base":"AIXBT","quote":"USDT"},{"exchange":"kraken","base":"AI16Z","quote":"ZUSD"},{"exchange":"bybit","base":"AI16Z","quote":"USDT"},{"exchange":"bitget","base":"AI16Z","quote":"USDT"},{"exchange":"gateio","base":"AI16Z","quote":"USDT"},{"exchange":"cryptocom","base":"AI16Z","quote":"USD"},{"exchange":"kraken","base":"VINE","quote":"ZUSD"},{"exchange":"okx","base":"VINE","quote":"USDT"},{"exchange":"bitget","base":"VINE","quote":"USDT"},{"exchange":"gateio","base":"VINE","quote":"USDT"},{"exchange":"cryptocom","base":"VINE","quote":"USD"},{"exchange":"kraken","base":"PENDLE","quote":"ZUSD"},{"exchange":"okx","base":"PENDLE","quote":"USDT"},{"exchange":"bitget","base":"PENDLE","quote":"USDT"},{"exchange":"bybit","base":"PENDLE","quote":"USDT"},{"exchange":"binance","base":"PENDLE","quote":"USDT"},{"exchange":"okx","base":"UXLINK","quote":"USDT"},{"exchange":"bitget","base":"UXLINK","quote":"USDT"},{"exchange":"bybit","base":"UXLINK","quote":"USDT"},{"exchange":"gateio","base":"UXLINK","quote":"USDT"},{"exchange":"okx","base":"SHIB","quote":"USDT"},{"exchange":"kraken","base":"SHIB","quote":"ZUSD"},{"exchange":"bybit","base":"SHIB","quote":"USDT"},{"exchange":"binance","base":"SHIB","quote":"USDT"},{"exchange":"coinbase","base":"SHIB","quote":"USD"},{"exchange":"okx","base":"BONK","quote":"USDT"},{"exchange":"kraken","base":"BONK","quote":"ZUSD"},{"exchange":"bybit","base":"BONK","quote":"USDT"},{"exchange":"binance","base":"BONK","quote":"USDT"},{"exchange":"coinbase","base":"BONK","quote":"USD"},{"exchange":"okx","base":"DEGEN","quote":"USDT"},{"exchange":"coinbase","base":"DEGEN","quote":"USD"},{"exchange":"cryptocom","base":"DEGEN","quote":"USD"},{"exchange":"gateio","base":"DEGEN","quote":"USDT"},{"exchange":"bitget","base":"DEGEN","quote":"USDT"},{"exchange":"coinbase","base":"XLM","quote":"USD"},{"exchange":"kraken","base":"XLM","quote":"ZUSD"},{"exchange":"cryptocom","base":"XLM","quote":"USD"},{"exchange":"okx","base":"XLM","quote":"USDT"},{"exchange":"binance","base":"XLM","quote":"USDT"},{"exchange":"coinbase","base":"POPCAT","quote":"USD"},{"exchange":"bybit","base":"POPCAT","quote":"USDT"},{"exchange":"kraken","base":"POPCAT","quote":"ZUSD"},{"exchange":"gateio","base":"POPCAT","quote":"USDT"},{"exchange":"cryptocom","base":"POPCAT","quote":"USD"},{"exchange":"coinbase","base":"PENGU","quote":"USD"},{"exchange":"bybit","base":"PENGU","quote":"USDT"},{"exchange":"kraken","base":"PENGU","quote":"ZUSD"},{"exchange":"gateio","base":"PENGU","quote":"USDT"},{"exchange":"binance","base":"PENGU","quote":"USDT"},{"exchange":"binance","base":"JUP","quote":"USDT"},{"exchange":"bybit","base":"JUP","quote":"USDT"},{"exchange":"kraken","base":"JUP","quote":"ZUSD"},{"exchange":"gateio","base":"JUP","quote":"USDT"},{"exchange":"okx","base":"JUP","quote":"USDT"},{"exchange":"bitstamp","base":"IMX","quote":"USD"},{"exchange":"kraken","base":"IMX","quote":"ZUSD"},{"exchange":"coinbase","base":"IMX","quote":"USD"},{"exchange":"cryptocom","base":"IMX","quote":"USD"},{"exchange":"okx","base":"IMX","quote":"USDT"},{"exchange":"bitget","base":"FARTCOIN","quote":"USDT"},{"exchange":"gateio","base":"FARTCOIN","quote":"USDT"},{"exchange":"kraken","base":"FARTCOIN","quote":"ZUSD"},{"exchange":"cryptocom","base":"FARTCOIN","quote":"USD"},{"exchange":"bitget","base":"BERA","quote":"USDT"},{"exchange":"gateio","base":"BERA","quote":"USDT"},{"exchange":"bybit","base":"BERA","quote":"USDT"},{"exchange":"coinbase","base":"BERA","quote":"USD"},{"exchange":"binance","base":"BERA","quote":"USDT"},{"exchange":"cryptocom","base":"AAVE","quote":"USD"},{"exchange":"bitstamp","base":"AAVE","quote":"USD"},{"exchange":"coinbase","base":"AAVE","quote":"USD"},{"exchange":"kraken","base":"AAVE","quote":"ZUSD"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestspot3:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/spot3
      Type: String
      Value: '{"spot":{"asset_class":"spot","index_currency":"USD","output_frequency_ms":1000,"allow_wider_range":true,"components":[{"exchange":"okx","base":"AAVE","quote":"USDT"},{"exchange":"cryptocom","base":"SUI","quote":"USD"},{"exchange":"bitstamp","base":"SUI","quote":"USD"},{"exchange":"coinbase","base":"SUI","quote":"USD"},{"exchange":"kraken","base":"SUI","quote":"ZUSD"},{"exchange":"okx","base":"SUI","quote":"USDT"},{"exchange":"coinbase","base":"TIA","quote":"USD"},{"exchange":"kraken","base":"TIA","quote":"ZUSD"},{"exchange":"cryptocom","base":"TIA","quote":"USD"},{"exchange":"okx","base":"TIA","quote":"USDT"},{"exchange":"binance","base":"TIA","quote":"USDT"},{"exchange":"binance","base":"BNB","quote":"USDT"},{"exchange":"okx","base":"BNB","quote":"USDT"},{"exchange":"bybit","base":"BNB","quote":"USDT"},{"exchange":"gateio","base":"BNB","quote":"USDT"},{"exchange":"bitget","base":"BNB","quote":"USDT"},{"exchange":"cryptocom","base":"WLD","quote":"USD"},{"exchange":"binance","base":"WLD","quote":"USDT"},{"exchange":"okx","base":"WLD","quote":"USDT"},{"exchange":"gateio","base":"WLD","quote":"USDT"},{"exchange":"bybit","base":"WLD","quote":"USDT"},{"exchange":"cryptocom","base":"ZK","quote":"USD"},{"exchange":"okx","base":"ZK","quote":"USDT"},{"exchange":"binance","base":"ZK","quote":"USDT"},{"exchange":"gateio","base":"ZK","quote":"USDT"},{"exchange":"bybit","base":"ZK","quote":"USDT"},{"exchange":"coinbase","base":"AVAX","quote":"USD"},{"exchange":"kraken","base":"AVAX","quote":"ZUSD"},{"exchange":"bitstamp","base":"AVAX","quote":"USD"},{"exchange":"cryptocom","base":"AVAX","quote":"USD"},{"exchange":"okx","base":"AVAX","quote":"USDT"},{"exchange":"coinbase","base":"UNI","quote":"USD"},{"exchange":"kraken","base":"UNI","quote":"ZUSD"},{"exchange":"bitstamp","base":"UNI","quote":"USD"},{"exchange":"cryptocom","base":"UNI","quote":"USD"},{"exchange":"okx","base":"UNI","quote":"USDT"},{"exchange":"binance","base":"UNI","quote":"USDT"},{"exchange":"kraken","base":"TAO","quote":"ZUSD"},{"exchange":"cryptocom","base":"TAO","quote":"USD"},{"exchange":"binance","base":"TAO","quote":"USDT"},{"exchange":"gateio","base":"TAO","quote":"USDT"},{"exchange":"bitget","base":"TAO","quote":"USDT"},{"exchange":"coinbase","base":"SEI","quote":"USD"},{"exchange":"kraken","base":"SEI","quote":"ZUSD"},{"exchange":"cryptocom","base":"SEI","quote":"USD"},{"exchange":"binance","base":"SEI","quote":"USDT"},{"exchange":"bitget","base":"SEI","quote":"USDT"},{"exchange":"coinbase","base":"EIGEN","quote":"USD"},{"exchange":"kraken","base":"EIGEN","quote":"ZUSD"},{"exchange":"cryptocom","base":"EIGEN","quote":"USD"},{"exchange":"okx","base":"EIGEN","quote":"USDT"},{"exchange":"binance","base":"EIGEN","quote":"USDT"},{"exchange":"kraken","base":"ENA","quote":"ZUSD"},{"exchange":"cryptocom","base":"ENA","quote":"USDT"},{"exchange":"binance","base":"ENA","quote":"USDT"},{"exchange":"gateio","base":"ENA","quote":"USDT"},{"exchange":"bitget","base":"ENA","quote":"USDT"},{"exchange":"bybit","base":"TRUMP","quote":"USDT"},{"exchange":"kraken","base":"TRUMP","quote":"ZUSD"},{"exchange":"cryptocom","base":"TRUMP","quote":"USD"},{"exchange":"okx","base":"TRUMP","quote":"USDT"},{"exchange":"binance","base":"TRUMP","quote":"USDT"},{"exchange":"bitstamp","base":"ADA","quote":"USD"},{"exchange":"kraken","base":"ADA","quote":"ZUSD"},{"exchange":"coinbase","base":"ADA","quote":"USD"},{"exchange":"cryptocom","base":"ADA","quote":"USD"},{"exchange":"okx","base":"ADA","quote":"USDT"},{"exchange":"bitstamp","base":"PEPE","quote":"USD"},{"exchange":"kraken","base":"PEPE","quote":"ZUSD"},{"exchange":"cryptocom","base":"PEPE","quote":"USD"},{"exchange":"okx","base":"PEPE","quote":"USDT"},{"exchange":"binance","base":"PEPE","quote":"USDT"},{"exchange":"bitstamp","base":"WIF","quote":"USD"},{"exchange":"kraken","base":"WIF","quote":"ZUSD"},{"exchange":"cryptocom","base":"WIF","quote":"USD"},{"exchange":"okx","base":"WIF","quote":"USDT"},{"exchange":"binance","base":"WIF","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestspot4:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/spot4
      Type: String
      Value: '{"spot":{"asset_class":"spot","index_currency":"USD","output_frequency_ms":1000,"allow_wider_range":true,"components":[{"exchange":"bybit","base":"VIRTUAL","quote":"USDT"},{"exchange":"kraken","base":"VIRTUAL","quote":"ZUSD"},{"exchange":"cryptocom","base":"VIRTUAL","quote":"USD"},{"exchange":"bitget","base":"VIRTUAL","quote":"USDT"},{"exchange":"gateio","base":"VIRTUAL","quote":"USDT"},{"exchange":"kraken","base":"DRV","quote":"ZUSD"},{"exchange":"gateio","base":"DRV","quote":"USDT"},{"exchange":"coinbase","base":"POL","quote":"USDT"},{"exchange":"kraken","base":"POL","quote":"ZUSD"},{"exchange":"cryptocom","base":"POL","quote":"USD"},{"exchange":"binance","base":"POL","quote":"USDT"},{"exchange":"okx","base":"POL","quote":"USDT"},{"exchange":"cryptocom","base":"TON","quote":"USD"},{"exchange":"bitfinex","base":"TON","quote":"USDT"},{"exchange":"okx","base":"TON","quote":"USDT"},{"exchange":"binance","base":"TON","quote":"USDT"},{"exchange":"bitget","base":"TON","quote":"USDT"},{"exchange":"coinbase","base":"KAITO","quote":"USD"},{"exchange":"binance","base":"KAITO","quote":"USDT"},{"exchange":"okx","base":"KAITO","quote":"USDT"},{"exchange":"gateio","base":"KAITO","quote":"USDT"},{"exchange":"bitget","base":"KAITO","quote":"USDT"},{"exchange":"bybit","base":"IP","quote":"USDT"},{"exchange":"okx","base":"IP","quote":"USDT"},{"exchange":"gateio","base":"IP","quote":"USDT"},{"exchange":"bitget","base":"IP","quote":"USDT"},{"exchange":"coinbase","base":"IP","quote":"USD"},{"exchange":"bybit","base":"RED","quote":"USDT"},{"exchange":"gateio","base":"RED","quote":"USDT"},{"exchange":"bitget","base":"RED","quote":"USDT"},{"exchange":"binance","base":"RED","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"WSTETH","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"WSTETH","quote":"ETH"},{"exchange":"curve-ethereum","base":"WSTETH","quote":"USD"},{"exchange":"zerox-ethereum","base":"WSTETH","quote":"ETH"},{"exchange":"v3uniswap-ethereum","base":"RSWETH","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"RSWETH","quote":"ETH"},{"exchange":"curve-ethereum","base":"RSWETH","quote":"USD"},{"exchange":"zerox-ethereum","base":"RSWETH","quote":"ETH"},{"exchange":"v3uniswap-ethereum","base":"SUSDE","quote":"USDT"},{"exchange":"kyberswap-ethereum","base":"SUSDE","quote":"USDT"},{"exchange":"curve-ethereum","base":"SUSDE","quote":"USD"},{"exchange":"v3uniswap-ethereum","base":"WEETH","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"WEETH","quote":"ETH"},{"exchange":"curve-ethereum","base":"WEETH","quote":"USD"},{"exchange":"zerox-ethereum","base":"WEETH","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"DAI","quote":"USDT"},{"exchange":"curve-ethereum","base":"DAI","quote":"USD"},{"exchange":"zerox-ethereum","base":"DAI","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"DAI","quote":"ETH"},{"exchange":"v3uniswap-ethereum","base":"SDAI","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"SDAI","quote":"USDT"},{"exchange":"oneinch-ethereum","base":"SDAI","quote":"USD"},{"exchange":"curve-ethereum","base":"SDAI","quote":"USD"},{"exchange":"zerox-ethereum","base":"SDAI","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"USDE","quote":"USDT"},{"exchange":"kyberswap-ethereum","base":"USDE","quote":"USDT"},{"exchange":"curve-ethereum","base":"USDE","quote":"USD"},{"exchange":"zerox-ethereum","base":"USDE","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"PYUSD","quote":"USDC"},{"exchange":"kyberswap-ethereum","base":"PYUSD","quote":"USDT"},{"exchange":"curve-ethereum","base":"PYUSD","quote":"USD"},{"exchange":"zerox-ethereum","base":"PYUSD","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"LBTC","quote":"WBTC"},{"exchange":"kyberswap-ethereum","base":"LBTC","quote":"USDT"},{"exchange":"curve-ethereum","base":"LBTC","quote":"USD"},{"exchange":"zerox-ethereum","base":"LBTC","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"CBBTC","quote":"ETH"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestspot5:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/spot5
      Type: String
      Value: '{"spot":{"asset_class":"spot","index_currency":"USD","output_frequency_ms":1000,"allow_wider_range":true,"components":[{"exchange":"kyberswap-ethereum","base":"CBBTC","quote":"USDT"},{"exchange":"curve-ethereum","base":"CBBTC","quote":"USD"},{"exchange":"zerox-ethereum","base":"CBBTC","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"AGETH","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"AGETH","quote":"ETH"},{"exchange":"zerox-ethereum","base":"AGETH","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"EBTC","quote":"WBTC"},{"exchange":"curve-ethereum","base":"EBTC","quote":"USD"},{"exchange":"v3uniswap-ethereum","base":"EBTC","quote":"LBTC"},{"exchange":"zerox-ethereum","base":"EBTC","quote":"WBTC"},{"exchange":"v3uniswap-ethereum","base":"DEUSD","quote":"USDC"},{"exchange":"kyberswap-ethereum","base":"DEUSD","quote":"USDT"},{"exchange":"curve-ethereum","base":"DEUSD","quote":"USD"},{"exchange":"zerox-ethereum","base":"DEUSD","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"WBTC","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"WBTC","quote":"USDT"},{"exchange":"curve-ethereum","base":"WBTC","quote":"USD"},{"exchange":"zerox-ethereum","base":"WBTC","quote":"USDT"},{"exchange":"v3uniswap-ethereum","base":"SOLVBTC","quote":"WBTC"},{"exchange":"kyberswap-ethereum","base":"SOLVBTC","quote":"WBTC"},{"exchange":"kyberswap-bsc","base":"SOLVBTC","quote":"BNB"},{"exchange":"curve-ethereum","base":"SOLVBTC","quote":"USD"},{"exchange":"zerox-ethereum","base":"SOLVBTC","quote":"WBTC"},{"exchange":"v3uniswap-ethereum","base":"SOLVBTC-BBN","quote":"SOLVBTC"},{"exchange":"kyberswap-ethereum","base":"SOLVBTC-BBN","quote":"WBTC"},{"exchange":"kyberswap-bsc","base":"SOLVBTC-BBN","quote":"BNB"},{"exchange":"curve-ethereum","base":"SOLVBTC-BBN","quote":"USD"},{"exchange":"v3uniswap-ethereum","base":"RSETH","quote":"ETH"},{"exchange":"kyberswap-ethereum","base":"RSETH","quote":"ETH"},{"exchange":"curve-ethereum","base":"RSETH","quote":"USD"},{"exchange":"zerox-ethereum","base":"RSETH","quote":"ETH"},{"exchange":"gateio","base":"BITCOIN","quote":"USDT"},{"exchange":"kyberswap-ethereum","base":"BITCOIN","quote":"ETH"},{"exchange":"v3uniswap-ethereum","base":"BITCOIN","quote":"ETH"},{"exchange":"okx","base":"CRV","quote":"USDT"},{"exchange":"binance","base":"CRV","quote":"USDT"},{"exchange":"kraken","base":"CRV","quote":"ZUSD"},{"exchange":"coinbase","base":"CRV","quote":"USD"},{"exchange":"cryptocom","base":"CRV","quote":"USD"},{"exchange":"bitstamp","base":"CRV","quote":"USD"},{"exchange":"bitfinex","base":"CRV","quote":"USD"}]}}'
    Type: AWS::SSM::Parameter
  priceindicestestspot6:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/price_indices_test/spot6
      Type: String
      Value: '{"spot":{"asset_class":"spot","index_currency":"USD","output_frequency_ms":1000,"allow_wider_range":true,"global_exchange_weights":[{"exchange":"v3uniswap-ethereum","global_volume_weight":0.3},{"exchange":"kyberswap-ethereum","global_volume_weight":0.25},{"exchange":"curve-ethereum","global_volume_weight":0.2},{"exchange":"zerox-ethereum","global_volume_weight":0.25},{"exchange":"v3uniswap-ethereum","global_volume_weight":0.2,"base":["DAI","EBTC","RSETH","SOLVBTC"]},{"exchange":"kyberswap-ethereum","global_volume_weight":0.2,"base":["LBTC","SDAI","SOLVBTC-BBN","USDE","WBTC"]},{"exchange":"zerox-ethereum","global_volume_weight":0.2,"base":["EBTC","SDAI","USDE","WBTC"]},{"exchange":"v3uniswap-ethereum","global_volume_weight":0.05,"base":["SDAI"]},{"exchange":"oneinch-ethereum","global_volume_weight":0.25,"base":["SDAI"]},{"exchange":"curve-ethereum","global_volume_weight":0.3,"base":["DAI","SDAI"]},{"exchange":"v3uniswap-ethereum","global_volume_weight":0.4,"base":["AGETH","BITCOIN","SOLVBTC-BBN","USDE","WBTC"]},{"exchange":"curve-ethereum","global_volume_weight":0.2,"base":["CBBTC","DEUSD","EBTC","PYUSD","RSETH","SOLVBTC-BBN","USDE","WBTC"]},{"exchange":"v3uniswap-ethereum","global_volume_weight":0.3,"base":["CBBTC","DEUSD","LBTC","PYUSD"]},{"exchange":"zerox-ethereum","global_volume_weight":0.25,"base":["CBBTC","DAI","DEUSD","LBTC","PYUSD"]},{"exchange":"kyberswap-bsc","global_volume_weight":0.1,"base":["LBTC"]},{"exchange":"curve-ethereum","global_volume_weight":0.25,"base":["LBTC"]},{"exchange":"kyberswap-ethereum","global_volume_weight":0.25,"base":["CBBTC","DAI","DEUSD","PYUSD"]},{"exchange":"kyberswap-ethereum","global_volume_weight":0.3,"base":["AGETH","RSETH"]},{"exchange":"zerox-ethereum","global_volume_weight":0.3,"base":["AGETH","RSETH"]},{"exchange":"kyberswap-ethereum","global_volume_weight":0.4,"base":["EBTC"]},{"exchange":"kyberswap-ethereum","global_volume_weight":0.15,"base":["SOLVBTC"]},{"exchange":"kyberswap-bsc","global_volume_weight":0.4,"base":["SOLVBTC"]},{"exchange":"curve-ethereum","global_volume_weight":0.1,"base":["SOLVBTC"]},{"exchange":"zerox-ethereum","global_volume_weight":0.15,"base":["SOLVBTC"]},{"exchange":"kyberswap-bsc","global_volume_weight":0.2,"base":["SOLVBTC-BBN"]},{"exchange":"kyberswap-ethereum","global_volume_weight":0.1,"base":["BITCOIN"]}]}}'
    Type: AWS::SSM::Parameter
  tgbottestperpetual1:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/perpetual1
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","components":[{"exchange":"binance","base":"1000BONK","quote":"USD"},{"exchange":"binance","base":"1000BONK","quote":"USDC"},{"exchange":"binance","base":"1000BONK","quote":"USDT"},{"exchange":"binance","base":"1000SHIB","quote":"USD"},{"exchange":"binance","base":"1000SHIB","quote":"USDC"},{"exchange":"binance","base":"1000SHIB","quote":"USDT"},{"exchange":"binance","base":"AAVE","quote":"USD"},{"exchange":"binance","base":"AAVE","quote":"USDC"},{"exchange":"binance","base":"AAVE","quote":"USDT"},{"exchange":"binance","base":"ADA","quote":"USD"},{"exchange":"binance","base":"ADA","quote":"USDC"},{"exchange":"binance","base":"ADA","quote":"USDT"},{"exchange":"binance","base":"AIXBT","quote":"USD"},{"exchange":"binance","base":"AIXBT","quote":"USDC"},{"exchange":"binance","base":"AIXBT","quote":"USDT"},{"exchange":"binance","base":"ATOM","quote":"USD"},{"exchange":"binance","base":"ATOM","quote":"USDC"},{"exchange":"binance","base":"ATOM","quote":"USDT"},{"exchange":"binance","base":"BERA","quote":"USD"},{"exchange":"binance","base":"BERA","quote":"USDC"},{"exchange":"binance","base":"BERA","quote":"USDT"},{"exchange":"binance","base":"BNB","quote":"USD"},{"exchange":"binance","base":"BNB","quote":"USDC"},{"exchange":"binance","base":"BNB","quote":"USDT"},{"exchange":"binance","base":"BTC","quote":"USD"},{"exchange":"binance","base":"BTC","quote":"USDC"},{"exchange":"binance","base":"BTC","quote":"USDT"},{"exchange":"binance","base":"DOGE","quote":"USD"},{"exchange":"binance","base":"DOGE","quote":"USDC"},{"exchange":"binance","base":"DOGE","quote":"USDT"},{"exchange":"binance","base":"ENA","quote":"USD"},{"exchange":"binance","base":"ENA","quote":"USDC"},{"exchange":"binance","base":"ENA","quote":"USDT"},{"exchange":"binance","base":"ETH","quote":"USD"},{"exchange":"binance","base":"ETH","quote":"USDC"},{"exchange":"binance","base":"ETH","quote":"USDT"},{"exchange":"binance","base":"FARTCOIN","quote":"USD"},{"exchange":"binance","base":"FARTCOIN","quote":"USDC"},{"exchange":"binance","base":"FARTCOIN","quote":"USDT"},{"exchange":"binance","base":"GRT","quote":"USD"},{"exchange":"binance","base":"GRT","quote":"USDC"},{"exchange":"binance","base":"GRT","quote":"USDT"},{"exchange":"binance","base":"IMX","quote":"USD"},{"exchange":"binance","base":"IMX","quote":"USDC"},{"exchange":"binance","base":"IMX","quote":"USDT"},{"exchange":"binance","base":"JUP","quote":"USD"},{"exchange":"binance","base":"JUP","quote":"USDC"},{"exchange":"binance","base":"JUP","quote":"USDT"},{"exchange":"binance","base":"KAITO","quote":"USD"},{"exchange":"binance","base":"KAITO","quote":"USDC"},{"exchange":"binance","base":"KAITO","quote":"USDT"},{"exchange":"binance","base":"LINK","quote":"USD"},{"exchange":"binance","base":"LINK","quote":"USDC"},{"exchange":"binance","base":"LINK","quote":"USDT"},{"exchange":"binance","base":"NEAR","quote":"USD"},{"exchange":"binance","base":"NEAR","quote":"USDC"},{"exchange":"binance","base":"NEAR","quote":"USDT"},{"exchange":"binance","base":"OP","quote":"USD"},{"exchange":"binance","base":"OP","quote":"USDC"},{"exchange":"binance","base":"OP","quote":"USDT"},{"exchange":"binance","base":"PENDLE","quote":"USD"},{"exchange":"binance","base":"PENDLE","quote":"USDC"},{"exchange":"binance","base":"PENDLE","quote":"USDT"},{"exchange":"binance","base":"PENGU","quote":"USD"},{"exchange":"binance","base":"PENGU","quote":"USDC"},{"exchange":"binance","base":"PENGU","quote":"USDT"},{"exchange":"binance","base":"POL","quote":"USD"},{"exchange":"binance","base":"POL","quote":"USDC"},{"exchange":"binance","base":"POL","quote":"USDT"},{"exchange":"binance","base":"POPCAT","quote":"USD"},{"exchange":"binance","base":"POPCAT","quote":"USDC"},{"exchange":"binance","base":"POPCAT","quote":"USDT"},{"exchange":"binance","base":"SOL","quote":"USD"},{"exchange":"binance","base":"SOL","quote":"USDC"},{"exchange":"binance","base":"SOL","quote":"USDT"},{"exchange":"binance","base":"SUI","quote":"USD"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestperpetual2:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/perpetual2
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","components":[{"exchange":"binance","base":"SUI","quote":"USDC"},{"exchange":"binance","base":"SUI","quote":"USDT"},{"exchange":"binance","base":"TON","quote":"USD"},{"exchange":"binance","base":"TON","quote":"USDC"},{"exchange":"binance","base":"TON","quote":"USDT"},{"exchange":"binance","base":"TRUMP","quote":"USD"},{"exchange":"binance","base":"TRUMP","quote":"USDC"},{"exchange":"binance","base":"TRUMP","quote":"USDT"},{"exchange":"binance","base":"VINE","quote":"USD"},{"exchange":"binance","base":"VINE","quote":"USDC"},{"exchange":"binance","base":"VINE","quote":"USDT"},{"exchange":"binance","base":"WIF","quote":"USD"},{"exchange":"binance","base":"WIF","quote":"USDC"},{"exchange":"binance","base":"WIF","quote":"USDT"},{"exchange":"binance","base":"WLD","quote":"USD"},{"exchange":"binance","base":"WLD","quote":"USDC"},{"exchange":"binance","base":"WLD","quote":"USDT"},{"exchange":"binance","base":"XLM","quote":"USD"},{"exchange":"binance","base":"XLM","quote":"USDC"},{"exchange":"binance","base":"XLM","quote":"USDT"},{"exchange":"binance","base":"XRP","quote":"USD"},{"exchange":"binance","base":"XRP","quote":"USDC"},{"exchange":"binance","base":"XRP","quote":"USDT"},{"exchange":"binance","base":"ZK","quote":"USD"},{"exchange":"binance","base":"ZK","quote":"USDC"},{"exchange":"binance","base":"ZK","quote":"USDT"},{"exchange":"okx","base":"AAVE","quote":"USD"},{"exchange":"okx","base":"AAVE","quote":"USDC"},{"exchange":"okx","base":"AAVE","quote":"USDT"},{"exchange":"okx","base":"ADA","quote":"USD"},{"exchange":"okx","base":"ADA","quote":"USDC"},{"exchange":"okx","base":"ADA","quote":"USDT"},{"exchange":"okx","base":"AI16Z","quote":"USD"},{"exchange":"okx","base":"AI16Z","quote":"USDC"},{"exchange":"okx","base":"AI16Z","quote":"USDT"},{"exchange":"okx","base":"AIXBT","quote":"USD"},{"exchange":"okx","base":"AIXBT","quote":"USDC"},{"exchange":"okx","base":"AIXBT","quote":"USDT"},{"exchange":"okx","base":"ARB","quote":"USD"},{"exchange":"okx","base":"ARB","quote":"USDC"},{"exchange":"okx","base":"ARB","quote":"USDT"},{"exchange":"okx","base":"ATOM","quote":"USD"},{"exchange":"okx","base":"ATOM","quote":"USDC"},{"exchange":"okx","base":"ATOM","quote":"USDT"},{"exchange":"okx","base":"BERA","quote":"USD"},{"exchange":"okx","base":"BERA","quote":"USDC"},{"exchange":"okx","base":"BERA","quote":"USDT"},{"exchange":"okx","base":"BNB","quote":"USD"},{"exchange":"okx","base":"BNB","quote":"USDC"},{"exchange":"okx","base":"BNB","quote":"USDT"},{"exchange":"okx","base":"BONK","quote":"USD"},{"exchange":"okx","base":"BONK","quote":"USDC"},{"exchange":"okx","base":"BONK","quote":"USDT"},{"exchange":"okx","base":"BTC","quote":"USD"},{"exchange":"okx","base":"BTC","quote":"USDC"},{"exchange":"okx","base":"BTC","quote":"USDT"},{"exchange":"okx","base":"BTC","quote":"USD"},{"exchange":"okx","base":"BTC","quote":"USDC"},{"exchange":"okx","base":"BTC","quote":"USDT"},{"exchange":"okx","base":"DOGE","quote":"USD"},{"exchange":"okx","base":"DOGE","quote":"USDC"},{"exchange":"okx","base":"DOGE","quote":"USDT"},{"exchange":"okx","base":"ETH","quote":"USD"},{"exchange":"okx","base":"ETH","quote":"USDC"},{"exchange":"okx","base":"ETH","quote":"USDT"},{"exchange":"okx","base":"FARTCOIN","quote":"USD"},{"exchange":"okx","base":"FARTCOIN","quote":"USDC"},{"exchange":"okx","base":"FARTCOIN","quote":"USDT"},{"exchange":"okx","base":"GRT","quote":"USD"},{"exchange":"okx","base":"GRT","quote":"USDC"},{"exchange":"okx","base":"GRT","quote":"USDT"},{"exchange":"okx","base":"IMX","quote":"USD"},{"exchange":"okx","base":"IMX","quote":"USDC"},{"exchange":"okx","base":"IMX","quote":"USDT"},{"exchange":"okx","base":"JUP","quote":"USD"},{"exchange":"okx","base":"JUP","quote":"USDC"},{"exchange":"okx","base":"JUP","quote":"USDT"},{"exchange":"okx","base":"KAITO","quote":"USD"},{"exchange":"okx","base":"KAITO","quote":"USDC"},{"exchange":"okx","base":"KAITO","quote":"USDT"},{"exchange":"okx","base":"LINK","quote":"USD"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestperpetual3:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/perpetual3
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","components":[{"exchange":"okx","base":"LINK","quote":"USDC"},{"exchange":"okx","base":"LINK","quote":"USDT"},{"exchange":"okx","base":"NEAR","quote":"USD"},{"exchange":"okx","base":"NEAR","quote":"USDC"},{"exchange":"okx","base":"NEAR","quote":"USDT"},{"exchange":"okx","base":"ONDO","quote":"USD"},{"exchange":"okx","base":"ONDO","quote":"USDC"},{"exchange":"okx","base":"ONDO","quote":"USDT"},{"exchange":"okx","base":"OP","quote":"USD"},{"exchange":"okx","base":"OP","quote":"USDC"},{"exchange":"okx","base":"OP","quote":"USDT"},{"exchange":"okx","base":"PENGU","quote":"USD"},{"exchange":"okx","base":"PENGU","quote":"USDC"},{"exchange":"okx","base":"PENGU","quote":"USDT"},{"exchange":"okx","base":"PEPE","quote":"USD"},{"exchange":"okx","base":"PEPE","quote":"USDC"},{"exchange":"okx","base":"PEPE","quote":"USDT"},{"exchange":"okx","base":"POL","quote":"USD"},{"exchange":"okx","base":"POL","quote":"USDC"},{"exchange":"okx","base":"POL","quote":"USDT"},{"exchange":"okx","base":"POPCAT","quote":"USD"},{"exchange":"okx","base":"POPCAT","quote":"USDC"},{"exchange":"okx","base":"POPCAT","quote":"USDT"},{"exchange":"okx","base":"SHIB","quote":"USD"},{"exchange":"okx","base":"SHIB","quote":"USDC"},{"exchange":"okx","base":"SHIB","quote":"USDT"},{"exchange":"okx","base":"SOL","quote":"USD"},{"exchange":"okx","base":"SOL","quote":"USDC"},{"exchange":"okx","base":"SOL","quote":"USDT"},{"exchange":"okx","base":"SUI","quote":"USD"},{"exchange":"okx","base":"SUI","quote":"USDC"},{"exchange":"okx","base":"SUI","quote":"USDT"},{"exchange":"okx","base":"TON","quote":"USD"},{"exchange":"okx","base":"TON","quote":"USDC"},{"exchange":"okx","base":"TON","quote":"USDT"},{"exchange":"okx","base":"TRUMP","quote":"USD"},{"exchange":"okx","base":"TRUMP","quote":"USDC"},{"exchange":"okx","base":"TRUMP","quote":"USDT"},{"exchange":"okx","base":"UXLINK","quote":"USD"},{"exchange":"okx","base":"UXLINK","quote":"USDC"},{"exchange":"okx","base":"UXLINK","quote":"USDT"},{"exchange":"okx","base":"VINE","quote":"USD"},{"exchange":"okx","base":"VINE","quote":"USDC"},{"exchange":"okx","base":"VINE","quote":"USDT"},{"exchange":"okx","base":"VIRTUAL","quote":"USD"},{"exchange":"okx","base":"VIRTUAL","quote":"USDC"},{"exchange":"okx","base":"VIRTUAL","quote":"USDT"},{"exchange":"okx","base":"WIF","quote":"USD"},{"exchange":"okx","base":"WIF","quote":"USDC"},{"exchange":"okx","base":"WIF","quote":"USDT"},{"exchange":"okx","base":"WLD","quote":"USD"},{"exchange":"okx","base":"WLD","quote":"USDC"},{"exchange":"okx","base":"WLD","quote":"USDT"},{"exchange":"okx","base":"XLM","quote":"USD"},{"exchange":"okx","base":"XLM","quote":"USDC"},{"exchange":"okx","base":"XLM","quote":"USDT"},{"exchange":"okx","base":"XRP","quote":"USD"},{"exchange":"okx","base":"XRP","quote":"USDC"},{"exchange":"okx","base":"XRP","quote":"USDT"},{"exchange":"okx","base":"ZK","quote":"USD"},{"exchange":"okx","base":"ZK","quote":"USDC"},{"exchange":"okx","base":"ZK","quote":"USDT"},{"exchange":"deribit","base":"ADA","quote":"USD"},{"exchange":"deribit","base":"ADA","quote":"USDC"},{"exchange":"deribit","base":"ADA","quote":"USDT"},{"exchange":"deribit","base":"BNB","quote":"USD"},{"exchange":"deribit","base":"BNB","quote":"USDC"},{"exchange":"deribit","base":"BNB","quote":"USDT"},{"exchange":"deribit","base":"BTC","quote":"USD"},{"exchange":"deribit","base":"BTC","quote":"USDC"},{"exchange":"deribit","base":"BTC","quote":"USDT"},{"exchange":"deribit","base":"DOGE","quote":"USD"},{"exchange":"deribit","base":"DOGE","quote":"USDC"},{"exchange":"deribit","base":"DOGE","quote":"USDT"},{"exchange":"deribit","base":"ETH","quote":"USD"},{"exchange":"deribit","base":"ETH","quote":"USDC"},{"exchange":"deribit","base":"ETH","quote":"USDT"},{"exchange":"deribit","base":"LINK","quote":"USD"},{"exchange":"deribit","base":"LINK","quote":"USDC"},{"exchange":"deribit","base":"LINK","quote":"USDT"},{"exchange":"deribit","base":"NEAR","quote":"USD"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestperpetual4:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/perpetual4
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","components":[{"exchange":"deribit","base":"NEAR","quote":"USDC"},{"exchange":"deribit","base":"NEAR","quote":"USDT"},{"exchange":"deribit","base":"PAXG","quote":"USD"},{"exchange":"deribit","base":"PAXG","quote":"USDC"},{"exchange":"deribit","base":"PAXG","quote":"USDT"},{"exchange":"deribit","base":"XRP","quote":"USD"},{"exchange":"deribit","base":"XRP","quote":"USDC"},{"exchange":"deribit","base":"XRP","quote":"USDT"},{"exchange":"bitget","base":"1000BONK","quote":"USD"},{"exchange":"bitget","base":"1000BONK","quote":"USDC"},{"exchange":"bitget","base":"1000BONK","quote":"USDT"},{"exchange":"bitget","base":"AAVE","quote":"USD"},{"exchange":"bitget","base":"AAVE","quote":"USDC"},{"exchange":"bitget","base":"AAVE","quote":"USDT"},{"exchange":"bitget","base":"ADA","quote":"USD"},{"exchange":"bitget","base":"ADA","quote":"USDC"},{"exchange":"bitget","base":"ADA","quote":"USDT"},{"exchange":"bitget","base":"AI16Z","quote":"USD"},{"exchange":"bitget","base":"AI16Z","quote":"USDC"},{"exchange":"bitget","base":"AI16Z","quote":"USDT"},{"exchange":"bitget","base":"AIXBT","quote":"USD"},{"exchange":"bitget","base":"AIXBT","quote":"USDC"},{"exchange":"bitget","base":"AIXBT","quote":"USDT"},{"exchange":"bitget","base":"ARB","quote":"USD"},{"exchange":"bitget","base":"ARB","quote":"USDC"},{"exchange":"bitget","base":"ARB","quote":"USDT"},{"exchange":"bitget","base":"ATOM","quote":"USD"},{"exchange":"bitget","base":"ATOM","quote":"USDC"},{"exchange":"bitget","base":"ATOM","quote":"USDT"},{"exchange":"bitget","base":"BERA","quote":"USD"},{"exchange":"bitget","base":"BERA","quote":"USDC"},{"exchange":"bitget","base":"BERA","quote":"USDT"},{"exchange":"bitget","base":"BNB","quote":"USD"},{"exchange":"bitget","base":"BNB","quote":"USDC"},{"exchange":"bitget","base":"BNB","quote":"USDT"},{"exchange":"bitget","base":"BTC","quote":"USD"},{"exchange":"bitget","base":"BTC","quote":"USDC"},{"exchange":"bitget","base":"BTC","quote":"USDT"},{"exchange":"bitget","base":"DOGE","quote":"USD"},{"exchange":"bitget","base":"DOGE","quote":"USDC"},{"exchange":"bitget","base":"DOGE","quote":"USDT"},{"exchange":"bitget","base":"ENA","quote":"USD"},{"exchange":"bitget","base":"ENA","quote":"USDC"},{"exchange":"bitget","base":"ENA","quote":"USDT"},{"exchange":"bitget","base":"ETH","quote":"USD"},{"exchange":"bitget","base":"ETH","quote":"USDC"},{"exchange":"bitget","base":"ETH","quote":"USDT"},{"exchange":"bitget","base":"FARTCOIN","quote":"USD"},{"exchange":"bitget","base":"FARTCOIN","quote":"USDC"},{"exchange":"bitget","base":"FARTCOIN","quote":"USDT"},{"exchange":"bitget","base":"GRT","quote":"USD"},{"exchange":"bitget","base":"GRT","quote":"USDC"},{"exchange":"bitget","base":"GRT","quote":"USDT"},{"exchange":"bitget","base":"IMX","quote":"USD"},{"exchange":"bitget","base":"IMX","quote":"USDC"},{"exchange":"bitget","base":"IMX","quote":"USDT"},{"exchange":"bitget","base":"JUP","quote":"USD"},{"exchange":"bitget","base":"JUP","quote":"USDC"},{"exchange":"bitget","base":"JUP","quote":"USDT"},{"exchange":"bitget","base":"KAITO","quote":"USD"},{"exchange":"bitget","base":"KAITO","quote":"USDC"},{"exchange":"bitget","base":"KAITO","quote":"USDT"},{"exchange":"bitget","base":"LINK","quote":"USD"},{"exchange":"bitget","base":"LINK","quote":"USDC"},{"exchange":"bitget","base":"LINK","quote":"USDT"},{"exchange":"bitget","base":"NEAR","quote":"USD"},{"exchange":"bitget","base":"NEAR","quote":"USDC"},{"exchange":"bitget","base":"NEAR","quote":"USDT"},{"exchange":"bitget","base":"ONDO","quote":"USD"},{"exchange":"bitget","base":"ONDO","quote":"USDC"},{"exchange":"bitget","base":"ONDO","quote":"USDT"},{"exchange":"bitget","base":"OP","quote":"USD"},{"exchange":"bitget","base":"OP","quote":"USDC"},{"exchange":"bitget","base":"OP","quote":"USDT"},{"exchange":"bitget","base":"PENDLE","quote":"USD"},{"exchange":"bitget","base":"PENDLE","quote":"USDC"},{"exchange":"bitget","base":"PENDLE","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestperpetual5:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/perpetual5
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","components":[{"exchange":"bitget","base":"PENGU","quote":"USD"},{"exchange":"bitget","base":"PENGU","quote":"USDC"},{"exchange":"bitget","base":"PENGU","quote":"USDT"},{"exchange":"bitget","base":"PEPE","quote":"USD"},{"exchange":"bitget","base":"PEPE","quote":"USDC"},{"exchange":"bitget","base":"PEPE","quote":"USDT"},{"exchange":"bitget","base":"POL","quote":"USD"},{"exchange":"bitget","base":"POL","quote":"USDC"},{"exchange":"bitget","base":"POL","quote":"USDT"},{"exchange":"bitget","base":"POPCAT","quote":"USD"},{"exchange":"bitget","base":"POPCAT","quote":"USDC"},{"exchange":"bitget","base":"POPCAT","quote":"USDT"},{"exchange":"bitget","base":"SHIB","quote":"USD"},{"exchange":"bitget","base":"SHIB","quote":"USDC"},{"exchange":"bitget","base":"SHIB","quote":"USDT"},{"exchange":"bitget","base":"SOL","quote":"USD"},{"exchange":"bitget","base":"SOL","quote":"USDC"},{"exchange":"bitget","base":"SOL","quote":"USDT"},{"exchange":"bitget","base":"SUI","quote":"USD"},{"exchange":"bitget","base":"SUI","quote":"USDC"},{"exchange":"bitget","base":"SUI","quote":"USDT"},{"exchange":"bitget","base":"TON","quote":"USD"},{"exchange":"bitget","base":"TON","quote":"USDC"},{"exchange":"bitget","base":"TON","quote":"USDT"},{"exchange":"bitget","base":"TRUMP","quote":"USD"},{"exchange":"bitget","base":"TRUMP","quote":"USDC"},{"exchange":"bitget","base":"TRUMP","quote":"USDT"},{"exchange":"bitget","base":"UXLINK","quote":"USD"},{"exchange":"bitget","base":"UXLINK","quote":"USDC"},{"exchange":"bitget","base":"UXLINK","quote":"USDT"},{"exchange":"bitget","base":"VINE","quote":"USD"},{"exchange":"bitget","base":"VINE","quote":"USDC"},{"exchange":"bitget","base":"VINE","quote":"USDT"},{"exchange":"bitget","base":"VIRTUAL","quote":"USD"},{"exchange":"bitget","base":"VIRTUAL","quote":"USDC"},{"exchange":"bitget","base":"VIRTUAL","quote":"USDT"},{"exchange":"bitget","base":"WIF","quote":"USD"},{"exchange":"bitget","base":"WIF","quote":"USDC"},{"exchange":"bitget","base":"WIF","quote":"USDT"},{"exchange":"bitget","base":"WLD","quote":"USD"},{"exchange":"bitget","base":"WLD","quote":"USDC"},{"exchange":"bitget","base":"WLD","quote":"USDT"},{"exchange":"bitget","base":"XLM","quote":"USD"},{"exchange":"bitget","base":"XLM","quote":"USDC"},{"exchange":"bitget","base":"XLM","quote":"USDT"},{"exchange":"bitget","base":"XRP","quote":"USD"},{"exchange":"bitget","base":"XRP","quote":"USDC"},{"exchange":"bitget","base":"XRP","quote":"USDT"},{"exchange":"bitget","base":"ZK","quote":"USD"},{"exchange":"bitget","base":"ZK","quote":"USDC"},{"exchange":"bitget","base":"ZK","quote":"USDT"},{"exchange":"bitmart","base":"1000BONK","quote":"USD"},{"exchange":"bitmart","base":"1000BONK","quote":"USDC"},{"exchange":"bitmart","base":"1000BONK","quote":"USDT"},{"exchange":"bitmart","base":"AAVE","quote":"USD"},{"exchange":"bitmart","base":"AAVE","quote":"USDC"},{"exchange":"bitmart","base":"AAVE","quote":"USDT"},{"exchange":"bitmart","base":"ADA","quote":"USD"},{"exchange":"bitmart","base":"ADA","quote":"USDC"},{"exchange":"bitmart","base":"ADA","quote":"USDT"},{"exchange":"bitmart","base":"AI16Z","quote":"USD"},{"exchange":"bitmart","base":"AI16Z","quote":"USDC"},{"exchange":"bitmart","base":"AI16Z","quote":"USDT"},{"exchange":"bitmart","base":"AIXBT","quote":"USD"},{"exchange":"bitmart","base":"AIXBT","quote":"USDC"},{"exchange":"bitmart","base":"AIXBT","quote":"USDT"},{"exchange":"bitmart","base":"ARB","quote":"USD"},{"exchange":"bitmart","base":"ARB","quote":"USDC"},{"exchange":"bitmart","base":"ARB","quote":"USDT"},{"exchange":"bitmart","base":"ATOM","quote":"USD"},{"exchange":"bitmart","base":"ATOM","quote":"USDC"},{"exchange":"bitmart","base":"ATOM","quote":"USDT"},{"exchange":"bitmart","base":"BERA","quote":"USD"},{"exchange":"bitmart","base":"BERA","quote":"USDC"},{"exchange":"bitmart","base":"BERA","quote":"USDT"},{"exchange":"bitmart","base":"BNB","quote":"USD"},{"exchange":"bitmart","base":"BNB","quote":"USDC"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestperpetual6:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/perpetual6
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","components":[{"exchange":"bitmart","base":"BNB","quote":"USDT"},{"exchange":"bitmart","base":"BTC","quote":"USD"},{"exchange":"bitmart","base":"BTC","quote":"USDC"},{"exchange":"bitmart","base":"BTC","quote":"USDT"},{"exchange":"bitmart","base":"DOGE","quote":"USD"},{"exchange":"bitmart","base":"DOGE","quote":"USDC"},{"exchange":"bitmart","base":"DOGE","quote":"USDT"},{"exchange":"bitmart","base":"ENA","quote":"USD"},{"exchange":"bitmart","base":"ENA","quote":"USDC"},{"exchange":"bitmart","base":"ENA","quote":"USDT"},{"exchange":"bitmart","base":"ETH","quote":"USD"},{"exchange":"bitmart","base":"ETH","quote":"USDC"},{"exchange":"bitmart","base":"ETH","quote":"USDT"},{"exchange":"bitmart","base":"FARTCOIN","quote":"USD"},{"exchange":"bitmart","base":"FARTCOIN","quote":"USDC"},{"exchange":"bitmart","base":"FARTCOIN","quote":"USDT"},{"exchange":"bitmart","base":"GRT","quote":"USD"},{"exchange":"bitmart","base":"GRT","quote":"USDC"},{"exchange":"bitmart","base":"GRT","quote":"USDT"},{"exchange":"bitmart","base":"IMX","quote":"USD"},{"exchange":"bitmart","base":"IMX","quote":"USDC"},{"exchange":"bitmart","base":"IMX","quote":"USDT"},{"exchange":"bitmart","base":"JUP","quote":"USD"},{"exchange":"bitmart","base":"JUP","quote":"USDC"},{"exchange":"bitmart","base":"JUP","quote":"USDT"},{"exchange":"bitmart","base":"KAITO","quote":"USD"},{"exchange":"bitmart","base":"KAITO","quote":"USDC"},{"exchange":"bitmart","base":"KAITO","quote":"USDT"},{"exchange":"bitmart","base":"LINK","quote":"USD"},{"exchange":"bitmart","base":"LINK","quote":"USDC"},{"exchange":"bitmart","base":"LINK","quote":"USDT"},{"exchange":"bitmart","base":"NEAR","quote":"USD"},{"exchange":"bitmart","base":"NEAR","quote":"USDC"},{"exchange":"bitmart","base":"NEAR","quote":"USDT"},{"exchange":"bitmart","base":"ONDO","quote":"USD"},{"exchange":"bitmart","base":"ONDO","quote":"USDC"},{"exchange":"bitmart","base":"ONDO","quote":"USDT"},{"exchange":"bitmart","base":"OP","quote":"USD"},{"exchange":"bitmart","base":"OP","quote":"USDC"},{"exchange":"bitmart","base":"OP","quote":"USDT"},{"exchange":"bitmart","base":"PENDLE","quote":"USD"},{"exchange":"bitmart","base":"PENDLE","quote":"USDC"},{"exchange":"bitmart","base":"PENDLE","quote":"USDT"},{"exchange":"bitmart","base":"PENGU","quote":"USD"},{"exchange":"bitmart","base":"PENGU","quote":"USDC"},{"exchange":"bitmart","base":"PENGU","quote":"USDT"},{"exchange":"bitmart","base":"POL","quote":"USD"},{"exchange":"bitmart","base":"POL","quote":"USDC"},{"exchange":"bitmart","base":"POL","quote":"USDT"},{"exchange":"bitmart","base":"POPCAT","quote":"USD"},{"exchange":"bitmart","base":"POPCAT","quote":"USDC"},{"exchange":"bitmart","base":"POPCAT","quote":"USDT"},{"exchange":"bitmart","base":"SOL","quote":"USD"},{"exchange":"bitmart","base":"SOL","quote":"USDC"},{"exchange":"bitmart","base":"SOL","quote":"USDT"},{"exchange":"bitmart","base":"SUI","quote":"USD"},{"exchange":"bitmart","base":"SUI","quote":"USDC"},{"exchange":"bitmart","base":"SUI","quote":"USDT"},{"exchange":"bitmart","base":"TON","quote":"USD"},{"exchange":"bitmart","base":"TON","quote":"USDC"},{"exchange":"bitmart","base":"TON","quote":"USDT"},{"exchange":"bitmart","base":"TRUMP","quote":"USD"},{"exchange":"bitmart","base":"TRUMP","quote":"USDC"},{"exchange":"bitmart","base":"TRUMP","quote":"USDT"},{"exchange":"bitmart","base":"UXLINK","quote":"USD"},{"exchange":"bitmart","base":"UXLINK","quote":"USDC"},{"exchange":"bitmart","base":"UXLINK","quote":"USDT"},{"exchange":"bitmart","base":"VINE","quote":"USD"},{"exchange":"bitmart","base":"VINE","quote":"USDC"},{"exchange":"bitmart","base":"VINE","quote":"USDT"},{"exchange":"bitmart","base":"VIRTUAL","quote":"USD"},{"exchange":"bitmart","base":"VIRTUAL","quote":"USDC"},{"exchange":"bitmart","base":"VIRTUAL","quote":"USDT"},{"exchange":"bitmart","base":"WIF","quote":"USD"},{"exchange":"bitmart","base":"WIF","quote":"USDC"},{"exchange":"bitmart","base":"WIF","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestperpetual7:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/perpetual7
      Type: String
      Value: '{"perpetual":{"asset_class":"perpetual","components":[{"exchange":"bitmart","base":"WLD","quote":"USD"},{"exchange":"bitmart","base":"WLD","quote":"USDC"},{"exchange":"bitmart","base":"WLD","quote":"USDT"},{"exchange":"bitmart","base":"XLM","quote":"USD"},{"exchange":"bitmart","base":"XLM","quote":"USDC"},{"exchange":"bitmart","base":"XLM","quote":"USDT"},{"exchange":"bitmart","base":"XRP","quote":"USD"},{"exchange":"bitmart","base":"XRP","quote":"USDC"},{"exchange":"bitmart","base":"XRP","quote":"USDT"},{"exchange":"bitmart","base":"ZK","quote":"USD"},{"exchange":"bitmart","base":"ZK","quote":"USDC"},{"exchange":"bitmart","base":"ZK","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestspot1:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/spot1
      Type: String
      Value: '{"spot":{"asset_class":"spot","components":[{"exchange":"binance","base":"AIXBT","quote":"USD"},{"exchange":"binance","base":"AIXBT","quote":"USDC"},{"exchange":"binance","base":"AIXBT","quote":"USDT"},{"exchange":"binance","base":"ARB","quote":"USD"},{"exchange":"binance","base":"ARB","quote":"USDC"},{"exchange":"binance","base":"ARB","quote":"USDT"},{"exchange":"binance","base":"ATOM","quote":"USD"},{"exchange":"binance","base":"ATOM","quote":"USDC"},{"exchange":"binance","base":"ATOM","quote":"USDT"},{"exchange":"binance","base":"BERA","quote":"USD"},{"exchange":"binance","base":"BERA","quote":"USDC"},{"exchange":"binance","base":"BERA","quote":"USDT"},{"exchange":"binance","base":"BNB","quote":"USD"},{"exchange":"binance","base":"BNB","quote":"USDC"},{"exchange":"binance","base":"BNB","quote":"USDT"},{"exchange":"binance","base":"BONK","quote":"USD"},{"exchange":"binance","base":"BONK","quote":"USDC"},{"exchange":"binance","base":"BONK","quote":"USDT"},{"exchange":"binance","base":"BTC","quote":"USD"},{"exchange":"binance","base":"BTC","quote":"USDC"},{"exchange":"binance","base":"BTC","quote":"USDT"},{"exchange":"binance","base":"DOGE","quote":"USD"},{"exchange":"binance","base":"DOGE","quote":"USDC"},{"exchange":"binance","base":"DOGE","quote":"USDT"},{"exchange":"binance","base":"EIGEN","quote":"USD"},{"exchange":"binance","base":"EIGEN","quote":"USDC"},{"exchange":"binance","base":"EIGEN","quote":"USDT"},{"exchange":"binance","base":"ENA","quote":"USD"},{"exchange":"binance","base":"ENA","quote":"USDC"},{"exchange":"binance","base":"ENA","quote":"USDT"},{"exchange":"binance","base":"ETH","quote":"USD"},{"exchange":"binance","base":"ETH","quote":"USDC"},{"exchange":"binance","base":"ETH","quote":"USDT"},{"exchange":"binance","base":"EUR","quote":"USD"},{"exchange":"binance","base":"EUR","quote":"USDC"},{"exchange":"binance","base":"EUR","quote":"USDT"},{"exchange":"binance","base":"GRT","quote":"USD"},{"exchange":"binance","base":"GRT","quote":"USDC"},{"exchange":"binance","base":"GRT","quote":"USDT"},{"exchange":"binance","base":"IMX","quote":"USD"},{"exchange":"binance","base":"IMX","quote":"USDC"},{"exchange":"binance","base":"IMX","quote":"USDT"},{"exchange":"binance","base":"JUP","quote":"USD"},{"exchange":"binance","base":"JUP","quote":"USDC"},{"exchange":"binance","base":"JUP","quote":"USDT"},{"exchange":"binance","base":"KAITO","quote":"USD"},{"exchange":"binance","base":"KAITO","quote":"USDC"},{"exchange":"binance","base":"KAITO","quote":"USDT"},{"exchange":"binance","base":"OP","quote":"USD"},{"exchange":"binance","base":"OP","quote":"USDC"},{"exchange":"binance","base":"OP","quote":"USDT"},{"exchange":"binance","base":"PENDLE","quote":"USD"},{"exchange":"binance","base":"PENDLE","quote":"USDC"},{"exchange":"binance","base":"PENDLE","quote":"USDT"},{"exchange":"binance","base":"PENGU","quote":"USD"},{"exchange":"binance","base":"PENGU","quote":"USDC"},{"exchange":"binance","base":"PENGU","quote":"USDT"},{"exchange":"binance","base":"PEPE","quote":"USD"},{"exchange":"binance","base":"PEPE","quote":"USDC"},{"exchange":"binance","base":"PEPE","quote":"USDT"},{"exchange":"binance","base":"POL","quote":"USD"},{"exchange":"binance","base":"POL","quote":"USDC"},{"exchange":"binance","base":"POL","quote":"USDT"},{"exchange":"binance","base":"SEI","quote":"USD"},{"exchange":"binance","base":"SEI","quote":"USDC"},{"exchange":"binance","base":"SEI","quote":"USDT"},{"exchange":"binance","base":"SHIB","quote":"USD"},{"exchange":"binance","base":"SHIB","quote":"USDC"},{"exchange":"binance","base":"SHIB","quote":"USDT"},{"exchange":"binance","base":"TAO","quote":"USD"},{"exchange":"binance","base":"TAO","quote":"USDC"},{"exchange":"binance","base":"TAO","quote":"USDT"},{"exchange":"binance","base":"TIA","quote":"USD"},{"exchange":"binance","base":"TIA","quote":"USDC"},{"exchange":"binance","base":"TIA","quote":"USDT"},{"exchange":"binance","base":"TON","quote":"USD"},{"exchange":"binance","base":"TON","quote":"USDC"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestspot2:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/spot2
      Type: String
      Value: '{"spot":{"asset_class":"spot","components":[{"exchange":"binance","base":"TON","quote":"USDT"},{"exchange":"binance","base":"TRUMP","quote":"USD"},{"exchange":"binance","base":"TRUMP","quote":"USDC"},{"exchange":"binance","base":"TRUMP","quote":"USDT"},{"exchange":"binance","base":"UNI","quote":"USD"},{"exchange":"binance","base":"UNI","quote":"USDC"},{"exchange":"binance","base":"UNI","quote":"USDT"},{"exchange":"binance","base":"WIF","quote":"USD"},{"exchange":"binance","base":"WIF","quote":"USDC"},{"exchange":"binance","base":"WIF","quote":"USDT"},{"exchange":"binance","base":"WLD","quote":"USD"},{"exchange":"binance","base":"WLD","quote":"USDC"},{"exchange":"binance","base":"WLD","quote":"USDT"},{"exchange":"binance","base":"XLM","quote":"USD"},{"exchange":"binance","base":"XLM","quote":"USDC"},{"exchange":"binance","base":"XLM","quote":"USDT"},{"exchange":"binance","base":"XRP","quote":"USD"},{"exchange":"binance","base":"XRP","quote":"USDC"},{"exchange":"binance","base":"XRP","quote":"USDT"},{"exchange":"binance","base":"ZK","quote":"USD"},{"exchange":"binance","base":"ZK","quote":"USDC"},{"exchange":"binance","base":"ZK","quote":"USDT"},{"exchange":"okx","base":"AAVE","quote":"USD"},{"exchange":"okx","base":"AAVE","quote":"USDC"},{"exchange":"okx","base":"AAVE","quote":"USDT"},{"exchange":"okx","base":"ADA","quote":"USD"},{"exchange":"okx","base":"ADA","quote":"USDC"},{"exchange":"okx","base":"ADA","quote":"USDT"},{"exchange":"okx","base":"AIXBT","quote":"USD"},{"exchange":"okx","base":"AIXBT","quote":"USDC"},{"exchange":"okx","base":"AIXBT","quote":"USDT"},{"exchange":"okx","base":"ARB","quote":"USD"},{"exchange":"okx","base":"ARB","quote":"USDC"},{"exchange":"okx","base":"ARB","quote":"USDT"},{"exchange":"okx","base":"ATOM","quote":"USD"},{"exchange":"okx","base":"ATOM","quote":"USDC"},{"exchange":"okx","base":"ATOM","quote":"USDT"},{"exchange":"okx","base":"AVAX","quote":"USD"},{"exchange":"okx","base":"AVAX","quote":"USDC"},{"exchange":"okx","base":"AVAX","quote":"USDT"},{"exchange":"okx","base":"BERA","quote":"USD"},{"exchange":"okx","base":"BERA","quote":"USDC"},{"exchange":"okx","base":"BERA","quote":"USDT"},{"exchange":"okx","base":"BNB","quote":"USD"},{"exchange":"okx","base":"BNB","quote":"USDC"},{"exchange":"okx","base":"BNB","quote":"USDT"},{"exchange":"okx","base":"BONK","quote":"USD"},{"exchange":"okx","base":"BONK","quote":"USDC"},{"exchange":"okx","base":"BONK","quote":"USDT"},{"exchange":"okx","base":"BTC","quote":"USD"},{"exchange":"okx","base":"BTC","quote":"USDC"},{"exchange":"okx","base":"BTC","quote":"USDT"},{"exchange":"okx","base":"DEGEN","quote":"USD"},{"exchange":"okx","base":"DEGEN","quote":"USDC"},{"exchange":"okx","base":"DEGEN","quote":"USDT"},{"exchange":"okx","base":"DOGE","quote":"USD"},{"exchange":"okx","base":"DOGE","quote":"USDC"},{"exchange":"okx","base":"DOGE","quote":"USDT"},{"exchange":"okx","base":"EIGEN","quote":"USD"},{"exchange":"okx","base":"EIGEN","quote":"USDC"},{"exchange":"okx","base":"EIGEN","quote":"USDT"},{"exchange":"okx","base":"ETH","quote":"USD"},{"exchange":"okx","base":"ETH","quote":"USDC"},{"exchange":"okx","base":"ETH","quote":"USDT"},{"exchange":"okx","base":"GRT","quote":"USD"},{"exchange":"okx","base":"GRT","quote":"USDC"},{"exchange":"okx","base":"GRT","quote":"USDT"},{"exchange":"okx","base":"IMX","quote":"USD"},{"exchange":"okx","base":"IMX","quote":"USDC"},{"exchange":"okx","base":"IMX","quote":"USDT"},{"exchange":"okx","base":"JUP","quote":"USD"},{"exchange":"okx","base":"JUP","quote":"USDC"},{"exchange":"okx","base":"JUP","quote":"USDT"},{"exchange":"okx","base":"KAITO","quote":"USD"},{"exchange":"okx","base":"KAITO","quote":"USDC"},{"exchange":"okx","base":"KAITO","quote":"USDT"},{"exchange":"okx","base":"LINK","quote":"USD"},{"exchange":"okx","base":"LINK","quote":"USDC"},{"exchange":"okx","base":"LINK","quote":"USDT"},{"exchange":"okx","base":"NEAR","quote":"USD"},{"exchange":"okx","base":"NEAR","quote":"USDC"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestspot3:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/spot3
      Type: String
      Value: '{"spot":{"asset_class":"spot","components":[{"exchange":"okx","base":"NEAR","quote":"USDT"},{"exchange":"okx","base":"ONDO","quote":"USD"},{"exchange":"okx","base":"ONDO","quote":"USDC"},{"exchange":"okx","base":"ONDO","quote":"USDT"},{"exchange":"okx","base":"OP","quote":"USD"},{"exchange":"okx","base":"OP","quote":"USDC"},{"exchange":"okx","base":"OP","quote":"USDT"},{"exchange":"okx","base":"PENDLE","quote":"USD"},{"exchange":"okx","base":"PENDLE","quote":"USDC"},{"exchange":"okx","base":"PENDLE","quote":"USDT"},{"exchange":"okx","base":"PENGU","quote":"USD"},{"exchange":"okx","base":"PENGU","quote":"USDC"},{"exchange":"okx","base":"PENGU","quote":"USDT"},{"exchange":"okx","base":"PEPE","quote":"USD"},{"exchange":"okx","base":"PEPE","quote":"USDC"},{"exchange":"okx","base":"PEPE","quote":"USDT"},{"exchange":"okx","base":"POL","quote":"USD"},{"exchange":"okx","base":"POL","quote":"USDC"},{"exchange":"okx","base":"POL","quote":"USDT"},{"exchange":"okx","base":"SHIB","quote":"USD"},{"exchange":"okx","base":"SHIB","quote":"USDC"},{"exchange":"okx","base":"SHIB","quote":"USDT"},{"exchange":"okx","base":"SUI","quote":"USD"},{"exchange":"okx","base":"SUI","quote":"USDC"},{"exchange":"okx","base":"SUI","quote":"USDT"},{"exchange":"okx","base":"TIA","quote":"USD"},{"exchange":"okx","base":"TIA","quote":"USDC"},{"exchange":"okx","base":"TIA","quote":"USDT"},{"exchange":"okx","base":"TON","quote":"USD"},{"exchange":"okx","base":"TON","quote":"USDC"},{"exchange":"okx","base":"TON","quote":"USDT"},{"exchange":"okx","base":"TRUMP","quote":"USD"},{"exchange":"okx","base":"TRUMP","quote":"USDC"},{"exchange":"okx","base":"TRUMP","quote":"USDT"},{"exchange":"okx","base":"UNI","quote":"USD"},{"exchange":"okx","base":"UNI","quote":"USDC"},{"exchange":"okx","base":"UNI","quote":"USDT"},{"exchange":"okx","base":"UXLINK","quote":"USD"},{"exchange":"okx","base":"UXLINK","quote":"USDC"},{"exchange":"okx","base":"UXLINK","quote":"USDT"},{"exchange":"okx","base":"VINE","quote":"USD"},{"exchange":"okx","base":"VINE","quote":"USDC"},{"exchange":"okx","base":"VINE","quote":"USDT"},{"exchange":"okx","base":"WIF","quote":"USD"},{"exchange":"okx","base":"WIF","quote":"USDC"},{"exchange":"okx","base":"WIF","quote":"USDT"},{"exchange":"okx","base":"WLD","quote":"USD"},{"exchange":"okx","base":"WLD","quote":"USDC"},{"exchange":"okx","base":"WLD","quote":"USDT"},{"exchange":"okx","base":"XLM","quote":"USD"},{"exchange":"okx","base":"XLM","quote":"USDC"},{"exchange":"okx","base":"XLM","quote":"USDT"},{"exchange":"okx","base":"XRP","quote":"USD"},{"exchange":"okx","base":"XRP","quote":"USDC"},{"exchange":"okx","base":"XRP","quote":"USDT"},{"exchange":"okx","base":"ZK","quote":"USD"},{"exchange":"okx","base":"ZK","quote":"USDC"},{"exchange":"okx","base":"ZK","quote":"USDT"},{"exchange":"bitget","base":"AAVE","quote":"USD"},{"exchange":"bitget","base":"AAVE","quote":"USDC"},{"exchange":"bitget","base":"AAVE","quote":"USDT"},{"exchange":"bitget","base":"ADA","quote":"USD"},{"exchange":"bitget","base":"ADA","quote":"USDC"},{"exchange":"bitget","base":"ADA","quote":"USDT"},{"exchange":"bitget","base":"AI16Z","quote":"USD"},{"exchange":"bitget","base":"AI16Z","quote":"USDC"},{"exchange":"bitget","base":"AI16Z","quote":"USDT"},{"exchange":"bitget","base":"AIXBT","quote":"USD"},{"exchange":"bitget","base":"AIXBT","quote":"USDC"},{"exchange":"bitget","base":"AIXBT","quote":"USDT"},{"exchange":"bitget","base":"ARB","quote":"USD"},{"exchange":"bitget","base":"ARB","quote":"USDC"},{"exchange":"bitget","base":"ARB","quote":"USDT"},{"exchange":"bitget","base":"ATOM","quote":"USD"},{"exchange":"bitget","base":"ATOM","quote":"USDC"},{"exchange":"bitget","base":"ATOM","quote":"USDT"},{"exchange":"bitget","base":"AVAX","quote":"USD"},{"exchange":"bitget","base":"AVAX","quote":"USDC"},{"exchange":"bitget","base":"AVAX","quote":"USDT"},{"exchange":"bitget","base":"BERA","quote":"USD"},{"exchange":"bitget","base":"BERA","quote":"USDC"},{"exchange":"bitget","base":"BERA","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestspot4:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/spot4
      Type: String
      Value: '{"spot":{"asset_class":"spot","components":[{"exchange":"bitget","base":"BITCOIN","quote":"USD"},{"exchange":"bitget","base":"BITCOIN","quote":"USDC"},{"exchange":"bitget","base":"BITCOIN","quote":"USDT"},{"exchange":"bitget","base":"BNB","quote":"USD"},{"exchange":"bitget","base":"BNB","quote":"USDC"},{"exchange":"bitget","base":"BNB","quote":"USDT"},{"exchange":"bitget","base":"BONK","quote":"USD"},{"exchange":"bitget","base":"BONK","quote":"USDC"},{"exchange":"bitget","base":"BONK","quote":"USDT"},{"exchange":"bitget","base":"BTC","quote":"USD"},{"exchange":"bitget","base":"BTC","quote":"USDC"},{"exchange":"bitget","base":"BTC","quote":"USDT"},{"exchange":"bitget","base":"DEGEN","quote":"USD"},{"exchange":"bitget","base":"DEGEN","quote":"USDC"},{"exchange":"bitget","base":"DEGEN","quote":"USDT"},{"exchange":"bitget","base":"DOGE","quote":"USD"},{"exchange":"bitget","base":"DOGE","quote":"USDC"},{"exchange":"bitget","base":"DOGE","quote":"USDT"},{"exchange":"bitget","base":"EIGEN","quote":"USD"},{"exchange":"bitget","base":"EIGEN","quote":"USDC"},{"exchange":"bitget","base":"EIGEN","quote":"USDT"},{"exchange":"bitget","base":"ENA","quote":"USD"},{"exchange":"bitget","base":"ENA","quote":"USDC"},{"exchange":"bitget","base":"ENA","quote":"USDT"},{"exchange":"bitget","base":"ETH","quote":"USD"},{"exchange":"bitget","base":"ETH","quote":"USDC"},{"exchange":"bitget","base":"ETH","quote":"USDT"},{"exchange":"bitget","base":"FARTCOIN","quote":"USD"},{"exchange":"bitget","base":"FARTCOIN","quote":"USDC"},{"exchange":"bitget","base":"FARTCOIN","quote":"USDT"},{"exchange":"bitget","base":"GRT","quote":"USD"},{"exchange":"bitget","base":"GRT","quote":"USDC"},{"exchange":"bitget","base":"GRT","quote":"USDT"},{"exchange":"bitget","base":"IMX","quote":"USD"},{"exchange":"bitget","base":"IMX","quote":"USDC"},{"exchange":"bitget","base":"IMX","quote":"USDT"},{"exchange":"bitget","base":"JUP","quote":"USD"},{"exchange":"bitget","base":"JUP","quote":"USDC"},{"exchange":"bitget","base":"JUP","quote":"USDT"},{"exchange":"bitget","base":"KAITO","quote":"USD"},{"exchange":"bitget","base":"KAITO","quote":"USDC"},{"exchange":"bitget","base":"KAITO","quote":"USDT"},{"exchange":"bitget","base":"LINK","quote":"USD"},{"exchange":"bitget","base":"LINK","quote":"USDC"},{"exchange":"bitget","base":"LINK","quote":"USDT"},{"exchange":"bitget","base":"NEAR","quote":"USD"},{"exchange":"bitget","base":"NEAR","quote":"USDC"},{"exchange":"bitget","base":"NEAR","quote":"USDT"},{"exchange":"bitget","base":"ONDO","quote":"USD"},{"exchange":"bitget","base":"ONDO","quote":"USDC"},{"exchange":"bitget","base":"ONDO","quote":"USDT"},{"exchange":"bitget","base":"OP","quote":"USD"},{"exchange":"bitget","base":"OP","quote":"USDC"},{"exchange":"bitget","base":"OP","quote":"USDT"},{"exchange":"bitget","base":"PENDLE","quote":"USD"},{"exchange":"bitget","base":"PENDLE","quote":"USDC"},{"exchange":"bitget","base":"PENDLE","quote":"USDT"},{"exchange":"bitget","base":"PENGU","quote":"USD"},{"exchange":"bitget","base":"PENGU","quote":"USDC"},{"exchange":"bitget","base":"PENGU","quote":"USDT"},{"exchange":"bitget","base":"PEPE","quote":"USD"},{"exchange":"bitget","base":"PEPE","quote":"USDC"},{"exchange":"bitget","base":"PEPE","quote":"USDT"},{"exchange":"bitget","base":"POL","quote":"USD"},{"exchange":"bitget","base":"POL","quote":"USDC"},{"exchange":"bitget","base":"POL","quote":"USDT"},{"exchange":"bitget","base":"SEI","quote":"USD"},{"exchange":"bitget","base":"SEI","quote":"USDC"},{"exchange":"bitget","base":"SEI","quote":"USDT"},{"exchange":"bitget","base":"SHIB","quote":"USD"},{"exchange":"bitget","base":"SHIB","quote":"USDC"},{"exchange":"bitget","base":"SHIB","quote":"USDT"},{"exchange":"bitget","base":"SUI","quote":"USD"},{"exchange":"bitget","base":"SUI","quote":"USDC"},{"exchange":"bitget","base":"SUI","quote":"USDT"},{"exchange":"bitget","base":"TAO","quote":"USD"},{"exchange":"bitget","base":"TAO","quote":"USDC"},{"exchange":"bitget","base":"TAO","quote":"USDT"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestspot5:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/spot5
      Type: String
      Value: '{"spot":{"asset_class":"spot","components":[{"exchange":"bitget","base":"TIA","quote":"USD"},{"exchange":"bitget","base":"TIA","quote":"USDC"},{"exchange":"bitget","base":"TIA","quote":"USDT"},{"exchange":"bitget","base":"TON","quote":"USD"},{"exchange":"bitget","base":"TON","quote":"USDC"},{"exchange":"bitget","base":"TON","quote":"USDT"},{"exchange":"bitget","base":"UNI","quote":"USD"},{"exchange":"bitget","base":"UNI","quote":"USDC"},{"exchange":"bitget","base":"UNI","quote":"USDT"},{"exchange":"bitget","base":"UXLINK","quote":"USD"},{"exchange":"bitget","base":"UXLINK","quote":"USDC"},{"exchange":"bitget","base":"UXLINK","quote":"USDT"},{"exchange":"bitget","base":"VINE","quote":"USD"},{"exchange":"bitget","base":"VINE","quote":"USDC"},{"exchange":"bitget","base":"VINE","quote":"USDT"},{"exchange":"bitget","base":"VIRTUAL","quote":"USD"},{"exchange":"bitget","base":"VIRTUAL","quote":"USDC"},{"exchange":"bitget","base":"VIRTUAL","quote":"USDT"},{"exchange":"bitget","base":"WIF","quote":"USD"},{"exchange":"bitget","base":"WIF","quote":"USDC"},{"exchange":"bitget","base":"WIF","quote":"USDT"},{"exchange":"bitget","base":"WLD","quote":"USD"},{"exchange":"bitget","base":"WLD","quote":"USDC"},{"exchange":"bitget","base":"WLD","quote":"USDT"},{"exchange":"bitget","base":"XLM","quote":"USD"},{"exchange":"bitget","base":"XLM","quote":"USDC"},{"exchange":"bitget","base":"XLM","quote":"USDT"},{"exchange":"bitget","base":"XRP","quote":"USD"},{"exchange":"bitget","base":"XRP","quote":"USDC"},{"exchange":"bitget","base":"XRP","quote":"USDT"},{"exchange":"bitget","base":"ZK","quote":"USD"},{"exchange":"bitget","base":"ZK","quote":"USDC"},{"exchange":"bitget","base":"ZK","quote":"USDT"},{"exchange":"coinbase","base":"AAVE","quote":"USD"},{"exchange":"coinbase","base":"AAVE","quote":"USDC"},{"exchange":"coinbase","base":"AAVE","quote":"USDT"},{"exchange":"coinbase","base":"ADA","quote":"USD"},{"exchange":"coinbase","base":"ADA","quote":"USDC"},{"exchange":"coinbase","base":"ADA","quote":"USDT"},{"exchange":"coinbase","base":"ARB","quote":"USD"},{"exchange":"coinbase","base":"ARB","quote":"USDC"},{"exchange":"coinbase","base":"ARB","quote":"USDT"},{"exchange":"coinbase","base":"ATOM","quote":"USD"},{"exchange":"coinbase","base":"ATOM","quote":"USDC"},{"exchange":"coinbase","base":"ATOM","quote":"USDT"},{"exchange":"coinbase","base":"AVAX","quote":"USD"},{"exchange":"coinbase","base":"AVAX","quote":"USDC"},{"exchange":"coinbase","base":"AVAX","quote":"USDT"},{"exchange":"coinbase","base":"BERA","quote":"USD"},{"exchange":"coinbase","base":"BERA","quote":"USDC"},{"exchange":"coinbase","base":"BERA","quote":"USDT"},{"exchange":"coinbase","base":"BONK","quote":"USD"},{"exchange":"coinbase","base":"BONK","quote":"USDC"},{"exchange":"coinbase","base":"BONK","quote":"USDT"},{"exchange":"coinbase","base":"BTC","quote":"USD"},{"exchange":"coinbase","base":"BTC","quote":"USDC"},{"exchange":"coinbase","base":"BTC","quote":"USDT"},{"exchange":"coinbase","base":"DEGEN","quote":"USD"},{"exchange":"coinbase","base":"DEGEN","quote":"USDC"},{"exchange":"coinbase","base":"DEGEN","quote":"USDT"},{"exchange":"coinbase","base":"DOGE","quote":"USD"},{"exchange":"coinbase","base":"DOGE","quote":"USDC"},{"exchange":"coinbase","base":"DOGE","quote":"USDT"},{"exchange":"coinbase","base":"EIGEN","quote":"USD"},{"exchange":"coinbase","base":"EIGEN","quote":"USDC"},{"exchange":"coinbase","base":"EIGEN","quote":"USDT"},{"exchange":"coinbase","base":"ETH","quote":"USD"},{"exchange":"coinbase","base":"ETH","quote":"USDC"},{"exchange":"coinbase","base":"ETH","quote":"USDT"},{"exchange":"coinbase","base":"GRT","quote":"USD"},{"exchange":"coinbase","base":"GRT","quote":"USDC"},{"exchange":"coinbase","base":"GRT","quote":"USDT"},{"exchange":"coinbase","base":"IMX","quote":"USD"},{"exchange":"coinbase","base":"IMX","quote":"USDC"},{"exchange":"coinbase","base":"IMX","quote":"USDT"},{"exchange":"coinbase","base":"KAITO","quote":"USD"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestspot6:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/spot6
      Type: String
      Value: '{"spot":{"asset_class":"spot","components":[{"exchange":"coinbase","base":"KAITO","quote":"USDC"},{"exchange":"coinbase","base":"KAITO","quote":"USDT"},{"exchange":"coinbase","base":"LINK","quote":"USD"},{"exchange":"coinbase","base":"LINK","quote":"USDC"},{"exchange":"coinbase","base":"LINK","quote":"USDT"},{"exchange":"coinbase","base":"MATIC","quote":"USD"},{"exchange":"coinbase","base":"MATIC","quote":"USDC"},{"exchange":"coinbase","base":"MATIC","quote":"USDT"},{"exchange":"coinbase","base":"NEAR","quote":"USD"},{"exchange":"coinbase","base":"NEAR","quote":"USDC"},{"exchange":"coinbase","base":"NEAR","quote":"USDT"},{"exchange":"coinbase","base":"ONDO","quote":"USD"},{"exchange":"coinbase","base":"ONDO","quote":"USDC"},{"exchange":"coinbase","base":"ONDO","quote":"USDT"},{"exchange":"coinbase","base":"OP","quote":"USD"},{"exchange":"coinbase","base":"OP","quote":"USDC"},{"exchange":"coinbase","base":"OP","quote":"USDT"},{"exchange":"coinbase","base":"PENGU","quote":"USD"},{"exchange":"coinbase","base":"PENGU","quote":"USDC"},{"exchange":"coinbase","base":"PENGU","quote":"USDT"},{"exchange":"coinbase","base":"POL","quote":"USD"},{"exchange":"coinbase","base":"POL","quote":"USDC"},{"exchange":"coinbase","base":"POL","quote":"USDT"},{"exchange":"coinbase","base":"POPCAT","quote":"USD"},{"exchange":"coinbase","base":"POPCAT","quote":"USDC"},{"exchange":"coinbase","base":"POPCAT","quote":"USDT"},{"exchange":"coinbase","base":"SEI","quote":"USD"},{"exchange":"coinbase","base":"SEI","quote":"USDC"},{"exchange":"coinbase","base":"SEI","quote":"USDT"},{"exchange":"coinbase","base":"SHIB","quote":"USD"},{"exchange":"coinbase","base":"SHIB","quote":"USDC"},{"exchange":"coinbase","base":"SHIB","quote":"USDT"},{"exchange":"coinbase","base":"SNX","quote":"USD"},{"exchange":"coinbase","base":"SNX","quote":"USDC"},{"exchange":"coinbase","base":"SNX","quote":"USDT"},{"exchange":"coinbase","base":"SOL","quote":"USD"},{"exchange":"coinbase","base":"SOL","quote":"USDC"},{"exchange":"coinbase","base":"SOL","quote":"USDT"},{"exchange":"coinbase","base":"SUI","quote":"USD"},{"exchange":"coinbase","base":"SUI","quote":"USDC"},{"exchange":"coinbase","base":"SUI","quote":"USDT"},{"exchange":"coinbase","base":"TIA","quote":"USD"},{"exchange":"coinbase","base":"TIA","quote":"USDC"},{"exchange":"coinbase","base":"TIA","quote":"USDT"},{"exchange":"coinbase","base":"UNI","quote":"USD"},{"exchange":"coinbase","base":"UNI","quote":"USDC"},{"exchange":"coinbase","base":"UNI","quote":"USDT"},{"exchange":"coinbase","base":"USDT","quote":"USD"},{"exchange":"coinbase","base":"USDT","quote":"USDC"},{"exchange":"coinbase","base":"USDT","quote":"USDT"},{"exchange":"coinbase","base":"XLM","quote":"USD"},{"exchange":"coinbase","base":"XLM","quote":"USDC"},{"exchange":"coinbase","base":"XLM","quote":"USDT"},{"exchange":"coinbase","base":"XRP","quote":"USD"},{"exchange":"coinbase","base":"XRP","quote":"USDC"},{"exchange":"coinbase","base":"XRP","quote":"USDT"},{"exchange":"kraken","base":"AAVE","quote":"USD"},{"exchange":"kraken","base":"AAVE","quote":"USDC"},{"exchange":"kraken","base":"AAVE","quote":"USDT"},{"exchange":"kraken","base":"AAVE","quote":"ZUSD"},{"exchange":"kraken","base":"ADA","quote":"USD"},{"exchange":"kraken","base":"ADA","quote":"USDC"},{"exchange":"kraken","base":"ADA","quote":"USDT"},{"exchange":"kraken","base":"ADA","quote":"ZUSD"},{"exchange":"kraken","base":"AI16Z","quote":"USD"},{"exchange":"kraken","base":"AI16Z","quote":"USDC"},{"exchange":"kraken","base":"AI16Z","quote":"USDT"},{"exchange":"kraken","base":"AI16Z","quote":"ZUSD"},{"exchange":"kraken","base":"ATOM","quote":"USD"},{"exchange":"kraken","base":"ATOM","quote":"USDC"},{"exchange":"kraken","base":"ATOM","quote":"USDT"},{"exchange":"kraken","base":"ATOM","quote":"ZUSD"},{"exchange":"kraken","base":"AVAX","quote":"USD"},{"exchange":"kraken","base":"AVAX","quote":"USDC"},{"exchange":"kraken","base":"AVAX","quote":"USDT"},{"exchange":"kraken","base":"AVAX","quote":"ZUSD"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestspot7:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/spot7
      Type: String
      Value: '{"spot":{"asset_class":"spot","components":[{"exchange":"kraken","base":"BERA","quote":"USD"},{"exchange":"kraken","base":"BERA","quote":"USDC"},{"exchange":"kraken","base":"BERA","quote":"USDT"},{"exchange":"kraken","base":"BERA","quote":"ZUSD"},{"exchange":"kraken","base":"BONK","quote":"USD"},{"exchange":"kraken","base":"BONK","quote":"USDC"},{"exchange":"kraken","base":"BONK","quote":"USDT"},{"exchange":"kraken","base":"BONK","quote":"ZUSD"},{"exchange":"kraken","base":"BTC","quote":"USD"},{"exchange":"kraken","base":"BTC","quote":"USDC"},{"exchange":"kraken","base":"BTC","quote":"USDT"},{"exchange":"kraken","base":"BTC","quote":"ZUSD"},{"exchange":"kraken","base":"DRV","quote":"USD"},{"exchange":"kraken","base":"DRV","quote":"USDC"},{"exchange":"kraken","base":"DRV","quote":"USDT"},{"exchange":"kraken","base":"DRV","quote":"ZUSD"},{"exchange":"kraken","base":"EIGEN","quote":"USD"},{"exchange":"kraken","base":"EIGEN","quote":"USDC"},{"exchange":"kraken","base":"EIGEN","quote":"USDT"},{"exchange":"kraken","base":"EIGEN","quote":"ZUSD"},{"exchange":"kraken","base":"ETH","quote":"USD"},{"exchange":"kraken","base":"ETH","quote":"USDC"},{"exchange":"kraken","base":"ETH","quote":"USDT"},{"exchange":"kraken","base":"ETH","quote":"ZUSD"},{"exchange":"kraken","base":"FARTCOIN","quote":"USD"},{"exchange":"kraken","base":"FARTCOIN","quote":"USDC"},{"exchange":"kraken","base":"FARTCOIN","quote":"USDT"},{"exchange":"kraken","base":"FARTCOIN","quote":"ZUSD"},{"exchange":"kraken","base":"GRT","quote":"USD"},{"exchange":"kraken","base":"GRT","quote":"USDC"},{"exchange":"kraken","base":"GRT","quote":"USDT"},{"exchange":"kraken","base":"GRT","quote":"ZUSD"},{"exchange":"kraken","base":"IMX","quote":"USD"},{"exchange":"kraken","base":"IMX","quote":"USDC"},{"exchange":"kraken","base":"IMX","quote":"USDT"},{"exchange":"kraken","base":"IMX","quote":"ZUSD"},{"exchange":"kraken","base":"JUP","quote":"USD"},{"exchange":"kraken","base":"JUP","quote":"USDC"},{"exchange":"kraken","base":"JUP","quote":"USDT"},{"exchange":"kraken","base":"JUP","quote":"ZUSD"},{"exchange":"kraken","base":"KAITO","quote":"USD"},{"exchange":"kraken","base":"KAITO","quote":"USDC"},{"exchange":"kraken","base":"KAITO","quote":"USDT"},{"exchange":"kraken","base":"KAITO","quote":"ZUSD"},{"exchange":"kraken","base":"LINK","quote":"USD"},{"exchange":"kraken","base":"LINK","quote":"USDC"},{"exchange":"kraken","base":"LINK","quote":"USDT"},{"exchange":"kraken","base":"LINK","quote":"ZUSD"},{"exchange":"kraken","base":"MATIC","quote":"USD"},{"exchange":"kraken","base":"MATIC","quote":"USDC"},{"exchange":"kraken","base":"MATIC","quote":"USDT"},{"exchange":"kraken","base":"MATIC","quote":"ZUSD"},{"exchange":"kraken","base":"NEAR","quote":"USD"},{"exchange":"kraken","base":"NEAR","quote":"USDC"},{"exchange":"kraken","base":"NEAR","quote":"USDT"},{"exchange":"kraken","base":"NEAR","quote":"ZUSD"},{"exchange":"kraken","base":"ONDO","quote":"USD"},{"exchange":"kraken","base":"ONDO","quote":"USDC"},{"exchange":"kraken","base":"ONDO","quote":"USDT"},{"exchange":"kraken","base":"ONDO","quote":"ZUSD"},{"exchange":"kraken","base":"OP","quote":"USD"},{"exchange":"kraken","base":"OP","quote":"USDC"},{"exchange":"kraken","base":"OP","quote":"USDT"},{"exchange":"kraken","base":"OP","quote":"ZUSD"},{"exchange":"kraken","base":"PENDLE","quote":"USD"},{"exchange":"kraken","base":"PENDLE","quote":"USDC"},{"exchange":"kraken","base":"PENDLE","quote":"USDT"},{"exchange":"kraken","base":"PENDLE","quote":"ZUSD"},{"exchange":"kraken","base":"PENGU","quote":"USD"},{"exchange":"kraken","base":"PENGU","quote":"USDC"},{"exchange":"kraken","base":"PENGU","quote":"USDT"},{"exchange":"kraken","base":"PENGU","quote":"ZUSD"},{"exchange":"kraken","base":"PEPE","quote":"USD"},{"exchange":"kraken","base":"PEPE","quote":"USDC"},{"exchange":"kraken","base":"PEPE","quote":"USDT"},{"exchange":"kraken","base":"PEPE","quote":"ZUSD"},{"exchange":"kraken","base":"POL","quote":"USD"}]}}'
    Type: AWS::SSM::Parameter
  tgbottestspot8:
    Properties:
      DataType: text
      Description: Auto generated param
      Name: /config/tg_bot_test/spot8
      Type: String
      Value: '{"spot":{"asset_class":"spot","components":[{"exchange":"kraken","base":"POL","quote":"USDC"},{"exchange":"kraken","base":"POL","quote":"USDT"},{"exchange":"kraken","base":"POL","quote":"ZUSD"},{"exchange":"kraken","base":"POPCAT","quote":"USD"},{"exchange":"kraken","base":"POPCAT","quote":"USDC"},{"exchange":"kraken","base":"POPCAT","quote":"USDT"},{"exchange":"kraken","base":"POPCAT","quote":"ZUSD"},{"exchange":"kraken","base":"SEI","quote":"USD"},{"exchange":"kraken","base":"SEI","quote":"USDC"},{"exchange":"kraken","base":"SEI","quote":"USDT"},{"exchange":"kraken","base":"SEI","quote":"ZUSD"},{"exchange":"kraken","base":"SHIB","quote":"USD"},{"exchange":"kraken","base":"SHIB","quote":"USDC"},{"exchange":"kraken","base":"SHIB","quote":"USDT"},{"exchange":"kraken","base":"SHIB","quote":"ZUSD"},{"exchange":"kraken","base":"SNX","quote":"USD"},{"exchange":"kraken","base":"SNX","quote":"USDC"},{"exchange":"kraken","base":"SNX","quote":"USDT"},{"exchange":"kraken","base":"SNX","quote":"ZUSD"},{"exchange":"kraken","base":"SOL","quote":"USD"},{"exchange":"kraken","base":"SOL","quote":"USDC"},{"exchange":"kraken","base":"SOL","quote":"USDT"},{"exchange":"kraken","base":"SOL","quote":"ZUSD"},{"exchange":"kraken","base":"SUI","quote":"USD"},{"exchange":"kraken","base":"SUI","quote":"USDC"},{"exchange":"kraken","base":"SUI","quote":"USDT"},{"exchange":"kraken","base":"SUI","quote":"ZUSD"},{"exchange":"kraken","base":"TAO","quote":"USD"},{"exchange":"kraken","base":"TAO","quote":"USDC"},{"exchange":"kraken","base":"TAO","quote":"USDT"},{"exchange":"kraken","base":"TAO","quote":"ZUSD"},{"exchange":"kraken","base":"TIA","quote":"USD"},{"exchange":"kraken","base":"TIA","quote":"USDC"},{"exchange":"kraken","base":"TIA","quote":"USDT"},{"exchange":"kraken","base":"TIA","quote":"ZUSD"},{"exchange":"kraken","base":"TRUMP","quote":"USD"},{"exchange":"kraken","base":"TRUMP","quote":"USDC"},{"exchange":"kraken","base":"TRUMP","quote":"USDT"},{"exchange":"kraken","base":"TRUMP","quote":"ZUSD"},{"exchange":"kraken","base":"UNI","quote":"USD"},{"exchange":"kraken","base":"UNI","quote":"USDC"},{"exchange":"kraken","base":"UNI","quote":"USDT"},{"exchange":"kraken","base":"UNI","quote":"ZUSD"},{"exchange":"kraken","base":"USDC","quote":"USD"},{"exchange":"kraken","base":"USDC","quote":"USDC"},{"exchange":"kraken","base":"USDC","quote":"USDT"},{"exchange":"kraken","base":"USDC","quote":"ZUSD"},{"exchange":"kraken","base":"USDT","quote":"USD"},{"exchange":"kraken","base":"USDT","quote":"USDC"},{"exchange":"kraken","base":"USDT","quote":"USDT"},{"exchange":"kraken","base":"USDT","quote":"ZUSD"},{"exchange":"kraken","base":"VINE","quote":"USD"},{"exchange":"kraken","base":"VINE","quote":"USDC"},{"exchange":"kraken","base":"VINE","quote":"USDT"},{"exchange":"kraken","base":"VINE","quote":"ZUSD"},{"exchange":"kraken","base":"VIRTUAL","quote":"USD"},{"exchange":"kraken","base":"VIRTUAL","quote":"USDC"},{"exchange":"kraken","base":"VIRTUAL","quote":"USDT"},{"exchange":"kraken","base":"VIRTUAL","quote":"ZUSD"},{"exchange":"kraken","base":"WIF","quote":"USD"},{"exchange":"kraken","base":"WIF","quote":"USDC"},{"exchange":"kraken","base":"WIF","quote":"USDT"},{"exchange":"kraken","base":"WIF","quote":"ZUSD"},{"exchange":"kraken","base":"XLM","quote":"USD"},{"exchange":"kraken","base":"XLM","quote":"USDC"},{"exchange":"kraken","base":"XLM","quote":"USDT"},{"exchange":"kraken","base":"XLM","quote":"ZUSD"},{"exchange":"kraken","base":"XRP","quote":"USD"},{"exchange":"kraken","base":"XRP","quote":"USDC"},{"exchange":"kraken","base":"XRP","quote":"USDT"},{"exchange":"kraken","base":"XRP","quote":"ZUSD"}]}}'
    Type: AWS::SSM::Parameter