{"containerDefinitions": [{"name": "s3Saver-container", "image": "public.ecr.aws/docker/library/python:3.11", "cpu": 0, "portMappings": [{"containerPort": 8080, "hostPort": 8080, "protocol": "tcp"}], "essential": true, "environment": [{"name": "LOG_LEVEL", "value": "INFO"}, {"name": "STAGE", "value": "1"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/s3Saver-1-containers-production", "awslogs-region": "eu-west-1", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "s3Saver-task-definition-1", "taskRoleArn": "arn:aws:iam::685767522279:role/s3SaverContainerRole-2", "executionRoleArn": "arn:aws:iam::685767522279:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "8192", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "ARM64"}}